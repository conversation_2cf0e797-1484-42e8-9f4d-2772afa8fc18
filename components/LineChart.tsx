'use client'

import { useMemo } from 'react'
import { CartesianGrid, Line, LineChart, XAxis, YAxis } from 'recharts'

import {
	type ChartConfig,
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent,
} from '@/components/ui/chart'

// Chart configuration
const chartConfig = {
	price: {
		label: 'Price',
		color: 'hsl(var(--chart-1))',
	},
} satisfies ChartConfig

interface priceHistoryData {
	price: number
	createdAt: Date
}

interface ProcessedChartData {
	label: string
	price: number
	timestamp: number
}

/**
 * Process price history data based on time difference
 * @param data Raw price history data
 * @returns Processed data for chart display
 */
function processChartData(data: priceHistoryData[]): ProcessedChartData[] {
	if (!data || data.length === 0) {
		return []
	}

	// Sort data by date (oldest to newest)
	const sortedData = [...data].sort(
		(a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
	)

	// Get first and last timestamps
	const firstTimestamp = new Date(sortedData[0].createdAt).getTime()
	const lastTimestamp = new Date(
		sortedData[sortedData.length - 1].createdAt,
	).getTime()

	// Calculate time difference in milliseconds
	const timeDifference = lastTimestamp - firstTimestamp

	// Determine format based on time difference
	let format: 'hour' | 'day' | 'month' = 'hour'

	if (timeDifference > 30 * 24 * 60 * 60 * 1000) {
		// More than a month
		format = 'month'
	} else if (timeDifference > 24 * 60 * 60 * 1000) {
		// More than a day
		format = 'day'
	}

	// Check if we need to aggregate data (more than 10 data points)
	if (sortedData.length <= 10) {
		// Format each data point based on the determined format
		return sortedData.map((item) => {
			const date = new Date(item.createdAt)
			let label = ''

			if (format === 'hour') {
				label = date.toLocaleTimeString([], {
					hour: '2-digit',
					minute: '2-digit',
				})
			} else if (format === 'day') {
				label = date.toLocaleDateString([], { month: 'short', day: 'numeric' })
			} else {
				label = date.toLocaleDateString([], { month: 'short', year: '2-digit' })
			}

			return {
				label,
				price: item.price,
				timestamp: new Date(item.createdAt).getTime(),
			}
		})
	}

	// We need to aggregate data to have at most 10 points
	const segmentSize = Math.ceil(sortedData.length / 10)
	const aggregatedData: ProcessedChartData[] = []

	for (let i = 0; i < sortedData.length; i += segmentSize) {
		const segment = sortedData.slice(i, i + segmentSize)

		// Calculate average price for this segment
		const totalPrice = segment.reduce((sum, item) => sum + item.price, 0)
		const avgPrice = totalPrice / segment.length

		// Use the middle point's timestamp for the label
		const midIndex = Math.floor(segment.length / 2)
		const midDate = new Date(segment[midIndex].createdAt)
		let label = ''

		if (format === 'hour') {
			label = midDate.toLocaleTimeString([], {
				hour: '2-digit',
				minute: '2-digit',
			})
		} else if (format === 'day') {
			label = midDate.toLocaleDateString([], {
				month: 'short',
				day: 'numeric',
			})
		} else {
			label = midDate.toLocaleDateString([], {
				month: 'short',
				year: '2-digit',
			})
		}

		aggregatedData.push({
			label,
			price: avgPrice,
			timestamp: midDate.getTime(),
		})
	}

	return aggregatedData
}

export function ActivityLineChart({ data }: { data: priceHistoryData[] }) {
	// Process the price history data
	const processedData = useMemo(() => processChartData(data), [data])

	return (
		<div className='bg-[#191919] pr-4 py-4 rounded-md'>
			{processedData.length > 0 ? (
				<ChartContainer
					config={chartConfig}
					className='lg:aspect-auto lg:h-[440px]'
				>
					<LineChart height={440} accessibilityLayer data={processedData}>
						<CartesianGrid vertical={false} />
						<XAxis
							dataKey='label'
							tickLine={false}
							axisLine={false}
							tickMargin={8}
						/>
						<YAxis
							tickLine={false}
							axisLine={false}
							tickMargin={8}
							tickFormatter={(value) => `${value.toLocaleString()} SOL`}
						/>
						<ChartTooltip
							cursor={false}
							content={<ChartTooltipContent hideLabel />}
						/>
						<Line
							dataKey='price'
							type='natural'
							stroke='var(--color-primary)'
							strokeWidth={2}
							dot={{
								fill: 'var(--color-primary)',
							}}
							activeDot={{
								r: 6,
							}}
						/>
					</LineChart>
				</ChartContainer>
			) : (
				<div className='flex justify-center items-center h-[440px] text-gray-400'>
					No price history data available
				</div>
			)}
		</div>
	)
}
