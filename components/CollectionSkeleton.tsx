import { useId } from 'react'
import { cn } from '@/lib/utils'

export default function CollectionSkeleton() {
	const id = useId()

	return (
		<div className='containerPadding pt-4 animate-pulse'>
			{/* Breadcrumb */}
			<div className='flex gap-2 items-center'>
				<div className='h-4 w-12 bg-gray-200/20 rounded-full' />
				<div className='h-4 w-4 bg-gray-200/20 rounded-full' />
				<div className='h-4 w-32 bg-gray-200/20 rounded-full' />
			</div>

			{/* Collection Header */}
			<div className='pt-[4rem] lg:pt-[5rem]'>
				<div className='relative flex flex-col items-start'>
					{/* Profile Image */}
					<div className='relative rounded-full w-auto after:contents-[*] after:absolute after:-inset-2 after:p-1 after:bg-gradient-to-r after:from-[#FF8F1F]/30 after:to-[#DD0003]/30 after:-z-[1] after:rounded-full'>
						<div className='h-[100px] w-[100px] lg:w-[180px] lg:h-[180px] rounded-full bg-gray-200/20' />
					</div>

					<div className='flex flex-col lg:flex-row w-full justify-between'>
						<div>
							{/* Collection Name */}
							<div className='flex items-center mt-4'>
								<div className='h-6 w-8 bg-gray-200/20 rounded-full' />
								<div className='h-6 w-48 bg-gray-200/20 rounded-full ml-2' />
								<div className='h-6 w-6 bg-gray-200/20 rounded-full ml-2' />
							</div>

							{/* Description */}
							<div className='mt-2 space-y-2'>
								<div className='h-4 w-full lg:w-2/3 bg-gray-200/20 rounded-full' />
								<div className='h-4 w-5/6 lg:w-1/2 bg-gray-200/20 rounded-full' />
								<div className='h-4 w-4/6 lg:w-1/3 bg-gray-200/20 rounded-full' />
							</div>
						</div>

						{/* Stats */}
						<div className='flex flex-col mt-6 lg:mt-0'>
							<div className='flex justify-center divide-x-2 gap-x-5'>
								<div className='h-6 w-24 bg-gray-200/20 rounded-full pr-5' />
								<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
							</div>
						</div>
					</div>

					{/* Action Buttons */}
					<div className='absolute flex gap-x-3 items-center justify-end w-full -top-4 lg:top-2'>
						<div className='h-8 w-8 bg-gray-200/20 rounded-full' />
						<div className='h-8 w-8 bg-gray-200/20 rounded-full' />
					</div>
				</div>
			</div>

			{/* Tabs */}
			<div className='mt-7 w-full flex gap-x-5 lg:gap-x-10 items-center py-4 border-b-[1.5px] border-[#3A3939]'>
				<div className='h-8 w-16 bg-gray-200/20 rounded-full' />
				<div className='h-8 w-24 bg-gray-200/20 rounded-full opacity-50' />
			</div>

			{/* Filter Bar */}
			<div className='flex items-end justify-between mt-7'>
				<div className='flex flex-col-reverse gap-y-1 items-center lg:flex-row'>
					<div className='h-5 w-24 bg-gray-200/20 rounded-full' />
				</div>
			</div>

			{/* NFT Grid */}
			<div className='mt-7 lg:mt-10'>
				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5 lg:gap-7'>
					{Array(8)
						.fill(0)
						.map((_, index) => (
							<div
								key={`skeleton-nft-${index}-${id}`}
								className='flex flex-col rounded-[20px] overflow-hidden bg-gray-200/10'
							>
								{/* NFT Image */}
								<div className='aspect-square w-full bg-gray-200/20' />

								{/* NFT Details */}
								<div className='p-4 space-y-3'>
									{/* NFT Name */}
									<div className='h-6 w-3/4 bg-gray-200/20 rounded-full' />

									{/* Owner */}
									<div className='flex items-center gap-2'>
										<div className='h-8 w-8 bg-gray-200/20 rounded-full' />
										<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
									</div>

									{/* Price */}
									<div className='flex justify-between'>
										<div className='h-5 w-20 bg-gray-200/20 rounded-full' />
										<div className='h-5 w-12 bg-gray-200/20 rounded-full' />
									</div>
								</div>
							</div>
						))}
				</div>
			</div>

			{/* Pagination */}
			<div className='flex justify-center mt-7'>
				<div className='flex gap-2'>
					{Array(5)
						.fill(0)
						.map((_, index) => (
							<div
								key={`skeleton-pagination-${index}-${id}`}
								className='h-8 w-8 bg-gray-200/20 rounded-md'
							/>
						))}
				</div>
			</div>
		</div>
	)
}
