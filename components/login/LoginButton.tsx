'use client'
import { publicKey as createPub<PERSON><PERSON><PERSON> } from '@metaplex-foundation/umi'
import type { Adapter } from '@solana/wallet-adapter-base'
import { useWallet } from '@solana/wallet-adapter-react'
import { jwtDecode } from 'jwt-decode'
import { LogOutIcon, UserIcon, WalletIcon } from 'lucide-react'
import { useCallback, useContext, useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'
import { Button } from '@/components/ui/button'
import {
	Dialog,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog'
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuPortal,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useMeUser } from '@/hooks/useMeUser'
import { UmiContext } from '@/lib/umi'
import {
	encoder,
	getCurrentTimeStamp,
	toggleOverlay,
	truncateAddress,
	uint8ArrayToBase64,
} from '@/lib/utils'
import customToast from '../CustomToast'
import { useLogin } from '@/hooks/useLogin' // Import your hook here

interface LoginButtonProps {
	disableOverlay?: boolean
}

export const LoginButton: React.FC<LoginButtonProps> = ({
	disableOverlay = false,
}) => {
	const {
		wallets,
		select,
		connect,
		disconnect,
		connected,
		publicKey,
		signMessage,
	} = useWallet()
	const umi = useContext(UmiContext)
	const [isOpen, setIsOpen] = useState(false)
	const [accessToken, setAccessToken] = useState<string | null>(null)
	const [loading, setLoading] = useState(false)
	const [continueSignIn, setContinueSignIn] = useState(false)
	const { data: user, isLoading: isUserLoading, error: userError } = useMeUser()

	// Use your useLogin hook here
	const {
		login,
		logout,
		isLoggingIn,
		isLoggingOut,
		error: loginError,
	} = useLogin()

	// Simple modal toggle
	const toggleModal = () => setIsOpen(!isOpen)

	// Get wallet balance using Umi
	const getBalance = useCallback(async () => {
		if (!publicKey || !umi) return '0'
		try {
			const umiPublicKey = createPublicKey(publicKey.toBase58())
			const balance = await umi.rpc.getBalance(umiPublicKey)
			if (!balance) return '0'
			const solValue = (Number(balance.basisPoints) / 10 ** balance.decimals)
				.toFixed(4)
				.replace(/\.?0+$/, '')
			return solValue
		} catch (error) {
			console.error('Error fetching balance:', error)
			return '0'
		}
	}, [publicKey, umi])

	const [solBalance, setSolBalance] = useState('0')
	const [dropdownOpen, setDropdownOpen] = useState(false)

	// Fetch balance when wallet is connected
	useEffect(() => {
		if (connected && publicKey) {
			getBalance().then(setSolBalance)
		} else {
			setSolBalance('0')
		}
	}, [connected, publicKey, getBalance])

	// Handle wallet login with signature
	const handleSignIn = useCallback(async () => {
		if (!connected || !publicKey || !signMessage) {
			customToast.error('Wallet not connected properly')
			setLoading(false)
			return
		}

		let newUser = false
		try {
			setLoading(true)
			const message = `signin${getCurrentTimeStamp()}`

			let signature: Uint8Array
			try {
				signature = await signMessage(encoder.encode(message))
			} catch (signError) {
				customToast.error('Failed to sign message')
				setLoading(false)
				return
			}

			if (!signature) {
				customToast.error('Failed to get signature from wallet')
				setLoading(false)
				return
			}

			const encodedSignature = uint8ArrayToBase64(signature)

			// Use login function from hook instead of onLogin
			const response = await login({
				publicKey: publicKey.toString(),
				message,
				signature: encodedSignature,
			})

			if (response.success && response.data) {
				customToast.success('Logged in successfully')
				localStorage.setItem('accessToken', response.data.accessToken || '')
				localStorage.setItem('refreshToken', response.data.refreshToken || '')
				localStorage.setItem('views', '{}')
				setAccessToken(response.data.accessToken || null)

				try {
					toggleOverlay(false)
				} catch (overlayError) {
					console.error('Error toggling overlay:', overlayError)
				}

				newUser = response?.isNewUser ?? false

				if (response.isNewUser) {
					navigate('/edit-profile')
				}
			} else {
				customToast.error(response.message || 'Failed to log in')
			}
		} catch (error) {
			customToast.error('Failed to log in')
			console.error('Sign-in error:', error)
		} finally {
			setLoading(false)
			const token = localStorage.getItem('accessToken')
			if (token && !newUser) {
				setTimeout(() => {
					window.location.reload()
				}, 1000)
			}
		}
	}, [connected, publicKey, signMessage, login])

	// Handle logout
	const handleSignOut = useCallback(async () => {
		try {
			setLoading(true)
			await logout()
			customToast('Logged out successfully')
			disconnect()
			localStorage.clear()
			setAccessToken(null)

			const currentPath = window.location.pathname
			if (
				currentPath.startsWith('/profile') ||
				currentPath.startsWith('/protected')
			) {
				navigate('/')
			} else {
				window.location.reload()
			}
		} catch (error) {
			console.error('Sign-out error:', error)
		} finally {
			setLoading(false)
		}
	}, [logout, disconnect])

	// Determine button text based on connection and auth state
	const getButtonText = () => {
		if (loading || isLoggingIn) return 'Signing in...'
		if (isLoggingOut) return 'Logging out...'
		if (!connected) return 'Connect Wallet'
		if (!accessToken) return 'Sign Message'
		return 'Log out'
	}

	const handleButtonClick = async () => {
		if (connected && accessToken) {
			await handleSignOut()
		} else if (!connected) {
			toggleModal()
		} else {
			await handleSignIn()
		}
	}

	const handleWalletConnect = async (adapter: Adapter) => {
		try {
			setLoading(true)
			select(adapter.name)
			await disconnect()
			await connect()
			toggleModal()
			if (!accessToken) {
				setTimeout(() => {
					setContinueSignIn(true)
				}, 300)
			}
			setLoading(false)
		} catch (error) {
			console.error('Wallet connection error:', error)
			setLoading(false)
		}
	}

	useEffect(() => {
		if (accessToken && publicKey) {
			try {
				const decoded = jwtDecode(accessToken) as { publicKey: string }
				if (publicKey.toString() !== decoded?.publicKey) {
					handleSignOut()
					return
				}
			} catch {
				// ignore decode errors
			}
		}

		if (publicKey && continueSignIn) {
			const timer = setTimeout(() => {
				handleSignIn()
				setContinueSignIn(false)
			}, 500)
			return () => clearTimeout(timer)
		}
	}, [publicKey, continueSignIn, handleSignIn, accessToken, handleSignOut])

	const handleProfileClick = () => {
		navigate(`/profile/${publicKey}`)
	}

	useEffect(() => {
		setAccessToken(localStorage.getItem('accessToken'))
	}, [])

	useEffect(() => {
		if (dropdownOpen) {
			if (document.body.style.overflow === 'hidden') {
				document.body.style.overflow = ''
			}
		}
	}, [dropdownOpen])

	return (
		<div className='relative'>
			{/* Wallet Button with Dropdown */}
			{connected && accessToken ? (
				<DropdownMenu onOpenChange={setDropdownOpen}>
					<DropdownMenuTrigger asChild>
						<Button
							variant='secondary'
							className='flex text-xs lg:text-sm items-center gap-2 bg-card/80 text-card-foreground hover:bg-secondary/80 h-10 pl-2 pr-1 backdrop-blur-sm border border-muted/20 lg:pl-4 lg:pr-2'
							disabled={isLoggingIn || isLoggingOut}
						>
							<div className='flex items-center gap-2'>
								<WalletIcon className='h-4 w-4' />
								<span className='text-card-foreground'>{solBalance} SOL</span>
								<span className='mx-1 h-4 w-[1px] bg-gray-600/50' />
								<span className='max-w-[100px] min-[430px]:max-w-[130px] md:max-w-[100px] lg:max-w-[130px] truncate'>
									{user?.userName}
								</span>
							</div>
							<div className='h-8 w-8 rounded-full bg-primary/90 overflow-hidden shadow-sm'>
								{isLoggingOut ? (
									<div className='h-full w-full flex items-center justify-center'>
										<span className='animate-spin border-2 border-white border-t-transparent rounded-full w-4 h-4' />
									</div>
								) : (
									<img
										src={user?.imageUrl || '/assets/img/default-profile.png'}
										alt='Profile'
										className='h-full w-full object-cover'
									/>
								)}
							</div>
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuPortal>
						<DropdownMenuContent
							align='end'
							className='w-56 bg-card/80 backdrop-blur-md border border-muted/30 shadow-xl rounded-xl overflow-hidden'
							sideOffset={8}
							avoidCollisions={true}
							forceMount
						>
							{connected && publicKey && (
								<>
									<div className='px-2 py-1.5 text-sm'>
										<div className='font-medium'>Wallet</div>
										<div className='text-xs text-muted-foreground'>
											{truncateAddress(publicKey.toString())}
										</div>
										<div className='mt-1 text-xs font-medium'>
											Balance:{' '}
											<span className='text-card-foreground'>
												{solBalance} SOL
											</span>
										</div>
									</div>
									<DropdownMenuSeparator />
								</>
							)}
							<DropdownMenuItem
								onClick={handleProfileClick}
								className='cursor-pointer !text-foreground hover:!text-foreground/70 hover:font-medium hover:bg-primary/10 focus:bg-primary/10 py-2'
								disabled={isLoggingOut}
							>
								<UserIcon className='mr-2 h-4 w-4 text-foreground' />
								<span>Profile</span>
							</DropdownMenuItem>
							<DropdownMenuSeparator className='bg-muted/20' />
							<DropdownMenuItem
								onClick={handleSignOut}
								className='cursor-pointer !text-secondary hover:!text-secondary/70 hover:font-medium hover:bg-secondary/10 focus:bg-secondary/10 py-2'
								disabled={isLoggingOut}
							>
								{isLoggingOut ? (
									<span className='animate-spin border-2 border-secondary border-t-transparent rounded-full w-4 h-4 mr-2' />
								) : (
									<LogOutIcon className='mr-2 h-4 w-4 text-secondary' />
								)}
								<span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenuPortal>
				</DropdownMenu>
			) : (
				<Button
					variant='default'
					onClick={handleButtonClick}
					className='bg-primary text-primary-foreground'
					disabled={loading || isLoggingIn}
				>
					{loading || isLoggingIn ? (
						<span className='animate-spin border-2 border-white border-t-transparent rounded-full w-5 h-5 mr-2' />
					) : null}
					{getButtonText()}
				</Button>
			)}

			{/* Wallet Selection Modal */}
			<Dialog
				open={isOpen}
				onOpenChange={(open) => {
					if (!loading) {
						setIsOpen(open)
					}
				}}
			>
				<DialogContent className='sm:max-w-md bg-card/90 backdrop-blur-md border border-muted/30 z-999'>
					<DialogHeader>
						<DialogTitle>Select a Wallet</DialogTitle>
					</DialogHeader>
					<div className='space-y-2 mt-4'>
						{wallets.map((wallet) => (
							<Button
								key={wallet.adapter.name}
								variant='outline'
								className='w-full justify-start bg-background/50 hover:bg-muted/50'
								onClick={() => handleWalletConnect(wallet.adapter)}
								disabled={loading}
							>
								{wallet.adapter.name}
							</Button>
						))}
					</div>
					<DialogFooter className='mt-6'>
						<Button
							variant='destructive'
							onClick={toggleModal}
							disabled={loading}
						>
							Close
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	)
}
