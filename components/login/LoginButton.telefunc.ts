import { sign } from 'hono/jwt'
import { base64ToUint8Array, verifySolanaSignature } from '@/lib/utils'
import {
	ACCESS_TOKEN_EXPIRATION,
	ACCESS_TOKEN_SECRET,
	REFRESH_TOKEN_EXPIRATION,
	REFRESH_TOKEN_SECRET,
} from '@/server/config/auth.config'
import { getCurrentTimeStamp } from '@/server/helpers/getTime'
import { generateUniqueUsername } from '@/server/helpers/getUniqueUserName'
import { getUser } from '@/server/helpers/getUser'
import { prisma } from '@/server/lib/prismaClient'

export async function onLogin({
	publicKey,
	message,
	signature,
}: {
	publicKey: string
	message: string
	signature: string
}) {
	try {
		if (!publicKey) throw new Error('Public key is required')
		if (!message) throw new Error('Message is required')
		if (!signature) throw new Error('Signature is required')

		let isNewUser = false

		const decodedSignature = base64ToUint8Array(signature)

		const verified = await verifySolanaSignature({
			publicKey: publicKey,
			signature: decodedSignature,
			message: message,
		})

		if (!verified) {
			return {
				success: false,
				message: 'Login Failed!',
				data: {
					accessToken: '',
					refreshToken: '',
				},
			}
		}

		let user = await prisma.user.findUnique({
			where: {
				publicKey: publicKey,
			},
			select: {
				id: true,
				publicKey: true,
				userName: true,
			},
		})

		if (!user) {
			const genratedUserName = await generateUniqueUsername()
			user = await prisma.user.create({
				data: {
					publicKey,
					userName: genratedUserName,
					username: genratedUserName.toLowerCase(),
				},
			})
			isNewUser = true
		}

		const basePayload = {
			publicKey: user.publicKey,
			userId: user.id,
		}

		const refreshTokenPayload = {
			...basePayload,
			exp: getCurrentTimeStamp() + REFRESH_TOKEN_EXPIRATION,
		}

		const refreshToken = await sign(refreshTokenPayload, REFRESH_TOKEN_SECRET)

		const session = await prisma.session.create({
			data: {
				userId: user.id,
				refreshToken: refreshToken,
				active: true,
			},
		})

		const accessTokenPayload = {
			...basePayload,
			exp: getCurrentTimeStamp() + ACCESS_TOKEN_EXPIRATION,
			sessionId: session.id,
		}

		const accessToken = await sign(accessTokenPayload, ACCESS_TOKEN_SECRET)

		return {
			success: true,
			message: 'Login Successful!',
			isNewUser,
			data: {
				accessToken: accessToken,
				refreshToken: refreshToken,
				userName: user.userName,
			},
		}
	} catch (e) {
		return {
			success: false,
			message: 'Login Failed!',
			isNewUser: false,
			data: {
				accessToken: '',
				refreshToken: '',
			},
		}
	}
}

export async function onLogout() {
	const user = getUser()
	try {
		await prisma.session.updateMany({
			where: {
				id: user.sessionId,
			},
			data: {
				active: false,
			},
		})
		return {
			success: true,
			message: 'Logout successful',
			data: {},
		}
	} catch (e) {
		return {
			success: false,
			message: 'Logout failed',
			data: {},
		}
	}
}

export async function onLogoutOfAllDevices() {
	const user = getUser()
	try {
		await prisma.session.updateMany({
			where: {
				userId: user.userId,
				active: true,
			},
			data: {
				active: false,
			},
		})
		return {
			success: true,
			message: 'Logout successful',
			data: {},
		}
	} catch (e) {
		return {
			success: false,
			message: 'Logout failed',
			data: {},
		}
	}
}
