'use client'
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog'
import { LoginButton } from './LoginButton'

interface LoginModalProps {
	isOpen: boolean
	setIsOpen: (open: boolean) => void
}

const LoginModal = ({ isOpen, setIsOpen }: LoginModalProps) => {
	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogContent className='sm:max-w-md bg-white/95 backdrop-blur-md border border-gray-200 shadow-xl rounded-lg'>
				<DialogHeader>
					<DialogTitle className='text-xl font-semibold text-gray-900'>
						Connect Your Wallet
					</DialogTitle>
				</DialogHeader>
				<div className='mt-4 flex justify-center mx-auto'>
					<LoginButton />
				</div>
			</DialogContent>
		</Dialog>
	)
}

export default LoginModal
