import { useState } from 'react'
import { navigate } from 'vike/client/router'
import { LoginButton } from './login/LoginButton'

export default function LoginOverlay() {
	const [isVisible, setIsVisible] = useState(true) // State to control overlay visibility

	const handleLoginButtonClick = async () => {
		// Step 1: Navigate to the home screen
		navigate('/') // Replace "/home" with your actual home route
		// Step 2: Hide the overlay
		setIsVisible(false)
		// Step 3: Perform the action inside the LoginButton
		// This will be handled by the LoginButton component itself
	}

	if (!isVisible) {
		return null // Don't render the overlay if it's not visible
	}
	return (
		<div
			style={{
				position: 'fixed',
				top: 0,
				left: 0,
				width: '100vw',
				height: '100vh',
				backgroundColor: 'rgba(0, 0, 0, 0.8)',
				display: 'flex',
				justifyContent: 'center',
				alignItems: 'center',
				zIndex: 9999,
			}}
		>
			<LoginButton />
			{/* Pass the callback to the LoginButton */}
		</div>
	)
}
