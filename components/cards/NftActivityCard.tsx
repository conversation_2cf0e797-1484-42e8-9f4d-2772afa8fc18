import {
	ArrowLeftRight,
	Gavel,
	HandCoins,
	ShoppingCart,
	Tag,
	Trash2,
	User,
} from 'lucide-react'
import type { NFTActivity } from '@/pages/nft-details/Page.telefunc'

interface NftActivityCardProps {
	activity: NFTActivity
}

export default function NftActivityCard({ activity }: NftActivityCardProps) {
	// Helper function to format SOL amount
	const formatSolAmount = (amount: number | null | undefined): number => {
		if (!amount || amount < 0.000001) return 0
		return Number(amount.toFixed(4))
	}

	// Format date to a more readable format
	const formatDate = (dateString: string) => {
		const date = new Date(dateString)
		// If less than 24 hours ago, show relative time
		const now = new Date()
		const diffMs = now.getTime() - date.getTime()
		const diffHours = diffMs / (1000 * 60 * 60)

		if (diffHours < 24) {
			if (diffHours < 1) {
				const diffMinutes = Math.floor(diffMs / (1000 * 60))
				return `${diffMinutes} min ago`
			}
			return `${Math.floor(diffHours)} hours ago`
		}

		// Otherwise show the date
		return date.toLocaleDateString()
	}

	// Get icon based on activity type
	const getActivityIcon = (type: string) => {
		switch (type) {
			case 'NFT_LISTED':
				return <Tag className='w-3 h-auto' />
			case 'NFT_PRICE_UPDATED':
				return <Tag className='w-3 h-auto' />
			case 'NFT_TRANSFERRED':
				return <ArrowLeftRight className='w-3 h-auto' />
			case 'NFT_BID_PLACED':
				return <Gavel className='w-3 h-auto' />
			case 'NFT_LISTING_CANCELLED':
				return <Trash2 className='w-3 h-auto' />
			case 'NFT_OFFER_CREATED':
				return <HandCoins className='w-3 h-auto' />
			case 'NFT_OFFER_ACCEPTED':
				return <HandCoins className='w-3 h-auto' />
			case 'NFT_OFFER_CANCELLED':
				return <Trash2 className='w-3 h-auto' />
			case 'ACCOUNT_CREATED':
				return <User className='w-3 h-auto' />
			case 'NFT_MINTED':
				return <ShoppingCart className='w-3 h-auto' />
			case 'COLLECTION_CREATED':
				return <ShoppingCart className='w-3 h-auto' />
			case 'METADATA_UPDATED':
				return <ShoppingCart className='w-3 h-auto' />
			default:
				return <ShoppingCart className='w-3 h-auto' />
		}
	}

	// Format activity type for display with more meaningful descriptions
	const formatActivityType = (type: string) => {
		switch (type) {
			case 'NFT_MINTED':
				return 'Minted NFT'
			case 'NFT_LISTED':
				return 'Listed for Sale'
			case 'NFT_PRICE_UPDATED':
				return 'Updated Price'
			case 'NFT_TRANSFERRED':
				return 'Transferred NFT'
			case 'COLLECTION_CREATED':
				return 'Created Collection'
			case 'METADATA_UPDATED':
				return 'Updated Metadata'
			case 'NFT_BID_PLACED':
				return 'Placed Bid'
			case 'NFT_LISTING_CANCELLED':
				return 'Cancelled Listing'
			case 'NFT_OFFER_CREATED':
				return 'Made Offer'
			case 'NFT_OFFER_ACCEPTED':
				return 'Accepted Offer'
			case 'NFT_OFFER_CANCELLED':
				return 'Cancelled Offer'
			case 'ACCOUNT_CREATED':
				return 'Created Account'
			default:
				return type
					.split('_')
					.map((word) => word.charAt(0) + word.slice(1).toLowerCase())
					.join(' ')
		}
	}

	return (
		<div className='px-3 py-4 rounded-[5px] bg-[#1F1F1F]'>
			<div className='flex items-center justify-between'>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>When</span>
					<span className='text-[10px] font-medium mt-1'>
						{formatDate(activity.date)}
					</span>
				</div>
			</div>
			<div className='flex items-center justify-between mt-4'>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>Activity</span>
					<div className='flex items-center gap-x-1 mt-2'>
						{getActivityIcon(activity.type)}
						<span className='text-[10px] font-medium ml-1'>
							{formatActivityType(activity.type)}
						</span>
					</div>
				</div>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>Amount</span>
					{activity.price ? (
						<>
							<span className='text-[10px] font-medium mt-2'>
								{formatSolAmount(activity.price.sol)} SOL
							</span>
							<span className='text-[10px] text-[#7E7E7E] mt-1'>
								$
								{activity.price.usd ? Number(activity.price.usd.toFixed(2)) : 0}{' '}
								USD
							</span>
						</>
					) : (
						<span className='text-[10px] font-medium mt-2'>-</span>
					)}
				</div>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>User</span>
					<span className='text-[10px] font-medium text-primary mt-2'>
						{activity.from ? activity.from.username : '-'}
					</span>
				</div>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>Recipient</span>
					<span className='text-[10px] font-medium text-primary mt-2'>
						{activity.to ? activity.to.username : '-'}
					</span>
				</div>
			</div>
		</div>
	)
}
