import { Image } from '@unpic/react'
import { Link } from '@/components/atoms/Link'

type CreatorType = {
	img?: string
	creator: {
		profileImg?: string
		name: string
		caption: string
	}
	username?: string // Username for navigation
}

export default function TrendingCreatorCard({
	img,
	creator,
	username,
}: CreatorType) {
	const cardContent = (
		<div className='trendingCreatorCard relative group cursor-pointer transition-transform duration-300 hover:translate-y-[-5px]'>
			<Image
				src={img || '/assets/img/banner.png'}
				className='w-full h-[188px] lg:h-[210px] object-cover rounded-[20px] transition-transform duration-500 group-hover:scale-[1.02]'
				layout='fixed'
				width={300}
				height={210}
				alt=''
			/>

			<div className='flex flex-col items-center -mt-16'>
				<Image
					src={creator.profileImg || '/assets/img/default-profile.png'}
					className='block w-[96px] h-[96px] lg:w-[118px] lg:h-[118px] rounded-full transition-all duration-500 group-hover:scale-105 group-hover:shadow-[0_0_20px_rgba(255,255,255,0.25)]'
					layout='fixed'
					width={118}
					height={118}
					alt='user profile'
				/>
				<span className='text-sm lg:text-base font-semibold mt-3 transition-all duration-300 group-hover:text-white'>
					{creator.name}
				</span>
				<span className='text-xs lg:text-sm font-light transition-all duration-300 group-hover:text-white/80'>
					{creator.caption}
				</span>
			</div>
		</div>
	)

	// If username is provided, wrap with Link component for navigation
	return username ? (
		<Link href={`/profile/${username}`}>{cardContent}</Link>
	) : (
		cardContent
	)
}
