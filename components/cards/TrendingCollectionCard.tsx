import { Image } from '@unpic/react'
import React, { useId } from 'react'
import { cn } from '@/lib/utils'
import { Link } from '../atoms/Link'
import { Button } from '../ui/button'

type TrendingCollectionCardProps = {
	collectionImgs: Array<{ img: string; alt?: string }>
	publicKey: string
	name: string
	ownerName: string
	ownerImageUrl: string
}

export default function TrendingCollectionCard({
	collectionImgs,
	publicKey,
	name,
	ownerName,
	ownerImageUrl,
}: TrendingCollectionCardProps) {
	const id = useId()

	//extend collectionImgs upto 3, if only one img is provided repeat that upto 3 times
	const extendedCollectionImgs: TrendingCollectionCardProps['collectionImgs'] =
		[
			...collectionImgs,
			...Array(Math.max(0, 3 - collectionImgs.length)).fill(collectionImgs[0]),
		]

	return (
		<Link
			href={`/collection/${publicKey}`}
			className={cn(
				'relative bg-card p-3 lg:p-4 rounded-[20px] block transition-all duration-300',
				'hover:shadow-[0_8px_30px_rgb(0,0,0,0.12)] hover:translate-y-[-5px] group',
			)}
		>
			<div className='grid grid-cols-2 gap-x-1.5 gap-y-2 lg:gap-x-3 lg:gap-y-4'>
				{extendedCollectionImgs.map((item, index) => (
					<Image
						src={item.img || '/assets/img/default-card-image.png'}
						key={`${id}-${
							// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
							index
						}`}
						className={cn(
							'w-full object-cover h-[93px] lg:h-[142px] rounded-[10px] transition-transform duration-500 group-hover:scale-[1.02]',
							index === 2 && 'col-span-2 h-[185px] md:h-[220px] lg:h-[280px]',
						)}
						layout='fixed'
						width={390}
						height={280}
						alt={item.alt || 'collection image'}
					/>
				))}
			</div>
			<div
				className={cn(
					'absolute bottom-5 lg:bottom-[30px] left-5 right-5 lg:left-[28px] lg:right-[28px]',
					'backdrop-blur-[10px] flex flex-col justify-between gap-y-1 lg:gap-y-2 font-semibold p-2 lg:p-4 rounded-[10px]',
					'transition-all duration-300 group-hover:backdrop-blur-[15px]',
				)}
				style={{ background: 'rgba(216, 216, 216, 0.5)' }}
			>
				<div className='flex gap-x-2 items-center'>
					<Image
						src={
							ownerImageUrl ? ownerImageUrl : '/assets/img/default-profile.png'
						}
						className='rounded-full w-[26px] h-[26px] shrink-0'
						layout='fixed'
						width={26}
						height={26}
						alt='user profile'
					/>
					<span className='text-xs lg:text-sm'>{ownerName}</span>
					{/*
						
					<span className='text-base font-semibold ml-auto'>5.5 SOL</span>
					*/}
				</div>
				<div className='flex items-center gap-x-2'>
					<span className='text-xs lg:text-sm font-semibold leading-none'>
						{name}
					</span>
					<Button
						className={cn(
							'h-[30px] lg:h-[36px] ml-auto px-3 text-xs lg:text-sm lg:px-5',
							'transition-all duration-300 group-hover:bg-opacity-90 group-hover:scale-105',
						)}
					>
						BUY NOW
					</Button>
				</div>
			</div>
		</Link>
	)
}

// import { Image } from '@unpic/react'
// import React, { useId } from 'react'
// import { cn } from '@/lib/utils'
// import { Link } from '../atoms/Link'
// import { Button } from '../ui/button'

// type TrendingCollectionCardProps = {
// 	collectionImgs: Array<{ img: string; alt?: string }>
// 	publicKey: string
// 	name: string
// }

// export default function TrendingCollectionCard({
// 	collectionImgs,
// 	publicKey,
// 	name,
// }: TrendingCollectionCardProps) {
// 	const id = useId()
// 	return (
// 		<Link
// 			href={`/collection/${publicKey}`}
// 			className='relative bg-card p-3 lg:p-4 rounded-[20px]'
// 		>
// 			<div className='grid grid-col-2 gap-x-1.5 gap-y-2 lg:gap-x-3 lg:gap-y-4'>
// 				{collectionImgs.map((item, index) => (
// 					<Image
// 						src={item.img}
// 						key={id}
// 						className={cn(
// 							'w-full object-cover h-[93px] lg:h-[142px] rounded-[10px] transition-transform duration-500 group-hover:scale-[1.02]',
// 							index === 2 && 'col-span-2 h-[185px] md:h-[220px] lg:h-[280px]',
// 						)}
// 						layout='fixed'
// 						width={390}
// 						height={280}
// 						alt={item.alt}
// 					/>
// 				))}
// 			</div>
// 			<div
// 				className='absolute bottom-5 lg:bottom-[30px] left-5 right-5 lg:left-[28px] lg:right-[28px] backdrop-blur-[10px] flex flex-col justify-between gap-y-1 lg:gap-y-2 font-semibold p-2 lg:p-4 rounded-[10px] transition-all duration-300 group-hover:backdrop-blur-[15px]'
// 				style={{ background: 'rgba(216, 216, 216, 0.5)' }}
// 			>
// 				<div className='flex gap-x-2'>
// 					<Image
// 						src='/assets/temp/user-profile.png'
// 						className='rounded-full transition-transform duration-300 group-hover:scale-110'
// 						layout='fixed'
// 						width={26}
// 						height={26}
// 						alt='user profile'
// 					/>
// 					<span className='text-xs lg:text-sm'>Peter Mussolini</span>
// 					<span className='text-base font-semibold ml-auto'>5.5 SOL</span>
// 				</div>
// 				<div className='flex items-center gap-x-2'>
// 					<span className='text-xs lg:text-sm font-semibold leading-none'>
// 						{name}
// 					</span>
// 					<Button className='h-[30px] lg:h-[36px] ml-auto px-3 text-xs lg:text-sm lg:px-5 transition-all duration-300 group-hover:bg-opacity-90 group-hover:scale-105'>
// 						BUY NOW
// 					</Button>
// 				</div>
// 			</div>
// 		</Link>
// 	)
// }
