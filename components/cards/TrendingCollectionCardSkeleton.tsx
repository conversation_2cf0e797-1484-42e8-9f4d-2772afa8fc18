import React from 'react'
import { cn } from '@/lib/utils'

export default function TrendingCollectionCardSkeleton() {
	return (
		<div
			className={cn(
				'relative bg-card p-3 lg:p-4 rounded-[20px] block animate-pulse',
			)}
		>
			<div className='grid grid-cols-2 gap-x-1.5 gap-y-2 lg:gap-x-3 lg:gap-y-4'>
				{/* First image skeleton */}
				<div className='w-full bg-gray-200/20 h-[93px] lg:h-[142px] rounded-[10px]' />

				{/* Second image skeleton */}
				<div className='w-full bg-gray-200/20 h-[93px] lg:h-[142px] rounded-[10px]' />

				{/* Third image skeleton (spans 2 columns) */}
				<div className='col-span-2 w-full bg-gray-200/20 h-[185px] md:h-[220px] lg:h-[280px] rounded-[10px]' />
			</div>

			{/* Info panel skeleton */}
			<div
				className={cn(
					'absolute bottom-5 lg:bottom-[30px] left-5 right-5 lg:left-[28px] lg:right-[28px]',
					'backdrop-blur-[10px] flex flex-col justify-between gap-y-1 lg:gap-y-2 p-2 lg:p-4 rounded-[10px]',
				)}
				style={{ background: 'rgba(216, 216, 216, 0.5)' }}
			>
				<div className='flex gap-x-2 items-center'>
					{/* Profile image skeleton */}
					<div className='rounded-full bg-gray-200/30 w-[26px] h-[26px]' />

					{/* Username skeleton */}
					<div className='bg-gray-200/30 h-3 w-20 rounded-full' />

					{/* Price skeleton */}
					<div className='bg-gray-200/30 h-4 w-14 rounded-full ml-auto' />
				</div>
				<div className='flex items-center gap-x-2'>
					{/* Collection name skeleton */}
					<div className='bg-gray-200/30 h-3 w-24 rounded-full' />

					{/* Button skeleton */}
					<div className='bg-gray-200/30 h-[30px] lg:h-[36px] w-[80px] lg:w-[100px] ml-auto rounded-[10px]' />
				</div>
			</div>
		</div>
	)
}
