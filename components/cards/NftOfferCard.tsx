import dayjs from 'dayjs'
import { useSolanaPrice } from '@/lib/hooks/useSolanaPrice'
import CountdownTimer from '../atoms/CountdownTimer'

interface NftOfferCardProps {
	price: number
	buyer: string
	status: string
	expiresAt?: string
	createdAt: string
	expirationTimestamp?: number | null
}

export default function NftOfferCard({
	price,
	buyer,
	status,
	expiresAt,
	createdAt,
	expirationTimestamp,
}: NftOfferCardProps) {
	const solToUsd = useSolanaPrice()

	return (
		<div className='px-3 py-4 rounded-[5px] bg-[#1F1F1F]'>
			<div className='flex items-center'>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>Status</span>
					<span className='text-[10px] font-medium mt-2'>{status}</span>
				</div>
				<div className='flex flex-col ml-auto text-[10px]'>
					<span className='font-medium'>{price} SOL</span>
					<span className='text-[#7E7E7E] mt-2'>
						${(price * solToUsd).toFixed(2)} USD
					</span>
				</div>
			</div>

			<div className='flex mt-7 items-center'>
				<div className='text-[8px]'>
					<span className='font-light'>Expiration</span>
					<div className='text-[8px] mt-1'>
						{expirationTimestamp ? (
							<CountdownTimer
								endsAt={expirationTimestamp}
								className='text-[10px] font-medium'
							/>
						) : (
							<span>No expiration</span>
						)}
					</div>
				</div>
				<div className='flex flex-col ml-auto'>
					<span className='text-[8px] font-light'>From</span>
					<span className='text-[10px] font-medium text-primary mt-2'>
						{buyer}
					</span>
				</div>
				<div className='flex flex-col ml-5'>
					<span className='text-[8px] font-light'>Created</span>
					<span className='text-[10px] font-medium mt-2'>
						{dayjs(createdAt).format('MMM DD, YYYY')}
					</span>
				</div>
			</div>
		</div>
	)
}
