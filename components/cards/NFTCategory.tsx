import { Image } from '@unpic/react'

type NFTCategory = {
	img: string
	caption: string
}

export default function NFTCategory({ img, caption }: NFTCategory) {
	return (
		<div className='bg-[#1F1F1F] flex-center pt-5 pb-4 lg:pt-8 lg:pb-5 flex-col rounded-[20px] transition-all duration-300 hover:shadow-[0_8px_30px_rgb(0,0,0,0.12)] hover:translate-y-[-5px] group'>
			<Image
				src={img}
				className='w-[50px] h-[50px] lg:w-[90px] lg:h-[90px] object-cover rounded-full transition-all duration-500 group-hover:scale-110 group-hover:shadow-[0_0_15px_rgba(255,255,255,0.2)]'
				layout='fixed'
				width={90}
				height={90}
				alt=''
			/>
			<div className='mt-4 text-[10px] lg:text-base bg-black flex-center text-white font-bold px-5 w-[80%] h-[25px] lg:h-[42px] rounded-[20px] transition-all duration-300 group-hover:bg-black/90 group-hover:w-[85%] group-hover:text-white'>
				{caption}
			</div>
		</div>
	)
}
