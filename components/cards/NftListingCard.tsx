import dayjs from 'dayjs'
import { useSolanaPrice } from '@/lib/hooks/useSolanaPrice'
import { Button } from '../ui/button'

interface NftListingCardProps {
	price: number
	listingType: string
	seller: string
	status: string
	createdAt: string
}

export default function NftListingCard({
	price,
	listingType,
	seller,
	status,
	createdAt,
}: NftListingCardProps) {
	const solToUsd = useSolanaPrice()

	return (
		<div className='px-3 py-4 rounded-[5px] bg-[#1F1F1F]'>
			<div className='flex items-center justify-between gap-x-1 gap-y-3 flex-wrap'>
				<div className='flex flex-col'>
					<span className='text-[10px] font-medium'>{price} SOL</span>
					<span className='text-[10px] text-[#7E7E7E] mt-2'>
						${(price * solToUsd).toFixed(2)} USD
					</span>
				</div>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>Type</span>
					<span className='text-[10px] font-medium text-primary mt-2'>
						{listingType === 'FIXED_PRICE' ? 'Fixed Price' : 'Auction'}
					</span>
				</div>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>From</span>
					<span className='text-[10px] font-medium text-primary mt-2'>
						{seller}
					</span>
				</div>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>Created</span>
					<span className='text-[8px] mt-2'>
						{dayjs(createdAt).format('MMM DD, YYYY')}
					</span>
				</div>
				<div className='flex flex-col'>
					<span className='text-[8px] font-light'>Status</span>
					<span className='text-[8px] mt-2'>{status}</span>
				</div>
			</div>
		</div>
	)
}
