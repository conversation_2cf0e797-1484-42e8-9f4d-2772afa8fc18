import { Image } from '@unpic/react'
import {
	ArrowLeftRight,
	CheckCircle,
	FileText, // For generic log or Collection Created
	Gift,
	ShoppingCart,
	Tag,
	Users,
	XCircle,
} from 'lucide-react'

// Helper to get icon and label for activity type (copied from MyActivities.tsx)
const getActivityIconAndLabel = (type: string) => {
	switch (type) {
		case 'NFT_LISTED':
		case 'LISTING':
			return { icon: <Tag className='w-4 h-auto' />, label: 'Listed' } // Adjusted icon size for card
		case 'NFT_SOLD':
		case 'SALE':
			return { icon: <ShoppingCart className='w-4 h-auto' />, label: 'Sale' }
		case 'NFT_TRANSFER':
		case 'TRANSFER':
			return {
				icon: <ArrowLeftRight className='w-4 h-auto' />,
				label: 'Transfer',
			}
		case 'NFT_MINTED':
		case 'MINT':
			return { icon: <Gift className='w-4 h-auto' />, label: 'Mint' }
		case 'NFT_DELISTED':
		case 'DELIST':
			return { icon: <XCircle className='w-4 h-auto' />, label: 'Delisted' }
		case 'COLLECTION_CREATED':
			return { icon: <Users className='w-4 h-auto' />, label: 'Created' }
		case 'NFT_BID_PLACED':
			return {
				icon: <CheckCircle className='w-4 h-auto' />,
				label: 'Bid Placed',
			}
		case 'NFT_BID_CANCELLED':
			return {
				icon: <XCircle className='w-4 h-auto' />,
				label: 'Bid Cancelled',
			}
		default:
			return { icon: <FileText className='w-4 h-auto' />, label: type }
	}
}

// Simple time formatter (copied from MyActivities.tsx)
const formatTimeAgo = (dateString?: string): string => {
	if (!dateString) return 'N/A'
	const date = new Date(dateString)
	const now = new Date()
	const seconds = Math.round((now.getTime() - date.getTime()) / 1000)
	const minutes = Math.round(seconds / 60)
	const hours = Math.round(minutes / 60)
	const days = Math.round(hours / 24)

	if (seconds < 0) return 'just now' // Handle potential future dates or clock sync issues
	if (seconds < 60) return `${seconds} sec ago`
	if (minutes < 60) return `${minutes} min ago`
	if (hours < 24) return `${hours} hours ago`
	if (days === 1) return '1 day ago'
	return `${days} days ago`
}

interface ActivityCardProps {
	activityType: string // Raw activity type string e.g. "NFT_SOLD"
	nftImageUrl?: string
	nftName?: string
	userName?: string // Actor: buyer, seller, transferer etc.
	priceSol?: number | null
	quantity?: number
	fromUser?: string
	toUser?: string
	time: string // ISO date string, needs formatting
}

export default function ActivityCard(props: ActivityCardProps) {
	const {
		activityType: rawActivityType, // Renamed to avoid conflict with the object
		nftImageUrl,
		nftName,
		userName,
		priceSol,
		quantity,
		fromUser,
		toUser,
		time: isoTime, // Renamed to indicate it's an ISO string
	} = props

	const activityDisplay = getActivityIconAndLabel(rawActivityType)
	const formattedTime = formatTimeAgo(isoTime)

	return (
		<div className='px-3 py-4 rounded-[5px] bg-[#1F1F1F] text-xs'>
			<div className='flex items-center justify-between'>
				<span className='text-[10px]'>{formattedTime}</span>
				<div className='flex items-center gap-x-1'>
					{activityDisplay.icon}
					<span className='text-[10px] font-medium text-center'>
						{activityDisplay.label}
					</span>
				</div>
			</div>

			<div className='flex items-center mt-4'>
				<div className='flex gap-x-2 flex-grow min-w-0'>
					<Image
						src={nftImageUrl || '/assets/img/default-nft.png'}
						className='object-cover w-[35px] h-[35px] rounded-full shrink-0'
						layout='fixed'
						width={35} // Adjusted size
						height={35}
						alt={nftName || 'NFT Image'}
					/>
					<div className='flex text-[10px] flex-col ml-1 min-w-0'>
						<span className='font-medium truncate'>{nftName || 'N/A'}</span>
						<span className='mt-0.5 font-normal text-[#7E7E7E] truncate'>
							{userName ? userName : 'N/A'}
						</span>
					</div>
				</div>

				<div className='flex flex-col items-end ml-2 shrink-0'>
					{typeof priceSol === 'number' ? (
						<span className='font-medium'>{priceSol.toLocaleString()} SOL</span>
					) : (
						<span className='font-medium'>N/A</span>
					)}
					{typeof quantity === 'number' &&
						quantity > 1 && ( // Show quantity only if > 1 for card view
							<span className='text-[10px] text-[#7E7E7E] mt-0.5'>
								Qty: {quantity}
							</span>
						)}
				</div>
			</div>

			<div className='flex justify-between mt-4 text-[10px]'>
				<div className='flex flex-col items-start max-w-[45%]'>
					<span className='text-[8px] font-light text-[#AEAEAE]'>From</span>
					<span className='font-medium text-primary truncate'>
						{fromUser || 'N/A'}
					</span>
				</div>
				<div className='flex flex-col items-end text-right max-w-[45%]'>
					<span className='text-[8px] font-light text-[#AEAEAE]'>To</span>
					<span className='font-medium text-primary truncate'>
						{toUser || 'N/A'}
					</span>
				</div>
			</div>
		</div>
	)
}
