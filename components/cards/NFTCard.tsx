import { Image } from '@unpic/react'
import { BadgeCheck, Play, ShoppingCart } from 'lucide-react'
import { useEffect } from 'react'
import { cn, truncateAddress } from '@/lib/utils'
import AuctionTimer from '../atoms/AuctionTimer'
import { Link } from '../atoms/Link'

type BaseProps = {
	img: string
	name?: string
	profileImg: string
	ownerName?: string
	ownerPublicKey?: string
	biddingPrice?: number
	className?: string
	isLoading?: boolean
	publicKey?: string
	isListed?: boolean
	listingEndsAt?: number | bigint | null
	videoSrc?: string
	collectionName?: string
	isAuction?: boolean
} & (LiveAuctionProps | TrendingNFTProps | TrendingFusionNFTProps)

type LiveAuctionProps = {
	variant: 'LiveAuction'
}

type TrendingNFTProps = {
	variant: 'TrendingNFT'
}

type TrendingFusionNFTProps = {
	variant: 'TrendingFusionNFT'
}

const defaultProfileImg = '/assets/img/default-profile.png'
const defaultCardImg = '/assets/img/default-card-image.png'
export default function NFTCard(props: BaseProps) {
	const { isAuction = false } = props

	const isLiveAuction = props.variant === 'LiveAuction'
	// const isTrendingNft = props.variant === 'TrendingNFT'
	const isTrendingFusion = props.variant === 'TrendingFusionNFT'

	const cardContent = (
		<div
			className={cn(
				'w-full bg-card rounded-[20px] transition-all duration-300 hover:shadow-[0_8px_30px_rgb(0,0,0,0.12)] hover:translate-y-[-5px] group cursor-pointer',
				props.className,
			)}
		>
			<div className={cn('cardImgContainer relative', isLiveAuction && 'p-0')}>
				<div
					className={cn(
						'cardImgWrapper relative group overflow-hidden rounded-t-[10px]',
						isLiveAuction && 'rounded-[10px]',
					)}
				>
					<Image
						src={props.img || defaultCardImg}
						className={cn(
							'w-full object-cover h-[200px] lg:h-[308px] transition-transform duration-500 group-hover:scale-[1.02]',
							isLiveAuction && 'h-[222px]',
						)}
						layout='fixed'
						width={296}
						height={308}
						alt='NFT Card Image'
					/>
				</div>

				{(isLiveAuction || props.isListed) && props.listingEndsAt && (
					<div className='auctionTimer z-40 flex items-center absolute left-1/2 -translate-x-1/2 -bottom-4 h-auto py-1 font-semibold px-6 text-nowrap rounded-[10px] bg-[#4d4d4d]/50 backdrop-blur-[10px] transition-all duration-300 group-hover:backdrop-blur-[15px] group-hover:bg-[#4d4d4d]/60'>
						<AuctionTimer endsAt={props.listingEndsAt} />
					</div>
				)}

				<div className='topBar absolute top-0 left-0 right-0 flex items-center gap-x-2 justify-between px-4 pt-3'>
					<div className='flex items-center gap-x-2 flex-1 min-w-0'>
						{/* {props.collectionName && props.collectionName?.length > 0 && (
							<>
								<Image
									src={props.profileImg || defaultProfileImg}
									className='rounded-full w-[45px] h-[45px] object-cover shrink-0 transition-transform duration-300 group-hover:scale-110'
									layout='fixed'
									width={45}
									height={45}
									alt='user profile'
								/>
								<span className='text-xs lg:text-sm font-semibold transition-all duration-300 group-hover:text-white truncate'>
									{props.collectionName}
								</span>
							</>
						)} */}
						{isTrendingFusion && (
							<div className='trendingFusionBg flex-center px-5 h-[38px] lg:h-[50px] rounded-[8px] transition-all duration-300 group-hover:scale-105 backdrop-blur-sm'>
								Fusion NFT
							</div>
						)}
					</div>
					{props?.isListed && (
						<div className='flex-center text-xs lg:text-sm text-white font-semibold px-2 lg:px-4 py-2 rounded-sm bg-primary shadow-sm animate-pulse transition-all duration-3000'>
							{isAuction ? 'Auction' : 'Listed'}
						</div>
					)}
				</div>

				<div className='bottomBar absolute bottom-0 inset-x-0  flex items-center justify-between p-0 w-full bg-gradient-to-b to-black/90 from-black/0  px-4 pb-4'>
					{/* {isTrendingFusion && (
						<div className='flex items-center gap-x-2 flex-1 min-w-0'>
							<Image
								src={props.profileImg || defaultProfileImg}
								className='rounded-full w-[45px] h-[45px] object-cover shrink-0 transition-transform duration-300 group-hover:scale-110'
								layout='fixed'
								width={45}
								height={45}
								alt='user profile'
							/>
							<span className='text-xs lg:text-sm font-semibold transition-all duration-300 group-hover:text-white truncate'>
								{props.ownerName}
							</span>
						</div>
					)} */}

					{props.videoSrc && (
						<div className='flex-center ml-auto w-[38px] h-[38px] lg:w-[50px] lg:h-[50px] rounded-[8px] bg-white/50 backdrop-blur-[10px] cursor-pointer transition-all duration-300 group-hover:bg-white/60 group-hover:backdrop-blur-[15px] group-hover:scale-105'>
							<Play className='w-4 lg:w-5 h-auto transition-transform duration-300 group-hover:translate-x-[1px]' />
						</div>
					)}
					{props.publicKey ? (
						<span
							className='font-medium text-xs transition-all duration-300 group-hover:text-white flex items-center gap-x-2 cursor-pointer'
							onClick={(e) => {
								e.stopPropagation()
								e.preventDefault()
								window.location.href = `/profile/${props.ownerName}`
							}}
							role='link'
							tabIndex={0}
							onKeyDown={(e) => {
								if (e.key === 'Enter' || e.key === ' ') {
									e.stopPropagation()
									e.preventDefault()
									window.location.href = `/profile/${props.ownerName}`
								}
							}}
						>
							<Image
								src={props.profileImg || defaultProfileImg}
								className='rounded-full shrink-0 w-[36px] h-[36px] object-cover transition-transform duration-300 group-hover:scale-110'
								layout='fixed'
								width={36}
								height={36}
								alt='user profile'
							/>
							<span className='text-sm transition-all duration-300 group-hover:text-primary'>
								{props.ownerName}
							</span>
						</span>
					) : (
						<a
							href={`/profile/${props.ownerName}`}
							className='font-medium text-xs transition-all duration-300 group-hover:text-white flex items-center gap-x-2'
						>
							<Image
								src={props.profileImg || defaultProfileImg}
								className='rounded-full shrink-0 w-[36px] h-[36px] object-cover transition-transform duration-300 group-hover:scale-110'
								layout='fixed'
								width={36}
								height={36}
								alt='user profile'
							/>
							<span className='text-sm transition-all duration-300 group-hover:text-primary'>
								{props.ownerName}
							</span>
						</a>
					)}
				</div>
			</div>

			<div
				className={cn(
					'cardCaption px-3 lg:px-5 py-5 relative group',
					isLiveAuction && 'pt-9',
				)}
			>
				<div className='flex items-center justify-between'>
					<span className='font-semibold text-xs lg:text-sm xl:text-base transition-all duration-300 group-hover:text-white'>
						{props.name}
					</span>
					{isLiveAuction && (
						<Image
							src={props.profileImg || defaultProfileImg}
							className='rounded-full shrink-0 w-[36px] h-[36px] object-cover transition-transform duration-300 group-hover:scale-110'
							layout='fixed'
							width={36}
							height={36}
							alt='user profile'
						/>
					)}
				</div>
				<div className='flex items-center gap-x-2 my-1'>
					{props.collectionName && props.collectionName?.length > 0 ? (
						<>
							<span className='text-xs transition-all duration-300 group-hover:text-white'>
								{props.collectionName}
							</span>
							<BadgeCheck className='hidden size-4 stroke-black fill-primary' />
						</>
					) : props.ownerPublicKey ? (
						<span className='text-xs transition-all duration-300'>
							By:{' '}
							<span className='font-semibold text-primary'>
								{truncateAddress(props.ownerPublicKey ?? '')}
							</span>
						</span>
					) : (
						''
					)}
				</div>

				<div
					className={cn(
						'flex text-xs justify-between mt-3 pt-2.5 border-t-[0.5px] transition-all duration-300',
						props.isListed && 'group-hover:invisible',
					)}
					style={{
						borderImage:
							'linear-gradient(270deg, rgba(255, 255, 255, 0) 2%, #646464 49%, rgba(255, 255, 255, 0) 99%)',
						borderImageSlice: 1,
					}}
				>
					<span className='font-light'>Price</span>
					<span className='font-bold group-hover:text-white transition-colors duration-300'>
						{+(props.biddingPrice?.toFixed(2) || 0)} SOL
					</span>
				</div>

				{props.isListed && (
					<div className='w-full h-[45px] hidden group-hover:flex items-center justify-between absolute left-0 right-0 bottom-0 rounded-b-[20px] overflow-hidden'>
						<button className='w-full h-full bg-[#F97703] text-base font-semibold cursor-pointer'>
							{isAuction ? 'Bid Now' : 'Buy Now'}
						</button>
						{/* <button className='w-[60px] h-full flex-center bg-[#CA6001] cursor-pointer'>
							<ShoppingCart className='w-4 h-auto' />
						</button> */}
					</div>
				)}
			</div>
		</div>
	)

	// Return wrapped content if publicKey is provided, otherwise return content as is
	return props.publicKey ? (
		<Link href={`/nft-details/${props.publicKey}`}>{cardContent}</Link>
	) : (
		cardContent
	)
}
