export default function NFTCardSkeleton() {
	return (
		<div className='w-full bg-card rounded-[20px] animate-pulse'>
			<div className='relative p-2.5'>
				<div className='w-full h-[222px] bg-gray-300 rounded-[10px]' />
				<div className='absolute left-1/2 -translate-x-1/2 -bottom-[21px] h-[42px] w-[180px] rounded-[10px] bg-gray-400/50 backdrop-blur-[10px]' />
			</div>

			<div className='px-3 lg:px-5 pt-9 pb-5'>
				<div className='flex items-center justify-between'>
					<div className='h-4 w-1/2 bg-gray-300 rounded-full' />
					<div className='w-[36px] h-[36px] bg-gray-300 rounded-full' />
				</div>
				<div className='flex justify-between items-center mt-4 pt-3 border-t border-gray-400/40'>
					<div className='h-3 w-10 bg-gray-300 rounded' />
					<div className='h-3 w-12 bg-gray-300 rounded' />
				</div>
			</div>
		</div>
	)
}
