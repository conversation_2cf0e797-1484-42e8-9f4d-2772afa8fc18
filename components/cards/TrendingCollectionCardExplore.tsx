import { Image } from '@unpic/react'
import { BadgeCheck } from 'lucide-react'
import { Link } from '../atoms/Link'
import { Button } from '../ui/button'

type TrendingCollectionCardProps = {
	creater: {
		id: string
		username: string
		imageUrl: string
		publicKey: string
	}
	collectionImgs: Array<{ img: string; alt?: string }>
	publicKey: string
	name: string
}

export default function TrendingCollectionCardExplore({
	creater,
	collectionImgs,
	publicKey,
	name,
}: TrendingCollectionCardProps) {
	const mainImage =
		collectionImgs[0]?.img || '/assets/img/default-card-image.png'
	const cardContent = (
		<div className='trendingCollectionCard rounded-2xl bg-[#1F1F1F] relative group cursor-pointer transition-transform duration-300 hover:-translate-y-1'>
			<Image
				src={mainImage}
				className='w-full p-4 h-[220px] lg:h-[250px] object-cover rounded-[30px] transition-shadow duration-500 group-hover:shadow-[0_8px_30px_rgba(0,0,0,0.15)]'
				layout='fixed'
				width={257}
				height={250}
				alt={collectionImgs[0]?.alt || 'collection image'}
			/>

			<div className='flex flex-col items-center -mt-20 px-4 pb-5'>
				<div className='bg-gradient-to-tr from-[#DD0003] to-[#FF8F1F] p-[5px] rounded-full'>
					<Image
						src={creater.imageUrl || '/assets/img/default-profile.png'}
						className='block w-[96px] h-[96px] lg:w-[118px] lg:h-[118px] rounded-full transition-transform duration-500 group-hover:scale-105 group-hover:shadow-[0_0_20px_rgba(255,255,255,0.25)]'
						layout='fixed'
						width={118}
						height={118}
						alt='user profile'
					/>
				</div>
				<div className='w-full text-left mt-3'>
					<div className='flex'>
						<span className='text-sm lg:text-base font-semibold transition-colors duration-300 group-hover:text-white'>
							{name}
						</span>
						<BadgeCheck className='hidden w-5 h-5 text-black fill-primary ml-2 shrink-0' />
					</div>

					<span className='block text-xs lg:text-sm font-light transition-colors duration-300 group-hover:text-white/80'>
						<span className='text-[#646464]'>By</span> {creater.username}
					</span>
				</div>
				<Button className='w-full h-[26px] mt-4 text-sm font-semibold transition-all duration-300 hover:bg-opacity-90 hover:scale-105'>
					BUY NOW
				</Button>
			</div>
		</div>
	)

	return <Link href={`/collection/${publicKey}`}>{cardContent}</Link>
}
