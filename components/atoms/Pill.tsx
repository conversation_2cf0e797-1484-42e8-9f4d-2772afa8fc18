import { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

export default function Pill({
	text,
	isActive,
	onToggle,
}: {
	text: string
	isActive?: boolean
	onToggle?: (text: string) => void
}) {
	// Initialize state with the isActive prop
	const [internalActive, setInternalActive] = useState(isActive || false)

	// Update internal state when prop changes
	useEffect(() => {
		if (isActive !== undefined) {
			setInternalActive(isActive)
		}
	}, [isActive])

	const handleToggle = () => {
		const newState = !internalActive
		setInternalActive(newState)
		onToggle?.(text)
	}

	return (
		<button
			className={cn(
				'px-2 lg:px-4 py-2 bg-[#292929] text-white rounded-[6px] lg:rounded-xl text-xs lg:text-lg font-medium max-w-[150px] text-truncate cursor-pointer',
				internalActive && 'bg-white text-black',
			)}
			onClick={handleToggle}
		>
			{text}
		</button>
	)
}
