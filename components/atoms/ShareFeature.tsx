import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { copyText } from '@/lib/utils'
import BrandLogo from '@/public/assets/Logo/logo.svg?react'
import Share from '@/public/assets/svg/share.svg?react'
import TwitterX from '@/public/assets/svg/twitter.svg?react'

type ShareFeatureProps = {
	twitter?: {
		text: string
		url: string
		hashtags: string
	}
}

export default function ShareFeature({ twitter }: ShareFeatureProps) {
	const tweetText = encodeURIComponent(twitter?.text || '')
	const tweetUrl = encodeURIComponent(twitter?.url || '')
	const _hashtags = encodeURIComponent(twitter?.hashtags || '')
	const currentUrl = window.location.href // Get the current page URL

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild>
				<button className='blurredBtn outline-none'>
					<Share className='w-4 lg:w-6 h-auto' />
				</button>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				align='end'
				className='bg-[#333333] !border-none rounded-[8px]'
			>
				<DropdownMenuItem
					className='flex items-center gap-x-4 text-sm font-bold'
					onClick={() => copyText({ data: currentUrl })} // Copy the current URL
				>
					<BrandLogo className='w-4 h-auto text-white' /> Copy Link
				</DropdownMenuItem>
				<DropdownMenuItem className='flex items-center text-sm font-bold'>
					<a
						href={`https://twitter.com/intent/tweet?text=${tweetText}&url=${tweetUrl}&hashtags=${_hashtags}`}
						rel='noopener noreferrer'
						target='_blank'
						className='flex gap-x-4 w-full h-full'
					>
						<TwitterX className='w-4 h-auto text-white' /> Share on X
					</a>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	)
}
