import Send from '@/public/assets/svg/send.svg?react'
import TransferNFTModal from '../modals/TransferNftModal'

interface TransferNftFeatureProps {
	isOwner: boolean
	nftName: string
	nftImage: string
	collectionName?: string
	collectionAddress?: string
	mintAddress: string
	onTransferComplete?: () => void
}

export default function TransferNftFeature({
	isOwner,
	nftName,
	nftImage,
	collectionName,
	collectionAddress,
	mintAddress,
	onTransferComplete,
}: TransferNftFeatureProps) {
	return isOwner ? (
		<TransferNFTModal
			nftName={nftName}
			nftImage={nftImage}
			collectionName={collectionName}
			collectionAddress={collectionAddress}
			mintAddress={mintAddress}
			onTransferComplete={onTransferComplete}
		>
			<button className={'blurredBtn'}>
				<Send className='w-4 lg:w-6 h-auto text-white fill-transparent' />
			</button>
		</TransferNFTModal>
	) : null
}
