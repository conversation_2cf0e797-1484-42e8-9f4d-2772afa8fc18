import { cn } from '../../lib/utils'
export function TabContent({
	tab,
	value,
	children,
	className,
}: {
	tab: string
	value: string
	children: React.ReactNode
	className?: string
}) {
	if (tab !== value) return
	return (
		<div title={tab} className={cn('pt-5 lg:pt-8', className)}>
			{children}
		</div>
	)
}

export function Tab({
	value,
	currentTab,
	onClickTab,
	children,
}: {
	value: string
	currentTab: string
	onClickTab: () => void
	children: React.ReactNode
}) {
	return (
		<span
			onClick={onClickTab}
			role='tab'
			className={cn(
				'cursor-pointer text-nowrap hover:text-white/70',
				currentTab === value && 'text-white',
			)}
		>
			{children}
		</span>
	)
}
