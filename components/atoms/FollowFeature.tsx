'use client'
import { cn } from '@/lib/utils'

type props = {
	isFollowed?: boolean
	onChangeFollow?: (value: boolean) => void
	className?: string
}

export default function FollowFeature({
	isFollowed = false,
	onChangeFollow,
	className,
}: props) {
	const followHandler = () => {
		// Simply call the parent handler with the opposite of current state
		onChangeFollow?.(!isFollowed)
	}

	return (
		<button
			className={cn(
				'flex-center px-3 lg:px-5 h-[30px] lg:h-[50px] bg-white/20 backdrop-blur-md text-sm lg:text-base font-semibold text-white rounded-full cursor-pointer border-primary hover:border',
				isFollowed && 'text-primary border',
				className,
			)}
			onClick={followHandler}
		>
			{isFollowed ? 'Unfollow' : 'Follow'}
		</button>
	)
}
