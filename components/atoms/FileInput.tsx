'use client'
import { X } from 'lucide-react'
import { useRef, useState } from 'react'
import { cn } from '@/lib/utils'

interface FileInputProps {
	onFileSelect?: (file: File | null) => void
}

export default function FileInput({ onFileSelect }: FileInputProps) {
	const [fileName, setFileName] = useState<string>('Upload CSV File')
	const [isShowPlaceholder, setShowPlaceholder] = useState<boolean>(true)
	const [hasFile, setHasFile] = useState<boolean>(false)
	const inputRef = useRef<HTMLInputElement>(null)
	const [err, setErr] = useState<string | null>(null)

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.files && e.target.files.length > 0) {
			const file = e.target.files[0]
			if (file.type !== 'text/csv') {
				setErr('Only .csv file is accepted')
				setHasFile(false)
				setShowPlaceholder(true)
			} else {
				setFileName(file.name)
				setHasFile(true)
				setShowPlaceholder(false)
				setErr(null)

				// Call the callback with the selected file
				if (onFileSelect) {
					onFileSelect(file)
				}
			}
		}
	}

	const handleClick = () => {
		inputRef.current?.click()
	}

	const clearFile = (e: React.MouseEvent) => {
		e.stopPropagation()
		setFileName('Upload CSV File')
		setHasFile(false)
		setShowPlaceholder(true)
		if (inputRef.current) {
			inputRef.current.value = ''
		}

		// Call the callback with null to indicate no file is selected
		if (onFileSelect) {
			onFileSelect(null)
		}
	}
	return (
		<div>
			<div className='flex w-full relative'>
				<button
					onClick={handleClick}
					className='bg-[#313131] text-white w-[100px] lg:w-[150px] xl:w-[210px] h-[45px] lg:h-[60px] rounded-l-[10px] hover:bg-zinc-700 text-sm cursor-pointer '
				>
					Choose
				</button>
				<div className='flex-1 flex items-center px-5 bg-[#191919] text-white h-[45px] lg:h-[60px] rounded-r-[10px] text-sm lg:text-xl relative'>
					<span
						className={cn(
							'truncate max-w-[160px] md:max-w-[240px] lg:max-w-[460px] text-sm',
							isShowPlaceholder && 'text-white/50',
						)}
					>
						{fileName}
					</span>
					{hasFile && (
						<button
							onClick={clearFile}
							className='absolute right-2 top-1/2 transform -translate-y-1/2 text-zinc-400 hover:text-white cursor-pointer'
						>
							<X size={16} />
						</button>
					)}
				</div>
				<input
					ref={inputRef}
					type='file'
					accept={'.csv'}
					onChange={handleInputChange}
					className='hidden'
				/>
			</div>
			{err && <p className='text-red-500 text-sm lg:text-base mt-2'>{err}</p>}
		</div>
	)
}
