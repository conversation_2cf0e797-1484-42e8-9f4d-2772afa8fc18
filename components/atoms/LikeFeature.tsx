'use client'
import { Heart } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { useCollectionLike, useNftLike } from '@/hooks/useFollowUser'
import { cn } from '@/lib/utils'

type props = {
	isLiked?: boolean
	onChangeLike?: (value: boolean) => void
	className?: string
	itemId?: string
	type?: 'nft' | 'collection'
}

export default function LikeFeature({
	isLiked: initialIsLiked,
	onChangeLike,
	className,
	itemId,
	type = 'collection',
}: props) {
	// Use useRef to track if this is the initial render
	//const isFirstRender = useRef(true)
	const [isItemLiked, setIsItemLiked] = useState(initialIsLiked || false)
	const { toggleNftLike, isLoading: nftLikeLoading } = useNftLike()
	const { toggleCollectionLike, isLoading: collectionLikeLoading } =
		useCollectionLike()

	//// Only update state from props when props actually change, not on every render
	//useEffect(() => {
	//	// Skip the first render since we already set the initial state
	//	if (isFirstRender.current) {
	//		isFirstRender.current = false
	//		return
	//	}
	//
	//	// Only update if initialIsLiked is defined and different from current state
	//	if (initialIsLiked !== undefined && initialIsLiked !== isItemLiked) {
	//		console.log(initialIsLiked, "intial is Liekd")
	//		console.log("running this")
	//		setIsItemLiked(initialIsLiked)
	//	}
	//}, [initialIsLiked, isItemLiked])
	//
	const likeHandler = () => {
		// Prevent action if loading
		if (type === 'nft' ? nftLikeLoading : collectionLikeLoading) {
			return
		}

		// Calculate the new state once to avoid inconsistencies
		const newLikedState = !isItemLiked

		if (!itemId) {
			// If no itemId provided, just toggle the UI state without API call
			setIsItemLiked(newLikedState)
			// Call the parent handler with the new state
			onChangeLike?.(newLikedState)
			return
		}

		// Toggle like status with appropriate hook based on type
		if (type === 'nft') {
			toggleNftLike(itemId, isItemLiked)
		} else {
			toggleCollectionLike(itemId, isItemLiked)
		}

		// Optimistically update UI
		setIsItemLiked(newLikedState)
		// Call the parent handler with the new state
		onChangeLike?.(newLikedState)
	}

	const isLoading = type === 'nft' ? nftLikeLoading : collectionLikeLoading

	return (
		<button
			className={cn('blurredBtn', isItemLiked && '!border', className)}
			onClick={likeHandler}
			disabled={isLoading}
		>
			<Heart
				className={cn(
					'w-4 lg:w-6 h-auto',
					isItemLiked && 'stroke-primary fill-primary',
					isLoading && 'opacity-50',
				)}
			/>
		</button>
	)
}
