'use client'
import { X } from 'lucide-react'
import { useRef, useState } from 'react'
import { cn } from '@/lib/utils'

// Extend HTMLInputElement to include directory attributes
declare module 'react' {
	interface HTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
		// Add directory attributes
		directory?: string
		webkitdirectory?: string
		mozdirectory?: string
	}
}

// Extend File interface to include webkitRelativePath
interface FileWithPath extends File {
	webkitRelativePath: string
}

interface FolderInputProps {
	onFolderSelect?: (files: FileList | null) => void
}

export default function FolderInput({ onFolderSelect }: FolderInputProps) {
	const [folderName, setFolderName] = useState<string>('Upload Folder')
	const [isShowPlaceholder, setShowPlaceholder] = useState<boolean>(true)
	const [hasFolder, setHasFolder] = useState<boolean>(false)
	const inputRef = useRef<HTMLInputElement>(null)

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.files && e.target.files.length > 0) {
			// Get the folder name from the first file's path
			const firstFile = e.target.files[0] as FileWithPath
			const folderPath = firstFile.webkitRelativePath
			const folderName = folderPath.split('/')[0] // Extract the folder name

			setFolderName(`${folderName} (${e.target.files.length} files)`)
			setHasFolder(true)
			setShowPlaceholder(false)

			// Call the callback with the selected files
			if (onFolderSelect) {
				onFolderSelect(e.target.files)
			}
		}
	}

	const handleClick = () => {
		inputRef.current?.click()
	}

	const clearFolder = (e: React.MouseEvent) => {
		e.stopPropagation()
		setFolderName('Upload Folder')
		setHasFolder(false)
		setShowPlaceholder(true)
		if (inputRef.current) {
			inputRef.current.value = ''
		}

		// Call the callback with null to indicate no folder is selected
		if (onFolderSelect) {
			onFolderSelect(null)
		}
	}
	return (
		<div className='flex w-full relative'>
			<button
				onClick={handleClick}
				className='bg-[#313131] text-white w-[100px] lg:w-[150px] xl:w-[210px] h-[45px] lg:h-[60px] rounded-l-[10px] hover:bg-zinc-700 text-sm cursor-pointer '
			>
				Choose
			</button>
			<div className='flex-1 flex items-center px-5 bg-[#191919] text-white h-[45px] lg:h-[60px] rounded-r-[10px] text-sm lg:text-xl relative'>
				<span
					className={cn(
						'truncate max-w-[160px] md:max-w-[240px] lg:max-w-[460px] text-sm',
						isShowPlaceholder && 'text-white/50',
					)}
				>
					{folderName}
				</span>
				{hasFolder && (
					<button
						onClick={clearFolder}
						className='absolute right-2 top-1/2 transform -translate-y-1/2 text-zinc-400 hover:text-white cursor-pointer'
					>
						<X size={16} />
					</button>
				)}
			</div>
			<input
				ref={inputRef}
				type='file'
				webkitdirectory=''
				mozdirectory=''
				directory=''
				multiple
				onChange={handleInputChange}
				className='hidden'
			/>
		</div>
	)
}
