import { useEffect, useRef, useState } from 'react'

type CountdownTimerProps = {
	endsAt: number | bigint | null | undefined
	className?: string
}

export default function CountdownTimer({
	endsAt,
	className,
}: CountdownTimerProps) {
	const [timeLeft, setTimeLeft] = useState<{
		days: number
		hours: number
		minutes: number
		seconds: number
	} | null>(null)

	// Use refs to store values that shouldn't trigger re-renders
	const timerRef = useRef<NodeJS.Timeout | null>(null)
	const endTimeRef = useRef<number | null>(null)

	useEffect(() => {
		// Clear any existing interval when props change
		if (timerRef.current) {
			clearInterval(timerRef.current)
			timerRef.current = null
		}

		if (!endsAt) {
			setTimeLeft(null)
			return
		}

		// Convert bigint to number if needed and store in ref
		endTimeRef.current = typeof endsAt === 'bigint' ? Number(endsAt) : endsAt

		// Function to calculate time left
		const calculateTimeLeft = () => {
			if (!endTimeRef.current) return

			// Convert Unix timestamp (seconds) to milliseconds
			const endTimeMs = endTimeRef.current * 1000
			const now = Date.now()
			const difference = endTimeMs - now

			// If auction has ended
			if (difference <= 0) {
				setTimeLeft(null)
				// Clear interval if auction has ended
				if (timerRef.current) {
					clearInterval(timerRef.current)
					timerRef.current = null
				}
				return
			}

			// Calculate time components
			const days = Math.floor(difference / (1000 * 60 * 60 * 24))
			const hours = Math.floor(
				(difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
			)
			const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
			const seconds = Math.floor((difference % (1000 * 60)) / 1000)

			setTimeLeft({ days, hours, minutes, seconds })
		}

		// Calculate immediately
		calculateTimeLeft()

		// Set interval and store the ID in the ref
		timerRef.current = setInterval(calculateTimeLeft, 1000)

		// Clean up interval on unmount or when props change
		return () => {
			if (timerRef.current) {
				clearInterval(timerRef.current)
				timerRef.current = null
			}
		}
	}, [endsAt])

	if (!timeLeft) {
		return <span className={className}>Auction ended</span>
	}

	// Format the time components
	return (
		<span className={className}>
			{timeLeft.days > 0 && `${timeLeft.days}d : `}
			{timeLeft.hours.toString().padStart(2, '0')}h :
			{timeLeft.minutes.toString().padStart(2, '0')}m :
			{timeLeft.seconds.toString().padStart(2, '0')}s
		</span>
	)
}
