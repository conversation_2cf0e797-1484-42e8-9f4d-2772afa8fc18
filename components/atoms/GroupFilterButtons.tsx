import { cn } from '@/lib/utils'

type props = {
	values: readonly string[]
	onChange: (value: string) => void
	active: string
}

export default function GroupFilterButtons({
	active,
	onChange,
	values,
}: props) {
	return (
		<div
			className='flex items-center h-[40px] lg:h-[50px] p-1 lg:p-1.5 border border-[#1D1D1D] rounded-full'
			style={{
				background:
					'radial-gradient(170% 170% at 50% 6%, #191919 16%, rgba(0, 0, 0, 0) 100%)',
			}}
		>
			{values.map((value) => (
				<button
					key={value}
					onClick={() => onChange(value)}
					className={cn(
						'text-[10px] lg:text-sm flex-center px-3 lg:px-4 h-full rounded-full cursor-pointer',
						active === value && 'activeFilterBtn font-semibold',
					)}
				>
					{value}
				</button>
			))}
		</div>
	)
}
