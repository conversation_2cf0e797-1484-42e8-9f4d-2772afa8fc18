import { Facebook, Globe, Instagram, Twitter } from 'lucide-react'
import { cn } from '@/lib/utils'
import SolanaIcon from '@/public/assets/svg/solana-colorless.svg?react'
import XLogoWhite from '@/public/assets/svg/x-logo-white.svg?react'
import { Link } from './Link'

type Props = {
	links: Partial<{
		twitter: string | undefined
		instagram: string | undefined
		website: string | undefined
		facebook: string | undefined
		solana: string
	}>
	showRoundBlur?: boolean
	className?: string
}

export default function GroupedSocialButtons({
	links,
	className,
	showRoundBlur = true,
}: Props) {
	return (
		<div className={cn('flex items-center gap-x-3 lg:gap-x-4', className)}>
			{links?.facebook && (
				<Link href={links.facebook} target='_blank'>
					<div className={cn(showRoundBlur && 'blurredBtn')}>
						<Facebook className='w-4 lg:w-6 h-auto' />
					</div>
				</Link>
			)}
			{links?.twitter && (
				<Link href={links.twitter} target='_blank'>
					<div className={cn(showRoundBlur && 'blurredBtn')}>
						<XLogoWhite className='w-4 lg:w-6 h-auto' />
					</div>
				</Link>
			)}
			{links?.instagram && (
				<Link href={links.instagram} target='_blank'>
					<div className={cn(showRoundBlur && 'blurredBtn')}>
						<Instagram className='w-4 lg:w-6 h-auto' />
					</div>
				</Link>
			)}
			{links?.website && (
				<Link href={links.website} target='_blank'>
					<div className={cn(showRoundBlur && 'blurredBtn')}>
						<Globe className='w-4 lg:w-6 h-auto' />
					</div>
				</Link>
			)}
			{links?.solana && (
				<Link
					href={`https://solscan.io/account/${links.solana}?cluster=devnet`}
					target='_blank'
				>
					<div className={cn(showRoundBlur && 'blurredBtn')}>
						<SolanaIcon className='w-4 lg:w-6 h-auto text-white' />
					</div>
				</Link>
			)}
		</div>
	)
}
