import { Timer } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { cn } from '@/lib/utils'

type AuctionTimerProps = {
	endsAt: number | bigint | null | undefined
	className?: string
	showIcon?: boolean
}

export default function AuctionTimer({
	endsAt,
	className,
	showIcon = true,
}: AuctionTimerProps) {
	const [timeLeft, setTimeLeft] = useState<{
		days: number
		hours: number
		minutes: number
		seconds: number
	} | null>(null)

	// Use a ref to store the interval ID to prevent it from being a dependency
	const timerRef = useRef<NodeJS.Timeout | null>(null)
	// Use a ref to store the end time to avoid recalculating it on every render
	const endTimeRef = useRef<number | null>(null)

	useEffect(() => {
		// Clear any existing interval when props change
		if (timerRef.current) {
			clearInterval(timerRef.current)
			timerRef.current = null
		}

		if (!endsAt) {
			setTimeLeft(null)
			return
		}

		// Convert bigint to number if needed and store in ref
		endTimeRef.current = typeof endsAt === 'bigint' ? Number(endsAt) : endsAt

		// Function to calculate time left
		const calculateTimeLeft = () => {
			if (!endTimeRef.current) return

			const now = Date.now()
			const difference = endTimeRef.current - now

			// If auction has ended
			if (difference <= 0) {
				setTimeLeft(null)
				// Clear interval if auction has ended
				if (timerRef.current) {
					clearInterval(timerRef.current)
					timerRef.current = null
				}
				return
			}

			// Calculate time components
			const days = Math.floor(difference / (1000 * 60 * 60 * 24))
			const hours = Math.floor(
				(difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
			)
			const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
			const seconds = Math.floor((difference % (1000 * 60)) / 1000)

			setTimeLeft({ days, hours, minutes, seconds })
		}

		// Calculate immediately
		calculateTimeLeft()

		// Set interval and store the ID in the ref
		timerRef.current = setInterval(calculateTimeLeft, 1000)

		// Clean up interval on unmount or when props change
		return () => {
			if (timerRef.current) {
				clearInterval(timerRef.current)
				timerRef.current = null
			}
		}
	}, [endsAt])

	if (!timeLeft) {
		return <span className={className}>Auction ended</span>
	}

	// Format the time components
	return (
		<div className={cn('flex items-center gap-2', className)}>
			{showIcon && <Timer className='w-4 h-4' />}
			<span>
				{timeLeft.days > 0 && `${timeLeft.days}d : `}
				{timeLeft.hours.toString().padStart(2, '0')}h :{' '}
				{timeLeft.minutes.toString().padStart(2, '0')}m :{' '}
				{timeLeft.seconds.toString().padStart(2, '0')}s
			</span>
		</div>
	)
}
