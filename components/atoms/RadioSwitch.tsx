import { useState } from 'react'
import { cn } from '@/lib/utils'

type Props<T> = {
	values: { label: string; value: T }[]
	onChange?: (value: T) => void
	active?: T
}

export default function RadioSwitch<T extends string>({
	values,
	onChange,
	active,
}: Props<T>) {
	const [_active, setActive] = useState<T>(active || values[0].value)

	const onClickHandler = (value: T) => {
		setActive(value)
		onChange?.(value)
	}

	return (
		<div className='bg-[#2E2B28] flex items-center w-max h-[45px] lg:h-[63px] border border-border backdrop-blur-[10px] rounded-full overflow-hidden'>
			{values.map((item) => (
				<button
					key={item.value}
					onClick={() => onClickHandler(item.value)}
					className={cn(
						'flex-center h-full w-[120px] lg:w-[172px] text-[0.7rem] lg:text-[0.9rem] font-medium text-white rounded-full cursor-pointer truncate',
						_active === item.value && 'bg-primary w-[130px] lg:w-[197px]',
					)}
				>
					{item.label}
				</button>
			))}
		</div>
	)
}
