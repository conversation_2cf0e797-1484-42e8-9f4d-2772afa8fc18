import { ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Link } from './Link'

type FooterLinksProps = {
	links: Array<{ link: string; title: string }>
	category: string
	id: number
	isOpen: boolean
	onClick: React.Dispatch<React.SetStateAction<number>>
}

export default function FooterLinksByCategory({
	id,
	category,
	links,
	isOpen,
	onClick,
}: FooterLinksProps) {
	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter' || e.key === ' ') {
			onClick(id)
			e.preventDefault()
		}
	}

	return (
		<div className='flex flex-col'>
			<button
				type='button'
				className='text-sm lg:text-xl font-semibold flex item-center justify-center lg:justify-start gap-x-2 bg-transparent border-none text-left'
				onClick={() => onClick(id)}
				onKeyDown={handleKeyDown}
			>
				{category}{' '}
				<ChevronDown
					className={cn('w-5 h-auto lg:hidden', isOpen && 'rotate-180')}
				/>
			</button>
			{isOpen && (
				<div className='mt-4'>
					<ul className='flex flex-col gap-y-2 text-center lg:text-left'>
						{links.map((link) => (
							<li key={link.link} className='text-sm lg:text-xl font-semibold'>
								<Link
									href={link.link}
									className='text-xs lg:text-base text-[#7B7B7B] hover:text-primary'
								>
									{link.title}
								</Link>
							</li>
						))}
					</ul>
				</div>
			)}
		</div>
	)
}
