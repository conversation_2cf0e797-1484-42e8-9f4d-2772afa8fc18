import { Image } from '@unpic/react'
import {
	ArrowLeftRight,
	CheckCircle,
	FileText, // For generic log or Collection Created
	Gift,
	ShoppingCart,
	Tag,
	Users,
	XCircle,
} from 'lucide-react'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table'
import type { ActivityLogs } from '@/hooks/useCollectionActivity' // Import the type
import { useIsDesktop } from '@/lib/hooks/useIsDesktop'
import ActivityCard from './cards/ActivityCard' // Assuming this will be updated later

interface MyActivitiesProps {
	activities: ActivityLogs[]
	collectionUrl: string
}

// Helper to get icon and label for activity type
const getActivityIconAndLabel = (type: string) => {
	switch (type) {
		case 'NFT_LISTED':
		case 'LISTING': // common alias
			return { icon: <Tag className='w-5 h-auto' />, label: 'Listed' }
		case 'NFT_TRANSFERRED':
			return {
				icon: <ArrowLeftRight className='w-5 h-auto' />,
				label: 'Transfer',
			}
		case 'TRANSFER': // common alias
			return {
				icon: <ArrowLeftRight className='w-5 h-auto' />,
				label: 'Transfer',
			}
		case 'NFT_MINTED':
			return { icon: <Gift className='w-5 h-auto' />, label: 'Mint' }
		case 'MINT': // common alias
			return { icon: <Gift className='w-5 h-auto' />, label: 'Mint' }
		case 'COLLECTION_CREATED':
			return { icon: <Users className='w-5 h-auto' />, label: 'Created' }
		case 'NFT_BID_PLACED':
			return {
				icon: <CheckCircle className='w-5 h-auto' />,
				label: 'Bid Placed',
			}
		case 'NFT_OFFER_CREATED':
			return {
				icon: <CheckCircle className='w-5 h-auto' />,
				label: 'Offer Created',
			}
		case 'NFT_OFFER_ACCEPTED':
			return {
				icon: <CheckCircle className='w-5 h-auto' />,
				label: 'Offer Accepted',
			}

		default:
			return { icon: <FileText className='w-5 h-auto' />, label: type }
	}
}

// Simple time formatter (can be replaced with a library like date-fns if available)
const formatTimeAgo = (dateString: string): string => {
	const date = new Date(dateString)
	const now = new Date()
	const seconds = Math.round((now.getTime() - date.getTime()) / 1000)
	const minutes = Math.round(seconds / 60)
	const hours = Math.round(minutes / 60)
	const days = Math.round(hours / 24)

	if (seconds < 60) return `${seconds} sec ago`
	if (minutes < 60) return `${minutes} min ago`
	if (hours < 24) return `${hours} hours ago`
	if (days === 1) return '1 day ago'
	return `${days} days ago` // Or format as date for older entries: date.toLocaleDateString()
}

export default function MyActivities({
	activities,
	collectionUrl,
}: MyActivitiesProps) {
	const isDesktop = useIsDesktop()

	if (!activities || activities.length === 0) {
		return (
			<div className='text-center py-10 text-gray-500'>
				No activities found.
			</div>
		)
	}

	if (isDesktop) {
		return (
			<ActivitiesTable activities={activities} collectionUrl={collectionUrl} />
		)
	}
	return (
		<ActivitiesCards activities={activities} collectionUrl={collectionUrl} />
	)
}

function ActivitiesTable({ activities, collectionUrl }: MyActivitiesProps) {
	return (
		<div className='hidden lg:block collectionTableWrapper w-full overflow-x-auto mt-10'>
			<Table>
				<TableHeader>
					<TableRow className='bg-muted/50'>
						<TableHead className='min-w-[150px]'>Type</TableHead>
						<TableHead className='min-w-[220px]'>Item</TableHead>
						<TableHead className='min-w-[120px]'>Price</TableHead>
						<TableHead className='min-w-[150px]'>From</TableHead>
						<TableHead className='min-w-[150px]'>To</TableHead>
						<TableHead className='min-w-[150px]'>Time</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{activities.map((activity) => {
						const { icon, label } = getActivityIconAndLabel(activity.type)
						return (
							<TableRow key={activity.id} className='text-base font-medium'>
								<TableCell>
									<div className='flex items-center gap-x-2'>
										{icon} {label}
									</div>
								</TableCell>
								<TableCell>
									<div className='flex gap-x-2 items-center'>
										<Image
											src={activity.nft?.imageUrl || collectionUrl}
											className='object-cover w-10 h-10 rounded-full'
											layout='fixed'
											width={40}
											height={40}
											alt={activity.nft?.name || 'NFT Image'}
										/>
										<div className='flex flex-col ml-1'>
											<span>{activity.nft?.name || 'N/A'}</span>
											<span className='text-sm font-normal text-[#7E7E7E]'>
												{activity.fromUser?.username || 'System'}
											</span>
										</div>
									</div>
								</TableCell>
								<TableCell>
									{activity.amount ? (
										<div className='flex flex-col'>
											<span>{activity.amount.toLocaleString()} SOL</span>
										</div>
									) : (
										'N/A'
									)}
								</TableCell>
								<TableCell className='text-primary'>
									{activity.fromUser?.username || 'N/A'}
								</TableCell>
								<TableCell className='text-primary'>
									{activity.toUser?.username || 'N/A'}
								</TableCell>
								<TableCell>{formatTimeAgo(activity.createdAt)}</TableCell>
							</TableRow>
						)
					})}
				</TableBody>
			</Table>
		</div>
	)
}

function ActivitiesCards({ activities, collectionUrl }: MyActivitiesProps) {
	// This will render ActivityCard for each activity.
	// ActivityCard.tsx will need to be updated to accept these props.
	return (
		<div className='lg:hidden mt-10'>
			<span className='text-sm font-medium mb-4 block'>Activity Details</span>
			<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
				{activities.map((activity) => (
					<ActivityCard
						key={activity.id}
						activityType={activity.type}
						nftImageUrl={activity.nft?.imageUrl || collectionUrl}
						nftName={activity.nft?.name}
						userName={activity.fromUser?.username}
						priceSol={activity.amount}
						fromUser={activity.fromUser?.username}
						toUser={activity.toUser?.username}
						time={activity.createdAt} // ActivityCard will need to format this
						// Rarity is removed
					/>
				))}
			</div>
		</div>
	)
}
