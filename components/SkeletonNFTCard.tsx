import { cn } from '@/lib/utils'

type SkeletonNFTCardProps = {
	variant?: 'LiveAuction' | 'TrendingNFT' | 'TrendingFusionNFT'
	className?: string
}

export default function SkeletonNFTCard({
	variant = 'LiveAuction',
	className,
}: SkeletonNFTCardProps) {
	const isLiveAuction = variant === 'LiveAuction'
	const isTrendingNft = variant === 'TrendingNFT'
	const isTrendingFusion = variant === 'TrendingFusionNFT'

	return (
		<div
			className={cn('w-full bg-card rounded-[20px] animate-pulse', className)}
		>
			<div className={cn('relative', isLiveAuction && 'p-2.5')}>
				{/* Skeleton image */}
				<div
					className={cn(
						'w-full bg-gray-200/20 h-[200px] lg:h-[308px] rounded-t-[10px]',
						isLiveAuction && 'rounded-[10px] h-[222px]',
					)}
				/>

				{/* Skeleton for live auction timer */}
				{isLiveAuction && (
					<div className='absolute left-1/2 -translate-x-1/2 -bottom-[21px] h-[42px] w-[180px] bg-gray-200/20 rounded-[10px]' />
				)}

				{/* Skeleton for trending NFT header */}
				{isTrendingNft && (
					<div className='flex items-center gap-x-2 px-4 pt-3 absolute top-0 left-0 right-0'>
						<div className='rounded-full w-[45px] h-[45px] bg-gray-200/20' />
						<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
						<div className='ml-auto w-[38px] h-[38px] lg:w-[50px] lg:h-[50px] rounded-[8px] bg-gray-200/20' />
					</div>
				)}

				{/* Skeleton for trending fusion NFT header */}
				{isTrendingFusion && (
					<div className='flex items-center gap-x-2 px-4 pt-3 absolute top-0 left-0 right-0'>
						<div className='h-[38px] lg:h-[50px] w-[120px] rounded-[8px] bg-gray-200/20' />
						<div className='ml-auto w-[38px] h-[38px] lg:w-[50px] lg:h-[50px] rounded-[8px] bg-gray-200/20' />
					</div>
				)}
			</div>

			<div className={cn('px-3 lg:px-5 py-4.5', isLiveAuction && 'pt-9')}>
				<div className='flex items-center justify-between'>
					<div className='h-5 w-32 bg-gray-200/20 rounded-full' />
					{isLiveAuction && (
						<div className='rounded-full w-[36px] h-[36px] bg-gray-200/20' />
					)}
				</div>
				<div
					className='flex justify-between mt-3 pt-2.5 border-t-[0.5px]'
					style={{
						borderImage:
							'linear-gradient(270deg, rgba(255, 255, 255, 0) 2%, #646464 49%, rgba(255, 255, 255, 0) 99%)',
						borderImageSlice: 1,
					}}
				>
					<div className='h-4 w-12 bg-gray-200/20 rounded-full' />
					<div className='h-4 w-16 bg-gray-200/20 rounded-full' />
				</div>
			</div>
		</div>
	)
}
