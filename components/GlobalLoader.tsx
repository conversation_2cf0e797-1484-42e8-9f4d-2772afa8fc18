import { LoaderCircle } from 'lucide-react'
import Brand<PERSON>ogo from '@/public/assets/Logo/logo-white.svg?react'

export default function GlobalLoader() {
	return (
		<div
			id='globalLoader'
			// dynamically add flex class on page transition
			className='hidden items-center justify-center fixed inset-0 bg-black/50 backdrop-blur-[30px]'
		>
			<div className='relative'>
				<LoaderCircle className='size-[70px] lg:size-[136px] animate-spin stroke-1' />
				<BrandLogo className='w-[35px] h-auto lg:w-[65px] text-white absolute inset-1/2 -translate-1/2' />
			</div>
		</div>
	)
}
