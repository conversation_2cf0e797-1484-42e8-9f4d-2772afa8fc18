import { Share2, X } from 'lucide-react'
import { useState } from 'react'
import {
	FacebookIcon,
	FacebookShareButton,
	LinkedinIcon,
	LinkedinShareButton,
	TelegramIcon,
	TelegramShareButton,
	TwitterShareButton,
	WhatsappIcon,
	WhatsappShareButton,
	XIcon,
} from 'react-share'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { copyToClipboard, shareContent } from '@/lib/utils/sharing'

interface ShareModalProps {
	isOpen: boolean
	onClose: () => void
	title: string
	url: string
	description?: string
}

export default function ShareModal({
	isOpen,
	onClose,
	title,
	url,
	description = 'Check out this NFT I created on Shogunex Fusion!',
}: ShareModalProps) {
	const [shareUrl, setShareUrl] = useState(url)

	const handleCopyLink = () => {
		copyToClipboard(shareUrl)
	}

	const handleNativeShare = async () => {
		await shareContent({
			title,
			text: description,
			url: shareUrl,
		})
	}

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className='sm:max-w-md'>
				<DialogHeader>
					<DialogTitle className='text-xl font-bold'>Share NFT</DialogTitle>
					<Button
						variant='ghost'
						className='absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground'
						onClick={onClose}
					>
						<X className='h-4 w-4' />
						<span className='sr-only'>Close</span>
					</Button>
				</DialogHeader>
				<div className='flex flex-col gap-4'>
					<div className='flex justify-center gap-4 py-4'>
						{/* Native Web Share API (if available) */}
						{typeof navigator !== 'undefined' && 'share' in navigator && (
							<Button
								variant='outline'
								className='rounded-full h-12 w-12 p-0'
								onClick={handleNativeShare}
							>
								<Share2 size={24} />
							</Button>
						)}
						<FacebookShareButton
							url={shareUrl}
							// quote={description}
							hashtag='#NFT'
						>
							<FacebookIcon size={48} round />
						</FacebookShareButton>

						<TwitterShareButton
							url={shareUrl}
							title={description}
							hashtags={['NFT', 'Solana', 'ShogunexFusion']}
						>
							<XIcon size={48} round />
						</TwitterShareButton>

						<LinkedinShareButton
							url={shareUrl}
							title={title}
							summary={description}
						>
							<LinkedinIcon size={48} round />
						</LinkedinShareButton>

						<TelegramShareButton url={shareUrl} title={description}>
							<TelegramIcon size={48} round />
						</TelegramShareButton>

						<WhatsappShareButton url={shareUrl} title={description}>
							<WhatsappIcon size={48} round />
						</WhatsappShareButton>
					</div>

					<div className='flex items-center gap-2'>
						<Input
							value={shareUrl}
							onChange={(e) => setShareUrl(e.target.value)}
							className='flex-1'
						/>
						<Button onClick={handleCopyLink} variant='secondary'>
							Copy
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	)
}
