import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '../ui/button'

type DepositFundModalProps = {
	children: React.ReactNode
	onConfirm: () => void
	para?: string
	pending?: boolean
}

export default function ConfirmCancel({
	children,
	onConfirm,
	para,
	pending,
}: DepositFundModalProps) {
	return (
		<Dialog>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className='lg:w-[900px] lg:max-w-screen overflow-y-auto max-h-[90svh] border-border bg-[#191919]'>
				<DialogHeader>
					<DialogTitle className='text-lg md:text-3xl text-center font-semibold'>
						Confirm Cancel ?
					</DialogTitle>
				</DialogHeader>

				<DialogDescription className='mt-5 text-white text-center'>
					<p className='text-xs md:text-2xl font-semibold'>{para}</p>

					<div className='grid grid-cols-1 lg:grid-cols-2 gap-y-3 lg:gap-x-5 mt-10'>
						<DialogClose asChild>
							<Button
								variant='outline'
								className='w-full text-xs lg:text-xl font-semibold lg:!h-[55px]'
							>
								Back
							</Button>
						</DialogClose>
						<Button
							disabled={pending}
							variant='destructive'
							className='text-xs lg:text-xl font-medium lg:!h-[55px]'
							onClick={onConfirm}
						>
							{pending ? 'Cancelling...' : 'Cancel'}
						</Button>
					</div>
				</DialogDescription>
			</DialogContent>
		</Dialog>
	)
}
