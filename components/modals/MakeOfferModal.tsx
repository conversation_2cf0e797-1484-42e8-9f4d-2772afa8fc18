import { publicKey, transactionBuilder } from '@metaplex-foundation/umi'
import { base58 } from '@metaplex-foundation/umi/serializers'
import { DialogClose } from '@radix-ui/react-dialog'
import { useWallet } from '@solana/wallet-adapter-react'
import { Image } from '@unpic/react'
import { ArrowLeft, Check } from 'lucide-react'
import { useContext, useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog'
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select'
import { createOffer } from '@/lib/generated'
import { useSolanaPrice } from '@/lib/hooks/useSolanaPrice'
import { UmiContext } from '@/lib/umi'
import customToast from '../CustomToast'
import { Button } from '../ui/button'
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '../ui/form'
import { Input } from '../ui/input'

type MakeOfferModalProps = {
	children: React.ReactNode
	assetPublicKey?: string
	assetName?: string
	assetImage?: string
	assetPrice?: number
	floorPrice?: number
}

export default function MakeOfferModal({
	children,
	assetPublicKey = '',
	assetName = '',
	assetImage = '',
	assetPrice = 0,
	floorPrice = 0,
}: MakeOfferModalProps) {
	const form = useForm()
	const wallet = useWallet()
	const umi = useContext(UmiContext)
	const solToUsd = useSolanaPrice()
	const [offerPrice, setOfferPrice] = useState('')
	const [duration, setDuration] = useState('86400') // 1 day in seconds (default)
	const [loading, setLoading] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [transactionHash, setTransactionHash] = useState('')
	const [success, setSuccess] = useState(false)
	const [walletBalance, setWalletBalance] = useState('0')
	const closeRef = useRef<HTMLButtonElement>(null)

	// Calculate USD value is done inline where needed

	// Calculate expiration date based on duration
	const expirationDate = new Date(Date.now() + Number(duration) * 1000)

	// Get wallet balance
	useEffect(() => {
		const getBalance = async () => {
			if (!wallet.publicKey || !umi) return
			try {
				const umiPublicKey = publicKey(wallet.publicKey.toBase58())
				const balance = await umi.rpc.getBalance(umiPublicKey)
				if (!balance) return
				const solValue = (Number(balance.basisPoints) / 10 ** balance.decimals)
					.toFixed(4)
					.replace(/\.?0+$/, '')
				setWalletBalance(solValue)
			} catch (error) {
				console.error('Error fetching balance:', error)
			}
		}

		if (wallet.connected && wallet.publicKey) {
			getBalance()
		}
	}, [wallet.publicKey, wallet.connected, umi])

	const handleCreateOffer = async () => {
		if (
			!umi ||
			!wallet.publicKey ||
			!wallet.signTransaction ||
			!offerPrice ||
			!assetPublicKey
		) {
			setError(
				'Wallet not connected or offer price not set or asset not specified.',
			)
			return
		}

		setLoading(true)
		setError(null)

		try {
			const priceLamports = BigInt(
				Math.floor(Number.parseFloat(offerPrice) * 1_000_000_000),
			)
			if (priceLamports <= 0) {
				throw new Error('Price must be positive.')
			}

			// Build the transaction
			const builder = transactionBuilder().add(
				createOffer(umi, {
					buyer: umi.identity,
					asset: publicKey(assetPublicKey),
					price: priceLamports,
					expiresIn: BigInt(Number.parseInt(duration)),
				}),
			)

			const { signature } = await builder.sendAndConfirm(umi).catch((err) => {
				console.error('Failed to create offer:', err)
				throw err
			})

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)
			setOfferPrice('')
			setSuccess(true)
			customToast.success(`Offer Created Successfully for ${offerPrice} SOL`)
			closeRef.current?.click()
		} catch (err: unknown) {
			console.error('Failed to create offer:', err)
			setError(
				`Offer creation failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(false)
		}
	}
	return (
		<Dialog>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className='md:w-[85%] lg:w-[900px] md:max-w-screen overflow-y-auto max-h-[90svh] border-border bg-[#191919] '>
				<DialogHeader>
					<DialogTitle className='md:text-3xl font-semibold'>
						Make an Offer
					</DialogTitle>
					{/* Hidden DialogClose */}
					<DialogClose asChild>
						<button ref={closeRef} className='hidden'>
							Close
						</button>
					</DialogClose>
				</DialogHeader>

				<div className='flex items-center mt-10'>
					<Image
						src={assetImage}
						width={126}
						height={126}
						className='aspect-square max-w-[80px] max-h-[80px] md:max-w-[126px] md:max-h-[126px] rounded-[10px]'
					/>

					<div className='flex flex-col ml-3'>
						<span className='text-xs md:text-xl font-semibold'>
							{assetName}
						</span>
						<span className='text-[10px] md:text-lg text-[#7E7E7E] font-light'>
							{/* Collection name would go here if available */}
						</span>
					</div>

					<div className='flex flex-col items-end ml-auto'>
						<span className='text-xs md:text-2xl font-bold'>
							{assetPrice > 0 ? `${assetPrice} SOL` : '-- SOL'}
						</span>
						<span className='text-[10px] md:text-sm text-[#7E7E7E] font-light'>
							(
							{assetPrice > 0
								? `$${(assetPrice * solToUsd).toFixed(2)} USD`
								: '-- USD'}
							)
						</span>
					</div>
				</div>

				<div className='p-4 lg:p-10 text-xs md:text-xl font-semibold rounded-[10px] bg-[#2D2D2D] mt-8'>
					<div className='w-full flex justify-between'>
						<span>Balance</span>
						<span>{walletBalance} SOL</span>
					</div>
					<div className='w-full flex justify-between mt-7'>
						<span>Floor Price</span>
						<span>{floorPrice > 0 ? `${floorPrice} SOL` : '-- SOL'}</span>
					</div>
					<div className='w-full flex justify-between mt-7'>
						<span>Best Price</span>
						<span>{assetPrice > 0 ? `${assetPrice} SOL` : '-- SOL'}</span>
					</div>
				</div>

				<Form {...form}>
					<FormField
						name='reservePrice'
						render={({ field }) => (
							<FormItem className='mt-8'>
								<FormLabel className='ml-2'>Bidding Amount</FormLabel>
								<FormControl>
									<div className='relative flex items-center gap-x-0 h-10 lg:h-[60px] bg-[#0A0A0A] border border-border rounded-[10px] focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]'>
										<Input
											{...field}
											placeholder='0.00'
											type='number'
											value={offerPrice}
											onChange={(e) => setOfferPrice(e.target.value)}
											onWheel={(e) => e.currentTarget.blur()}
											className='w-full h-full lg:text-xl placeholder:!text-xl bg-transparent border-none focus-visible:ring-0'
										/>
										<div className='flex text-xs md:text-xl items-center justify-end pl-1 h-full'>
											<span className='text-[#393939] pr-4'>
												$
												{offerPrice
													? (Number(offerPrice) * solToUsd).toFixed(2)
													: '0.00'}
											</span>
											<div className='px-3 lg:px-5 flex-center xl:px-8 border-l border-[#2D2D2D] h-full'>
												SOL
											</div>
										</div>
									</div>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<div className='flex justify-between text-[10px] md:text-xl'>
						<span>
							$
							{offerPrice ? (Number(offerPrice) * solToUsd).toFixed(2) : '0.00'}{' '}
							Total
						</span>
						<span>
							Total offer amount: {offerPrice || '0'} SOL ($
							{offerPrice ? (Number(offerPrice) * solToUsd).toFixed(2) : '0.00'}
							)
						</span>
					</div>

					<div className='mt-3'>
						<FormLabel>Duration</FormLabel>
						<div className='grid grid-cols-1 md:grid-cols-[40%_60%] gap-4 mt-2'>
							<Select
								value={duration}
								onValueChange={(value) => setDuration(value)}
							>
								<SelectTrigger className='w-full text-xs lg:text-xl !h-10 lg:!h-[60px] rounded-[10px] bg-[#0A0A0A] cursor-pointer'>
									<SelectValue placeholder='Select a duration' />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value='86400'>1 Day</SelectItem>
									<SelectItem value='604800'>7 Days</SelectItem>
									<SelectItem value='1296000'>15 Days</SelectItem>
								</SelectContent>
							</Select>
							<div className='w-full text-xs md:text-lg font-semibold flex items-center px-5 justify-between h-10 lg:h-[60px] rounded-[10px] bg-[#0A0A0A] text-white border border-border'>
								<span>{expirationDate.toLocaleDateString()}</span>
								<span>
									{expirationDate.toLocaleTimeString([], {
										hour: '2-digit',
										minute: '2-digit',
									})}
								</span>
							</div>
						</div>
					</div>

					<div className='flex justify-center w-full mt-8'>
						<ConfirmOffer onConfirm={handleCreateOffer} loading={loading}>
							<Button
								variant='default'
								className='w-full text-xs lg:text-xl font-medium lg:!h-[55px]'
								disabled={
									loading ||
									!wallet.publicKey ||
									!offerPrice ||
									Number(offerPrice) <= 0
								}
							>
								Make an Offer
							</Button>
						</ConfirmOffer>
					</div>

					{error && (
						<div className='mt-4 p-3 bg-red-900/30 border border-red-500 rounded-md text-red-200'>
							{error}
						</div>
					)}

					{success && (
						<div className='mt-4 p-3 bg-green-900/30 border border-green-500 rounded-md text-green-200 flex items-center'>
							<Check className='w-5 h-5 mr-2' />
							Offer created successfully! Transaction hash: {transactionHash}
						</div>
					)}
				</Form>
			</DialogContent>
		</Dialog>
	)
}

const ConfirmOffer = ({
	children,
	onConfirm,
	loading,
}: {
	children: React.ReactNode
	onConfirm: () => Promise<void>
	loading: boolean
}) => {
	const [open, setOpen] = useState(false)

	const handleConfirm = async () => {
		await onConfirm()
		setOpen(false)
	}

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className='lg:w-[900px] lg:max-w-screen border-border'>
				<DialogHeader>
					<ArrowLeft className='w-5 h-auto' />
				</DialogHeader>
				<DialogDescription className='mt-[50px] text-white'>
					<h3 className='text-lg md:text-3xl text-center font-semibold'>
						Confirm your offer
					</h3>
					<p className='text-xs md:text-xl text-center font-semibold mt-2'>
						Please confirm that you want to make this offer
					</p>
				</DialogDescription>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-y-3 lg:gap-x-5 mt-10'>
					<Button
						variant='outline'
						className='w-full text-xs lg:text-xl font-semibold lg:!h-[55px]'
						onClick={() => setOpen(false)}
						disabled={loading}
					>
						Cancel
					</Button>
					<Button
						variant='default'
						className='text-xs lg:text-xl font-medium lg:!h-[55px]'
						onClick={handleConfirm}
						disabled={loading}
					>
						{loading ? 'Processing...' : 'Confirm Offer'}
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	)
}
