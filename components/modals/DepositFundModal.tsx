import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '../ui/button'

type DepositFundModalProps = {
	children: React.ReactNode
}

export default function DepositFundModal({ children }: DepositFundModalProps) {
	return (
		<Dialog>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className='lg:w-[900px] lg:max-w-screen overflow-y-auto max-h-[90svh] border-border bg-[#191919]'>
				<DialogHeader>
					<DialogTitle className='text-lg md:text-3xl font-semibold'>
						Deposit SOL in Polygon
					</DialogTitle>
				</DialogHeader>

				<DialogDescription className='mt-5 text-white'>
					<p className='text-xs md:text-2xl font-semibold'>
						You don't have enough funds to complete the purchase. Please deposit
						or convert your funds.
					</p>

					<Button className='mt-10 lg:min-w-[217px]'>Deposit</Button>
				</DialogDescription>
			</DialogContent>
		</Dialog>
	)
}
