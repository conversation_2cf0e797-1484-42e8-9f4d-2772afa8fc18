import { Image } from '@unpic/react'
import {
	Dialog,
	DialogContent,
	Di<PERSON>Header,
	DialogTrigger,
} from '@/components/ui/dialog'

export default function ApproveCollectionModal({
	children,
}: {
	children: React.ReactNode
}) {
	return (
		<Dialog>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className='lg:w-[900px] lg:max-w-screen border-border'>
				<DialogHeader>
					<DialogHeader className='text-lg md:text-3xl font-semibold'>
						Approve Collection
					</DialogHeader>
				</DialogHeader>

				<div className='mt-6 pb-6'>
					<div className='flex items-center'>
						<Image
							src='/assets/temp/trending-nft2.png'
							width={126}
							height={126}
							className='aspect-square max-w-[80px] max-h-[80px] md:max-w-[126px] md:max-h-[126px] rounded-[10px]'
						/>

						<div className='flex flex-col ml-3'>
							<span className='text-xs md:text-xl font-semibold'>
								The Lightning of love
							</span>
							<span className='text-[10px] md:text-lg text-[#7E7E7E] font-light'>
								The Lightning of love
							</span>
						</div>
					</div>
					<div className='w-full border-t-[2px] gradientBorder mt-10' />
					<div className='mt-8'>
						<span className='text-sm md:text-xl font-semibold'>
							Go to your wallet
						</span>
						<p className='text-xs md:text-xl font-semibold mt-2'>
							You'll be asked to approve this collection from your wallet. You
							only need to approve each collection once.
						</p>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	)
}
