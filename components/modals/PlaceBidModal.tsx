import { DialogClose } from '@radix-ui/react-dialog'
import { publicKey } from '@metaplex-foundation/umi'
import { useWallet } from '@solana/wallet-adapter-react'
import { Image } from '@unpic/react'
import { useRef, useState, useEffect, useContext } from 'react'
import { useForm } from 'react-hook-form'
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog'
import { useSolanaPrice } from '@/lib/hooks/useSolanaPrice'
import { UmiContext } from '@/lib/umi'
import type { NFTDetailResponse } from '@/server/app/nft/nft.route'
import { Button } from '../ui/button'
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '../ui/form'
import { Input } from '../ui/input'

type PlaceBidModalProps = {
	children: React.ReactNode
	nft: NFTDetailResponse['nft']
	onPlaceBid?: (bidAmount: number) => Promise<void>
}

export default function PlaceBidModal({
	children,
	nft,
	onPlaceBid,
}: PlaceBidModalProps) {
	const solToUsd = useSolanaPrice()
	const wallet = useWallet()
	const umi = useContext(UmiContext)
	const form = useForm()
	const [bidAmount, setBidAmount] = useState<string>('')
	const [loading, setLoading] = useState(false)
	const [walletBalance, setWalletBalance] = useState('0')
	const closeRef = useRef<HTMLButtonElement>(null)

	// Get wallet balance
	useEffect(() => {
		const getBalance = async () => {
			if (!wallet.publicKey || !umi) return
			try {
				const umiPublicKey = publicKey(wallet.publicKey.toBase58())
				const balance = await umi.rpc.getBalance(umiPublicKey)
				if (!balance) return
				const solValue = (Number(balance.basisPoints) / 10 ** balance.decimals)
					.toFixed(4)
					.replace(/\.?0+$/, '')
				setWalletBalance(solValue)
			} catch (error) {
				console.error('Error fetching balance:', error)
			}
		}

		if (wallet.connected && wallet.publicKey) {
			getBalance()
		}
	}, [wallet.publicKey, wallet.connected, umi])

	return (
		<Dialog>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className='lg:w-[900px] lg:max-w-screen overflow-y-auto max-h-[90svh] border-border bg-[#191919]'>
				<DialogHeader>
					<DialogTitle className='text-3xl font-semibold'>
						Place a Bid
					</DialogTitle>
					{/* Hidden DialogClose */}
					<DialogClose asChild>
						<button ref={closeRef} className='hidden'>
							Close
						</button>
					</DialogClose>
				</DialogHeader>

				<div className='flex items-center mt-10'>
					<Image
						src={nft?.logoUrl || '/assets/temp/trending-nft2.png'}
						width={126}
						height={126}
						className='aspect-square rounded-[10px]'
					/>

					<div className='flex flex-col ml-3'>
						<span className='text-xl font-semibold'>
							{nft?.name || 'NFT Name'}
						</span>
						<span className='text-lg text-[#7E7E7E] font-light'>
							{nft?.symbol || 'Symbol'}
						</span>
					</div>

					<div className='flex flex-col items-end ml-auto'>
						<span className='text-2xl font-bold'>
							{
								+(
									nft.latestListing?.status === 'ACTIVE'
										? Number(nft.latestListing.price)
										: nft?.biddingPrice || 0
								).toFixed(2)
							}{' '}
							SOL
						</span>
						<span className='text-sm text-[#7E7E7E] font-light'>
							($
							{
								+(
									(nft.latestListing?.status === 'ACTIVE'
										? Number(nft.latestListing.price)
										: nft?.biddingPrice || 0) * solToUsd
								).toFixed(2)
							}{' '}
							USD)
						</span>
					</div>
				</div>

				<div className='p-10 text-xl font-semibold rounded-[10px] bg-[#2D2D2D] mt-8'>
					<div className='w-full flex justify-between'>
						<span>Balance</span>
						<span>{walletBalance} SOL</span>
					</div>
					<div className='w-full flex justify-between mt-7'>
						<span>Floor Price</span>
						<span>{nft.biddingPrice} SOL</span>
					</div>
					<div className='w-full flex justify-between mt-7'>
						<span>Best Price</span>
						<span>{nft.latestBid?.amount} SOL</span>
					</div>
				</div>

				<Form {...form}>
					<FormField
						name='bidAmount'
						render={({ field }) => (
							<FormItem className='mt-8'>
								<FormLabel className='ml-2'>Bidding Amount</FormLabel>
								<FormControl>
									<div className='relative flex items-center h-[60px] bg-[#0A0A0A] border border-border rounded-md focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]'>
										<Input
											{...field}
											placeholder='0.00'
											type='number'
											value={bidAmount}
											onChange={(e) => setBidAmount(e.target.value)}
											onWheel={(e) => e.currentTarget.blur()}
											className='w-full h-full lg:text-xl placeholder:!text-xl bg-transparent border-none focus-visible:ring-0'
										/>
										<div className='flex text-xs md:text-xl items-center justify-end pl-1 h-full'>
											<span className='text-[#393939] pr-4'>
												$
												{(
													Number.parseFloat(bidAmount || '0') * solToUsd
												).toFixed(2)}
											</span>
											<div className='px-3 lg:px-5 flex-center xl:px-8 border-l border-[#2D2D2D] h-full'>
												SOL
											</div>
										</div>
									</div>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<div className='flex justify-between text-xl mt-2'>
						<span>
							${(Number.parseFloat(bidAmount || '0') * solToUsd).toFixed(2)}{' '}
							Total
						</span>
						<span>
							Total bid amount: {bidAmount || '0'} SOL ($
							{(Number.parseFloat(bidAmount || '0') * solToUsd).toFixed(2)})
						</span>
					</div>

					<div className='grid grid-cols-1 lg:grid-cols-2 gap-y-3 lg:gap-x-5 mt-8'>
						<Button
							variant='default'
							className='text-xs lg:text-xl font-medium lg:!h-[55px]'
							onClick={async () => {
								try {
									if (onPlaceBid && bidAmount) {
										setLoading(true)
										await onPlaceBid(Number.parseFloat(bidAmount)).finally(() =>
											setLoading(false),
										)
									}
								} catch (err) {
									console.error('Failed to place bid:', err)
								} finally {
									setBidAmount('')
									closeRef.current?.click()
								}
							}}
							disabled={
								loading || !bidAmount || Number.parseFloat(bidAmount) <= 0
							}
						>
							{loading ? 'Processing...' : 'Place a Bid'}
						</Button>
					</div>
				</Form>
			</DialogContent>
		</Dialog>
	)
}
