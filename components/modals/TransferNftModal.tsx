import { transferV1 } from '@metaplex-foundation/mpl-core'
import { publicKey } from '@metaplex-foundation/umi'
import { useWallet } from '@solana/wallet-adapter-react'
import { Image } from '@unpic/react'
import {
	AlertCircle,
	AlertTriangle,
	BadgeCheck,
	Check,
	InfoIcon,
	ShieldAlert,
	Trash2,
} from 'lucide-react'
import type React from 'react'
import { useContext, useEffect, useState } from 'react'
import { toast } from 'sonner'
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTrigger,
} from '@/components/ui/dialog'
import { useTransferNftOwner } from '@/hooks/useNftTransfer'
import { UmiContext } from '@/lib/umi'
import { cn } from '@/lib/utils'
import { Button } from '../ui/button'
import { Input } from '../ui/input'

// Simple validation function for Solana address format
const isValidSolanaAddress = (address: string): boolean => {
	// Solana addresses are 32-bytes (44 characters in base58)
	// They can be 32-45 characters as some addresses have extra info
	return /^[1-9A-HJ-NP-Za-km-z]{32,45}$/.test(address)
}

// Estimated fee for transfer transaction in lamports
const ESTIMATED_TRANSFER_FEE = 10000 // 0.00001 SOL - increased to be safer

// Metaplex error codes
const METAPLEX_ERRORS = {
	INSUFFICIENT_FUNDS: 'insufficient funds',
	CUSTOM_PROGRAM_ERROR_57: 'custom program error: 57',
	METADATA_AUTHORITY: 'metadata authority',
	UPDATE_AUTHORITY: 'update authority',
	TOKEN_ACCOUNT: 'token account',
	NOT_FOUND: 'not found',
}

// Component props
interface TransferNFTModalProps {
	children: React.ReactNode
	nftName: string
	nftImage: string
	collectionName?: string
	mintAddress: string
	collectionAddress?: string
	onTransferComplete?: () => void
}

export default function TransferNFTModal({
	children,
	nftName,
	nftImage,
	collectionName = '',
	mintAddress,
	collectionAddress,
	onTransferComplete,
}: TransferNFTModalProps) {
	const umi = useContext(UmiContext)
	const wallet = useWallet()
	const [recipientAddress, setRecipientAddress] = useState('')
	const [isAddressValid, setIsAddressValid] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [showTransferApproval, setShowTransferApproval] = useState(false)
	const [isLoading, setIsLoading] = useState(false)
	const [isSuccess, setIsSuccess] = useState(false)
	const [open, setOpen] = useState(false)
	const [insufficientBalance, setInsufficientBalance] = useState(false)
	const [networkInfo, setNetworkInfo] = useState<string | null>(null)
	const [isMetadataError, setIsMetadataError] = useState(false)
	const [debugInfo, setDebugInfo] = useState<string | null>(null)

	const { mutate: transferNftOwner, isPending: isTransferring } =
		useTransferNftOwner()

	// Check if we're on devnet or mainnet
	const getNetworkInfo = async () => {
		if (!umi) return null

		try {
			const endpoint = umi.rpc.getEndpoint()
			const isDevnet = endpoint.includes('devnet')
			setNetworkInfo(isDevnet ? 'Devnet' : 'Mainnet')
			return isDevnet ? 'Devnet' : 'Mainnet'
		} catch (err) {
			console.error('Failed to determine network:', err)
			return null
		}
	}

	// Check if user has enough SOL for the transfer
	const checkBalance = async () => {
		if (!umi || !wallet.publicKey) return false

		try {
			const balance = await umi.rpc.getBalance(umi.identity.publicKey)
			// Compare using the basisPoints value which is a bigint
			const hasEnoughBalance =
				balance.basisPoints >= BigInt(ESTIMATED_TRANSFER_FEE)
			setInsufficientBalance(!hasEnoughBalance)
			return hasEnoughBalance
		} catch (err) {
			console.error('Failed to check balance:', err)
			return false
		}
	}

	const validateAddress = (address: string) => {
		// Reset any previous errors
		setError(null)
		setInsufficientBalance(false)
		setIsMetadataError(false)

		// Empty check
		if (!address.trim()) {
			setIsAddressValid(false)
			return
		}

		// Self-transfer check
		if (wallet.publicKey && address === wallet.publicKey.toString()) {
			setError('Cannot transfer to self')
			setIsAddressValid(false)
			return
		}

		// Validate Solana address format
		if (!isValidSolanaAddress(address)) {
			setError('Invalid Solana address format')
			setIsAddressValid(false)
			return
		}

		setIsAddressValid(true)
	}

	const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value
		setRecipientAddress(value)
		validateAddress(value)
	}

	const clearInput = () => {
		setRecipientAddress('')
		setIsAddressValid(false)
		setError(null)
		setInsufficientBalance(false)
		setIsMetadataError(false)
		setDebugInfo(null)
	}

	const handleTransfer = async () => {
		if (!umi || !wallet.publicKey) {
			toast.error('Wallet not connected')
			return
		}

		if (!isAddressValid) {
			return
		}

		setIsLoading(true)
		setError(null)
		setInsufficientBalance(false)
		setIsMetadataError(false)
		setDebugInfo(null)

		try {
			// Check network
			const network = await getNetworkInfo()

			// Check balance before proceeding
			const hasEnoughBalance = await checkBalance()
			if (!hasEnoughBalance) {
				setError(
					`Insufficient SOL balance for transfer on ${network}. Please add more SOL to your account.`,
				)
				setIsLoading(false)
				return
			}

			const mintId = publicKey(mintAddress)
			const destinationOwner = publicKey(recipientAddress)

			setShowTransferApproval(true)

			// Use MPL Core transferV1
			const result = await transferV1(umi, {
				asset: mintId,
				newOwner: destinationOwner,
				collection: collectionAddress
					? publicKey(collectionAddress)
					: undefined,
			}).sendAndConfirm(umi, {
				send: {
					skipPreflight: true,
				},
				confirm: {
					commitment: 'finalized',
				},
			})

			// Check if we have a signature
			if (result.signature) {
				setIsSuccess(true)
				toast.success(
					'NFT transfer successful! Transaction confirmed on-chain.',
				)
				await transferNftOwner({
					publicKey: mintAddress,
					ownerWalletAddress: recipientAddress,
				})

				// Call the callback function if provided
				if (onTransferComplete) {
					onTransferComplete()
				}

				// Close the modal after successful transfer
				setTimeout(() => {
					setOpen(false)
					// Reset states for next use
					setShowTransferApproval(false)
					setIsSuccess(false)
					clearInput()
				}, 2000)
			} else {
				throw new Error('Transaction failed to confirm')
			}
		} catch (error) {
			console.error('NFT transfer failed:', error)

			// Check for specific error types
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error'
			setDebugInfo(`Final error: ${errorMessage.substring(0, 150)}...`)

			// Check if the error is about signature expiration
			if (
				errorMessage.includes('has expired') ||
				errorMessage.includes('block height exceeded')
			) {
				// This usually means the transaction was successful but took too long to confirm
				setIsSuccess(true)
				toast.success(
					'NFT transfer initiated! Please check your wallet or Solscan for confirmation.',
				)

				// Call the callback function if provided
				if (onTransferComplete) {
					onTransferComplete()
				}

				// Close the modal after successful transfer
				setTimeout(() => {
					setOpen(false)
					// Reset states for next use
					setShowTransferApproval(false)
					setIsSuccess(false)
					clearInput()
				}, 2000)
				return
			}

			if (
				errorMessage.includes(METAPLEX_ERRORS.INSUFFICIENT_FUNDS) ||
				errorMessage.includes('0x1')
			) {
				setError(
					`Insufficient SOL balance for transfer on ${networkInfo}. Please add more SOL to your account.`,
				)
				setInsufficientBalance(true)
			} else if (
				errorMessage.includes(METAPLEX_ERRORS.CUSTOM_PROGRAM_ERROR_57) ||
				errorMessage.includes(METAPLEX_ERRORS.METADATA_AUTHORITY) ||
				errorMessage.includes(METAPLEX_ERRORS.UPDATE_AUTHORITY) ||
				errorMessage.includes('57')
			) {
				setError(
					'Cannot transfer this NFT. This NFT has transfer restrictions (error 57). This is common with certain collections that enforce royalties or have custom transfer rules.',
				)
				setIsMetadataError(true)
				setDebugInfo(
					`Authority error 57: This NFT requires special permissions to transfer. The mint address ${mintAddress} may be part of a collection with transfer restrictions.`,
				)
			} else if (
				errorMessage.includes(METAPLEX_ERRORS.TOKEN_ACCOUNT) ||
				errorMessage.includes(METAPLEX_ERRORS.NOT_FOUND)
			) {
				setError(
					'Token account not found. This can happen if the destination has never owned an NFT.',
				)
				setIsMetadataError(true)
			} else if (errorMessage.includes('network')) {
				setError(
					`Network error while transferring on ${networkInfo}. Please try again.`,
				)
			} else {
				setError(`Transfer failed: ${errorMessage}`)
			}

			setShowTransferApproval(false)
		} finally {
			setIsLoading(false)
		}
	}

	// When the modal opens, fetch the NFT metadata, tokenProgramId and pNFT flags
	useEffect(() => {
		if (open && umi && mintAddress && wallet.publicKey) {
			getNetworkInfo()
		}
		// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	}, [open, umi, mintAddress, wallet.publicKey, getNetworkInfo])

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger
				asChild
				onClick={() => {
					setOpen(true)
					// Check network when opening modal
					getNetworkInfo()
				}}
			>
				{children}
			</DialogTrigger>
			<DialogContent className='lg:w-[900px] lg:max-w-screen border-border xl:px-14'>
				<DialogHeader>
					<DialogHeader className='text-lg md:text-3xl font-semibold'>
						{showTransferApproval ? 'Transfer Approval' : 'Transfer'}
					</DialogHeader>
					{networkInfo && (
						<div className='text-xs text-primary font-medium'>
							Network: {networkInfo}
							{collectionName && collectionAddress && (
								<span className='ml-2'>• Collection: {collectionName}</span>
							)}
						</div>
					)}
				</DialogHeader>

				{isSuccess ? (
					<div className='mt-6 text-center'>
						<div className='flex items-center justify-center mb-4'>
							<Check className='w-12 h-12 text-green-500' />
						</div>
						<h4 className='text-lg md:text-2xl font-semibold'>
							Transfer Successful!
						</h4>
						<span className='text-sm md:text-lg block mt-2'>
							The NFT has been transferred to {recipientAddress.slice(0, 6)}...
							{recipientAddress.slice(-4)}
						</span>
					</div>
				) : showTransferApproval ? (
					<div className='mt-6 text-center'>
						<h4 className='text-lg md:text-2xl font-semibold'>
							{isLoading ? 'Processing transfer...' : 'Go to your wallet'}
						</h4>
						<span className='text-sm md:text-lg'>
							{isLoading
								? 'Please wait while the transaction is being processed'
								: 'You will be asked to approve this transfer from your wallet'}
						</span>
						{debugInfo && (
							<div className='mt-3 p-2 bg-gray-800 rounded text-xs text-left overflow-auto max-h-20'>
								<code>{debugInfo}</code>
							</div>
						)}
					</div>
				) : (
					<div className='mt-6'>
						<div className='flex items-center'>
							<Image
								src={nftImage}
								width={126}
								height={126}
								className='aspect-square max-w-[80px] max-h-[80px] md:max-w-[126px] md:max-h-[126px] rounded-[10px]'
								alt={nftName}
							/>

							<div className='flex flex-col ml-3'>
								<span className='text-xs md:text-xl font-semibold'>
									{nftName}
								</span>
								<div className='flex flex-col md:flex-row md:items-center gap-1'>
									{collectionName && (
										<span className='inline-flex items-center text-[10px] md:text-lg text-[#7E7E7E] font-light'>
											{collectionName}{' '}
											<BadgeCheck className='hidden w-4 md:w-6 h-auto stroke-black fill-primary ml-1' />
										</span>
									)}
								</div>
							</div>
						</div>
						<div className='w-full border-t-[2px] gradientBorder my-5 lg:mt-8' />
						<div>
							<label className='text-sm md:text-lg lg:text-xl font-semibold'>
								Transfer "{nftName}" to:
							</label>
							<div className='relative mt-4'>
								<Input
									type='text'
									placeholder='Enter wallet address'
									value={recipientAddress}
									onChange={handleAddressChange}
									className={cn(
										'pr-12',
										(error || insufficientBalance || isMetadataError) &&
											'border-[#DD0003] focus-visible:ring-[#DD0003]/50',
									)}
								/>
								{recipientAddress && (
									<Trash2
										className='w-4 lg:w-6 h-auto text-white hover:text-destructive absolute right-4 top-1/2 -translate-y-1/2 cursor-pointer'
										onClick={clearInput}
									/>
								)}
							</div>
							{error && (
								<span className='text-xs lg:text-base text-[#DD0003] inline-block mt-2 flex items-center gap-2'>
									{insufficientBalance && <AlertTriangle className='w-4 h-4' />}
									{isMetadataError && <ShieldAlert className='w-4 h-4' />}
									{error}
								</span>
							)}

							{insufficientBalance && (
								<div className='bg-[#3A1111] px-4 py-3 w-full mt-3 flex gap-x-2 rounded-[10px] border border-[#DD0003]'>
									<AlertTriangle className='w-5 h-auto text-[#DD0003] flex-shrink-0 mt-1' />
									<span className='text-xs md:text-sm'>
										You need more SOL in your wallet to complete this transfer
										on {networkInfo}. If you're on devnet, you can get free SOL
										from a faucet.
									</span>
								</div>
							)}

							{isMetadataError && (
								<div className='bg-[#3A1111] px-4 py-3 w-full mt-3 flex gap-x-2 rounded-[10px] border border-[#DD0003]'>
									<ShieldAlert className='w-5 h-auto text-[#DD0003] flex-shrink-0 mt-1' />
									<span className='text-xs md:text-sm'>
										Error 57 means you don't have the required authority to
										transfer this NFT. This can happen with certain NFTs that
										have transfer restrictions or if you're not the true owner.
									</span>
								</div>
							)}

							<div className='bg-[#262626] px-3 w-full min-h-[50px] mt-5 lg:min-h-[80px] flex-center gap-x-2 rounded-[10px]'>
								<InfoIcon className='w-6 h-auto text-primary' />
								<span className='text-xs md:text-sm lg:text-xl'>
									Items sent to the wrong address cannot be recovered.
								</span>
							</div>
						</div>
					</div>
				)}

				<div className='w-full max-w-[357px] mx-auto mt-2 lg:mt-4'>
					{isSuccess ? (
						<Button className='w-full' onClick={() => setOpen(false)}>
							Close
						</Button>
					) : (
						<Button
							className='w-full'
							onClick={handleTransfer}
							disabled={
								!isAddressValid ||
								isLoading ||
								insufficientBalance ||
								isMetadataError
							}
						>
							{isLoading ? 'Processing...' : 'Transfer'}
						</Button>
					)}
				</div>
			</DialogContent>
		</Dialog>
	)
}
