import { Image } from '@unpic/react'
import { Loader2, Search } from 'lucide-react'
import { useEffect, useId, useState } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTrigger,
} from '@/components/ui/dialog'
import {
	useFollowers,
	useFollowing,
	useFollowStatus,
	useFollowUser,
} from '@/hooks/useFollowUser'
import { cn } from '@/lib/utils'
import { Link } from '../atoms/Link'
import { PaginationComp } from '../MyPagination'
import { Input } from '../ui/input'

type User = {
	id: string
	userName: string
	avatar: string | null
	publicKey: string
}

type FollowListingModalProps = {
	children: React.ReactNode
	userId: string
	tabValue?: 'Following' | 'Followers'
}

export default function FollowListingModal({
	children,
	userId,
	tabValue = 'Followers',
}: FollowListingModalProps) {
	const [tab, setTab] = useState<'Following' | 'Followers'>(tabValue)
	const [search, setSearch] = useState('')
	const [page, setPage] = useState(1)
	const limit = 10
	const queryClient = useQueryClient()

	useEffect(() => {
		setTab(tabValue)
	}, [tabValue])

	// Fetch follow status which now includes counts
	const { data: followStatus } = useFollowStatus(userId)

	const { data: followersData, isLoading: isLoadingFollowers } = useFollowers({
		targetUserId: userId,
		page,
		limit,
		search,
	})

	const { data: followingData, isLoading: isLoadingFollowing } = useFollowing({
		targetUserId: userId,
		page,
		limit,
		search,
	})

	const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearch(e.target.value)
		setPage(1) // Reset to first page when searching
	}

	const activeData = tab === 'Followers' ? followersData : followingData
	const isLoading =
		tab === 'Followers' ? isLoadingFollowers : isLoadingFollowing
	const users = activeData?.users || []
	const total = activeData?.total || 0
	const totalPages = Math.ceil(total / limit)

	// Get count from follow status if available, otherwise from paginated data
	const followersCount =
		followStatus?.followersCount ?? followersData?.total ?? 0
	const followingCount =
		followStatus?.followingCount ?? followingData?.total ?? 0

	const handleFollowUpdate = () => {
		// Invalidate all relevant queries to trigger refetches
		queryClient.invalidateQueries({ queryKey: ['followStatus', userId] })
		queryClient.invalidateQueries({ queryKey: ['followers'] })
		queryClient.invalidateQueries({ queryKey: ['following'] })
	}

	return (
		<Dialog>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className='lg:w-[750px] p-0 pb-3 lg:max-w-screen overflow-y-auto max-h-[90svh] border-border !bg-black p-5'>
				<DialogHeader className='sticky top-0 !bg-black py-4 px-5'>
					<div className='max-w-fit h-[40px] lg:h-[50px] mx-auto grid grid-cols-2 text-sm lg:text-lg text-white font-semibold border border-border rounded-full overflow-hidden'>
						{(['Followers', 'Following'] as const).map((item) => (
							<button
								onClick={() => setTab(item)}
								key={item}
								className={cn(
									'px-3 lg:px-5 h-full cursor-pointer outline-none',
									tab === item && 'bg-primary',
								)}
							>
								{item}
							</button>
						))}
					</div>
					<span className='text-lg block lg:text-xl font-medium'>
						{tab === 'Followers'
							? `Followers ${followersCount}`
							: `Following ${followingCount}`}
					</span>
					{users.length > 0 && (
						<div className='relative mt-3 h-10 lg:h-[50px] pl-7 bg-black/60 border border-border rounded-full focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]'>
							<Search className='absolute left-4 w-5 h-auto top-1/2 -translate-y-1/2' />
							<Input
								placeholder='Search'
								value={search}
								onChange={handleSearch}
								className='h-full text-xs md:text-xl placeholder:!text-xs md:placeholder:!text-xl bg-transparent border-none focus-visible:ring-0'
							/>
						</div>
					)}
				</DialogHeader>

				<DialogDescription className='text-white px-5'>
					{isLoading ? (
						<div className='flex justify-center my-8'>
							<Loader2 className='w-8 h-8 animate-spin' />
						</div>
					) : users.length === 0 ? (
						<div className='text-center py-8'>No {tab.toLowerCase()} found</div>
					) : (
						<ul>
							{users.map((user: User) => (
								<UserItem
									key={user.id}
									user={user}
									onFollowUpdate={handleFollowUpdate}
								/>
							))}
						</ul>
					)}

					{!isLoading && users.length > 0 && (
						<PaginationComp
							className='mt-7'
							currentPage={page}
							setCurrentPage={setPage}
							totalPages={totalPages}
						/>
					)}
				</DialogDescription>
			</DialogContent>
		</Dialog>
	)
}

const UserItem = ({
	user,
	onFollowUpdate,
}: {
	user: User
	onFollowUpdate: () => void
}) => {
	const { toggleFollow } = useFollowUser()
	const { data: followStatus } = useFollowStatus(user.id)
	const isFollowing = followStatus?.isFollowing || false

	const handleToggleFollow = () => {
		toggleFollow(user.id, isFollowing)
		onFollowUpdate()
	}

	return (
		<li className='userItem flex flex-col gap-y-4 md:flex-row lg:items-center justify-between first:mt-0 mt-2.5 bg-[#262626] rounded-[10px] px-5 py-4'>
			<Link href={`/profile/${user.publicKey}`}>
				<div className='flex items-center gap-x-2 lg:gap-x-4'>
					<Image
						src={user.avatar || '/assets/img/default-profile.png'}
						className='rounded-full w-[40px] h-[40px] lg:w-[50px] lg:h-[50px] object-cover shrink-0'
						layout='fixed'
						width={50}
						height={50}
						alt='user profile'
					/>
					<div className='flex flex-col'>
						<div>
							<span className='text-sm lg:text-lg font-semibold'>
								{user.userName}
							</span>{' '}
							<span className='gradientText text-xs lg:text-sm ml-2 lg:ml-4'>
								@{user.userName.toLowerCase()}
							</span>
						</div>
						<span className='text-[10px] lg:text-xs font-light text-[#AFAEAE] mt-1.5'>
							{user.publicKey.substring(0, 10)}...
							{user.publicKey.substring(user.publicKey.length - 10)}
						</span>
					</div>
				</div>
			</Link>
			<span
				className='text-sm lg:text-base ml-auto lg:ml-0 font-semibold cursor-pointer'
				role='button'
				onClick={handleToggleFollow}
			>
				{isFollowing ? 'Unfollow' : 'Follow'}
			</span>
		</li>
	)
}
