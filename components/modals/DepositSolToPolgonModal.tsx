import { useForm } from 'react-hook-form'
import {
	<PERSON><PERSON>,
	<PERSON>alog<PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON>alogTitle,
	DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '../ui/button'
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '../ui/form'
import { Input } from '../ui/input'

type DepositFundModalProps = {
	children: React.ReactNode
}

export default function DepositSolIntoPolygonModal({
	children,
}: DepositFundModalProps) {
	const form = useForm()
	return (
		<Dialog>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className='lg:w-[900px] lg:max-w-screen overflow-y-auto max-h-[90svh] border-border bg-[#191919]'>
				<DialogHeader>
					<DialogTitle className='text-lg md:text-3xl font-semibold'>
						Deposit SOL in Polygon
					</DialogTitle>
				</DialogHeader>

				<div className='mt-5 text-white'>
					<p className='text-xs md:text-2xl font-semibold'>
						You don't have enough funds to complete the purchase. Please deposit
						or convert your funds.
					</p>

					<Form {...form}>
						<FormField
							name='reservePrice'
							render={({ field }) => (
								<FormItem className='mt-8'>
									<FormControl>
										<div className='relative h-10 md:h-[60px] bg-[#0A0A0A] border border-border rounded-[10px] focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]'>
											<Input
												{...field}
												placeholder='0.00'
												className='w-1/2 h-full text-xs md:text-xl placeholder:!text-xs md:placeholder:!text-xl bg-[#0A0A0A] border-none focus-visible:ring-0'
											/>
											<div className='absolute text-xs md:text-xl top-0 right-0 flex items-center justify-end pl-2 w-1/2 h-full'>
												<span className='text-[#393939] pr-4'>$0.00</span>
												<div className='px-3 lg:px-5 flex-center xl:px-8 border-l border-[#2D2D2D] h-full'>
													SOL
												</div>
											</div>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</Form>

					<Button className='mt-10 lg:min-w-[217px]'>Continue</Button>
				</div>
			</DialogContent>
		</Dialog>
	)
}
