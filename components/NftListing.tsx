import dayjs from 'dayjs'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table'
import { useIsDesktop } from '@/lib/hooks/useIsDesktop'
import { useSolanaPrice } from '@/lib/hooks/useSolanaPrice'
import type { NFTListingHistory } from '@/server/app/nft/helper'
import NftListingCard from './cards/NftListingCard'
import { Button } from './ui/button'

interface NftListingProps {
	listings?: NFTListingHistory[]
}

export default function NftListing({ listings = [] }: NftListingProps) {
	const isDesktop = useIsDesktop()

	if (isDesktop) {
		return <ListingTable listings={listings} />
	}
	return <ListingCards listings={listings} />
}

interface ListingTableProps {
	listings: NFTListingHistory[]
}

function ListingTable({ listings }: ListingTableProps) {
	const solToUsd = useSolanaPrice()

	return (
		<div className='hidden lg:block collectionTableWrapper w-full overflow-x-auto'>
			<Table>
				<TableHeader>
					<TableRow className='bg-muted/50'>
						<TableHead className='min-w-[100px]'>Price</TableHead>
						<TableHead className='min-w-[100px]'>USD Price</TableHead>
						<TableHead className='min-w-[50px]'>Type</TableHead>
						<TableHead className='min-w-[140px]'>From</TableHead>
						<TableHead className='min-w-[140px]'>Status</TableHead>
						<TableHead className='min-w-[140px]'>Created</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{listings.length > 0 ? (
						listings.map((listing) => (
							<TableRow
								key={listing.id}
								className='text-sm font-medium h-[70px]'
							>
								<TableCell>{listing.price} SOL</TableCell>
								<TableCell>
									$ {(listing.price * solToUsd).toFixed(2)} USD
								</TableCell>
								<TableCell>
									{listing.listingType === 'FIXED_PRICE'
										? 'Fixed Price'
										: 'Auction'}
								</TableCell>
								<TableCell className='text-primary'>
									{listing.seller.username}
								</TableCell>
								<TableCell>{listing.status}</TableCell>
								<TableCell>
									{dayjs(listing.createdAt).format('MMM DD, YYYY')}
								</TableCell>
							</TableRow>
						))
					) : (
						<TableRow>
							<TableCell
								colSpan={6}
								className='text-center py-8 text-[#7E7E7E]'
							>
								No listing history found for this NFT.
							</TableCell>
						</TableRow>
					)}
				</TableBody>
			</Table>
		</div>
	)
}

interface ListingCardsProps {
	listings: NFTListingHistory[]
}

function ListingCards({ listings }: ListingCardsProps) {
	return (
		<div className='lg:hidden'>
			<span className='text-[10px] font-medium'>Listing Details</span>
			<div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-4'>
				{listings.length > 0 ? (
					listings.map((listing) => (
						<NftListingCard
							key={listing.id}
							price={listing.price}
							listingType={listing.listingType}
							seller={listing.seller.username}
							status={listing.status}
							createdAt={listing.createdAt}
						/>
					))
				) : (
					<div className='col-span-full text-center py-8 text-[#7E7E7E]'>
						No listing history found for this NFT.
					</div>
				)}
			</div>
		</div>
	)
}
