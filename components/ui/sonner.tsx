import { Toaster as Sonner, type ToasterProps } from 'sonner'

const Toaster = ({ ...props }: ToasterProps) => {
	return (
		<Sonner
			className='toaster group'
			style={
				{
					'--normal-bg': 'var(--popover)',
					'--normal-text': 'var(--popover-foreground)',
					'--normal-border': 'var(--border)',
				} as React.CSSProperties
			}
			toastOptions={{
				classNames: {
					toast:
						'bg-black text-sm text-destructive-foreground font-medium min-h-[3rem]',
					title: 'text-black fonte-bold',
					description: 'text-black font-normal',
					actionButton: 'main-btn',
					cancelButton: 'main-btn',
				},
			}}
			{...props}
		/>
	)
}

export { Toaster }
