import * as SwitchPrimitive from '@radix-ui/react-switch'

import { cn } from '@/lib/utils'

function Switch({
	className,
	thumbClassName,
	...props
}: React.ComponentProps<typeof SwitchPrimitive.Root> & {
	thumbClassName?: string
}) {
	return (
		<SwitchPrimitive.Root
			data-slot='switch'
			className={cn(
				'peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex w-[40px] md:w-[80px] py-0.5 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer',
				className,
			)}
			{...props}
		>
			<SwitchPrimitive.Thumb
				data-slot='switch-thumb'
				className={cn(
					'bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 md:size-7 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[20px] md:data-[state=checked]:translate-x-[48px] data-[state=unchecked]:translate-x-0.5',
					thumbClassName,
				)}
			/>
		</SwitchPrimitive.Root>
	)
}

export { Switch }
