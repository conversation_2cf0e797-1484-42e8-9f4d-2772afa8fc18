import { Check, Plus, Search, Tag as TagIcon } from 'lucide-react'
import type { HTMLAttributes, KeyboardEvent } from 'react'
import { useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from './button'
import { Input } from './input'

export interface TagSelectProps extends HTMLAttributes<HTMLDivElement> {
	tags: string[]
	selectedTags: string[]
	onTagSelect: (tag: string) => void
	disabled?: boolean
	error?: boolean
	maxTagsDisplayed?: number
	maxTagsSelectable?: number
}

export function TagSelector({
	tags,
	selectedTags,
	onTagSelect,
	className,
	disabled = false,
	error = false,
	maxTagsDisplayed,
	maxTagsSelectable,
	...props
}: TagSelectProps) {
	const [searchQuery, setSearchQuery] = useState('')
	const inputRef = useRef<HTMLInputElement>(null)

	// Check if tag limit is reached
	const isTagLimitReached =
		maxTagsSelectable !== undefined && selectedTags.length >= maxTagsSelectable

	// Filter tags based on search query
	const filteredTags = tags.filter((tag) =>
		tag.toLowerCase().includes(searchQuery.toLowerCase()),
	)

	const displayTags =
		maxTagsDisplayed && !searchQuery
			? filteredTags.slice(0, maxTagsDisplayed)
			: filteredTags

	const handleContainerClick = () => {
		if (inputRef.current) {
			inputRef.current.focus()
		}
	}

	const handleAddCustomTag = () => {
		if (
			searchQuery.trim() &&
			!tags.includes(searchQuery.trim()) &&
			!selectedTags.includes(searchQuery.trim())
		) {
			// Don't add if max tags limit is reached and the tag is not already selected
			if (isTagLimitReached) return

			onTagSelect(searchQuery.trim())
			setSearchQuery('')
		}
	}

	const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
		if (e.key === 'Enter' && searchQuery.trim()) {
			e.preventDefault()
			handleAddCustomTag()
		}
	}

	// Check if we should show "Add custom tag" option
	const showAddCustom =
		searchQuery.trim() &&
		!tags.includes(searchQuery.trim()) &&
		!selectedTags.includes(searchQuery.trim()) &&
		!isTagLimitReached

	return (
		<div className='space-y-3'>
			<div className='relative flex'>
				<Input
					ref={inputRef}
					type='text'
					placeholder={
						isTagLimitReached
							? `Maximum ${maxTagsSelectable} tags reached`
							: 'Search tags or enter a custom tag...'
					}
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
					onKeyDown={handleKeyDown}
					className='pl-10 flex-1'
					disabled={disabled || isTagLimitReached}
				/>
				<div className='absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none'>
					<Search className='w-4 h-4 text-muted-foreground' />
				</div>
				{showAddCustom && (
					<Button
						type='button'
						size='sm'
						className='ml-2 whitespace-nowrap'
						onClick={handleAddCustomTag}
						disabled={disabled}
					>
						<Plus className='w-4 h-4 mr-1' />
						Add Tag
					</Button>
				)}
			</div>

			<div
				className={cn(
					'flex flex-wrap gap-2 p-3 border border-border rounded-md min-h-[100px] max-h-[250px] overflow-y-auto',
					error ? 'ring-2 ring-destructive' : '',
					disabled ? 'bg-muted opacity-70' : 'bg-card',
					className,
				)}
				onClick={handleContainerClick}
				{...props}
			>
				{selectedTags.length > 0 && (
					<div className='flex flex-wrap gap-2 w-full mb-2 pb-2 border-b border-border'>
						<div className='w-full text-xs text-muted-foreground mb-1'>
							Selected Tags:
						</div>
						{selectedTags.map((tag) => (
							<button
								key={`selected-${tag}`}
								type='button'
								onClick={() => onTagSelect(tag)}
								disabled={disabled}
								aria-pressed={true}
								className={cn(
									'inline-flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-full bg-primary text-primary-foreground',
									disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
								)}
							>
								<Check className='w-3 h-3' />
								{tag}
							</button>
						))}
					</div>
				)}

				{displayTags.length > 0 ? (
					<>
						{selectedTags.length > 0 && (
							<div className='w-full text-xs text-muted-foreground mb-1'>
								Available Tags:
							</div>
						)}
						{displayTags
							.filter((tag) => !selectedTags.includes(tag))
							.map((tag) => (
								<button
									key={`available-${tag}`}
									type='button'
									onClick={() => onTagSelect(tag)}
									disabled={
										disabled ||
										(isTagLimitReached && !selectedTags.includes(tag))
									}
									aria-pressed={false}
									className={cn(
										'inline-flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-full bg-secondary text-secondary-foreground hover:bg-secondary/80',
										disabled ||
											(isTagLimitReached && !selectedTags.includes(tag))
											? 'opacity-50 cursor-not-allowed'
											: 'cursor-pointer',
									)}
								>
									<TagIcon className='w-3 h-3' />
									{tag}
								</button>
							))}
					</>
				) : (
					<div className='flex items-center justify-center w-full h-full text-sm text-muted-foreground'>
						{searchQuery.trim()
							? 'No matching tags found'
							: 'No tags available'}
					</div>
				)}
			</div>

			<div className='flex items-center text-sm text-muted-foreground'>
				<span className='mr-1'>Selected:</span>
				<span className='font-medium'>{selectedTags.length}</span>
				{maxTagsSelectable !== undefined && (
					<span className='ml-1 mr-1'>/ {maxTagsSelectable}</span>
				)}
				<span className='ml-1'>tag{selectedTags.length !== 1 ? 's' : ''}</span>
				{showAddCustom && (
					<span className='ml-2 text-xs text-muted-foreground italic'>
						Press Enter or click Add Tag to add a custom tag
					</span>
				)}
				{isTagLimitReached && (
					<span className='ml-2 text-xs text-destructive'>
						Maximum number of tags reached
					</span>
				)}
			</div>
		</div>
	)
}
