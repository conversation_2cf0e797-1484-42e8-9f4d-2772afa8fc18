'use client'
import { Toaster as Sonner, type ToasterProps } from 'sonner'

const CustomToaster = ({ ...props }: ToasterProps) => {
	return (
		<Sonner
			className='toaster group !bg-black'
			toastOptions={{
				classNames: {
					toast:
						'!bg-background border !border-orange-500 backdrop-blur-md text-sm text-destructive-foreground font-medium min-h-[3rem] shadow-lg shadow-orange-500/50',
					title: '!text-orange-500 font-bold',
					description: 'text-foreground font-normal',
					actionButton: 'main-btn',
					cancelButton: 'main-btn',
				},
			}}
			{...props}
		/>
	)
}

export { CustomToaster }
