import { ImagePlus } from 'lucide-react'
import type { FC } from 'react'
import { useRef, useState } from 'react'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import customToast from '../CustomToast'

interface FileUploadProps {
	id: string
	label: string
	height?: string
	maxSizeMB?: number
	recommendedSize?: string
	objectFit?: 'contain' | 'cover' | 'scale-down'
	acceptTypes?: string
	disabled?: boolean
	onChange?: (file: File) => void
	uploadFileToCloud?: (file: File) => Promise<void>
}

export const FileUpload: FC<FileUploadProps> = ({
	id,
	label,
	onChange,
	height = 'h-72',
	maxSizeMB = 5,
	recommendedSize = '1000 x 1000px',
	objectFit = 'contain',
	acceptTypes = 'image/*',
	disabled = false,
	uploadFileToCloud,
}) => {
	const [imageSrc, setImageSrc] = useState<string | null>(null)
	const [_fileName, setFileName] = useState<string>('')
	const fileInputRef = useRef<HTMLInputElement>(null)

	const handleClick = () => {
		fileInputRef.current?.click()
	}

	const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0]
		if (file) {
			// Check file size
			if (file.size > maxSizeMB * 1024 * 1024) {
				customToast.error(`File size exceeds ${maxSizeMB}MB limit`)
				return
			}
			const imageUrl = URL.createObjectURL(file)
			setImageSrc(imageUrl)
			setFileName(file.name)
			onChange?.(file)
			if (uploadFileToCloud) {
				uploadFileToCloud(file).catch((error) => {
					customToast.error(
						`Failed to upload file: ${error.message || 'Unknown error'}`,
					)
				})
			}

			// const reader = new FileReader()

			// reader.onload = (e) => {
			// 	const result = e.target?.result as string
			// 	if (result) {
			// 		onChange?.(result, file.name)
			// 	}
			// }
			// reader.readAsDataURL(file)
		}
	}

	return (
		<div className='space-y-2'>
			<Label htmlFor={id}>{label}</Label>
			<input
				type='file'
				id={id}
				ref={fileInputRef}
				onChange={handleFileChange}
				className='hidden'
				accept={acceptTypes}
				disabled={disabled}
			/>

			{!imageSrc ? (
				<div className='rounded-lg p-3 bg-input'>
					<div
						onClick={handleClick}
						className={cn(
							'border border-dashed border-white/20 rounded-lg bg-input w-full flex flex-col items-center justify-center space-y-3 cursor-pointer text-left p-6',
							height,
						)}
						aria-label={`Upload ${label.toLowerCase()}`}
						role='button'
					>
						<ImagePlus className='h-12 w-12 bg-white/20 p-3 rounded-full text-foreground' />
						<div className='flex flex-col items-center'>
							<p className='text-center'>
								Click to Upload {label}
								<br />
								<span className='text-sm text-muted-foreground'>
									Recommended Size: {recommendedSize}, Max Size {maxSizeMB} MB
								</span>
							</p>
						</div>
					</div>
				</div>
			) : (
				<div className='relative p-3 bg-input rounded-lg'>
					<div className='w-full border border-dashed border-white/20 rounded-lg'>
						<div
							className={cn(
								'group relative overflow-hidden rounded-lg',
								height,
							)}
						>
							<img
								src={imageSrc}
								alt={`${label} Preview`}
								className={cn(
									'h-full w-full transition-transform duration-300 group-hover:scale-105',
									objectFit === 'contain'
										? 'object-contain'
										: objectFit === 'scale-down'
											? 'object-scale-down'
											: 'object-cover',
								)}
							/>
							<div className='absolute inset-0 bg-black/40 opacity-0 transition-opacity group-hover:opacity-100' />
							<button
								className='blurredBtn absolute bottom-4 right-4'
								onClick={handleClick}
							>
								<ImagePlus className='w-4 lg:w-6 h-auto' />
							</button>
						</div>
					</div>
					{_fileName && (
						<div className='mt-2 flex items-center gap-2 text-sm text-muted-foreground'>
							<span className='truncate'>{_fileName}</span>
						</div>
					)}
				</div>
			)}
		</div>
	)
}
