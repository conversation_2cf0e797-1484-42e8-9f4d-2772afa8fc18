import { PlusCircle, Trash2 } from 'lucide-react'
import type { FC } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export interface Attribute {
	id: string
	traitType: string
	value: string
}

interface AttributeEditorProps {
	attributes: Attribute[]
	onAddAttribute: () => void
	onRemoveAttribute: (id: string) => void
	disabled?: boolean
	onUpdateAttribute: (
		id: string,
		field: 'traitType' | 'value',
		value: string,
	) => void
}

export const AttributeEditor: FC<AttributeEditorProps> = ({
	attributes,
	onAddAttribute,
	onRemoveAttribute,
	onUpdateAttribute,
	disabled = false,
}) => {
	return (
		<div className='space-y-3'>
			<div className='flex justify-between items-center'>
				<Label>Attributes</Label>
				<Button
					disabled={disabled}
					type='button'
					variant='outline'
					size='sm'
					onClick={onAddAttribute}
					className='flex items-center gap-1 !text-xs'
				>
					<PlusCircle className='h-3 w-3' /> Add Attributes
				</Button>
			</div>

			{attributes.length === 0 ? (
				<div className='text-sm text-muted-foreground italic'>
					No attributes added yet
				</div>
			) : (
				<div className='space-y-3'>
					{attributes.map((attr) => (
						<div key={attr.id} className='flex items-start gap-3'>
							<div className='flex-1'>
								<Input
									disabled={disabled}
									value={attr.traitType}
									onChange={(e) =>
										onUpdateAttribute(attr.id, 'traitType', e.target.value)
									}
									placeholder='Trait Type (e.g. Color)'
									className='text-sm'
								/>
							</div>
							<div className='flex-1'>
								<Input
									disabled={disabled}
									value={attr.value}
									onChange={(e) =>
										onUpdateAttribute(attr.id, 'value', e.target.value)
									}
									placeholder='Value (e.g. Blue)'
									className='text-sm'
								/>
							</div>
							<div>
								<Button
									type='button'
									variant='ghost'
									disabled={disabled}
									size='icon'
									onClick={() => onRemoveAttribute(attr.id)}
									className='h-10 w-10 text-muted-foreground hover:text-destructive'
								>
									<Trash2 className='h-4 w-4' />
								</Button>
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	)
}
