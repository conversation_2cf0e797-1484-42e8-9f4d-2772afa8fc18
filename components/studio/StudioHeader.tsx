import type { <PERSON> } from 'react'

interface StudioHeaderProps {
	title: string
	description: string
}

export const StudioHeader: FC<StudioHeaderProps> = ({ title, description }) => {
	return (
		<>
			{/* Create header */}
			<div className='mb-6 md:mb-8'>
				<h1 className='text-2xl md:text-3xl font-semibold tracking-tight'>
					{title}
				</h1>
				<p className='text-sm text-muted-foreground mt-1'>{description}</p>
			</div>
		</>
	)
}
