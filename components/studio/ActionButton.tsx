import { ArrowLeft } from 'lucide-react'
import type { FC, ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface ActionButtonProps {
	icon: ReactNode
	title: string
	description: string
	disabled?: boolean
	onClick: () => void
}

export const ActionButton: FC<ActionButtonProps> = ({
	icon,
	title,
	description,
	onClick,
}) => {
	return (
		<button
			type='button'
			className={cn(
				'rounded-lg p-4 md:p-6 flex items-center gap-4 md:gap-6 cursor-pointer',
				'bg-border hover:bg-border/80 focus:ring-2 focus:ring-primary/20 focus:outline-none',
				'transition-all duration-300 w-full text-left transform hover:translate-y-[-2px]',
			)}
			onClick={onClick}
			aria-label={title}
		>
			{icon}
			<div className='flex-grow'>
				<h3 className='font-semibold text-base md:text-lg'>{title}</h3>
				<p className='text-muted-foreground text-xs md:text-sm'>
					{description}
				</p>
			</div>
			<div className='flex-shrink-0 transition-transform duration-300'>
				<ArrowLeft className='h-4 w-4 md:h-5 md:w-5 transform rotate-180' />
			</div>
		</button>
	)
}
