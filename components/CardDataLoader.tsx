import { LoaderCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import Brand<PERSON>ogo from '@/public/assets/Logo/logo-white.svg?react'

export default function CardDataLoader({ className }: { className?: string }) {
	return (
		<div className={cn('flex-center bg-black/20', className)}>
			<div className='relative'>
				<LoaderCircle className='size-[70px] lg:size-[100px] animate-spin stroke-1 text-white/80' />
				<BrandLogo className='w-[25px] h-auto lg:w-[45px] text-white absolute inset-1/2 -translate-1/2 -translate-y-1/2' />
			</div>
		</div>
	)
}
