'use client'
import { cn } from '@/lib/utils'

export default function SkeletonEditProfileForm() {
	return (
		<div className='containerPadding pb-20 pt-4 animate-pulse'>
			{/* Breadcrumb */}
			<div className='h-6 w-48 bg-gray-200/20 rounded-full' />

			{/* Profile Picture Section */}
			<div className='pt-[3.5rem] lg:pt-[7rem]'>
				<div className='relative flex flex-col items-center gap-y-3'>
					<div className='h-28 w-28 rounded-full bg-gray-200/20' />
					<div className='h-8 w-32 bg-gray-200/20 rounded-[10px]' />
				</div>
			</div>

			{/* User Info Section */}
			<div className='flex flex-col items-center gap-y-3 pt-4'>
				<div className='h-5 w-36 bg-gray-200/20 rounded-full' />
				<div className='h-4 w-28 bg-gray-200/20 rounded-full' />
			</div>

			{/* Form Section */}
			<div className='space-y-8 mt-7 mx-auto max-w-4xl'>
				<div>
					{/* Basic Information Header */}
					<div className='h-7 w-48 bg-gray-200/20 rounded-full' />
					<div className='w-full h-1 bg-gray-200/20 mt-3 mb-8 rounded-full' />

					{/* Form Fields Grid */}
					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div className='space-y-2'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-10 w-full bg-gray-200/20 rounded-[10px]' />
						</div>
						<div className='space-y-2'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-10 w-full bg-gray-200/20 rounded-[10px]' />
						</div>
						<div className='space-y-2'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-10 w-full bg-gray-200/20 rounded-[10px]' />
						</div>
						<div className='space-y-2'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-10 w-full bg-gray-200/20 rounded-[10px]' />
						</div>
					</div>

					{/* Bio Field */}
					<div className='mt-6 space-y-2'>
						<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
						<div className='h-24 w-full bg-gray-200/20 rounded-[10px]' />
					</div>

					{/* Social Media Header */}
					<div className='mt-12 h-7 w-48 bg-gray-200/20 rounded-full' />
					<div className='w-full h-1 bg-gray-200/20 mt-3 mb-8 rounded-full' />

					{/* Social Media Fields Grid */}
					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div className='space-y-2'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-10 w-full bg-gray-200/20 rounded-[10px]' />
						</div>
						<div className='space-y-2'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-10 w-full bg-gray-200/20 rounded-[10px]' />
						</div>
						<div className='space-y-2'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-10 w-full bg-gray-200/20 rounded-[10px]' />
						</div>
						<div className='space-y-2'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-10 w-full bg-gray-200/20 rounded-[10px]' />
						</div>
					</div>

					{/* Submit Button */}
					<div className='flex justify-center pt-4'>
						<div className='h-10 w-48 bg-gray-200/20 rounded-[10px]' />
					</div>
				</div>
			</div>
		</div>
	)
}
