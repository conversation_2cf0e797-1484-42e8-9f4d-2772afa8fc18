import {
	ArrowLeftRight,
	Gavel,
	HandCoins,
	ShoppingCart,
	Tag,
	Trash2,
	User,
} from 'lucide-react'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table'
import { useIsDesktop } from '@/lib/hooks/useIsDesktop'
import { useSolanaPrice } from '@/lib/hooks/useSolanaPrice'
import type { NFTActivity } from '@/pages/nft-details/Page.telefunc'
import NftActivityCard from './cards/NftActivityCard'

interface NftActivitiesProps {
	activities?: NFTActivity[]
}

export default function NftActivities({ activities = [] }: NftActivitiesProps) {
	const isDesktop = useIsDesktop()

	if (isDesktop) {
		return <ActivitiesTable activities={activities} />
	}
	return <ActivitiesCards activities={activities} />
}

interface ActivitiesTableProps {
	activities: NFTActivity[]
}

function ActivitiesTable({ activities }: ActivitiesTableProps) {
	const solToUsd = useSolanaPrice()

	// Helper function to format SOL amount
	const formatSolAmount = (amount: number | null | undefined): number => {
		if (!amount || amount < 0.000001) return 0
		return Number(amount.toFixed(4))
	}

	// Format date to a more readable format
	const formatDate = (dateString: string) => {
		const date = new Date(dateString)
		// If less than 24 hours ago, show relative time
		const now = new Date()
		const diffMs = now.getTime() - date.getTime()
		const diffHours = diffMs / (1000 * 60 * 60)

		if (diffHours < 24) {
			if (diffHours < 1) {
				const diffMinutes = Math.floor(diffMs / (1000 * 60))
				return `${diffMinutes} min ago`
			}
			return `${Math.floor(diffHours)} hours ago`
		}

		// Otherwise show the date
		return date.toLocaleDateString()
	}

	// Get icon based on activity type
	const getActivityIcon = (type: string) => {
		switch (type) {
			case 'NFT_LISTED':
				return <Tag className='w-5 h-auto' />
			case 'NFT_PRICE_UPDATED':
				return <Tag className='w-5 h-auto' />
			case 'NFT_TRANSFERRED':
				return <ArrowLeftRight className='w-5 h-auto' />
			case 'NFT_BID_PLACED':
				return <Gavel className='w-5 h-auto' />
			case 'NFT_LISTING_CANCELLED':
				return <Trash2 className='w-5 h-auto' />
			case 'NFT_OFFER_CREATED':
				return <HandCoins className='w-5 h-auto' />
			case 'NFT_OFFER_ACCEPTED':
				return <HandCoins className='w-5 h-auto' />
			case 'NFT_OFFER_CANCELLED':
				return <Trash2 className='w-5 h-auto' />
			case 'ACCOUNT_CREATED':
				return <User className='w-5 h-auto' />
			case 'NFT_MINTED':
				return <ShoppingCart className='w-5 h-auto' />
			case 'COLLECTION_CREATED':
				return <ShoppingCart className='w-5 h-auto' />
			case 'METADATA_UPDATED':
				return <ShoppingCart className='w-5 h-auto' />
			default:
				return <ShoppingCart className='w-5 h-auto' />
		}
	}

	// Format activity type for display with more meaningful descriptions
	const formatActivityType = (type: string) => {
		switch (type) {
			case 'NFT_MINTED':
				return 'Minted NFT'
			case 'NFT_LISTED':
				return 'Listed for Sale'
			case 'NFT_PRICE_UPDATED':
				return 'Updated Price'
			case 'NFT_TRANSFERRED':
				return 'Transferred NFT'
			case 'COLLECTION_CREATED':
				return 'Created Collection'
			case 'METADATA_UPDATED':
				return 'Updated Metadata'
			case 'NFT_BID_PLACED':
				return 'Placed Bid'
			case 'NFT_LISTING_CANCELLED':
				return 'Cancelled Listing'
			case 'NFT_OFFER_CREATED':
				return 'Made Offer'
			case 'NFT_OFFER_ACCEPTED':
				return 'Accepted Offer'
			case 'NFT_OFFER_CANCELLED':
				return 'Cancelled Offer'
			case 'ACCOUNT_CREATED':
				return 'Created Account'
			default:
				return type
					.split('_')
					.map((word) => word.charAt(0) + word.slice(1).toLowerCase())
					.join(' ')
		}
	}

	return (
		<div className='hidden lg:block collectionTableWrapper w-full overflow-x-auto'>
			<Table>
				<TableHeader>
					<TableRow className='bg-muted/50'>
						<TableHead className='min-w-[190px]'>Activity</TableHead>
						<TableHead className='min-w-[100px]'>Amount</TableHead>
						<TableHead className='min-w-[140px]'>User</TableHead>
						<TableHead className='min-w-[140px]'>Recipient</TableHead>
						<TableHead className='min-w-[140px]'>When</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{activities.length > 0 ? (
						activities.map((item) => (
							<TableRow key={item.id} className='text-sm font-medium h-[70px]'>
								<TableCell>
									<div className='flex items-center gap-x-2'>
										{getActivityIcon(item.type)}
										{formatActivityType(item.type)}
									</div>
								</TableCell>
								<TableCell>
									{item.price ? (
										<div className='flex flex-col'>
											<span>{formatSolAmount(item.price.sol)} SOL</span>
											<span className='text-sm font-normal text-[#7E7E7E]'>
												$
												{Number(
													(formatSolAmount(item.price.sol) * solToUsd).toFixed(
														2,
													),
												)}{' '}
												USD
											</span>
										</div>
									) : (
										<span>-</span>
									)}
								</TableCell>
								<TableCell className='text-primary'>
									{item.from ? item.from.username : '-'}
								</TableCell>
								<TableCell className='text-primary'>
									{item.to ? item.to.username : '-'}
								</TableCell>
								<TableCell>{formatDate(item.date)}</TableCell>
							</TableRow>
						))
					) : (
						<TableRow>
							<TableCell
								colSpan={5}
								className='text-center py-8 text-[#7E7E7E]'
							>
								No activity history found for this NFT yet.
							</TableCell>
						</TableRow>
					)}
				</TableBody>
			</Table>
		</div>
	)
}

interface ActivitiesCardsProps {
	activities: NFTActivity[]
}

function ActivitiesCards({ activities }: ActivitiesCardsProps) {
	return (
		<div className='lg:hidden mt-6'>
			<div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-4'>
				{activities.length > 0 ? (
					activities
						.slice(0, 6)
						.map((item) => <NftActivityCard key={item.id} activity={item} />)
				) : (
					<div className='col-span-full text-center py-8 text-[#7E7E7E]'>
						No activity history found for this NFT yet.
					</div>
				)}
			</div>
		</div>
	)
}
