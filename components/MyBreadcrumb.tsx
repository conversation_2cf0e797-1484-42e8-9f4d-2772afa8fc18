import React from 'react'
import {
	B<PERSON><PERSON>rumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { cn } from '@/lib/utils'

export type MyBreadcrumbProps = {
	items: Array<{ id: number; url?: string; title: string }>
	className?: string
}

export default function MyBreadcrumb({ items, className }: MyBreadcrumbProps) {
	return (
		<Breadcrumb
			className={cn(
				'bg-white/10 backdrop-blur-[10px] px-4 lg:px-5 py-1 lg:py-2 rounded-[8px] overflow-x-auto',
				className,
			)}
		>
			<BreadcrumbList>
				{items.map((item, index) => (
					<React.Fragment key={`breadcrumb-item-${item.id}`}>
						<BreadcrumbItem>
							{item.url ? (
								<BreadcrumbLink className='text-white' href={item.url}>
									{item.title}
								</BreadcrumbLink>
							) : (
								<BreadcrumbPage
									className={
										index === items.length - 1 ? 'text-muted-foreground' : ''
									}
								>
									{item.title}
								</BreadcrumbPage>
							)}
						</BreadcrumbItem>
						{items.length > 1 && index !== items.length - 1 && (
							<BreadcrumbSeparator className='text-white' />
						)}
					</React.Fragment>
				))}
			</BreadcrumbList>
		</Breadcrumb>
	)
}
