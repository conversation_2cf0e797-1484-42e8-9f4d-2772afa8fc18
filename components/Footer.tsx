import { useState } from 'react'
import { usePageContext } from 'vike-react/usePageContext'
import { useIsDesktop } from '@/lib/hooks/useIsDesktop'
import BrandLogo from '@/public/assets/Logo/logo-white.svg?react'
import FooterLinksByCategory from './atoms/FooterLinks'
import { Button } from './ui/button'
import { Input } from './ui/input'

export default function Footer() {
	const [activeId, setActiveId] = useState(0)
	const isDesktop = useIsDesktop()
	const urlPathname = usePageContext().urlPathname
	if (urlPathname === '/studio') return null
	return (
		<footer className='containerPadding pt-14 pb-10'>
			<div className='w-full flex flex-col lg:flex-row lg:gap-x-10 lg:items-start lg:justify-between py-10 border-y-[1.5px] gradientBorder'>
				<div className='flex flex-col lg:items-start items-center'>
					<BrandLogo className='w-[87px] h-auto lg:w-[60px] text-white' />
					<span className='text-sm lg:text-2xl font-semibold mt-4'>
						Join our community
					</span>
					<p className='text-xs lg:text-base font-light max-w-[80%] text-center lg:text-left lg:max-w-[448px] mt-3 lg:mt-1'>
						Be part of the Shogunex Fusion community! Connect with creators,
						collectors, and NFT enthusiasts to stay ahead in the world of
						digital assets.
					</p>
					{/* <div className="mt-2 flex items-center gap-x-3">
          </div> */}
				</div>
				<div className='w-full flex flex-col lg:flex-row items-center lg:justify-evenly lg:items-start gap-y-7 lg:gap-y-3 gap-x-10 mt-10 lg:mt-0 h-full'>
					{/* <FooterLinksByCategory
						category='Categories'
						id={1}
						isOpen={activeId === 1 || isDesktop}
						onClick={() => setActiveId(activeId === 1 ? 0 : 1)}
						links={[
							{ link: '/marketplace?category=art', title: 'Art' },
							{ link: '/marketplace?category=video', title: 'Video' },
							{ link: '/marketplace?category=music', title: 'Music' },
							{
								link: '/marketplace?category=entertainment',
								title: 'Entertainment',
							},
							{ link: '/marketplace?category=fashion', title: 'Fashion' },
						]}
						key={1}
					/> */}
					<FooterLinksByCategory
						category='Marketplace'
						id={2}
						isOpen={activeId === 2 || isDesktop}
						onClick={() => setActiveId(activeId === 2 ? 0 : 2)}
						links={[
							{ link: '/marketplace?category=Create', title: 'Create' },
							{ link: '/marketplace?category=Explore', title: 'Explore' },
							{ link: '/marketplace?category=music', title: 'Creators' },
							// {
							// 	link: '/marketplace?category=entertainment',
							// 	title: 'Collaborators',
							// },
						]}
						key={2}
					/>
					<FooterLinksByCategory
						category='More'
						id={3}
						isOpen={activeId === 3 || isDesktop}
						onClick={() => setActiveId(activeId === 3 ? 0 : 3)}
						links={[
							{ link: '/blog', title: 'Blog' },
							{ link: '/careers', title: 'Careers' },
							{ link: '/faq', title: 'FAQ' },
							{ link: '/help-center', title: 'Help Center' },
						]}
						key={3}
					/>
					<FooterLinksByCategory
						category='Company'
						id={4}
						isOpen={activeId === 4 || isDesktop}
						onClick={() => setActiveId(activeId === 4 ? 0 : 4)}
						links={[
							{ link: '/about-us', title: 'About Us' },
							{ link: '/terms-and-conditions', title: 'Terms and Conditions' },
							{ link: '/privacy-policy', title: 'Privacy Policy' },
							{ link: '/community-guidelines', title: 'Community Guidelines' },
						]}
						key={4}
					/>
				</div>
			</div>
			<span className='text-xs lg:text-base text-[#7B7B7B] block mt-7 text-center'>
				© 2025 - Shogunex Fusion . All Rights Received
			</span>
		</footer>
	)
}
