import { Image } from '@unpic/react'
import { motion } from 'framer-motion'
import { Menu } from 'lucide-react'
import { useEffect, useMemo, useState } from 'react'
import { useIntersectionObserver } from 'usehooks-ts'
import { usePageContext } from 'vike-react/usePageContext'
import {
	buttonVariants,
	headerVariants,
	heroImageVariants,
	linkVariants,
	logoVariants,
	mobileMenuVariants,
} from '@/lib/animation/animationVariants'
import { useBannerUrl } from '@/lib/providers/BannerProvider'
import { cn } from '@/lib/utils'
import BrandLogo from '@/public/assets/svg/logo-white.svg?react'
import { Link } from './atoms/Link'
import { LoginButton } from './login/LoginButton'
import MobileSidebar from './MobileSidebar'

const NavLinks: Array<{ id: number; link: string; title: string }> = [
	{ id: 1, link: '/explore', title: 'Explore' },
	{ id: 2, link: '/studio', title: 'Create' },
	{ id: 3, link: '/about', title: 'About' },
]

export default function Header() {
	const [isScrolled, setIsScrolled] = useState(false)
	const { isIntersecting, ref } = useIntersectionObserver({ threshold: 0.7 })
	const { bannerUrl } = useBannerUrl()

	useEffect(() => {
		if (isIntersecting) {
			setIsScrolled(false)
		} else {
			setIsScrolled(true)
		}
	}, [isIntersecting])

	const urlPathname = usePageContext().urlPathname

	const shouldShowSpacer = urlPathname !== '/'
	const isProfilePage = urlPathname.startsWith('/profile')
	const isExplorePage = urlPathname.startsWith('/explore')
	const isCollectionPage = urlPathname.startsWith('/collection/')
	const shouldShowHeroImage = useMemo(
		() =>
			shouldShowSpacer &&
			urlPathname !== '/list-for-sale' &&
			!urlPathname.startsWith('/nft-details') &&
			!urlPathname.startsWith('/studio'),
		[shouldShowSpacer, urlPathname],
	)

	if (urlPathname === '/studio') return null

	return (
		<>
			<motion.header
				className={cn(
					'fixed top-0 left-0 right-0 pb-4 pt-6 lg:pt-7 bg-transparent z-50 transition-colors',
					isScrolled && 'bg-black/50 backdrop-blur-sm',
				)}
				variants={headerVariants}
				initial='hidden'
				animate='visible'
			>
				<nav className='containerPadding flex justify-between items-center'>
					<Link href='/' className='hidden md:flex'>
						<motion.div
							className='flex items-center gap-x-2 lg:gap-x-4'
							variants={logoVariants}
							initial='initial'
							animate='animate'
							whileHover='hover'
						>
							<BrandLogo className='w-10 h-auto lg:w-[50px] transition-transform text-[#DD0003]' />

							<div className='text-2xl lg:text-3xl font-squada'>
								<span className='text-white'>SHOGUN</span>
								<span className='text-[#FF0000] ml-1.5'>FUSION</span>
							</div>
						</motion.div>
					</Link>
					<div className='hidden md:flex gap-x-6 items-center'>
						{NavLinks.map((item, index) => (
							<motion.div
								key={item.id}
								variants={linkVariants}
								initial='hidden'
								animate='visible'
								custom={index}
								whileHover='hover'
								className='font-medium relative'
							>
								<Link href={item.link} className='relative'>
									{item.title}
									<motion.span
										className='absolute -bottom-1 left-0 w-0 h-0.5 bg-rose-500 origin-left'
										initial={{ width: 0 }}
										whileHover={{
											width: '100%',
											transition: { duration: 0.2 },
										}}
									/>
								</Link>
							</motion.div>
						))}
					</div>
					<motion.div
						variants={mobileMenuVariants}
						initial='hidden'
						animate='visible'
						className='md:hidden'
					>
						<MobileSidebar>
							<Menu className='w-6 h-auto' />
						</MobileSidebar>
					</motion.div>
					<motion.div
						variants={buttonVariants}
						whileHover='hover'
						whileTap='tap'
					>
						<LoginButton />
					</motion.div>
				</nav>
			</motion.header>
			<div
				ref={ref}
				className='absolute top-[1px] left-0 right-0 headerPlaceholder'
			/>
			{shouldShowSpacer && <div className='w-full h-[70px] lg:h-[89px]' />}

			{shouldShowHeroImage && (
				<motion.div
					variants={heroImageVariants}
					initial='initial'
					animate='animate'
				>
					{/* {isProfilePage && ( */}
					<div
						className={cn(
							'w-full h-[212px] lg:h-[300px]  absolute left-0 right-0 top-0 z-[-1]',
							(isProfilePage || isExplorePage) && 'h-[520px] lg:h-[442px]',
							isCollectionPage ? 'headerGradientForCollection' : 'bg-black/60',
						)}
					/>
					{/* )} */}
					<Image
						height={442}
						aspectRatio={16 / 9}
						alt='hero-bg'
						layout='fixed'
						background='auto'
						className={cn(
							'h-[211px] lg:h-[300px] object-center object-cover absolute top-0 left-0 right-0 w-full z-[-2] select-none',
							(isProfilePage || isExplorePage) && 'lg:h-[442px]',
							isProfilePage && 'h-[520px]',
						)}
						loading='eager'
						sizes='(max-width: 768px) 0vw, 100vw'
						fetchPriority='high'
						src={
							isExplorePage
								? '/assets/img/explore-banner.png'
								: bannerUrl || '/assets/img/banner.png'
						}
					/>
				</motion.div>
			)}
		</>
	)
}
