'use client'
import { Check, <PERSON>Alert, X } from 'lucide-react'
import { toast as sonnerToast, type ToasterProps } from 'sonner'
import { cn } from '@/lib/utils'

type ToastProps = ToasterProps & {
	variant?: 'default' | 'success' | 'error' | 'warning'
	id: string | number
}

export default function customToast(
	text: string,
	toast?: Omit<ToastProps, 'id'>,
) {
	return sonnerToast.custom(
		(id) => <Toast id={id} variant={toast?.variant || 'default'} text={text} />,
		{ duration: toast?.duration || 3000, ...toast },
	)
}

customToast.error = (text: string, toast?: Omit<ToastProps, 'id'>) => {
	return customToast(text, { variant: 'error', ...toast })
}
customToast.success = (text: string, toast?: Omit<ToastProps, 'id'>) => {
	return customToast(text, { variant: 'success', ...toast })
}
customToast.warning = (text: string, toast?: Omit<ToastProps, 'id'>) => {
	return customToast(text, { variant: 'warning', ...toast })
}

/** A fully custom toast that still maintains the animations and interactions. */
function Toast({
	id,
	variant,
	text,
}: {
	id: string | number
	variant: ToastProps['variant']
	text: string
}) {
	return (
		<div className='relative w-full md:max-w-[600px] min-w-[300px] flex rounded-[10px] bg-card items-center p-4 cursor-pointer'>
			<div
				className={cn(
					'w-[30px] h-[30px] flex-shrink-0 flex-center rounded-full',
					variant === 'default'
						? 'bg-[#1a6af3]'
						: variant === 'error'
							? 'bg-red-500'
							: variant === 'success'
								? 'bg-[#0DFF00]'
								: 'bg-[#D8AD00]',
				)}
			>
				{variant === 'default' ? (
					<span className='text-black text-base font-medium'>i</span>
				) : variant === 'error' ? (
					<X className='text-black w-4 lg:w-5 h-auto' />
				) : variant === 'success' ? (
					<Check className='text-black w-4 lg:w-5 h-auto' />
				) : (
					<TriangleAlert className='text-black w-4 lg:w-5 h-auto' />
				)}
			</div>
			<div className='w-full ml-4 grow'>
				<p className='text-sm lg:text-base text-white leading-tight'>{text}</p>
			</div>
			<button
				onClick={() => {
					sonnerToast.dismiss(id)
				}}
				className='absolute -top-3 -right-3 flex-center shrink-0 bg-primary hover:bg-primary/80 w-[30px] h-[30px] rounded-full cursor-pointer'
			>
				<X className='text-black w-4 lg:w-5 h-auto' />
			</button>
		</div>
	)
}
