import { cn } from '@/lib/utils'

export default function SkeletonNFTDetails() {
	return (
		<div className='containerPadding pt-4 animate-pulse'>
			{/* Skeleton for Breadcrumb */}
			<div className='flex items-center gap-2 mb-4'>
				<div className='h-4 w-20 bg-gray-200/20 rounded-full' />
				<div className='h-4 w-4 bg-gray-200/20 rounded-full' />
				<div className='h-4 w-32 bg-gray-200/20 rounded-full' />
			</div>

			{/* Skeleton for NFT Details Banner */}
			<div className='grid grid-cols-1 lg:grid-cols-[clamp(0px,45%,587px)_1fr] gap-x-10 mt-10'>
				{/* Image Skeleton */}
				<div className='w-full aspect-square bg-gray-200/20 rounded-[10px] mx-auto' />

				{/* Content Skeleton */}
				<div className='mt-7 lg:mt-0'>
					{/* Collection Name */}
					<div className='flex gap-x-2'>
						<div className='h-6 w-40 bg-gray-200/20 rounded-full' />
						<div className='h-5 w-5 bg-gray-200/20 rounded-full' />
					</div>

					{/* NFT Name and Buttons */}
					<div className='flex items-center justify-between mt-2'>
						<div className='h-8 w-64 bg-gray-200/20 rounded-full' />
						<div className='flex items-center gap-x-3'>
							<div className='h-8 w-8 bg-gray-200/20 rounded-full' />
							<div className='h-8 w-8 bg-gray-200/20 rounded-full' />
							<div className='h-8 w-8 bg-gray-200/20 rounded-full' />
							<div className='h-8 w-8 bg-gray-200/20 rounded-full' />
						</div>
					</div>

					{/* Description */}
					<div className='mt-4 lg:mt-7 space-y-2'>
						<div className='h-4 w-full bg-gray-200/20 rounded-full' />
						<div className='h-4 w-3/4 bg-gray-200/20 rounded-full' />
						<div className='h-4 w-1/2 bg-gray-200/20 rounded-full' />
					</div>

					{/* Stats Grid */}
					<div className='py-4 bg-container grid grid-cols-2 lg:grid-cols-4 gap-y-7 mt-4 lg:mt-10 rounded-[10px]'>
						<div className='h-4 w-16 bg-gray-200/20 rounded-full mx-auto' />
						<div className='h-4 w-16 bg-gray-200/20 rounded-full mx-auto' />
						<div className='h-4 w-16 bg-gray-200/20 rounded-full mx-auto' />
						<div className='h-4 w-16 bg-gray-200/20 rounded-full mx-auto' />
					</div>

					{/* Price */}
					<div className='mt-5'>
						<div className='h-4 w-20 bg-gray-200/20 rounded-full' />
						<div className='flex items-center gap-2 mt-2'>
							<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-4 w-32 bg-gray-200/20 rounded-full' />
						</div>
					</div>

					{/* Buttons */}
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-y-3 lg:gap-x-5 mt-8'>
						<div className='h-12 w-full bg-gray-200/20 rounded-[10px]' />
						<div className='h-12 w-full bg-gray-200/20 rounded-[10px]' />
					</div>
				</div>
			</div>

			{/* Skeleton for Tabs */}
			<div className='mt-10 w-full flex gap-x-5 lg:gap-x-10 py-4 border-b-[1.5px]'>
				<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
				<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
				<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
				<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
				<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
			</div>

			{/* Skeleton for Tab Content */}
			<div className='mt-7'>
				{/* Owned By */}
				<div className='flex gap-x-2 items-center'>
					<div className='h-4 w-20 bg-gray-200/20 rounded-full' />
					<div className='h-4 w-32 bg-gray-200/20 rounded-full' />
					<div className='h-5 w-5 bg-gray-200/20 rounded-full' />
				</div>

				{/* Description */}
				<div className='mt-2 space-y-2'>
					<div className='h-4 w-full bg-gray-200/20 rounded-full' />
					<div className='h-4 w-3/4 bg-gray-200/20 rounded-full' />
				</div>

				{/* Traits */}
				<div className='mt-7'>
					<div className='h-6 w-32 bg-gray-200/20 rounded-full' />
					<div className='grid grid-cols-3 gap-4 mt-4'>
						<div className='h-24 bg-gray-200/20 rounded-[10px]' />
						<div className='h-24 bg-gray-200/20 rounded-[10px]' />
						<div className='h-24 bg-gray-200/20 rounded-[10px]' />
					</div>
				</div>

				{/* Collection Info */}
				<div className='mt-7'>
					<div className='h-6 w-48 bg-gray-200/20 rounded-full' />
					<div className='flex gap-x-5 mt-4'>
						<div className='w-14 h-14 bg-gray-200/20 rounded-full' />
						<div className='space-y-2 flex-1'>
							<div className='h-4 w-full bg-gray-200/20 rounded-full' />
							<div className='h-4 w-3/4 bg-gray-200/20 rounded-full' />
						</div>
					</div>
				</div>

				{/* Details Table */}
				<div className='mt-7'>
					<div className='h-6 w-32 bg-gray-200/20 rounded-full' />
					<div className='mt-4 space-y-3'>
						<div className='flex gap-10'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-4 w-48 bg-gray-200/20 rounded-full' />
						</div>
						<div className='flex gap-10'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-4 w-48 bg-gray-200/20 rounded-full' />
						</div>
						<div className='flex gap-10'>
							<div className='h-4 w-24 bg-gray-200/20 rounded-full' />
							<div className='h-4 w-48 bg-gray-200/20 rounded-full' />
						</div>
					</div>
				</div>
			</div>

			{/* Skeleton for Related NFTs */}
			<div className='mt-10'>
				<div className='flex items-center justify-between'>
					<div className='h-6 w-48 bg-gray-200/20 rounded-full' />
					<div className='h-8 w-32 bg-gray-200/20 rounded-full' />
				</div>
				<div className='grid grid-cols-1 sm:grid-cols-2 gap-5 mt-4'>
					<div className='h-96 bg-gray-200/20 rounded-[20px]' />
					<div className='h-96 bg-gray-200/20 rounded-[20px]' />
				</div>
			</div>
		</div>
	)
}
