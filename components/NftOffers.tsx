import { publicKey, transactionBuilder } from '@metaplex-foundation/umi'
import { useWallet } from '@solana/wallet-adapter-react'
import dayjs from 'dayjs'
import { useContext, useState } from 'react'
import { toast } from 'sonner'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table'
import { acceptOffer } from '@/lib/generated'
import { useIsDesktop } from '@/lib/hooks/useIsDesktop'
import { useSolanaPrice } from '@/lib/hooks/useSolanaPrice'
import { UmiContext } from '@/lib/umi'
import type { NFTOffer } from '@/server/app/nft/helper'

// Extend NFTOffer type to include escrow property
type ExtendedNFTOffer = NFTOffer & {
	escrow?: string
}

import type { OfferMadeType, OffersReceivedType } from '@/types/user.types'
import CountdownTimer from './atoms/CountdownTimer'
import customToast from './CustomToast'
import NftOfferCard from './cards/NftOfferCard'
import { Button } from './ui/button'

interface NftOffersProps {
	offers?: ExtendedNFTOffer[]
	isOwner: boolean
	nftPublicKey: string
	refreshData: () => void
}

export default function NftOffers({
	offers = [],
	isOwner = false,
	nftPublicKey,
	refreshData,
}: NftOffersProps) {
	const isDesktop = useIsDesktop()

	if (isDesktop) {
		return (
			<OffersTable
				offers={offers}
				isOwner={isOwner}
				nftPublicKey={nftPublicKey}
				refreshData={refreshData}
			/>
		)
	}
	return (
		<OffersCards
			offers={offers}
			isOwner={isOwner}
			nftPublicKey={nftPublicKey}
			refreshData={refreshData}
		/>
	)
}

interface OffersTableProps {
	offers: ExtendedNFTOffer[]
	isOwner: boolean
	nftPublicKey: string
	refreshData: () => void
}

function OffersTable({
	offers,
	isOwner,
	nftPublicKey,
	refreshData,
}: OffersTableProps) {
	console.table({
		offers,
		isOwner,
		nftPublicKey,
	})
	const solToUsd = useSolanaPrice()
	const umi = useContext(UmiContext)
	const wallet = useWallet()
	const [loading, setLoading] = useState<string | null>(null)

	// Function to handle accepting an offer
	const handleAcceptOffer = async (offer: NFTOffer) => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction || !nftPublicKey) {
			customToast.error('Wallet not connected.')
			return
		}

		setLoading(offer.id)

		try {
			await transactionBuilder()
				.add(
					acceptOffer(umi, {
						asset: publicKey(nftPublicKey),
						buyer: publicKey(offer.buyer.publicKey),
						offer: publicKey(offer.publicKey),
						escrow: publicKey(offer.escrow),
						seller: umi.identity,
						feeCollector: publicKey(
							'ArGtRWrGMMHoz6ATpuWYzrwNEjMd9bHfCHRcwMNfjZH6',
						),
					}),
				)
				.addRemainingAccounts([
					{
						pubkey: publicKey(offer.creator.publicKey),
						isWritable: false,
						isSigner: false,
					},
				])
				.sendAndConfirm(umi, {
					send: {
						skipPreflight: true,
					},
				})
				.catch((err) => {
					console.error('Failed to accept offer:', err)
					throw err
				})
			customToast.success('Offer Accepted Successfully')

			if (refreshData) {
				refreshData()
			}
		} catch (err: unknown) {
			console.error('Failed to accept offer:', err)
			customToast.error(
				`Accept offer failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(null)
		}
	}

	// Convert string date to timestamp for CountdownTimer
	const getExpirationTimestamp = (expiresAt?: string): number | null => {
		if (!expiresAt) return null
		return Math.floor(new Date(expiresAt).getTime() / 1000)
	}

	return (
		<div className='hidden lg:block collectionTableWrapper w-full overflow-x-auto'>
			<Table>
				<TableHeader>
					<TableRow className='bg-muted/50'>
						<TableHead className='min-w-[100px]'>Price</TableHead>
						<TableHead className='min-w-[100px]'>USD Price</TableHead>
						<TableHead className='min-w-[100px]'>Status</TableHead>
						<TableHead className='min-w-[140px]'>From</TableHead>
						<TableHead className='min-w-[140px]'>Expiration</TableHead>
						<TableHead className='min-w-[140px]'>Created</TableHead>
						{isOwner && <TableHead className='min-w-[100px]'>Action</TableHead>}
					</TableRow>
				</TableHeader>
				<TableBody className='py-6'>
					{offers.length > 0 ? (
						offers.map((offer) => (
							<TableRow key={offer.id} className='text-sm font-medium h-[70px]'>
								<TableCell>{offer.price} SOL</TableCell>
								<TableCell>{(offer.price * solToUsd).toFixed(2)} USD</TableCell>
								<TableCell>{offer.status}</TableCell>
								<TableCell className='text-primary'>
									{offer.buyer.username}
								</TableCell>
								<TableCell>
									{offer.expiresAt ? (
										<CountdownTimer
											endsAt={getExpirationTimestamp(offer.expiresAt)}
											className='text-sm'
										/>
									) : (
										'No expiration'
									)}
								</TableCell>
								<TableCell>
									{dayjs(offer.createdAt).format('MMM DD, YYYY')}
								</TableCell>
								{isOwner && (
									<TableCell>
										{offer.status === 'PENDING' && (
											<Button
												variant='default'
												size='sm'
												onClick={() => handleAcceptOffer(offer)}
												disabled={loading === offer.id}
											>
												{loading === offer.id ? 'Processing...' : 'Accept'}
											</Button>
										)}
									</TableCell>
								)}
							</TableRow>
						))
					) : (
						<TableRow>
							<TableCell
								colSpan={isOwner ? 7 : 6}
								className='text-center py-8 text-[#7E7E7E]'
							>
								No offers found for this NFT.
							</TableCell>
						</TableRow>
					)}
				</TableBody>
			</Table>
		</div>
	)
}

interface OffersCardsProps {
	offers: ExtendedNFTOffer[]
	isOwner?: boolean
	nftPublicKey?: string
	refreshData?: () => void
}

function OffersCards({
	offers,
	isOwner,
	nftPublicKey,
	refreshData,
}: OffersCardsProps) {
	const umi = useContext(UmiContext)
	const wallet = useWallet()
	const [loading, setLoading] = useState<string | null>(null)

	// Function to handle accepting an offer
	const handleAcceptOffer = async (offer: NFTOffer) => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction || !nftPublicKey) {
			customToast.error('Wallet not connected.')
			return
		}

		setLoading(offer.id)

		try {
			await transactionBuilder()
				.add(
					acceptOffer(umi, {
						asset: publicKey(nftPublicKey),
						buyer: publicKey(offer.buyer.publicKey),
						offer: publicKey(offer.publicKey),
						escrow: publicKey(offer.escrow),
						seller: umi.identity,
						feeCollector: publicKey(
							'ArGtRWrGMMHoz6ATpuWYzrwNEjMd9bHfCHRcwMNfjZH6',
						),
					}),
				)
				.addRemainingAccounts([
					{
						pubkey: publicKey(offer.creator.publicKey),
						isWritable: false,
						isSigner: false,
					},
				])
				.sendAndConfirm(umi, {
					send: {
						skipPreflight: true,
					},
				})
				.catch((err) => {
					console.error('Failed to accept offer:', err)
					throw err
				})

			customToast.success('Offer Accepted Successfully')

			if (refreshData) {
				refreshData()
			}
		} catch (err: unknown) {
			console.error('Failed to accept offer:', err)
			customToast.error(
				`Accept offer failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(null)
		}
	}

	// Convert string date to timestamp for CountdownTimer
	const getExpirationTimestamp = (expiresAt?: string): number | null => {
		if (!expiresAt) return null
		return Math.floor(new Date(expiresAt).getTime() / 1000)
	}

	return (
		<div className='lg:hidden'>
			<span className='text-[10px] font-medium'>Offers Details</span>
			<div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-4'>
				{offers.length > 0 ? (
					offers.map((offer) => (
						<div key={offer.id} className='relative'>
							<NftOfferCard
								key={offer.id}
								price={offer.price}
								buyer={offer.buyer.username}
								status={offer.status}
								expiresAt={offer.expiresAt}
								createdAt={offer.createdAt}
								expirationTimestamp={getExpirationTimestamp(offer.expiresAt)}
							/>
							{isOwner && offer.status === 'PENDING' && (
								<div className='mt-2'>
									<Button
										variant='default'
										size='sm'
										className='w-full'
										onClick={() => handleAcceptOffer(offer)}
										disabled={loading === offer.id}
									>
										{loading === offer.id ? 'Processing...' : 'Accept Offer'}
									</Button>
								</div>
							)}
						</div>
					))
				) : (
					<div className='col-span-full text-center py-8 text-[#7E7E7E]'>
						No offers found for this NFT.
					</div>
				)}
			</div>
		</div>
	)
}

export function MyOffersTable({ offers }: { offers: OfferMadeType[] }) {
	const solToUsd = useSolanaPrice()
	return (
		<div className='hidden lg:block collectionTableWrapper w-full overflow-x-auto'>
			<Table>
				<TableHeader>
					<TableRow className='bg-muted/50'>
						<TableHead className='min-w-[100px]'>Price oo</TableHead>
						<TableHead className='min-w-[100px]'>USD Price</TableHead>
						<TableHead className='min-w-[100px]'>Floor Difference</TableHead>
						<TableHead className='min-w-[140px]'>NFT</TableHead>
						<TableHead className='min-w-[140px]'>Expiration</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody className='py-6'>
					{offers?.map((item) => (
						<TableRow key={item.id} className='text-sm font-medium h-[70px]'>
							<TableCell className=''>{item.price} SOL</TableCell>
							<TableCell>$ {(solToUsd * item.price).toFixed(2)} USD</TableCell>
							<TableCell>{item?.floorDiff}</TableCell>
							<TableCell>
								<a
									href={`/nft-details/${item?.nft?.publicKey}`}
									className='text-primary no-underline hover:underline cursor-pointer'
								>
									{item?.nft?.name}
								</a>
							</TableCell>
							<TableCell>
								{dayjs(item?.expiresAt).format('MMM DD, YYYY')}
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</div>
	)
}

export function OffersReceivedTable({
	offers,
}: {
	offers: OffersReceivedType[]
}) {
	const solToUsd = useSolanaPrice()
	return (
		<div className='hidden lg:block collectionTableWrapper w-full overflow-x-auto'>
			<Table>
				<TableHeader>
					<TableRow className='bg-muted/50'>
						<TableHead className='min-w-[100px]'>Price </TableHead>
						<TableHead className='min-w-[100px]'>USD Price</TableHead>
						<TableHead className='min-w-[100px]'>Floor Difference</TableHead>
						<TableHead className='min-w-[140px]'>NFT</TableHead>
						<TableHead className='min-w-[140px]'>Expiration</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody className='py-6'>
					{offers?.map((item) => (
						<TableRow key={item.id} className='text-sm font-medium h-[70px]'>
							<TableCell className=''>{item.price} SOL</TableCell>
							<TableCell>$ {(solToUsd * item.price).toFixed(2)} USD</TableCell>
							<TableCell>{item?.floorDiff}</TableCell>
							<TableCell>
								<a
									href={`/nft-details/${item?.nft?.publicKey}`}
									className='text-primary no-underline hover:underline cursor-pointer'
								>
									{item?.nft?.name}
								</a>
							</TableCell>
							<TableCell>
								{dayjs(item?.expiresAt).format('MMM DD, YYYY')}
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</div>
	)
}
