// components/UmiProvider.tsx

import { useConnection, useWallet } from '@solana/wallet-adapter-react'
import type { FC, ReactNode } from 'react'
import { useMemo } from 'react'
import { createConfiguredUmi, UmiContext } from '@/lib/umi'

export const UmiProvider: FC<{ children: ReactNode }> = ({ children }) => {
	const { connection } = useConnection()
	const { wallet } = useWallet()

	// Create Umi instance when connection or wallet changes
	const umi = useMemo(() => {
		if (!wallet?.adapter) return null
		return createConfiguredUmi(connection.rpcEndpoint, wallet.adapter)
	}, [connection.rpcEndpoint, wallet])

	return <UmiContext.Provider value={umi}>{children}</UmiContext.Provider>
}
