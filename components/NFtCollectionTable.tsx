import { Image } from '@unpic/react'
import { Alert<PERSON>ircle, Check, Loader2, MoreH<PERSON>zontal } from 'lucide-react'
import { useEffect, useState } from 'react'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table'
import { handleViewOnSolscan } from '@/lib/utils'
import type { BulkNftItem } from '@/types/bulk-nft.types'

// Status types for NFT items - using the same as defined in BulkNftItem
type NftStatus = 'pending' | 'processing' | 'success' | 'failed'

interface NftCollectionTableProps {
	data: BulkNftItem[]
}

export default function NftCollectionTable({
	data = [],
}: NftCollectionTableProps) {
	// State to store image URLs
	const [imageUrls, setImageUrls] = useState<Record<number, string>>({})

	// Create object URLs for images and clean them up when component unmounts
	useEffect(() => {
		// Create object URLs for all images
		const urls: Record<number, string> = {}

		data.forEach((item) => {
			if (item.image) {
				urls[item.id] = URL.createObjectURL(item.image)
			}
		})

		setImageUrls(urls)

		// Cleanup function to revoke object URLs when component unmounts
		return () => {
			Object.values(urls).forEach((url) => {
				URL.revokeObjectURL(url)
			})
		}
	}, [data])

	return (
		<div className='w-full overflow-auto'>
			<Table className='border-collapse w-full'>
				<TableHeader className='bg-black'>
					<TableRow className='border-b border-zinc-800'>
						<TableHead className='text-zinc-400 font-normal text-xs lg:text-lg py-2'>
							ID
						</TableHead>
						<TableHead className='text-zinc-400 font-normal text-xs lg:text-lg py-2'>
							Name
						</TableHead>
						<TableHead className='text-zinc-400 font-normal text-xs lg:text-lg py-2'>
							Description
						</TableHead>
						<TableHead className='text-zinc-400 font-normal text-xs lg:text-lg py-2'>
							Image Name
						</TableHead>
						<TableHead className='text-zinc-400 font-normal text-xs lg:text-lg py-2'>
							Attributes
						</TableHead>
						<TableHead className='text-zinc-400 font-normal text-xs lg:text-lg py-2'>
							Status
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{data.length > 0 ? (
						data.map((item) => {
							// Use the status directly from the item
							const status: NftStatus = item.status

							// Get image URL from our state or use default
							const imageUrl =
								imageUrls[item.id] || '/assets/img/default-profile.png'

							// Get attributes from the item
							const attributes = item.attributes || {}

							return (
								<TableRow
									key={`nft-${item.id}`}
									className='border-b border-zinc-800'
								>
									<TableCell className='text-white text-sm py-4'>
										#{item.id}
									</TableCell>
									<TableCell className='text-white text-sm py-4'>
										<div className='flex items-center gap-2'>
											<div className='w-8 h-8 rounded-md overflow-hidden'>
												<Image
													src={imageUrl}
													alt={item.name || 'NFT'}
													width={37}
													height={40}
													className='object-cover'
												/>
											</div>
											<span>{item.name || 'Unnamed'}</span>
										</div>
									</TableCell>
									<TableCell className='text-white text-sm py-4 max-w-xs'>
										<div>
											<p className='line-clamp-2 inline'>
												{item.description || 'No description'}
											</p>
											{item.description && item.description.length > 60 && (
												<span className='text-primary ml-1 text-xs cursor-pointer'>
													More
												</span>
											)}
										</div>
									</TableCell>
									<TableCell className='text-white text-sm py-4'>
										{item.image?.name || 'No image'}
									</TableCell>
									<TableCell className='text-white text-sm py-4'>
										<div className='flex items-center gap-2 lg:gap-x-4'>
											<div className='flex flex-col'>
												{Object.entries(attributes).length > 0 ? (
													<>
														{Object.entries(attributes)
															.slice(0, 3)
															.map(([key, value]) => (
																<div key={key}>
																	<span className='text-primary'>{key}: </span>
																	<span>{value}</span>
																</div>
															))}
														{Object.entries(attributes).length > 3 && (
															<div className='flex items-center mt-1'>
																<div className='bg-primary rounded-full w-[40px] h-[40px] lg:w-[48px] lg:h-[48px] flex items-center justify-center'>
																	<span className='text-white text-xs font-bold'>
																		+{Object.entries(attributes).length - 3}
																	</span>
																</div>
															</div>
														)}
													</>
												) : (
													<div>No attributes</div>
												)}
											</div>
										</div>
									</TableCell>
									<TableCell className='text-white text-sm lg:text-lg py-4'>
										{status === 'success' && (
											<div className='flex justify-center'>
												<div
													className='cursor-pointer bg-green-600 rounded-full w-8 h-8 flex items-center justify-center'
													onClick={() => handleViewOnSolscan(item.hash)}
												>
													<Check className='text-white w-5 h-5' />
												</div>
											</div>
										)}
										{status === 'processing' && (
											<div className='flex justify-center'>
												<div className='w-8 h-8 flex items-center justify-center'>
													<Loader2 className='animate-spin h-6 w-6 text-primary' />
												</div>
											</div>
										)}
										{status === 'pending' && (
											<div className='flex justify-center'>
												<div className='border border-primary rounded-full w-8 h-8 flex items-center justify-center'>
													<MoreHorizontal className='text-primary w-5 h-5 cursor-pointer' />
												</div>
											</div>
										)}
										{status === 'failed' && (
											<div className='flex justify-center'>
												<div className='bg-red-600 rounded-full w-8 h-8 flex items-center justify-center'>
													<AlertCircle className='text-white w-5 h-5' />
												</div>
											</div>
										)}
									</TableCell>
								</TableRow>
							)
						})
					) : (
						<TableRow>
							<TableCell colSpan={6} className='text-center py-4 text-white'>
								No NFT data available. Please upload CSV and folder files.
							</TableCell>
						</TableRow>
					)}
				</TableBody>
			</Table>
		</div>
	)
}
