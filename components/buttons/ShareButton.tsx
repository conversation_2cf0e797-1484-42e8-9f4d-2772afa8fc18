import type { ComponentProps } from 'react'
import { useState } from 'react'
import ShareModal from '@/components/modals/ShareModal'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { shareContent } from '@/lib/utils/sharing'
import Share from '@/public/assets/svg/share.svg?react'

interface ShareButtonProps extends Omit<ComponentProps<'button'>, 'onClick'> {
	title: string
	url: string
	description?: string
	showText?: boolean
	iconClass?: string
	variant?:
		| 'default'
		| 'destructive'
		| 'outline'
		| 'secondary'
		| 'ghost'
		| 'link'
}

export default function ShareButton({
	title,
	url,
	description = 'Check out this NFT on Shogunex Fusion!',
	showText = true,
	className,
	variant = 'ghost',
	iconClass,
	...props
}: ShareButtonProps) {
	const [isShareModalOpen, setIsShareModalOpen] = useState(false)

	const handleShareToSocial = async () => {
		// Try native sharing first if available in the browser
		if (typeof navigator !== 'undefined' && 'share' in navigator) {
			const shared = await shareContent({
				title,
				text: description,
				url,
			})

			// If native sharing failed or was cancelled, show the modal
			if (!shared) {
				setIsShareModalOpen(true)
			}
		} else {
			// Fallback to modal if Web Share API is not available
			setIsShareModalOpen(true)
		}
	}

	return (
		<>
			<Button
				variant={variant}
				className={className}
				onClick={handleShareToSocial}
				{...props}
			>
				{showText && 'Share'}
				<Share className={cn('w-4 h-auto', iconClass)} />
			</Button>

			<ShareModal
				isOpen={isShareModalOpen}
				onClose={() => setIsShareModalOpen(false)}
				title={title}
				url={url}
				description={description}
			/>
		</>
	)
}
