import { cn } from '@/lib/utils'

export default function SkeletonProfilePage() {
	return (
		<div className='containerPadding pt-4 animate-pulse'>
			{/* Profile Header */}
			<div className='flex flex-col items-center lg:items-start lg:flex-row gap-6 mt-6'>
				{/* Profile Picture */}
				<div className='w-24 h-24 lg:w-32 lg:h-32 bg-gray-200/20 rounded-full' />

				<div className='flex-1 text-center lg:text-left'>
					{/* Username */}
					<div className='h-8 w-48 bg-gray-200/20 rounded-full mx-auto lg:mx-0' />
					{/* Bio */}
					<div className='mt-4 space-y-2'>
						<div className='h-4 w-3/4 bg-gray-200/20 rounded-full mx-auto lg:mx-0' />
						<div className='h-4 w-1/2 bg-gray-200/20 rounded-full mx-auto lg:mx-0' />
					</div>
					{/* Action Buttons */}
					<div className='flex gap-3 mt-4 justify-center lg:justify-start'>
						<div className='h-10 w-24 bg-gray-200/20 rounded-[10px]' />
						<div className='h-10 w-24 bg-gray-200/20 rounded-[10px]' />
					</div>
				</div>
			</div>

			{/* Stats Section */}
			<div className='mt-8 bg-container py-4 rounded-[10px] grid grid-cols-3 gap-4 text-center'>
				<div>
					<div className='h-4 w-16 bg-gray-200/20 rounded-full mx-auto' />
					<div className='h-6 w-12 bg-gray-200/20 rounded-full mx-auto mt-2' />
				</div>
				<div>
					<div className='h-4 w-16 bg-gray-200/20 rounded-full mx-auto' />
					<div className='h-6 w-12 bg-gray-200/20 rounded-full mx-auto mt-2' />
				</div>
				<div>
					<div className='h-4 w-16 bg-gray-200/20 rounded-full mx-auto' />
					<div className='h-6 w-12 bg-gray-200/20 rounded-full mx-auto mt-2' />
				</div>
			</div>

			{/* Tab Navigation */}
			<div className='mt-10 w-full flex gap-x-5 lg:gap-x-10 py-4 border-b-[1.5px] border-[#3A3939]'>
				<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
				<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
				<div className='h-6 w-24 bg-gray-200/20 rounded-full' />
			</div>

			{/* NFT/Activity Grid */}
			<div className='mt-8'>
				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5'>
					<div className='h-80 bg-gray-200/20 rounded-[20px]' />
					<div className='h-80 bg-gray-200/20 rounded-[20px]' />
					<div className='h-80 bg-gray-200/20 rounded-[20px]' />
					<div className='h-80 bg-gray-200/20 rounded-[20px]' />
					<div className='h-80 bg-gray-200/20 rounded-[20px]' />
					<div className='h-80 bg-gray-200/20 rounded-[20px]' />
				</div>
			</div>
		</div>
	)
}
