import { ChevronDown } from 'lucide-react'
import { useState } from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { Sheet, SheetContent } from '@/components/ui/sheet'
import { useIsDesktop } from '@/lib/hooks/useIsDesktop'
import { cn } from '@/lib/utils'
import type { NftTraitFilters } from '@/types/collection.types'

type FiltersProps = {
	isSidebarOpen: boolean
	setIsSidebarOpen: (state: boolean) => void
	filters: {
		traits?: NftTraitFilters
		onClickTrait?: (category: string, trait: string) => void
		selectedTraits?: {
			[category: string]: string[]
		}
	}
}

export default function Filters({
	isSidebarOpen,
	setIsSidebarOpen,
	filters,
}: FiltersProps) {
	const isDesktop = useIsDesktop()

	if (isDesktop && isSidebarOpen) {
		return <AllFilters {...filters} />
	}

	if (isDesktop && isSidebarOpen === false) {
		return null
	}

	return (
		<Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
			<SheetContent
				side='bottom'
				className='h-[80svh] rounded-t-[10px] px-6 pt-5 border-border overflow-y-auto'
			>
				<AllFilters {...filters} />
			</SheetContent>
		</Sheet>
	)
}

const AllFilters = ({
	traits,
	onClickTrait,
	selectedTraits,
}: FiltersProps['filters']) => {
	return (
		<div className='w-full lg:w-[300px] shrink-0 lg:h-full mt-5 lg:mt-0 lg:pr-2'>
			{traits && Object.keys(traits).length > 0 && (
				<Collapsible open>
					<CollapsibleTrigger className='w-full'>
						<div className='w-full flex items-center px-2 justify-between py-0.5 bg-[#292929] rounded-sm'>
							<span className='text-base lg:text-xl font-medium'>Traits</span>
							{/* <ChevronDown className='w-5 h-auto' /> */}
						</div>
					</CollapsibleTrigger>
					<CollapsibleContent className='mt-2'>
						{Object.entries(traits).map(([category, traitValues]) => (
							<TraitCategory
								key={category}
								category={category}
								traits={traitValues}
								selected={selectedTraits?.[category] || []}
								onToggle={(trait) => onClickTrait?.(category, trait)}
							/>
						))}
					</CollapsibleContent>
				</Collapsible>
			)}
		</div>
	)
}

type TraitCategoryProps = {
	category: string
	traits: Record<string, number>
	selected: string[]
	onToggle: (trait: string) => void
}

const TraitCategory = ({
	category,
	traits,
	selected,
	onToggle,
}: TraitCategoryProps) => {
	const [isOpen, setOpen] = useState(false)

	return (
		<Collapsible open={isOpen} className='my-3'>
			<CollapsibleTrigger
				onClick={() => setOpen(!isOpen)}
				className='w-full cursor-pointer'
			>
				<div className='w-full flex items-center px-2 justify-between'>
					<span
						className={cn(
							'text-sm lg:text-base font-semibold text-muted-foreground',
							isOpen && 'text-white',
						)}
					>
						{category}
					</span>
					<ChevronDown
						className={`w-4 h-auto transition-transform text-muted-foreground ${isOpen && 'rotate-180 text-white'}`}
					/>
				</div>
			</CollapsibleTrigger>
			<CollapsibleContent className='mt-3'>
				<ul className='space-y-3'>
					{Object.entries(traits).map(([trait, count]) => {
						const isSelectedTrait = selected.includes(trait)
						return (
							<li
								key={trait}
								onClick={() => onToggle(trait)}
								className={cn(
									'flex items-center justify-between pl-2 pr-3 text-muted-foreground rounded-sm hover:bg-muted cursor-pointer',
									isSelectedTrait && 'text-white',
								)}
							>
								<div className='flex items-center gap-x-2'>
									<Checkbox checked={selected.includes(trait)} />
									<span className='text-xs lg:text-sm'>{trait}</span>
								</div>
								<span
									className={cn(
										'text-xs lg:text-sm text-muted-foreground',
										isSelectedTrait && 'text-white',
									)}
								>
									{count}
								</span>
							</li>
						)
					})}
				</ul>
			</CollapsibleContent>
		</Collapsible>
	)
}
