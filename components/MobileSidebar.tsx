'use client'

import { useWallet } from '@solana/wallet-adapter-react'
import { Image } from '@unpic/react'
import { motion } from 'framer-motion'
import { Co<PERSON>, Wallet, X } from 'lucide-react' // Example of Lusid icons
import type React from 'react'
import { useState } from 'react'
import { useMeUser } from '@/hooks/useMeUser'
import {
	linkVariants,
	mobileMenuVariants,
} from '@/lib/animation/animationVariants'
import { copyText, truncateAddress } from '@/lib/utils'
import CreateIcon from '@/public/assets/svg/create.svg?react'
import FeedIcon from '@/public/assets/svg/feed.svg?react'
import BrandLogo from '@/public/assets/svg/logo-white.svg?react'
import ExploreIcon from '@/public/assets/svg/search.svg?react'
import { Link } from './atoms/Link'
import { LoginButton } from './login/LoginButton'
import {
	Sheet,
	SheetClose,
	Sheet<PERSON>ontent,
	She<PERSON><PERSON>eader,
	SheetTrigger,
} from './ui/sheet'

const NavLinks = [
	{
		id: 1,
		link: '/explore',
		title: 'Explore',
		icon: <ExploreIcon className='w-5 h-auto text-[#DD0003]' />,
	},
	{
		id: 2,
		link: '/studio',
		title: 'Create',
		icon: <CreateIcon className='w-5 h-auto text-[#DD0003]' />,
	},
	{
		id: 3,
		link: '/about',
		title: 'About',
		icon: <FeedIcon className='w-5 h-auto text-[#DD0003]' />,
	},
]

// Logo animation variants
const logoContainerVariants = {
	initial: { opacity: 0, y: -10 },
	animate: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.5,
			staggerChildren: 0.1,
		},
	},
}

const logoIconVariants = {
	initial: { rotate: -10, scale: 0.9, opacity: 0 },
	animate: {
		rotate: 0,
		scale: 1,
		opacity: 1,
		transition: { duration: 0.5, ease: 'easeOut' },
	},
	hover: {
		rotate: [0, -5, 5, -5, 0],
		scale: 1.1,
		filter: 'drop-shadow(0 0 8px rgba(255, 0, 0, 0.7))',
		transition: {
			duration: 0.5,
			ease: 'easeInOut',
			rotate: {
				repeat: Number.POSITIVE_INFINITY,
				repeatType: 'mirror',
				duration: 2,
			},
		},
	},
}

const textVariants = {
	initial: { opacity: 0, x: -20 },
	animate: { opacity: 1, x: 0, transition: { duration: 0.5, delay: 0.2 } },
	hover: {
		textShadow: '0 0 8px rgba(255, 255, 255, 0.7)',
		transition: { duration: 0.3 },
	},
}

const redTextVariants = {
	initial: { opacity: 0, x: -10 },
	animate: { opacity: 1, x: 0, transition: { duration: 0.5, delay: 0.3 } },
	hover: {
		textShadow: '0 0 12px rgba(255, 0, 0, 0.8)',
		scale: 1.05,
		transition: { duration: 0.3 },
	},
}

export default function MobileSidebar({
	children,
}: {
	children: React.ReactNode
}) {
	const [isOpen, setIsOpen] = useState(false)
	const [isLogoHovered, setIsLogoHovered] = useState(false)
	const { publicKey: walletPublicKey, wallet } = useWallet()

	const toggleSidebar = () => setIsOpen(!isOpen)
	const { data: user, isLoading: isUserLoading, error: userError } = useMeUser()
	const isCurrentUser = walletPublicKey?.toBase58() === user?.publicKey

	return (
		<Sheet open={isOpen} onOpenChange={setIsOpen}>
			<SheetTrigger asChild>
				<motion.div
					variants={mobileMenuVariants}
					initial='hidden'
					animate='visible'
					className='md:hidden'
				>
					{children}
				</motion.div>
			</SheetTrigger>
			<SheetContent
				hideCloseButton
				side='left'
				className='w-full bg-black p-6 flex flex-col justify-between'
			>
				<div>
					<SheetHeader className='mb-8 flex flex-row p-0 justify-between items-center'>
						<Link href='/'>
							<motion.div
								className='relative flex items-center gap-x-3 py-2 px-3 rounded-lg transition-all'
								variants={logoContainerVariants}
								initial='initial'
								animate='animate'
								whileHover='hover'
								onClick={toggleSidebar}
								onHoverStart={() => setIsLogoHovered(true)}
								onHoverEnd={() => setIsLogoHovered(false)}
							>
								<motion.div className='relative' variants={logoIconVariants}>
									<BrandLogo className='w-7 h-auto text-[#DD0003] relative z-10' />
								</motion.div>
								<div className='text-xl font-squada flex items-baseline'>
									<motion.span
										className='text-white tracking-wider'
										variants={textVariants}
									>
										SHOGUN
									</motion.span>
									<motion.span
										className='text-[#FF0000] ml-1 font-bold tracking-wide'
										variants={redTextVariants}
									>
										FUSION
									</motion.span>
								</div>
							</motion.div>
						</Link>
						<SheetClose>
							<X className='text-white w-5 h-auto shrink-0' />
						</SheetClose>
					</SheetHeader>

					{wallet?.adapter?.connected ? (
						<div className='relative userProfileDetails flex flex-row items-center'>
							<div className='relative shrink-0 rounded-full w-auto after:contents-[*] after:absolute after:-inset-1 after:p-1 after:bg-gradient-to-r after:from-[#FF8F1F] after:to-[#DD0003] after:-z-[1] after:rounded-full'>
								<Image
									src={user?.imageUrl || '/assets/img/default-profile.png'}
									className='h-[50px] w-[50px] rounded-full '
									layout='fixed'
									width={50}
									height={50}
									alt='user profile'
								/>
							</div>
							<div className='w-full flex justify-center flex-col shrink gap-y-1 ml-5 overflow-hidden'>
								<div className='text-sm text-primary font-semibold truncate'>
									@ {user?.userName} {isCurrentUser && '(You)'}
								</div>

								<div className='flex items-center gap-x-2 text-xs text-white mt-2'>
									<Wallet className='w-3 h-auto' />
									{truncateAddress(walletPublicKey?.toBase58() ?? '')}
									<Copy
										className='w-3 h-auto'
										onClick={() =>
											copyText({ data: walletPublicKey?.toBase58() ?? '' })
										}
									/>
								</div>
							</div>
						</div>
					) : (
						<LoginButton />
					)}

					<div className='gradientBorder border-t-[1px] mt-10' />

					<nav className='flex flex-col gap-y-8 mt-10'>
						{NavLinks.map((item, index) => (
							<motion.div
								key={item.id}
								variants={linkVariants}
								initial='hidden'
								animate='visible'
								custom={index}
								whileHover='hover'
								className='relative'
							>
								<Link
									href={item.link}
									className='flex items-center gap-x-4'
									onClick={toggleSidebar}
								>
									{item.icon}
									<span className='text-base text-white font-medium hover:text-[#FF0000]'>
										{item.title}
									</span>
								</Link>
							</motion.div>
						))}
					</nav>
				</div>

				{/* BrandLogo with slight upward movement and opacity decrease */}
				<motion.div
					className='absolute bottom-10 left-1/2 -translate-1/2' // Reduced py for smaller gap
					initial={{ opacity: 0, y: 20 }} // Set initial opacity and position (move down)
					animate={{
						opacity: 0.2,
						y: -10, // Move the logo upwards
						transition: { delay: 0.3, duration: 0.5 }, // Set delay and transition duration
					}}
				>
					<BrandLogo className='w-[120px] h-auto' />
				</motion.div>
			</SheetContent>
		</Sheet>
	)
}
