import devServer from '@hono/vite-dev-server'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import { telefunc } from 'telefunc/vite'
import vike from 'vike/plugin'
import { pages } from 'vike-cloudflare'
import { defineConfig } from 'vite'
import { analyzer } from 'vite-bundle-analyzer'
import { ViteMcp } from 'vite-plugin-mcp'
import { nodePolyfills } from 'vite-plugin-node-polyfills'
import svgr from 'vite-plugin-svgr'

export default defineConfig({
	plugins: [
		ViteMcp({}),
		vike({}),
		devServer({
			entry: 'hono-entry.ts',
			exclude: [
				/^\/@.+$/,
				/.*\.(ts|tsx|vue)($|\?)/,
				/.*\.(s?css|less)($|\?)/,
				/^\/favicon\.png$/,
				/.*\.(svg|png)($|\?)/,
				/^\/(public|assets|static)\/.+/,
				/^\/node_modules\/.*/,
			],
			injectClientScript: false,
		}),
		react({}),
		tailwindcss(),
		sentryVitePlugin({
			sourcemaps: {
				disable: false,
			},
		}),
		telefunc(),
		pages({
			server: {
				kind: 'hono',
				entry: 'hono-entry.ts',
			},
		}),
		analyzer(),
		nodePolyfills({
			// Explicitly include problematic modules
			include: ['stream', 'buffer', 'util', 'path'],
			// Enable polyfills for specific globals and modules
			globals: {
				Buffer: true,
				global: true,
				process: true,
			},
			protocolImports: true, // Polyfill node: protocol imports
		}),
		svgr({
			include: '**/*.svg?react',
		}),
	],
	resolve: {
		alias: {
			'@': new URL('./', import.meta.url).pathname,
			// Polyfill Node.js core modules
			crypto: 'crypto-browserify',
			stream: 'stream-browserify',
			os: 'os-browserify/browser',
			path: 'path-browserify',
		},
	},
	build: {
		target: 'es2022',
		sourcemap: true,
		rollupOptions: {
			external: ['prisma-client/edge'],
		},
	},
})
