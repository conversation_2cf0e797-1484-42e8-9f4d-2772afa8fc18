import type { AssetV1, CollectionV1 } from '@metaplex-foundation/mpl-core'
import type { MetaData } from './MetaData'

/**
 * Enhanced asset type that combines an asset with its metadata
 */
export type EnhancedAsset = AssetV1 & {
	metadata: MetaData
}

/**
 * Enhanced collection type that combines a collection with its metadata
 */
export type EnhancedCollection = CollectionV1 & {
	metadata: MetaData
}

/**
 * Collection data type for page context
 */
export type CollectionData = {
	assets: EnhancedAsset[]
	collection: EnhancedCollection
}

export type User = {
	id: string
	username: string
	imageUrl: string | null
	publicKey: string
}

export type Collection = {
	symbol: string
	name: string
	publicKey: string
	description: string
	logoUrl: string
	bannerUrl: string
	metadataUri: string
	creator?: User
	owner?: User
	lowestBiddingPrice: number
}

export type NFT = {
	symbol: string
	name: string
	publicKey: string
	description: string
	logoUrl: string
	owner: User
	biddingPrice: number
	totalLikes: number
	totalViews: number
	royaltyBasisPoints: number
	listing: string
	collectionName?: string
	collection?: {
		name: string
		description?: string
		bannerUrl?: string
		logoUrl?: string
		owner?: User
		publicKey: string
	}
}
