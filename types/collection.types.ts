import { z } from 'zod'

/**
 * Schema for collection details response.
 */

export type NftTraitFilters = {
	[traitType: string]: {
		[traitValue: string]: number
	}
}

export const collectionDetailsResponseSchema = z.object({
	id: z.string().uuid(),
	name: z.string(),
	bannerUrl: z.string().nullable(),
	totalLikes: z.number(),
	nftTraitFilters: z.any().nullable(),
	tags: z.array(z.string()),
	xUrl: z.string().nullable(),
	websiteUrl: z.string().nullable(),
	//_count: z.object({
	//	likes: z.number(),
	//}),
	isLiked: z.boolean(),
	nftCount: z.number(),
	ownerCount: z.number(),
})

export type CollectionDetailsResponse = z.infer<
	typeof collectionDetailsResponseSchema
>
