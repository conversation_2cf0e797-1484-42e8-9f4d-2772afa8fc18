/**
 * Types for bulk NFT minting functionality
 */

/**
 * Represents a single NFT item in the bulk upload process
 */
export interface BulkNftItem {
	/** Unique identifier for the NFT */
	id: number

	/** Name of the NFT */
	name: string

	/** Description of the NFT */
	description: string

	/** The image file for the NFT */
	image: File

	/** Additional metadata attributes for the NFT */
	attributes: Record<string, string | number>

	/** Current processing status of the NFT */
	status: 'pending' | 'processing' | 'success' | 'failed'

	/** solana transaction hash */
	hash: string
}

/**
 * Type for the merged NFT data used in the bulk upload process
 */
export type MergedNftData = Array<{
	/** Metadata from CSV including id, name, description, and other attributes */
	metadata: Record<string, string | number>

	/** The image file for the NFT */
	file: File | null
}>

/**
 * Converts merged NFT data to the BulkNftItem format
 * @param mergedData The merged data from CSV and folder files
 * @returns Array of BulkNftItem objects
 */
export function convertToBulkNftItems(
	mergedData: MergedNftData,
): BulkNftItem[] {
	return mergedData
		.filter((item) => item.file !== null) // Filter out items without files
		.map((item) => {
			const { metadata, file } = item

			// Extract the core properties that we need separately
			const { id, name, description, imageId, ...attributesOnly } = metadata

			return {
				id: Number(id || 0),
				name: String(name || ''),
				description: String(description || ''),
				image: file as File, // We know file is not null due to the filter
				attributes: attributesOnly, // Only include the remaining attributes
				status: 'pending',
				hash: '',
			}
		})
}
