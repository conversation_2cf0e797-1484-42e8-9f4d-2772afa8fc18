import { z } from 'zod'

/**
 * Schema for liking a collection.
 */
export const collectionLikeSchema = z.object({
	publicKey: z.string(),
})

/**
 * Response schema for a successful like.
 */
export const collectionLikeResponseSchema = z.object({
	id: z.string().uuid(),
	userId: z.string().uuid(),
	publicKey: z.string(),
	createdAt: z.date(),
})

/**
 * Response schema for a successful unlike.
 */
export const collectionUnlikeResponseSchema = z.object({
	message: z.string(),
})

export type CollectionLikeInput = z.infer<typeof collectionLikeSchema>
export type CollectionLikeResponse = z.infer<
	typeof collectionLikeResponseSchema
>
export type CollectionUnlikeResponse = z.infer<
	typeof collectionUnlikeResponseSchema
>
