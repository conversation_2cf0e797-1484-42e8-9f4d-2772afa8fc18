import { z } from 'zod'

/**
 * Schema for liking an NFT.
 */
export const nftLikeSchema = z.object({
	publicKey: z.string(),
})

/**
 * Response schema for a successful like.
 */
export const nftLikeResponseSchema = z.object({
	id: z.string().uuid(),
	userId: z.string().uuid(),
	publicKey: z.string(),
	createdAt: z.date(),
})

/**
 * Response schema for a successful unlike.
 */
export const nftUnlikeResponseSchema = z.object({
	message: z.string(),
	publicKey: z.string(),
})

export type NftLikeInput = z.infer<typeof nftLikeSchema>
export type NftLikeResponse = z.infer<typeof nftLikeResponseSchema>
export type NftUnlikeResponse = z.infer<typeof nftUnlikeResponseSchema>

export const fetchNftParamSchema = z.object({
	page: z.coerce.number().int().min(1).default(1),
	limit: z.coerce.number().int().min(1).max(100).default(10),
	search: z.string().optional(),
	collectionId: z.string().optional(),
	sort: z.enum(['asc', 'desc']).optional(),
	traits: z
		.union([
			z.string().transform((str) => {
				try {
					return JSON.parse(str)
				} catch {
					return {}
				}
			}),
			z.record(z.array(z.string())),
		])
		.optional(),
})

export const nftItemSchema = z.object({
	name: z.string(),
	publicKey: z.string(),
	description: z.string(),
	imageUrl: z.string(),
	metadata: z.string().optional(),
	attributes: z
		.object({
			traitType: z.string(),
			value: z.string(),
		})
		.array()
		.optional()
		.nullable(),
	lastBiddingPrice: z.number().optional(),
	owner: z.object({
		id: z.string(),
		username: z.string(),
		imageUrl: z.string().optional().nullable(),
		publicKey: z.string(),
	}),
	collection: z.object({
		name: z.string(),
	}),
	listing: z
		.object({
			price: z.number(),
			listingType: z.string(),
			endTime: z.date(),
			status: z.string(),
		})
		.optional()
		.nullable(),
})

export const nftListingSchema = z.object({
	nfts: z.array(nftItemSchema),
	total: z.number().int(),
	page: z.number().int(),
	limit: z.number().int(),
})

export type FetchNftParam = z.infer<typeof fetchNftParamSchema>
export type NftListIttem = z.infer<typeof nftItemSchema>
export type NftListing = z.infer<typeof nftListingSchema>

/**
 * Schema for liking an NFT.
 */
export const nftAfterTransferSchema = z.object({
	publicKey: z.string(),
	ownerWalletAddress: z.string(),
})

export type NftAfterTransfer = z.infer<typeof nftAfterTransferSchema>

const sortByData = [
	{ id: 1, value: 'high_activity', label: 'High Activity' },
	{ id: 2, value: 'most_viewed', label: 'Most Viewed' },
	{ id: 3, value: 'recently_sold', label: 'Recently Sold' },
	{ id: 4, value: 'auction_first', label: 'Auction First' },
	{ id: 5, value: 'most_liked', label: 'Most Liked' },
	{ id: 6, value: 'price_high_to_low', label: 'Price: High to Low' },
	{ id: 7, value: 'price_low_to_high', label: 'Price: Low to High' },
	{ id: 8, value: 'recently_created', label: 'Recently Created' },
] as const

// Extract sort values for Zod enum
const sortValues = sortByData.map((item) => item.value)

export const sortTypeSchema = z.enum(sortValues as [string, ...string[]])

export type SortType = z.infer<typeof sortTypeSchema>

// Export sortByData if it needs to be used elsewhere for default value logic
export { sortByData }
