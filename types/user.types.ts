import { z } from 'zod'

// Follow schemas
export const followSchema = z.object({
	followingId: z.string().uuid(),
})

export const followResponseSchema = z.object({
	id: z.string().uuid(),
	followerId: z.string().uuid(),
	followingId: z.string().uuid(),
	createdAt: z.date(),
})

export const paginationSchema = z.object({
	page: z.coerce.number().int().min(1).default(1),
	limit: z.coerce.number().int().min(1).max(100).default(10),
	search: z.string().optional(),
	sort: z.enum(['asc', 'desc']).optional(),
})

export type PaginationInput = z.infer<typeof paginationSchema>

export const followPaginationSchema = z.object({
	page: z.number().int().min(1).default(1),
	limit: z.number().int().min(1).max(100).default(10),
	search: z.string().optional(),
	targetUserId: z.string().uuid().optional(),
})

export type FollowPaginationInput = z.infer<typeof followPaginationSchema>
// User info schemas
export const userInfoSchema = z.object({
	id: z.string().uuid(),
	userName: z.string(),
	avatar: z.string().url().optional().nullable(),
	publicKey: z.string().optional(),
})

export const paginatedUsersResponseSchema = z.object({
	users: z.array(userInfoSchema),
	total: z.number().int(),
	page: z.number().int(),
	limit: z.number().int(),
})

// NFT schemas
export const nftInfoSchema = z.object({
	name: z.string(),
	collectionName: z.string(),
	publicKey: z.string(),
	description: z.string(),
	logoUrl: z.string(),
	symbol: z.string(),
	biddingPrice: z.number(),
	owner: z.object({
		id: z.string(),
		username: z.string(),
		imageUrl: z.string(),
		publicKey: z.string(),
	}),
	listing: z.string(),
})

export const paginatedNftsResponseSchema = z.object({
	nfts: z.array(nftInfoSchema),
	total: z.number().int(),
	page: z.number().int(),
	limit: z.number().int(),
})

// Offer schemas
export const offerInfoSchema = z.object({
	id: z.string().uuid(),
	publicKey: z.string(),
	escrow: z.string(),
	price: z.number(),
	status: z.enum(['PENDING', 'ACCEPTED', 'REJECTED', 'CANCELLED', 'EXPIRED']),
	expiresAt: z.date().nullable(),
	createdAt: z.date(),
	updatedAt: z.date(),
	nft: z
		.object({
			id: z.string().uuid(),
			name: z.string(),
			imageUrl: z.string(),
			publicKey: z.string(),
		})
		.nullable(),
	buyer: z
		.object({
			id: z.string().uuid(),
			userName: z.string(),
			avatar: z.string().url().nullable(),
			publicKey: z.string(),
		})
		.nullable(),
	uyer: z
		.object({
			id: z.string().uuid(),
			userName: z.string(),
			avatar: z.string().url().nullable(),
			publicKey: z.string(),
		})
		.nullable(),
})

export const offerMadeSchema = z.object({
	id: z.string().uuid(),
	publicKey: z.string(),
	price: z.number(),
	expiresAt: z.string().nullable(),
	floorDiff: z.string().nullable(),
	nft: z
		.object({
			id: z.string().uuid(),
			name: z.string(),
			imageUrl: z.string(),
			publicKey: z.string(),
		})
		.nullable(),
	seller: z
		.object({
			id: z.string().uuid(),
			userName: z.string(),
			avatar: z.string().url().nullable(),
			publicKey: z.string(),
		})
		.nullable(),
})

export const offersReceivedSchema = z.object({
	id: z.string().uuid(),
	publicKey: z.string(),
	price: z.number(),
	expiresAt: z.string().nullable(),
	floorDiff: z.string().nullable(),
	nft: z
		.object({
			id: z.string().uuid(),
			name: z.string(),
			imageUrl: z.string(),
			publicKey: z.string(),
		})
		.nullable(),
	buyer: z
		.object({
			id: z.string().uuid(),
			userName: z.string(),
			avatar: z.string().url().nullable(),
			publicKey: z.string(),
		})
		.nullable(),
})

export type OfferMadeType = z.infer<typeof offerMadeSchema>

export type OffersReceivedType = z.infer<typeof offersReceivedSchema>

export const paginatedOffersResponseSchema = z.object({
	offers: z.array(offerInfoSchema),
	total: z.number().int(),
	page: z.number().int(),
	limit: z.number().int(),
})

export const paginatedOffersMadeResponseSchema = z.object({
	offers: z.array(offerMadeSchema),
	total: z.number().int(),
	page: z.number().int(),
	limit: z.number().int(),
})

export const paginatedOffersReceivedResponseSchema = z.object({
	offers: z.array(offersReceivedSchema),
	total: z.number().int(),
	page: z.number().int(),
	limit: z.number().int(),
})

// Collection schemas
export const collectionInfoSchema = z.object({
	symbol: z.string(),
	name: z.string(),
	publicKey: z.string(),
	description: z.string(),
	logoUrl: z.string(),
	bannerUrl: z.string(),
	metadataUri: z.string(),
	creator: z
		.object({
			id: z.string().uuid(),
			userName: z.string(),
			avatar: z.string().url().optional().nullable(),
			publicKey: z.string(),
		})
		.optional(),
	owner: z
		.object({
			id: z.string().uuid(),
			userName: z.string(),
			avatar: z.string().url().optional().nullable(),
			publicKey: z.string(),
		})
		.optional(),
	lowestBiddingPrice: z.number(),
})

export const paginatedCollectionsResponseSchema = z.object({
	collections: z.array(collectionInfoSchema),
	total: z.number().int(),
	page: z.number().int(),
	limit: z.number().int(),
})

// Prisma select types
export type NftSelect = {
	name: string
	publicKey: string
	description: string | null
	imageUrl: string
	lastBiddingPrice: number | null
	collection: { name: string } | null
	owner: {
		id: string
		username: string
		imageUrl: string | null
		publicKey: string
	}
}

export type CollectionSelect = {
	id: string
	name: string
	symbol: string
	publicKey: string
	description: string
	logoUrl: string
	bannerUrl: string
	metadataUri: string
	totalLikes: number
	owner: {
		id: string
		userName: string
		imageUrl: string | null
		publicKey: string
	}
	nfts: {
		lastBiddingPrice: number | null
	}[]
}

export type OffersResponse = z.infer<typeof paginatedOffersResponseSchema>

export type OffersMadeResponse = z.infer<
	typeof paginatedOffersMadeResponseSchema
>
export type OffersReceivedResponse = z.infer<
	typeof paginatedOffersReceivedResponseSchema
>
