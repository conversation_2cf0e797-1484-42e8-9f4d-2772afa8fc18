import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'

type LoginInput = {
	publicKey: string
	message: string
	signature: string
}

type LoginResponse = {
	success: boolean
	message: string
	isNewUser?: boolean
	data: {
		accessToken: string
		refreshToken: string
		userName: string
	}
}

type LogoutResponse = {
	success: boolean
	message: string
	data: object
}

export function useLogin() {
	const queryClient = useQueryClient()
	const [error, setError] = useState<string | null>(null)

	const loginMutation = useMutation<LoginResponse, Error, LoginInput>({
		mutationFn: async (loginData: LoginInput) => {
			const res = await fetch('/api/auth/login', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(loginData),
			})

			if (!res.ok) {
				const errorData = await res.json()
				throw new Error(errorData.message || 'Login failed')
			}

			return res.json() as Promise<LoginResponse>
		},
		onError: (err: Error) => {
			setError(err.message)
		},
		onSuccess: (data: LoginResponse) => {
			setError(null)
			if (data.success) {
				localStorage.setItem('accessToken', data.data.accessToken)
				localStorage.setItem('refreshToken', data.data.refreshToken)
				queryClient.invalidateQueries({ queryKey: ['currentUser'] })
			}
		},
	})

	const logoutMutation = useMutation<LogoutResponse, Error, void>({
		mutationFn: async () => {
			const res = await fetch('/api/users/logout', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					// Add auth header if needed
					// Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
				},
			})

			if (!res.ok) {
				const errorData = await res.json()
				throw new Error(errorData.message || 'Logout failed')
			}

			return res.json() as Promise<LogoutResponse>
		},
		onSuccess: () => {
			localStorage.removeItem('accessToken')
			localStorage.removeItem('refreshToken')
			queryClient.invalidateQueries({ queryKey: ['currentUser'] })
		},
		onError: (err: Error) => {
			setError(err.message)
		},
	})

	return {
		login: loginMutation.mutateAsync,
		logout: logoutMutation.mutateAsync,
		isLoggingIn: loginMutation.status === 'pending',
		isLoggingOut: logoutMutation.status === 'pending',
		error,
	}
}
