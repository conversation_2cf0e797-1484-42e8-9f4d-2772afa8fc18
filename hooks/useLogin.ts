import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useLogout } from './useLogout'

type LoginInput = {
	publicKey: string
	message: string
	signature: string
}

type LoginResponse = {
	success: boolean
	message: string
	isNewUser?: boolean
	data: {
		accessToken: string
		refreshToken: string
		userName: string
	}
}

export function useLogin() {
	const queryClient = useQueryClient()
	const [error, setError] = useState<string | null>(null)
	const {
		logout,
		logoutAll,
		isLoggingOut,
		isLoggingOutAll,
		error: logoutError,
	} = useLogout()

	const loginMutation = useMutation<LoginResponse, Error, LoginInput>({
		mutationFn: async (loginData: LoginInput) => {
			const res = await fetch('/api/auth/login', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(loginData),
			})

			if (!res.ok) {
				const errorData = await res.json()
				throw new Error(errorData.message || 'Login failed')
			}

			return res.json() as Promise<LoginResponse>
		},
		onError: (err: Error) => {
			setError(err.message)
		},
		onSuccess: (data: LoginResponse) => {
			setError(null)
			if (data.success) {
				localStorage.setItem('accessToken', data.data.accessToken)
				localStorage.setItem('refreshToken', data.data.refreshToken)
				queryClient.invalidateQueries({ queryKey: ['currentUser'] })
			}
		},
	})

	return {
		login: loginMutation.mutateAsync,
		logout,
		logoutAll,
		isLoggingIn: loginMutation.status === 'pending',
		isLoggingOut,
		isLoggingOutAll,
		error: error || logoutError,
	}
}
