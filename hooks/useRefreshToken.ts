import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'

type RefreshTokenInput = {
	refreshToken: string
}

type RefreshTokenResponse = {
	success: boolean
	message: string
	accessToken: string
	refreshToken: string
}

export function useRefreshToken() {
	const queryClient = useQueryClient()
	const [error, setError] = useState<string | null>(null)

	const refreshTokenMutation = useMutation<RefreshTokenResponse, Error, RefreshTokenInput>({
		mutationFn: async (refreshData: RefreshTokenInput) => {
			const res = await fetch('/api/auth/refresh', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(refreshData),
			})

			if (!res.ok) {
				const errorData = await res.json()
				throw new Error(errorData.message || 'Token refresh failed')
			}

			return res.json() as Promise<RefreshTokenResponse>
		},
		onError: (err: Error) => {
			setError(err.message)
			// If refresh fails, clear tokens and redirect to login
			localStorage.removeItem('accessToken')
			localStorage.removeItem('refreshToken')
			queryClient.invalidateQueries({ queryKey: ['currentUser'] })
		},
		onSuccess: (data: RefreshTokenResponse) => {
			setError(null)
			if (data.success) {
				// Update tokens in localStorage
				localStorage.setItem('accessToken', data.accessToken)
				localStorage.setItem('refreshToken', data.refreshToken)
				// Optionally invalidate user queries to refetch with new token
				queryClient.invalidateQueries({ queryKey: ['currentUser'] })
			}
		},
	})

	/**
	 * Refresh the access token using the stored refresh token
	 * @returns Promise<string> - The new access token
	 */
	const refreshToken = async (): Promise<string> => {
		const storedRefreshToken = localStorage.getItem('refreshToken')
		
		if (!storedRefreshToken) {
			throw new Error('No refresh token available')
		}

		const result = await refreshTokenMutation.mutateAsync({
			refreshToken: storedRefreshToken,
		})

		if (!result.success) {
			throw new Error(result.message || 'Token refresh failed')
		}

		return result.accessToken
	}

	return {
		refreshToken,
		isRefreshing: refreshTokenMutation.status === 'pending',
		error,
		// Expose the mutation for direct access if needed
		refreshTokenMutation,
	}
}
