import { useQuery } from '@tanstack/react-query'
import type { NftListing } from '@/types/nft.types'

interface UseCollectionNFTListingProps {
	collectionId: string
	page: number
	limit: number
	initialData?: NftListing
}

export function useCollectionNFTListing({
	collectionId,
	page,
	limit,
	initialData,
}: UseCollectionNFTListingProps) {
	// Only fetch client-side when not using initial data (page 1)
	const { data: clientNftResponse, isLoading: nftsIsLoading, error } = useQuery<NftListing, Error>({
		queryKey: ['nft-listing', { collectionId, page, limit }],
		queryFn: async () => {
			const query = new URLSearchParams()
			const params = { collectionId, page, limit }
			Object.entries(params).forEach(([key, value]) => {
				if (value !== undefined && value !== null && value !== '') {
					query.append(key, String(value))
				}
			})
			const res = await fetch(`/api/nft?${query.toString()}`)
			if (!res.ok) {
				throw new Error('Failed to fetch NFT listings')
			}
			return res.json()
		},
		enabled: page !== 1, // Only fetch client-side when not on first page
	})

	// Use server-rendered data for first page, client data for other pages
	const nftResponse = page === 1 ? initialData : clientNftResponse

	return {
		data: nftResponse,
		isLoading: page === 1 ? !initialData : nftsIsLoading,
		error: page === 1 ? null : error,
	}
} 