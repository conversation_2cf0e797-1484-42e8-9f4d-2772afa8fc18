import { useQuery } from '@tanstack/react-query'

// Type for query parameters
export interface ActivityParams {
	activityType?: string
	startDate?: string // ISO 8601 date string
	endDate?: string // ISO 8601 date string
	sortOrder?: 'asc' | 'desc'
	page?: number
	limit?: number
}

export type User = {
	id: string
	publicKey: string
	email?: string | null
	imageUrl?: string | null
	bannerUrl?: string | null
	instagramId?: string | null
	telegramId?: string | null
	twitterId?: string | null
	websiteId?: string | null
	bio?: string | null
	createdAt: string
	updatedAt: string
	userName?: string | null
	facebookId?: string | null
	username: string
	totalSalesVolume: number
	totalPurchaseVolume: number
	ownedNFTCount: number
	createdNFTCount: number
	collectionCount: number
	followerCount: number
	followingCount: number
	isVerified: boolean
	activityScore: number
	lastActiveAt: string
}

// Type for a single activity log item (based on Prisma schema and API response)
export type ActivityLogs = {
	id: string
	type: string // e.g., 'MINT', 'SALE', 'TRANSFER', 'LISTING', 'DELISTING', 'COLLECTION_CREATED'
	amount?: number | null
	createdAt: string // ISO 8601 date string
	transactionHash?: string | null
	collectionId: string
	userId?: string | null
	nftId?: string | null
	data?: Record<string, unknown> | null
	listingId?: string | null
	bidId?: string | null
	orderId?: string | null
	// Include related entities from the Prisma query
	nft?: {
		id: string
		name: string
		publicKey: string
		description?: string | null
		imageUrl: string
		metadataUrl: string
		attributes?: Record<string, unknown> | null
		royaltyBasisPoints: number
		isLocked: boolean
		createdAt: string
		updatedAt: string
		lastBiddingPrice?: number | null
		totalLikes: number
		totalViews: number
		totalActivities: number
		totalActivities7Days: number
		totalActivities30Days: number
		lastActivityAt: string
		ownerId: string
		creatorId: string
		collectionId?: string | null
	} | null
	fromUser?: User | null
	toUser?: User | null
}

// Type for pagination information
export type PaginationInfo = {
	currentPage: number
	limit: number
	totalPages: number
	totalActivities: number
}

// Type for the overall API response
export type CollectionActivityResponse = {
	activities: ActivityLogs[]
	pagination: PaginationInfo
}

export function useCollectionActivity(
	collectionId?: string,
	params?: ActivityParams,
) {
	return useQuery<CollectionActivityResponse, Error>({
		queryKey: ['collectionActivity', collectionId, params],
		queryFn: async () => {
			if (!collectionId) {
				throw new Error('Collection ID is required to fetch activity.')
			}

			const searchParams = new URLSearchParams()
			if (params) {
				Object.entries(params).forEach(([key, value]) => {
					if (
						value !== undefined &&
						value !== null &&
						String(value).trim() !== ''
					) {
						searchParams.append(key, String(value))
					}
				})
			}

			const queryString = searchParams.toString()
			const apiUrl = `/api/collection/${collectionId}/activity${queryString ? `?${queryString}` : ''}`

			const response = await fetch(apiUrl)

			if (!response.ok) {
				let errorData: { message?: string } = {}
				try {
					errorData = await response.json()
				} catch (e) {
					// If response is not JSON or empty
					errorData = {
						message:
							response.statusText ||
							'Failed to fetch activity data. Server returned an error.',
					}
				}
				throw new Error(
					errorData?.message ||
						`Failed to fetch activity data. Status: ${response.status}`,
				)
			}
			return response.json()
		},
		enabled: !!collectionId,
	})
}
