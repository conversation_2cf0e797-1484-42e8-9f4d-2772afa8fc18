import { useMutation, useQueryClient } from '@tanstack/react-query'

type EditProfileInput = {
	userName?: string
	imageUrl?: string
	instagramId?: string
	twitterId?: string
	websiteId?: string
	telegramId?: string
	bio?: string
	email?: string
	bannerUrl?: string
	facebookId?: string
}

type EditProfileResponse = {
	success: boolean
	message: string
	errors?: { path: string[]; message: string }[]
}

type UseUpdateProfileOptions = {
	onSuccess?: (data: EditProfileResponse) => void
	onError?: (error: unknown) => void
}

async function updateProfile(
	data: EditProfileInput,
): Promise<EditProfileResponse> {
	const response = await fetch('/api/users/edit-profile', {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(data),
	})

	if (!response.ok) {
		const errorData = await response.json()
		throw new Error(errorData.message || 'Failed to update profile')
	}

	return response.json()
}

export function useUpdateProfile(options?: UseUpdateProfileOptions) {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: updateProfile,
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({
				queryKey: ['userProfile', 'me_user'],
			})

			// Call custom onSuccess if provided
			if (options?.onSuccess) {
				options.onSuccess(data)
			}
		},
		onError: (error: unknown) => {
			console.error('Update profile error:', error)

			// Call custom onError if provided
			if (options?.onError) {
				options.onError(error)
			}
		},
	})
}
