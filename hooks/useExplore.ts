import { type UseQueryOptions, useQuery } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { tryCatch } from '@/lib/try-catch'
import type { CreatorData } from '@/server/app/explore/helper'

// Import types from the server helper
export type ParamType = 'nft' | 'fusion' | 'collection' | 'creator'
export type TimeFilter = '1H' | '1D' | '7D' | '30D' | 'ALL'

// Define types for the API responses
export type ListingData = {
	id: string
	price: number
	currency: string
	status: string
	listingType: string
	startTime: Date
	endTime: Date | null
	minBidIncrement: number | null
	reservePrice: number | null
	instantBuyPrice: number | null
	createdAt: Date
	updatedAt: Date
	isFixedPrice: boolean
	isAuction: boolean
	isActive: boolean
}

export type NFTWithListing = {
	id: string
	name: string
	collectionName: string
	publicKey: string
	description: string
	logoUrl: string
	symbol: string
	biddingPrice: number
	owner: {
		id: string
		username: string
		imageUrl: string
		publicKey: string
	}
	listingData?: ListingData
}

export type CollectionData = {
	id: string
	symbol: string
	name: string
	publicKey: string
	description: string
	logoUrl: string
	bannerUrl: string
	metadataUri: string
	creator: {
		id: string
		username: string
		imageUrl: string
		publicKey: string
	}
	lowestBiddingPrice: number
}

export type ExploreParams = {
	tab?: ParamType
	timeFilter?: TimeFilter
	searchQuery?: string
	page?: number
	limit?: number
	sort?: string
}

export type PaginationMeta = {
	currentPage: number
	totalPages: number
	totalItems: number
	itemsPerPage: number
}

export type ExploreResponse = {
	success: boolean
	data: {
		nfts: NFTWithListing[] | null
		collections: CollectionData[] | null
		creators: CreatorData[] | null
		pagination: PaginationMeta
	}
}

/**
 * Fetch NFTs from the explore API
 */
const fetchExploreData = async ({
	tab = 'nft',
	timeFilter,
	searchQuery,
	page = 1,
	limit = 20,
	sort = 'recently_created',
}: ExploreParams): Promise<ExploreResponse> => {
	// Build query parameters
	const queryParams = new URLSearchParams()
	queryParams.append('tab', tab)
	if (timeFilter) queryParams.append('time', timeFilter)
	if (searchQuery) queryParams.append('search', searchQuery)
	queryParams.append('page', page.toString())
	queryParams.append('limit', limit.toString())
	queryParams.append('sort', sort)

	const { data, error } = await tryCatch(
		ofetch<ExploreResponse>(`/api/explore?${queryParams.toString()}`, {
			method: 'GET',
		}),
	)

	if (error) {
		console.error(`Error fetching explore data for tab ${tab}:`, error)
		throw new Error(`Error fetching explore data: ${error}`)
	}

	return data
}

/**
 * Hook to fetch NFTs from the explore API
 */
export const useExploreNFTs = (
	params: Omit<ExploreParams, 'tab'> = {},
	options?: UseQueryOptions<ExploreResponse>,
) => {
	const { timeFilter, searchQuery, page = 1, limit = 20, sort } = params

	return useQuery<ExploreResponse>({
		queryKey: ['explore_nfts', { timeFilter, searchQuery, page, limit, sort }],
		queryFn: () =>
			fetchExploreData({
				tab: 'nft',
				timeFilter,
				searchQuery,
				page,
				limit,
				sort,
			}),
		refetchInterval: 60000, // Refetch every minute
		refetchOnWindowFocus: false,
		refetchOnMount: false,
		staleTime: 30000, // Consider data stale after 30 seconds
		...options,
	})
}

/**
 * Hook to fetch fusion NFTs from the explore API
 */
export const useExploreFusionNFTs = (
	params: Omit<ExploreParams, 'tab'> = {},
	options?: UseQueryOptions<ExploreResponse>,
) => {
	const { timeFilter, searchQuery, page = 1, limit = 20, sort } = params

	return useQuery<ExploreResponse>({
		queryKey: [
			'explore_fusion_nfts',
			{ timeFilter, searchQuery, page, limit, sort },
		],
		queryFn: () =>
			fetchExploreData({
				tab: 'fusion',
				timeFilter,
				searchQuery,
				page,
				limit,
				sort,
			}),
		refetchInterval: 60000,
		refetchOnWindowFocus: false,
		refetchOnMount: false,
		staleTime: 30000,
		...options,
	})
}

/**
 * Hook to fetch collections from the explore API
 */
export const useExploreCollections = (
	params: Omit<ExploreParams, 'tab'> = {},
	options?: UseQueryOptions<ExploreResponse>,
) => {
	const { timeFilter, searchQuery, page = 1, limit = 20, sort } = params

	return useQuery<ExploreResponse>({
		queryKey: [
			'explore_collections',
			{ timeFilter, searchQuery, page, limit, sort },
		],
		queryFn: () =>
			fetchExploreData({
				tab: 'collection',
				timeFilter,
				searchQuery,
				page,
				limit,
				sort,
			}),
		refetchInterval: 60000,
		refetchOnWindowFocus: false,
		refetchOnMount: false,
		staleTime: 30000,
		...options,
	})
}

/**
 * Combined hook that returns the appropriate data based on the selected tab
 * This is useful for components that need to switch between tabs
 */
export const useExplore = (
	params: ExploreParams = {},
	options?: UseQueryOptions<ExploreResponse>,
) => {
	const {
		tab = 'nft',
		timeFilter,
		searchQuery,
		page = 1,
		limit = 20,
		sort,
	} = params

	return useQuery<ExploreResponse>({
		queryKey: ['explore', { tab, timeFilter, searchQuery, page, limit, sort }],
		queryFn: () =>
			fetchExploreData({ tab, timeFilter, searchQuery, page, limit, sort }),
		refetchInterval: 60000,
		...options,
	})
}
