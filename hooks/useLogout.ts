import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'

type LogoutResponse = {
	success: boolean
	message: string
	data: object
}

export function useLogout() {
	const queryClient = useQueryClient()
	const [error, setError] = useState<string | null>(null)

	const logoutMutation = useMutation<LogoutResponse, Error, void>({
		mutationFn: async () => {
			const accessToken = localStorage.getItem('accessToken')
			
			const res = await fetch('/api/auth/logout', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					...(accessToken && { Authorization: `Bearer ${accessToken}` }),
				},
			})

			if (!res.ok) {
				const errorData = await res.json()
				throw new Error(errorData.message || 'Logout failed')
			}

			return res.json() as Promise<LogoutResponse>
		},
		onSuccess: () => {
			setError(null)
			// Clear tokens from localStorage
			localStorage.removeItem('accessToken')
			localStorage.removeItem('refreshToken')
			// Invalidate all queries to clear cached user data
			queryClient.invalidateQueries({ queryKey: ['currentUser'] })
			queryClient.invalidateQueries({ queryKey: ['me_user'] })
		},
		onError: (err: Error) => {
			setError(err.message)
			// Even if logout fails on server, clear local tokens
			localStorage.removeItem('accessToken')
			localStorage.removeItem('refreshToken')
			queryClient.invalidateQueries({ queryKey: ['currentUser'] })
			queryClient.invalidateQueries({ queryKey: ['me_user'] })
		},
	})

	const logoutAllMutation = useMutation<LogoutResponse, Error, void>({
		mutationFn: async () => {
			const accessToken = localStorage.getItem('accessToken')
			
			const res = await fetch('/api/auth/logout-all', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					...(accessToken && { Authorization: `Bearer ${accessToken}` }),
				},
			})

			if (!res.ok) {
				const errorData = await res.json()
				throw new Error(errorData.message || 'Logout from all devices failed')
			}

			return res.json() as Promise<LogoutResponse>
		},
		onSuccess: () => {
			setError(null)
			// Clear tokens from localStorage
			localStorage.removeItem('accessToken')
			localStorage.removeItem('refreshToken')
			// Invalidate all queries to clear cached user data
			queryClient.invalidateQueries({ queryKey: ['currentUser'] })
			queryClient.invalidateQueries({ queryKey: ['me_user'] })
		},
		onError: (err: Error) => {
			setError(err.message)
			// Even if logout fails on server, clear local tokens
			localStorage.removeItem('accessToken')
			localStorage.removeItem('refreshToken')
			queryClient.invalidateQueries({ queryKey: ['currentUser'] })
			queryClient.invalidateQueries({ queryKey: ['me_user'] })
		},
	})

	/**
	 * Logout from current device/session
	 */
	const logout = async (): Promise<LogoutResponse> => {
		return await logoutMutation.mutateAsync()
	}

	/**
	 * Logout from all devices/sessions
	 */
	const logoutAll = async (): Promise<LogoutResponse> => {
		return await logoutAllMutation.mutateAsync()
	}

	return {
		logout,
		logoutAll,
		isLoggingOut: logoutMutation.status === 'pending',
		isLoggingOutAll: logoutAllMutation.status === 'pending',
		error,
		// Expose mutations for direct access if needed
		logoutMutation,
		logoutAllMutation,
	}
}
