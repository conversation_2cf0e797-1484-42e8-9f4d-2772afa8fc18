import { useMutation, type UseMutationOptions } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { tryCatch } from '@/lib/try-catch'

/**
 * Response type for the mint token API endpoint
 */
export type MintTokenResponse = {
	success: boolean
	message: string
	data: {
		mintToken: string
	} | null
}

/**
 * Error type for mint token operations
 */
export type MintTokenError = {
	message: string
	success: false
}

/**
 * Function to fetch a one-time access token for minting operations
 * This token can be used to authenticate with external minting services
 */
const fetchMintToken = async (): Promise<MintTokenResponse> => {
	const { data, error } = await tryCatch(
		ofetch<MintTokenResponse>('/api/nft/mint/token', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
		}),
	)

	if (error) {
		console.error('Error fetching mint token:', error)
		throw new Error(`Error fetching mint token: ${error}`)
	}

	if (!data) {
		throw new Error('No response data returned from mint token API')
	}

	if (!data.success) {
		throw new Error(data.message || 'Failed to generate mint token')
	}

	return data
}

/**
 * Hook for generating one-time access tokens for minting operations
 * 
 * This hook provides a mutation function that creates a minting session
 * and returns a JWT token that can be used to authenticate with external
 * minting services.
 * 
 * @param options - Optional mutation options for customizing behavior
 * @returns Mutation object for generating mint tokens
 * 
 * @example
 * ```tsx
 * const { mutate: getMintToken, isPending, error } = useMintToken();
 * 
 * const handleMint = async () => {
 *   getMintToken(undefined, {
 *     onSuccess: (data) => {
 *       console.log('Mint token:', data.data.mintToken);
 *       // Use the token for minting operations
 *     },
 *     onError: (error) => {
 *       console.error('Failed to get mint token:', error.message);
 *     }
 *   });
 * };
 * ```
 */
export const useMintToken = (
	options?: UseMutationOptions<MintTokenResponse, Error, void>,
) => {
	return useMutation({
		mutationFn: fetchMintToken,
		...options,
	})
}

export default useMintToken
