import { useQuery } from '@tanstack/react-query'
import type { ActivityParams, CollectionActivityResponse } from '@/hooks/useCollectionActivity'

interface UseCollectionActivityWithFallbackProps {
	collectionId: string
	activityFilters: ActivityParams
	initialData?: CollectionActivityResponse
}

export function useCollectionActivityWithFallback({
	collectionId,
	activityFilters,
	initialData,
}: UseCollectionActivityWithFallbackProps) {
	// Check if activity filters match initial state
	const isInitialActivityFilters = 
		activityFilters.activityType === 'ALL' &&
		activityFilters.sortOrder === 'desc' &&
		activityFilters.page === 1 &&
		activityFilters.limit === 10

	// Only fetch client-side activity data when filters change from initial state
	const {
		data: clientActivityData,
		isLoading: activityIsLoading,
		error: activityError,
	} = useQuery<CollectionActivityResponse, Error>({
		queryKey: ['collectionActivity', collectionId, activityFilters],
		queryFn: async () => {
			if (!collectionId) {
				throw new Error('Collection ID is required to fetch activity.')
			}

			const searchParams = new URLSearchParams()
			if (activityFilters) {
				Object.entries(activityFilters).forEach(([key, value]) => {
					if (value !== undefined && value !== null && String(value).trim() !== '') {
						searchParams.append(key, String(value))
					}
				})
			}

			const queryString = searchParams.toString()
			const apiUrl = `/api/collection/${collectionId}/activity${queryString ? `?${queryString}` : ''}`

			const response = await fetch(apiUrl)

			if (!response.ok) {
				let errorData: { message?: string } = {}
				try {
					errorData = await response.json()
				} catch (e) {
					errorData = { message: response.statusText || 'Failed to fetch activity data. Server returned an error.' }
				}
				throw new Error(errorData?.message || `Failed to fetch activity data. Status: ${response.status}`)
			}
			return response.json()
		},
		enabled: !!collectionId && !isInitialActivityFilters, // Only fetch when filters have changed
	})

	// Use server-rendered data for initial filters, client data for filtered results
	const activityData = isInitialActivityFilters ? initialData : clientActivityData

	return {
		data: activityData,
		isLoading: isInitialActivityFilters ? !initialData : activityIsLoading,
		error: activityError,
	}
} 