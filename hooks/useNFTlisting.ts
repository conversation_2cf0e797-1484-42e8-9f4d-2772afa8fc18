import { useQuery } from '@tanstack/react-query'
import type { FetchNftParam, NftListing } from '@/types/nft.types'

/**
 * Fetch NFT listings from the API.
 * @param params - Query parameters for fetching NFTs.
 * @returns Promise resolving to NftListing.
 */
const fetchNFTListing = async (params: FetchNftParam): Promise<NftListing> => {
	const query = new URLSearchParams()
	Object.entries(params).forEach(([key, value]) => {
		if (value !== undefined && value !== null && value !== '') {
			query.append(key, String(value))
		}
	})

	const res = await fetch(`/api/nft?${query.toString()}`)
	if (!res.ok) {
		throw new Error('Failed to fetch NFT listings')
	}
	return res.json()
}

type UseNFTListingOptions = Partial<FetchNftParam>

/**
 * React hook to fetch NFT listings with query parameters.
 * @param options - Query parameters for fetching NFTs.
 * @returns Query result containing data, loading, and error states.
 */
export function useNFTListing(options: UseNFTListingOptions = {}) {
	return useQuery<NftListing, Error>({
		queryKey: ['nft-listing', options],
		queryFn: () => fetchNFTListing(options as FetchNftParam),
	})
}
