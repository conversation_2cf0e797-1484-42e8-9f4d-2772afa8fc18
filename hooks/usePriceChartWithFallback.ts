import { useQuery } from '@tanstack/react-query'
import type { ChartDataItem } from '@/hooks/usePriceChart'

interface UsePriceChartWithFallbackProps {
	collectionId: string
	initialData?: ChartDataItem[]
}

export function usePriceChartWithFallback({
	collectionId,
	initialData,
}: UsePriceChartWithFallbackProps) {
	// Only fetch client-side price chart data if server data is empty (fallback)
	const {
		data: clientPriceChartData,
		isLoading: priceChartIsLoading,
		error: priceChartError,
	} = useQuery<ChartDataItem[], Error>({
		queryKey: ['collectionPriceChart', collectionId],
		queryFn: async () => {
			if (!collectionId) {
				throw new Error('Collection ID is required to fetch price chart.')
			}

			const response = await fetch(`/api/collection/${collectionId}/price-chart`)

			if (!response.ok) {
				let errorData: { message?: string } = {}
				try {
					errorData = await response.json()
				} catch (e) {
					errorData = {
						message: response.statusText || 'Failed to fetch price chart. Server returned an error.',
					}
				}
				throw new Error(errorData?.message || `Failed to fetch price chart. Status: ${response.status}`)
			}
			return response.json()
		},
		enabled: !!collectionId && (!initialData || initialData.length === 0), // Only fetch if no server data
	})

	// Use server-rendered data if available, otherwise client data
	const priceChartData = initialData && initialData.length > 0 
		? initialData 
		: clientPriceChartData

	return {
		data: priceChartData,
		isLoading: initialData && initialData.length > 0 ? false : priceChartIsLoading,
		error: priceChartError,
	}
} 