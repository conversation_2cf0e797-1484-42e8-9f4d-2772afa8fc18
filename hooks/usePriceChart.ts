import { useQuery } from '@tanstack/react-query'

// Type for chart data points
export type ChartDataItem = {
	price: number
	createdAt: Date
}

export function usePriceChart(collectionId?: string) {
	return useQuery<ChartDataItem[], Error>({
		queryKey: ['collectionPriceChart', collectionId],
		queryFn: async () => {
			if (!collectionId) {
				throw new Error('Collection ID is required to fetch price chart.')
			}

			const response = await fetch(
				`/api/collection/${collectionId}/price-chart`,
			)

			if (!response.ok) {
				let errorData: { message?: string } = {}
				try {
					errorData = await response.json()
				} catch (e) {
					// If response is not JSON or empty
					errorData = {
						message:
							response.statusText ||
							'Failed to fetch price chart. Server returned an error.',
					}
				}
				throw new Error(
					errorData?.message ||
						`Failed to fetch price chart. Status: ${response.status}`,
				)
			}
			return response.json()
		},
		enabled: !!collectionId,
	})
}
