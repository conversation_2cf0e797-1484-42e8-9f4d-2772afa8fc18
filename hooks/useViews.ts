import { useQuery } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { useEffect, useRef } from 'react'
import { tryCatch } from '@/lib/try-catch'

/**
 * Hook to update the backend view count for NFTs and collections.
 * This hook uses useQuery to POST to the backend and increment the view count.
 * @param type The type of asset ('nft' or 'collection')
 * @param id The public key of the asset
 * @param enabled Whether the query should run
 * @returns Query object for updating views
 */
export const useUpdateViews = (
	type: 'nft' | 'collection',
	id: string,
	enabled: boolean,
) => {
	return useQuery({
		queryKey: [`${type}_views_update_${id}`],
		queryFn: async () => {
			const { data, error } = await tryCatch(
				ofetch(`/api/${type}/views/${id}`, {
					method: 'POST',
				}),
			)
			if (error) {
				console.error(`Error incrementing ${type} views:`, error)
				throw new Error(`Error incrementing ${type} views: ${error}`)
			}
			return data
		},
		enabled,
		refetchOnWindowFocus: false,
		retry: false,
	})
}

/**
 * Hook to count views for NFTs and collections.
 * Handles localStorage logic and triggers backend update if needed.
 * @param type The type of asset ('nft' or 'collection')
 * @param id The public key of the asset
 * @returns Query object for updating views (or null if not triggered)
 */
export const useCountViews = (type: 'nft' | 'collection', id: string) => {
	// Only trigger backend update if this asset hasn't been viewed in the last 24 hours
	const shouldUpdate = useRef(false)

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		if (!id) return

		// Define a type for the views object in localStorage
		type ViewsObject = {
			[key: string]: number // key: asset id, value: timestamp
		}

		// Get existing views from localStorage
		const views = localStorage.getItem('views')
		let viewsObj: ViewsObject = {}

		if (views) {
			try {
				viewsObj = JSON.parse(views) as ViewsObject
			} catch (error) {
				console.error('Error parsing views from localStorage:', error)
				viewsObj = {}
			}

			// Check if this asset has been viewed recently (within 24 hours)
			if (viewsObj[id]) {
				const timestamp = viewsObj[id]
				const now = new Date().getTime()
				const diff = now - timestamp
				const oneDayInMs = 24 * 60 * 60 * 1000

				if (diff < oneDayInMs) {
					// Asset was viewed recently, don't increment count
					shouldUpdate.current = false
					return
				}
			}
		}

		// Update the timestamp for this asset while preserving other IDs
		viewsObj = {
			...viewsObj,
			[id]: new Date().getTime(),
		}
		// Save updated view timestamps to localStorage
		localStorage.setItem('views', JSON.stringify(viewsObj))

		// Mark that we should update the backend
		shouldUpdate.current = true
	}, [id, type])

	// Use the backend update hook, only enabled if shouldUpdate is true
	const updateResult = useUpdateViews(type, id, shouldUpdate.current && !!id)

	return updateResult
}
