import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import customToast from '@/components/CustomToast'

async function transferNftOwner({
	publicKey,
	ownerWalletAddress,
}: {
	publicKey: string
	ownerWalletAddress: string
}) {
	const response = await fetch('/api/nft/after-transfer', {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify({ publicKey, ownerWalletAddress }),
	})
	if (!response.ok) {
		throw new Error('Failed to transfer NFT')
	}
	return response.json()
}

export function useTransferNftOwner() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: transferNftOwner,
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({
				queryKey: ['nft_base', variables.publicKey],
			})
			customToast.success('NFT ownership transferred')
		},
		onError: (error) => {
			customToast.error('Failed to transfer NFT')
			console.error(error)
		},
	})
}
