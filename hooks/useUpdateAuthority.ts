import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import customToast from '@/components/CustomToast'
import { tryCatch } from '@/lib/try-catch'

/**
 * Request payload for updating collection authority
 */
export type UpdateAuthorityRequest = {
	/** The collection key/identifier */
	collectionKey: string
	/** The new authority public key */
	newAuthority: string
}

/**
 * Response from the update authority API
 */
export type UpdateAuthorityResponse = {
	/** Whether the operation was successful */
	success: boolean
	/** Response message */
	message: string
}

/**
 * Updates the authority of a collection
 * @param data - The update authority request data
 * @returns Promise resolving to the update authority response
 */
const updateAuthority = async (
	data: UpdateAuthorityRequest,
): Promise<UpdateAuthorityResponse> => {
	try {
		const { data: responseData, error } = await tryCatch(
			ofetch<UpdateAuthorityResponse>(
				`${import.meta.env.PUBLIC_ENV__NFT_UPLOAD_URL}/update-authority`,
				{
					method: 'POST',
					body: {
						collectionKey: data.collectionKey,
						newAuthority: data.newAuthority,
					},
				},
			),
		)

		if (error) {
			console.error('Error updating collection authority:', error)
			throw new Error(`Error updating collection authority: ${error}`)
		}

		if (!responseData) {
			throw new Error('No response data returned from API')
		}

		return responseData
	} catch (error) {
		console.error('Error in updateAuthority:', error)
		throw error
	}
}

/**
 * React Query mutation hook for updating collection authority
 *
 * This hook provides a mutation function to update the authority of a collection.
 * It handles success and error states, shows appropriate toast notifications,
 * and invalidates relevant queries on success.
 *
 * @returns TanStack Query mutation object for updating collection authority
 *
 * @example
 * ```tsx
 * const updateAuthorityMutation = useUpdateAuthority()
 *
 * const handleUpdateAuthority = () => {
 *   updateAuthorityMutation.mutate({
 *     collectionKey: 'collection-key-here',
 *     newAuthority: 'new-authority-public-key'
 *   })
 * }
 * ```
 */
export const useUpdateAuthority = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: updateAuthority,
		onSuccess: (data) => {
			// Invalidate relevant queries that might need to be updated
			queryClient.invalidateQueries({ queryKey: ['user_nfts'] })
			queryClient.invalidateQueries({ queryKey: ['collections'] })

			// Show success toast
			customToast.success(`Authority updated successfully: ${data.message}`)

			return data
		},
		onError: (error: Error) => {
			// Show error toast
			customToast.error(`Failed to update authority: ${error.message}`)
			console.error('Update authority error:', error)
		},
	})
}

export default useUpdateAuthority
