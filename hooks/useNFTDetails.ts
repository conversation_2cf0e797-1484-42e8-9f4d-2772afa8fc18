import { type UseQueryOptions, useQuery } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { tryCatch } from '@/lib/try-catch'
import type { NFTDetailResponse } from '@/server/app/nft/nft.route'

// Common fetch function for NFT details
const fetchNftDetail = async (
	publicKey: string,
	tab: string,
	activePage: number,
) => {
	const { data, error } = await tryCatch(
		ofetch<NFTDetailResponse>(`/api/nft/detail/${publicKey}`, {
			method: 'GET',
			query: {
				tab,
				activePage,
			},
		}),
	)

	if (error) {
		throw new Error(`Error fetching NFT detail: ${error}`)
	}

	return data
}

// Tab types
export type NFTDetailTab =
	| 'description'
	| 'priceHistory'
	| 'listing'
	| 'offers'
	| 'itemActivity'

// Define response types for each hook
type BaseResponse = {
	nft: NFTDetailResponse['nft']
	userInfo: NFTDetailResponse['userInfo']
}

type DescriptionResponse = BaseResponse & {
	relatedNFTs: NFTDetailResponse['relatedNFTs']
}

type PriceHistoryResponse = BaseResponse & {
	priceHistory: NFTDetailResponse['priceHistory']
}

type ListingResponse = BaseResponse & {
	listingHistory: NFTDetailResponse['listingHistory']
	pagination: NFTDetailResponse['pagination']
}

type OffersResponse = BaseResponse & {
	offers: NFTDetailResponse['offers']
	pagination: NFTDetailResponse['pagination']
}

type ActivityResponse = BaseResponse & {
	activities: NFTDetailResponse['activities']
	pagination: NFTDetailResponse['pagination']
}

// Base hook for NFT details - fetches the NFT data which is required for all tabs
export const useNFTBase = (
	publicKey: string,
	options?: Omit<
		UseQueryOptions<NFTDetailResponse, Error, BaseResponse>,
		'queryKey' | 'queryFn'
	>,
) => {
	return useQuery<NFTDetailResponse, Error, BaseResponse>({
		queryKey: ['nft_base', publicKey],
		queryFn: () => fetchNftDetail(publicKey, 'description', 1),
		refetchInterval: 60000,
		select: (data) => ({
			nft: data.nft,
			userInfo: data.userInfo,
		}),
		...options,
	})
}

// Description tab hook - fetches NFT details and related NFTs
export const useNFTDescription = (
	publicKey: string,
	options?: Omit<
		UseQueryOptions<NFTDetailResponse, Error, DescriptionResponse>,
		'queryKey' | 'queryFn'
	>,
) => {
	return useQuery<NFTDetailResponse, Error, DescriptionResponse>({
		queryKey: ['nft_description', publicKey],
		queryFn: () => fetchNftDetail(publicKey, 'description', 1),
		refetchInterval: 60000,
		select: (data) => ({
			nft: data.nft,
			relatedNFTs: data.relatedNFTs || [],
			userInfo: data.userInfo,
		}),
		...options,
	})
}

// Price history tab hook
export const useNFTPriceHistory = (
	publicKey: string,
	options?: Omit<
		UseQueryOptions<NFTDetailResponse, Error, PriceHistoryResponse>,
		'queryKey' | 'queryFn'
	>,
) => {
	return useQuery<NFTDetailResponse, Error, PriceHistoryResponse>({
		queryKey: ['nft_price_history', publicKey],
		queryFn: () => fetchNftDetail(publicKey, 'priceHistory', 1),
		refetchInterval: 60000,
		select: (data) => ({
			nft: data.nft,
			priceHistory: data.priceHistory || [],
			userInfo: data.userInfo,
		}),
		...options,
	})
}

// Listing tab hook with pagination
export const useNFTListing = (
	publicKey: string,
	activePage = 1,
	options?: Omit<
		UseQueryOptions<NFTDetailResponse, Error, ListingResponse>,
		'queryKey' | 'queryFn'
	>,
) => {
	return useQuery<NFTDetailResponse, Error, ListingResponse>({
		queryKey: ['nft_listing', publicKey, activePage],
		queryFn: () => fetchNftDetail(publicKey, 'listing', activePage),
		refetchInterval: 60000,
		select: (data) => ({
			nft: data.nft,
			listingHistory: data.listingHistory || [],
			pagination: data.pagination,
			userInfo: data.userInfo,
		}),
		...options,
	})
}

// Offers tab hook with pagination
export const useNFTOffers = (
	publicKey: string,
	activePage = 1,
	options?: Omit<
		UseQueryOptions<NFTDetailResponse, Error, OffersResponse>,
		'queryKey' | 'queryFn'
	>,
) => {
	return useQuery<NFTDetailResponse, Error, OffersResponse>({
		queryKey: ['nft_offers', publicKey, activePage],
		queryFn: () => fetchNftDetail(publicKey, 'offers', activePage),
		refetchInterval: 60000,
		select: (data) => ({
			nft: data.nft,
			offers: data.offers || [],
			pagination: data.pagination,
			userInfo: data.userInfo,
		}),
		...options,
	})
}

// Item activity tab hook with pagination
export const useNFTActivity = (
	publicKey: string,
	activePage = 1,
	options?: Omit<
		UseQueryOptions<NFTDetailResponse, Error, ActivityResponse>,
		'queryKey' | 'queryFn'
	>,
) => {
	return useQuery<NFTDetailResponse, Error, ActivityResponse>({
		queryKey: ['nft_activity', publicKey, activePage],
		queryFn: () => fetchNftDetail(publicKey, 'itemActivity', activePage),
		refetchInterval: 60000,
		select: (data) => ({
			nft: data.nft,
			activities: data.activities || [],
			pagination: data.pagination,
			userInfo: data.userInfo,
		}),
		...options,
	})
}

// Legacy hook for backward compatibility
export const useNFTDetails = ({
	publicKey,
	tab = 'description',
	activePage = 1,
}: {
	publicKey: string
	tab: NFTDetailTab
	activePage: number
}) => {
	return useQuery({
		queryKey: ['nft_detail', publicKey, tab, activePage],
		queryFn: () => fetchNftDetail(publicKey, tab, activePage),
	})
}
