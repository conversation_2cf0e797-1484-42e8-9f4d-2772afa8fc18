import { useMutation, type UseMutationOptions } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { tryCatch } from '@/lib/try-catch'

/**
 * Parameters for minting an NFT
 */
export interface MintNftParams {
	name: string
	description: string
	royaltyBasisPoints: number
	metadataUri: string
	collectionId: string | null
	transactionHash: string
	nftAddress: string
	nftImage: string
}

/**
 * Response type for the mint NFT API endpoint
 */
export type MintNftResponse = {
	success: boolean
	message: string
	data?: {
		nft: {
			id: string
			name: string
			description: string
			publicKey: string
			imageUrl: string
			metadataUrl: string
			royaltyBasisPoints: number
			createdAt: string
			updatedAt: string
			owner: {
				id: string
				publicKey: string
				username?: string
			}
			creator: {
				id: string
				publicKey: string
				username?: string
			}
			collection?: {
				id: string
				name: string
				publicKey: string
			}
			activityLogs: Array<{
				id: string
				type: string
				transactionHash: string
				createdAt: string
			}>
		}
	}
	error?: string
}

/**
 * Error type for mint NFT operations
 */
export type MintNftError = {
	message: string
	success: false
	error?: string
	errors?: Array<{
		code: string
		message: string
		path: string[]
	}>
}

/**
 * Function to mint an NFT via the REST API
 * This function creates an NFT record in the database with all associated metadata
 */
const mintNft = async (params: MintNftParams): Promise<MintNftResponse> => {
	const { data, error } = await tryCatch(
		ofetch<MintNftResponse>('/api/nft/mint', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(params),
		}),
	)

	if (error) {
		console.error('Error minting NFT:', error)
		throw new Error(`Error minting NFT: ${error}`)
	}

	if (!data) {
		throw new Error('No response data returned from mint NFT API')
	}

	if (!data.success) {
		throw new Error(data.message || data.error || 'Failed to mint NFT')
	}

	return data
}

/**
 * Hook for minting NFTs
 * 
 * This hook provides a mutation function that creates an NFT record in the database
 * with all associated metadata, connects it to a collection if specified, and creates
 * an activity log entry for the minting transaction.
 * 
 * @param options - Optional mutation options for customizing behavior
 * @returns Mutation object for minting NFTs
 * 
 * @example
 * ```tsx
 * const { mutate: mintNft, isPending, error } = useMintNft();
 * 
 * const handleMint = async () => {
 *   mintNft({
 *     name: "My NFT",
 *     description: "A beautiful NFT",
 *     royaltyBasisPoints: 500, // 5%
 *     metadataUri: "https://example.com/metadata.json",
 *     collectionId: "collection-id-or-null",
 *     transactionHash: "transaction-hash",
 *     nftAddress: "nft-public-key",
 *     nftImage: "https://example.com/image.png"
 *   }, {
 *     onSuccess: (data) => {
 *       console.log('NFT minted successfully:', data.data.nft);
 *     },
 *     onError: (error) => {
 *       console.error('Failed to mint NFT:', error.message);
 *     }
 *   });
 * };
 * ```
 */
export const useMintNft = (
	options?: UseMutationOptions<MintNftResponse, Error, MintNftParams>,
) => {
	return useMutation({
		mutationFn: mintNft,
		...options,
	})
}

export default useMintNft
