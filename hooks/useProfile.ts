import { useQuery } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { tryCatch } from '@/lib/try-catch'

// Define types for the API response
export type NftFilterType = 'all' | 'fixed-price' | 'auction'

export type Attribute = {
	id?: string
	traitType?: string
	value: string
	trait_type?: string
}

export type Metadata = {
	name: string
	description: string
	image: string
	attributes: Attribute[]
	seller_fee_basis_points: number
}

export type Listing = {
	id?: string
	price?: number
	currency?: string
	status?: string
	listingType?: string
	startTime?: Date
	endTime?: Date | null
	isAuction?: boolean
	isFixedPrice?: boolean
	isActive?: boolean
}

export type UserProfileNft = {
	metadata: Metadata
	name: string
	publicKey: string
	uri: string
	collectionName: string
	listing: Listing
}

export type UserNftsResponse = {
	nfts: UserProfileNft[]
	pagination: {
		currentPage: number
		totalPages: number
		totalItems: number
		itemsPerPage: number
	}
}

export type ProfileNftParams = {
	username: string
	filter?: NftFilterType
	page?: number
	limit?: number
	search?: string
}

/**
 * Fetch user NFTs from the API.
 * @param params - Query parameters for fetching NFTs.
 * @returns Promise resolving to UserNftsResponse.
 */
const fetchUserNfts = async (
	params: ProfileNftParams,
): Promise<UserNftsResponse> => {
	const { username, ...queryParams } = params

	if (!username) {
		throw new Error('Username is required')
	}

	const { data, error } = await tryCatch(
		ofetch<UserNftsResponse>('/api/users/nfts', {
			method: 'GET',
			query: {
				username,
				...queryParams,
			},
		}),
	)

	if (error) {
		throw new Error(`Failed to fetch user NFTs: ${error}`)
	}

	return data
}

/**
 * React hook to fetch user NFTs with filtering and pagination.
 * @param params - Query parameters for fetching NFTs.
 * @returns Query result containing data, loading, and error states.
 */
export function useUserNfts(params: ProfileNftParams) {
	const { username, filter = 'all', page = 1, limit = 20, search } = params

	return useQuery<UserNftsResponse, Error>({
		queryKey: ['user-nfts', username, filter, page, limit, search],
		queryFn: () => fetchUserNfts({ username, filter, page, limit, search }),
		enabled: !!username,
		refetchOnWindowFocus: false,
	})
}

/**
 * React hook to fetch user NFTs with auction listings.
 * @param username - Username of the NFT owner.
 * @param page - Page number for pagination.
 * @param limit - Number of items per page.
 * @returns Query result containing data, loading, and error states.
 */
export function useUserAuctionNfts(username: string, page = 1, limit = 20) {
	return useUserNfts({
		username,
		filter: 'auction',
		page,
		limit,
	})
}

/**
 * React hook to fetch user NFTs with fixed-price listings.
 * @param username - Username of the NFT owner.
 * @param page - Page number for pagination.
 * @param limit - Number of items per page.
 * @returns Query result containing data, loading, and error states.
 */
export function useUserFixedPriceNfts(username: string, page = 1, limit = 20) {
	return useUserNfts({
		username,
		filter: 'fixed-price',
		page,
		limit,
	})
}

/**
 * Example usage:
 *
 * ```tsx
 * import { useUserNfts, useUserAuctionNfts, useUserFixedPriceNfts } from '@/hooks/useProfile'
 * import { useState } from 'react'
 *
 * function UserNFTsComponent({ username }: { username: string }) {
 *   const [page, setPage] = useState(1)
 *   const [filter, setFilter] = useState<'all' | 'auction' | 'fixed-price'>('all')
 *
 *   // Use the appropriate hook based on the filter
 *   const { data, isLoading, error } =
 *     filter === 'auction'
 *       ? useUserAuctionNfts(username, page)
 *       : filter === 'fixed-price'
 *         ? useUserFixedPriceNfts(username, page)
 *         : useUserNfts({ username, page })
 *
 *   if (isLoading) return <div>Loading...</div>
 *   if (error) return <div>Error: {error.message}</div>
 *   if (!data || data.nfts.length === 0) return <div>No NFTs found</div>
 *
 *   return (
 *     <div>
 *       <div className="filter-buttons">
 *         <button onClick={() => setFilter('all')}>All NFTs</button>
 *         <button onClick={() => setFilter('auction')}>Auctions</button>
 *         <button onClick={() => setFilter('fixed-price')}>Fixed Price</button>
 *       </div>
 *
 *       <div className="nft-grid">
 *         {data.nfts.map(nft => (
 *           <div key={nft.publicKey} className="nft-card">
 *             <img src={nft.metadata.image} alt={nft.name} />
 *             <h3>{nft.name}</h3>
 *             {nft.listing.price && (
 *               <p>Price: {nft.listing.price} {nft.listing.currency || 'SOL'}</p>
 *             )}
 *             {nft.listing.isAuction && <span className="badge">Auction</span>}
 *             {nft.listing.isFixedPrice && <span className="badge">Fixed Price</span>}
 *           </div>
 *         ))}
 *       </div>
 *
 *       <div className="pagination">
 *         <button
 *           disabled={page === 1}
 *           onClick={() => setPage(p => Math.max(1, p - 1))}
 *         >
 *           Previous
 *         </button>
 *         <span>Page {page} of {data.pagination.totalPages}</span>
 *         <button
 *           disabled={page >= data.pagination.totalPages}
 *           onClick={() => setPage(p => p + 1)}
 *         >
 *           Next
 *         </button>
 *       </div>
 *     </div>
 *   )
 * }
 * ```
 */
