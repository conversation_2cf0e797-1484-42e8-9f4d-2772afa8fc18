import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import customToast from '@/components/CustomToast'
import { tryCatch } from '@/lib/try-catch'

/**
 * Type definition for the upload and mint request payload
 */
export type UploadMintRequest = {
	index: number
	/** The file to upload (File object) */
	file: File
	/** Title/name of the NFT */
	title: string
	/** Description of the NFT */
	description: string
	/** Metadata attributes for the NFT */
	attributes: Array<{
		trait_type: string
		value: string
	}>
	/** Royalty percentage (0-100) */
	royaltyPercentage: number
	/** Owner's wallet address */
	owner: string
	/** Collection ID or public key (optional) */
	collection?: string
}

/**
 * Type definition for the upload and mint response
 */
export type UploadMintResponse = {
	/** Whether the operation was successful */
	success: boolean
	/** Response message */
	message: string
	/** Index of the minted NFT */
	index: number
	/** Public key of the minted NFT */
	publicKey: string
	/** Transaction hash */
	txHash: string
}

/**
 * Function to upload and mint an NFT
 * @param data The upload and mint request data
 * @returns Promise resolving to the upload and mint response
 */
const uploadAndMintNFT = async (
	data: UploadMintRequest,
): Promise<UploadMintResponse> => {
	try {
		// Create FormData for the upload
		const formData = new FormData()

		// Add file to FormData
		formData.append('file', data.file)

		// Add other form fields
		formData.append('title', data.title)
		formData.append('description', data.description)
		formData.append('attributes', JSON.stringify(data.attributes))
		formData.append('royaltyPercentage', data.royaltyPercentage.toString())
		formData.append('owner', data.owner)
		if (data.collection) {
			formData.append('collection', data.collection)
		}

		// Make the API request
		const { data: responseData, error } = await tryCatch(
			ofetch<UploadMintResponse>(
				`${import.meta.env.PUBLIC_ENV__NFT_UPLOAD_URL}/upload-mint/${data.index}`,
				{
					method: 'POST',
					body: formData,
				},
			),
		)

		if (error) {
			console.error('Error uploading and minting NFT:', error)
			throw new Error(`Error uploading and minting NFT: ${error}`)
		}

		if (!responseData) {
			throw new Error('No response data returned from API')
		}

		return responseData
	} catch (error) {
		console.error('Error in uploadAndMintNFT:', error)
		throw error
	}
}

/**
 * Hook for uploading and minting NFTs
 *
 * @returns Mutation object for uploading and minting NFTs
 *
 * @example
 * ```tsx
 * const { mutate, isPending, error } = useUploadMint();
 *
 * const handleSubmit = (formData) => {
 *   mutate({
 *     file: imageFile,
 *     title: "My NFT",
 *     description: "This is my first NFT",
 *     attributes: [{ trait_type: "Background", value: "Blue" }],
 *     royaltyPercentage: 5,
 *     owner: walletAddress
 *   });
 * };
 * ```
 */
export const useUploadMint = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: uploadAndMintNFT,
		onSuccess: (data) => {
			// Invalidate relevant queries that might need to be updated
			queryClient.invalidateQueries({ queryKey: ['user_nfts'] })

			// Show success toast
			customToast.success(`NFT minted successfully: ${data.message}`)

			return data
		},
		onError: (error: Error) => {
			// Show error toast
			customToast.error(`Failed to mint NFT: ${error.message}`)
			console.error('Upload and mint error:', error)
		},
	})
}

export default useUploadMint
