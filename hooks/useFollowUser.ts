import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import customToast from '@/components/CustomToast'
import { tryCatch } from '../lib/try-catch'

// Check if user is following another user
const checkFollowStatus = async (userId: string) => {
	const { data, error } = await tryCatch(
		fetch(`/api/users/check-follow?userId=${userId}`, {
			method: 'GET',
		}),
	)

	if (error) {
		throw new Error(`Error checking follow status: ${error}`)
	}

	return await data.json()
}

// Check if the collection is liked by the user
const checkCollectionLikeStatus = async (publicKey: string) => {
	const { data, error } = await tryCatch(
		fetch(`/api/collection/isLiked/${publicKey}`, {
			method: 'GET',
		}),
	)

	if (error) {
		throw new Error(`Error checking collection like status: ${error}`)
	}

	return await data.json()
}

// Type for followStatus response
type FollowStatusResponse = {
	isFollowing: boolean
	followersCount: number
	followingCount: number
}

// Follow a user
const followUser = async (followingId: string) => {
	const { data, error } = await tryCatch(
		fetch('/api/users/follow', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ followingId }),
		}),
	)

	if (error) {
		throw new Error(`Error following user: ${error}`)
	}

	return await data.json()
}

// Unfollow a user
const unfollowUser = async (followingId: string) => {
	const { data, error } = await tryCatch(
		fetch('/api/users/unfollow', {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ followingId }),
		}),
	)

	if (error) {
		throw new Error(`Error unfollowing user: ${error}`)
	}

	return await data.json()
}

// Like an NFT
const likeNft = async (publicKey: string) => {
	const { data, error } = await tryCatch(
		fetch('/api/nft/like', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ publicKey }),
		}),
	)

	if (error) {
		throw new Error(`Error liking NFT: ${error}`)
	}

	return await data.json()
}

// Unlike an NFT
const unlikeNft = async (publicKey: string) => {
	const { data, error } = await tryCatch(
		fetch('/api/nft/unlike', {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ publicKey }),
		}),
	)

	if (error) {
		throw new Error(`Error unliking NFT: ${error}`)
	}

	return await data.json()
}

// Like a collection
const likeCollection = async (publicKey: string) => {
	const { data, error } = await tryCatch(
		fetch('/api/collection/like', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ publicKey }),
		}),
	)

	if (error) {
		throw new Error(`Error liking collection: ${error}`)
	}

	return await data.json()
}

// Unlike a collection
const unlikeCollection = async (publicKey: string) => {
	const { data, error } = await tryCatch(
		fetch('/api/collection/unlike', {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ publicKey }),
		}),
	)

	if (error) {
		throw new Error(`Error unliking collection: ${error}`)
	}

	return await data.json()
}

// Fetch followers list
const getFollowers = async ({
	targetUserId,
	page = 1,
	limit = 10,
	search = '',
}: {
	targetUserId: string
	page?: number
	limit?: number
	search?: string
}) => {
	const queryParams = new URLSearchParams({
		targetUserId: targetUserId.toString(),
		page: page.toString(),
		limit: limit.toString(),
		...(search ? { search } : {}),
	})

	const { data, error } = await tryCatch(
		fetch(`/api/users/followers?${queryParams}`, {
			method: 'GET',
		}),
	)

	if (error) {
		throw new Error(`Error fetching followers: ${error}`)
	}

	return await data.json()
}

// Fetch following list
const getFollowing = async ({
	targetUserId,
	page = 1,
	limit = 10,
	search = '',
}: {
	targetUserId: string
	page?: number
	limit?: number
	search?: string
}) => {
	const queryParams = new URLSearchParams({
		targetUserId: targetUserId.toString(),
		page: page.toString(),
		limit: limit.toString(),
		...(search ? { search } : {}),
	})

	const { data, error } = await tryCatch(
		fetch(`/api/users/following?${queryParams}`, {
			method: 'GET',
		}),
	)

	if (error) {
		throw new Error(`Error fetching following: ${error}`)
	}

	return await data.json()
}

export const useFollowStatus = (
	userId: string,
): {
	data: FollowStatusResponse | undefined
	isLoading: boolean
	error: Error | null
} => {
	return useQuery({
		queryKey: ['followStatus', userId],
		queryFn: () => checkFollowStatus(userId),
		enabled: !!userId,
	})
}

export const useFollowers = (
	options = { targetUserId: '', page: 1, limit: 10, search: '' },
) => {
	return useQuery({
		queryKey: ['followers', options],
		queryFn: () => getFollowers(options),
	})
}

export const useFollowing = (
	options = { targetUserId: '', page: 1, limit: 10, search: '' },
) => {
	return useQuery({
		queryKey: ['following', options],
		queryFn: () => getFollowing(options),
	})
}

export const useFollowUser = () => {
	const queryClient = useQueryClient()

	const followMutation = useMutation({
		mutationFn: followUser,
		onSuccess: (_, variables) => {
			// Invalidate and refetch
			queryClient.invalidateQueries({ queryKey: ['followStatus', variables] })
			queryClient.invalidateQueries({ queryKey: ['followers'] })
			queryClient.invalidateQueries({ queryKey: ['following'] })
			customToast('Now following')
		},
		onError: (error) => {
			console.error('Follow error:', error)
			customToast.error('Failed to follow user')
		},
	})

	const unfollowMutation = useMutation({
		mutationFn: unfollowUser,
		onSuccess: (_, variables) => {
			// Invalidate and refetch
			queryClient.invalidateQueries({ queryKey: ['followStatus', variables] })
			queryClient.invalidateQueries({ queryKey: ['followers'] })
			queryClient.invalidateQueries({ queryKey: ['following'] })
			customToast.error('Unfollowed')
		},
		onError: (error) => {
			console.error('Unfollow error:', error)
			customToast.error('Failed to unfollow user')
		},
	})

	const toggleFollow = (userId: string, currentFollowStatus: boolean) => {
		if (currentFollowStatus) {
			unfollowMutation.mutate(userId)
		} else {
			followMutation.mutate(userId)
		}
	}

	return {
		toggleFollow,
		isLoading: followMutation.isPending || unfollowMutation.isPending,
	}
}

// Hook for Collection like functionality
export const useCollectionLikeStatus = (publicKey: string) => {
	return useQuery({
		queryKey: ['collectionLikeStatus', publicKey],
		queryFn: () => checkCollectionLikeStatus(publicKey),
		enabled: !!publicKey,
	})
}

// Hook for NFT like functionality
export const useNftLike = () => {
	const queryClient = useQueryClient()

	const likeMutation = useMutation({
		mutationFn: likeNft,
		onSuccess: (data) => {
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			queryClient.setQueryData(['nft_base', data.publicKey], (oldData: any) => {
				if (!oldData) return oldData

				return {
					...oldData,
					userInfo: {
						...oldData.userInfo,
						isLiked: true,
					},
					nft: {
						...oldData.nft,
						totalLikes: oldData.nft.totalLikes + 1,
					},
				}
			})
			customToast('NFT liked')
		},
		onError: (error) => {
			console.error('Like NFT error:', error)
			customToast.error('Failed to like NFT')
		},
	})

	const unlikeMutation = useMutation({
		mutationFn: unlikeNft,
		onSuccess: (data) => {
			// Invalidate and refetch relevant queries
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			queryClient.setQueryData(['nft_base', data.publicKey], (oldData: any) => {
				if (!oldData) return oldData

				return {
					...oldData,
					userInfo: {
						...oldData.userInfo,
						isLiked: false,
					},
					nft: {
						...oldData.nft,
						totalLikes: oldData.nft.totalLikes - 1,
					},
				}
			})

			customToast('NFT unliked')
		},
		onError: (error) => {
			console.error('Unlike NFT error:', error)
			customToast.error('Failed to unlike NFT')
		},
	})

	const toggleNftLike = (publicKey: string, isLiked: boolean) => {
		if (isLiked) {
			unlikeMutation.mutate(publicKey)
		} else {
			likeMutation.mutate(publicKey)
		}
	}

	return {
		toggleNftLike,
		isLoading: likeMutation.isPending || unlikeMutation.isPending,
	}
}

// Hook for Collection like functionality
export const useCollectionLike = () => {
	const queryClient = useQueryClient()

	const likeMutation = useMutation({
		mutationFn: likeCollection,
		onSuccess: () => {
			// Invalidate and refetch relevant queries
			queryClient.invalidateQueries({ queryKey: ['collectionDetail'] })
			queryClient.invalidateQueries({ queryKey: ['likedCollections'] })
			customToast('Collection liked')
		},
		onError: (error) => {
			console.error('Like collection error:', error)
			customToast.error('Failed to like collection')
		},
	})

	const unlikeMutation = useMutation({
		mutationFn: unlikeCollection,
		onSuccess: () => {
			// Invalidate and refetch relevant queries
			queryClient.invalidateQueries({ queryKey: ['collectionDetail'] })
			queryClient.invalidateQueries({ queryKey: ['likedCollections'] })
			customToast('Collection unliked')
		},
		onError: (error) => {
			console.error('Unlike collection error:', error)
			customToast.error('Failed to unlike collection')
		},
	})

	const toggleCollectionLike = (publicKey: string, isLiked: boolean) => {
		if (isLiked) {
			unlikeMutation.mutate(publicKey)
		} else {
			likeMutation.mutate(publicKey)
		}
	}

	return {
		toggleCollectionLike,
		isLoading: likeMutation.isPending || unlikeMutation.isPending,
	}
}

// Hooks for fetching liked items
export const useLikedNfts = (options = { page: 1, limit: 10, search: '' }) => {
	const queryParams = new URLSearchParams({
		page: options.page.toString(),
		limit: options.limit.toString(),
		...(options.search ? { search: options.search } : {}),
	})

	return useQuery({
		queryKey: ['likedNfts', options],
		queryFn: async () => {
			const { data, error } = await tryCatch(
				fetch(`/api/users/liked-nfts?${queryParams}`, {
					method: 'GET',
				}),
			)
			if (error) throw new Error(`Error fetching liked NFTs: ${error}`)
			return data.json()
		},
	})
}

export const useLikedCollections = (
	options = { page: 1, limit: 10, search: '' },
) => {
	const queryParams = new URLSearchParams({
		page: options.page.toString(),
		limit: options.limit.toString(),
		...(options.search ? { search: options.search } : {}),
	})

	return useQuery({
		queryKey: ['likedCollections', options],
		queryFn: async () => {
			const { data, error } = await tryCatch(
				fetch(`/api/users/liked-collections?${queryParams}`, {
					method: 'GET',
				}),
			)
			if (error) throw new Error(`Error fetching liked collections: ${error}`)
			return await data.json()
		},
	})
}
