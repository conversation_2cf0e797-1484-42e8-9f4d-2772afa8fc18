import { useMutation, type UseMutationOptions } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { tryCatch } from '@/lib/try-catch'

/**
 * Parameters for deploying a collection
 */
export interface DeployCollectionParams {
	name: string
	description: string
	royaltyBasisPoints: number
	logoUrl: string
	bannerUrl: string
	metadataUri: string
	collectionAddress: string
	transactionHash: string
	xUrl?: string
	websiteUrl?: string
	tags?: string[]
}

/**
 * Response type for the deploy collection API endpoint
 */
export type DeployCollectionResponse = {
	success: boolean
	message: string
	data?: {
		collection: {
			id: string
			name: string
			description: string
			publicKey: string
			logoUrl: string
			bannerUrl: string
			metadataUri: string
			royaltyBasisPoints: number
			xUrl?: string | null
			websiteUrl?: string | null
			tags: string[]
			createdAt: string
			updatedAt: string
			owner: {
				id: string
				publicKey: string
				username?: string
			}
			activityLogs: Array<{
				id: string
				type: string
				transactionHash: string
				createdAt: string
			}>
		}
	}
	error?: string
}

/**
 * Error type for deploy collection operations
 */
export type DeployCollectionError = {
	message: string
	success: false
	error?: string
	errors?: Array<{
		code: string
		message: string
		path: string[]
	}>
}

/**
 * Function to deploy a collection via the REST API
 * This function creates a collection record in the database with all associated metadata
 */
const deployCollection = async (params: DeployCollectionParams): Promise<DeployCollectionResponse> => {
	const { data, error } = await tryCatch(
		ofetch<DeployCollectionResponse>('/api/collection/deploy', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(params),
		}),
	)

	if (error) {
		console.error('Error deploying collection:', error)
		throw new Error(`Error deploying collection: ${error}`)
	}

	if (!data) {
		throw new Error('No response data returned from deploy collection API')
	}

	if (!data.success) {
		throw new Error(data.message || data.error || 'Failed to deploy collection')
	}

	return data
}

/**
 * Hook for deploying collections
 * 
 * This hook provides a mutation function that creates a collection record in the database
 * with all associated metadata, connects it to the authenticated user as owner, and creates
 * an activity log entry for the deployment transaction.
 * 
 * @param options - Optional mutation options for customizing behavior
 * @returns Mutation object for deploying collections
 * 
 * @example
 * ```tsx
 * const { mutate: deployCollection, isPending, error } = useDeployCollection();
 * 
 * const handleDeploy = async () => {
 *   deployCollection({
 *     name: "My Collection",
 *     description: "A beautiful collection",
 *     royaltyBasisPoints: 500, // 5%
 *     logoUrl: "https://example.com/logo.png",
 *     bannerUrl: "https://example.com/banner.png",
 *     metadataUri: "https://example.com/metadata.json",
 *     collectionAddress: "collection-public-key",
 *     transactionHash: "transaction-hash",
 *     xUrl: "https://x.com/profile",
 *     websiteUrl: "https://example.com",
 *     tags: ["art", "digital"]
 *   }, {
 *     onSuccess: (data) => {
 *       console.log('Collection deployed successfully:', data.data.collection);
 *     },
 *     onError: (error) => {
 *       console.error('Failed to deploy collection:', error.message);
 *     }
 *   });
 * };
 * ```
 */
export const useDeployCollection = (
	options?: UseMutationOptions<DeployCollectionResponse, Error, DeployCollectionParams>,
) => {
	return useMutation({
		mutationFn: deployCollection,
		...options,
	})
}

export default useDeployCollection
