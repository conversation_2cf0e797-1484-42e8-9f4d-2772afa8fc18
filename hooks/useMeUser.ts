import { type UseQueryOptions, useQuery } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { tryCatch } from '@/lib/try-catch'

/**
 * Type definition for the current user data
 * Matches the response from the /api/users/me endpoint
 */
export type MeUserData = {
	id: string
	publicKey: string
	userName: string | null
	username: string | null
	email: string | null
	bio: string | null
	imageUrl: string | null
	bannerUrl: string | null
	instagramId: string | null
	telegramId: string | null
	twitterId: string | null
	websiteId: string | null
	facebookId: string | null
}

/**
 * Fetch the current authenticated user's data from the API
 * Uses the /api/users/me endpoint which returns user data from the database
 */
export const fetchMeUser = async (): Promise<MeUserData> => {
	const { data, error } = await tryCatch(
		ofetch<MeUserData>('/api/users/me', {
			method: 'GET',
		}),
	)

	if (error) {
		console.error('Error fetching current user data:', error)
		throw new Error(`Error fetching current user data: ${error}`)
	}

	if (!data) {
		throw new Error('No user data returned from API')
	}

	return data
}

/**
 * Hook to fetch and manage the current authenticated user's data
 *
 * @param options - Optional React Query options to customize the query behavior
 * @returns React Query result with the current user data
 *
 * @example
 * ```tsx
 * const { data: user, isLoading, error } = useMeUser();
 *
 * if (isLoading) return <LoadingSpinner />;
 * if (error) return <ErrorMessage error={error} />;
 * if (!user) return <NotLoggedIn />;
 *
 * return <UserProfile user={user} />;
 * ```
 */
export const useMeUser = (options?: UseQueryOptions<MeUserData>) => {
	return useQuery<MeUserData>({
		queryKey: ['me_user'],
		queryFn: fetchMeUser,
		...options,
	})
}
