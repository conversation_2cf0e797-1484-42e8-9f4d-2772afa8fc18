import { type UseQueryOptions, useQuery } from '@tanstack/react-query'
import { tryCatch } from '@/lib/try-catch'

/**
 * Type definition for the current user data from /api/auth/me
 * Matches the response from the auth endpoint
 */
export type MeData = {
	id: string
	publicKey: string
	userName: string | null
	email: string | null
	bio: string | null
	imageUrl: string | null
	bannerUrl: string | null
	instagramId: string | null
	telegramId: string | null
	twitterId: string | null
	websiteId: string | null
	facebookId: string | null
}

/**
 * Fetch the current authenticated user's data from the auth API
 * Uses the /api/auth/me endpoint which requires authentication
 */
export const fetchMe = async (): Promise<MeData> => {
	const accessToken = localStorage.getItem('accessToken')
	
	if (!accessToken) {
		throw new Error('No access token available')
	}

	const { data, error } = await tryCatch(
		fetch('/api/auth/me', {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
			},
		}),
	)

	if (error) {
		console.error('Error fetching current user data:', error)
		throw new Error(`Error fetching current user data: ${error}`)
	}

	if (!data.ok) {
		const errorData = await data.json()
		throw new Error(errorData.message || 'Failed to fetch user data')
	}

	const userData = await data.json()

	if (!userData) {
		throw new Error('No user data returned from API')
	}

	return userData
}

/**
 * Hook to fetch and manage the current authenticated user's data
 * Uses the /api/auth/me endpoint with proper authentication
 *
 * @param options - Optional React Query options to customize the query behavior
 * @returns React Query result with the current user data
 *
 * @example
 * ```tsx
 * const { data: user, isLoading, error } = useMe();
 *
 * if (isLoading) return <LoadingSpinner />;
 * if (error) return <ErrorMessage error={error} />;
 * if (!user) return <NotLoggedIn />;
 *
 * return <UserProfile user={user} />;
 * ```
 */
export const useMe = (options?: UseQueryOptions<MeData>) => {
	return useQuery<MeData>({
		queryKey: ['me'],
		queryFn: fetchMe,
		retry: (failureCount, error) => {
			// Don't retry on authentication errors
			if (error.message.includes('Unauthorized') || error.message.includes('No access token')) {
				return false
			}
			// Retry up to 3 times for other errors
			return failureCount < 3
		},
		staleTime: 5 * 60 * 1000, // 5 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
		...options,
	})
}
