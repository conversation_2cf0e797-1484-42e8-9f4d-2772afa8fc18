<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   version="1.1"
   id="svg1"
   width="165.64291"
   height="165.6436"
   viewBox="0 0 165.64291 165.6436"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs
     id="defs1">
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath26">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-230.7496,-182.4691)"
         id="path26" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath28">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-282.7024,-174.35211)"
         id="path28" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath30">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-168.2172,-174.35211)"
         id="path30" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath32">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-267.34351,-132.66381)"
         id="path32" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath34">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-220.3087,-232.68551)"
         id="path34" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath36">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-235.61701,-174.05841)"
         id="path36" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath38">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-235.61701,-165.8746)"
         id="path38" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath40">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-253.92661,-164.8783)"
         id="path40" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath42">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-206.86651,-178.9636)"
         id="path42" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath44">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-186.38811,-223.70121)"
         id="path44" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath46">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-263.48481,-221.50711)"
         id="path46" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath48">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-263.48481,-221.50711)"
         id="path48" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath50">
      <path
         d="M 0,288 H 432 V 0 H 0 Z"
         transform="translate(-178.02771,-226.00891)"
         id="path50" />
    </clipPath>
  </defs>
  <g
     id="layer-MC0"
     transform="translate(-214.52161,-67.246002)">
    <path
       id="path25"
       d="m 0,0 c -0.025,0 -0.038,0 -0.05,-0.013 -0.265,-0.063 -0.53,-0.126 -0.807,-0.176 -2.207,-0.504 -4.515,-0.769 -6.885,-0.769 -2.371,0 -4.679,0.265 -6.885,0.769 -0.278,0.05 -0.543,0.113 -0.807,0.176 C -15.447,0 -15.46,0 -15.485,0 c -1.551,0.505 -2.837,1.589 -3.606,3.001 2.913,-1.135 6.04,-1.841 9.293,-2.043 0.681,-0.05 1.362,-0.075 2.056,-0.075 0.693,0 1.374,0.025 2.055,0.075 C -2.434,1.16 0.694,1.866 3.606,3.001 2.837,1.589 1.551,0.505 0,0 m 23.505,29.822 c -0.719,0.089 -1.438,0.127 -2.169,0.127 -1.135,0 -2.232,-0.101 -3.304,-0.29 C 16.733,28.978 15.523,28.133 14.426,27.174 14.11,26.897 13.808,26.62 13.505,26.317 12.37,25.182 11.374,23.896 10.554,22.496 9.609,23.064 8.512,23.391 7.326,23.391 H 7.213 c -0.076,0 -0.139,0 -0.215,-0.025 -0.063,0.013 -0.126,0.013 -0.176,0 C 6.368,23.341 5.927,23.253 5.511,23.127 5.7,22.786 5.864,22.446 6.002,22.093 c 0.404,-0.959 0.681,-1.98 0.82,-3.052 0.088,-0.567 0.126,-1.147 0.126,-1.74 0,-1.185 -0.164,-2.32 -0.466,-3.405 -0.19,-0.706 -0.454,-1.387 -0.77,-2.03 0,-0.013 0,-0.025 -0.012,-0.038 C 5.536,11.601 5.372,11.374 5.195,11.16 4.905,10.807 4.603,10.466 4.287,10.151 4.262,10.126 4.224,10.088 4.199,10.063 4.073,9.949 3.947,9.823 3.821,9.71 3.43,9.357 3.014,9.016 2.572,8.726 V 8.714 C 2.32,8.537 2.068,8.373 1.803,8.222 1.059,7.768 0.252,7.402 -0.593,7.125 c 1.375,1.374 2.434,3.064 3.052,4.968 v 0.025 c -0.177,0.227 -0.341,0.442 -0.517,0.656 -0.252,0.29 -0.504,0.567 -0.769,0.832 -0.152,0.152 -0.303,0.303 -0.467,0.442 -2.232,2.017 -5.195,3.253 -8.448,3.253 -3.254,0 -6.217,-1.236 -8.449,-3.253 -0.164,-0.139 -0.315,-0.29 -0.467,-0.442 -0.264,-0.265 -0.517,-0.542 -0.769,-0.832 -0.176,-0.214 -0.34,-0.429 -0.517,-0.668 v -0.013 c 0,-0.013 0,-0.013 0.013,-0.025 0.618,-1.892 1.664,-3.569 3.039,-4.943 -0.845,0.277 -1.652,0.643 -2.396,1.097 -0.013,0 -0.013,0 -0.013,0.012 -0.265,0.152 -0.517,0.316 -0.756,0.48 v 0.012 c -0.442,0.29 -0.858,0.631 -1.249,0.984 -0.126,0.113 -0.252,0.239 -0.378,0.353 -0.025,0.025 -0.063,0.063 -0.088,0.088 -0.316,0.315 -0.618,0.643 -0.896,0.996 -0.176,0.215 -0.353,0.442 -0.517,0.681 -0.012,0.013 -0.012,0.025 -0.012,0.038 -0.315,0.643 -0.58,1.324 -0.769,2.03 v 0.038 c -0.303,1.072 -0.467,2.194 -0.467,3.367 0,0.593 0.038,1.173 0.126,1.74 0.139,1.072 0.416,2.093 0.82,3.052 0.151,0.353 0.302,0.706 0.492,1.046 -0.417,0.114 -0.858,0.202 -1.312,0.227 -0.05,0.013 -0.113,0.013 -0.176,0.013 -0.063,0.012 -0.139,0.012 -0.215,0.012 h -0.101 c -1.198,0 -2.295,-0.327 -3.24,-0.895 -0.82,1.4 -1.816,2.686 -2.951,3.821 -0.303,0.303 -0.605,0.58 -0.921,0.857 -1.097,0.959 -2.307,1.804 -3.606,2.485 -1.072,0.189 -2.169,0.29 -3.304,0.29 -0.731,0 -1.45,-0.038 -2.169,-0.127 -0.025,-0.1 -0.038,-0.201 -0.05,-0.302 0.693,-0.517 1.362,-1.097 1.98,-1.715 0.781,-0.795 1.5,-1.639 2.131,-2.56 0.012,-0.013 0.025,-0.025 0.025,-0.038 2.106,-2.257 3.657,-5.031 4.464,-8.108 0.05,-0.214 0.101,-0.429 0.151,-0.643 0.857,-3.316 2.598,-6.292 4.956,-8.65 0.302,-0.303 0.605,-0.58 0.92,-0.858 0.53,-0.466 1.097,-0.908 1.69,-1.311 l -3.644,-5.322 c 1.084,-0.794 2.206,-1.525 3.392,-2.169 2.106,-1.172 4.35,-2.093 6.721,-2.748 2.711,-0.77 5.561,-1.173 8.512,-1.173 2.95,0 5.813,0.403 8.524,1.185 2.358,0.643 4.602,1.564 6.696,2.736 1.185,0.644 2.32,1.375 3.404,2.182 L 7.238,5.637 c 0.593,0.403 1.16,0.845 1.69,1.311 0.315,0.278 0.618,0.555 0.92,0.858 2.358,2.358 4.099,5.334 4.956,8.65 0.05,0.214 0.101,0.429 0.151,0.643 0.807,3.09 2.371,5.876 4.489,8.146 0.631,0.921 1.35,1.765 2.132,2.56 0.617,0.618 1.286,1.198 1.979,1.715 -0.012,0.101 -0.025,0.202 -0.05,0.302"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,307.66613,140.70787)"
       clip-path="url(#clipPath26)" />
    <path
       id="path27"
       d="m 0,0 c -0.416,-0.946 -1.362,-1.601 -2.459,-1.601 -1.097,0 -2.03,0.655 -2.446,1.601 -0.152,0.328 -0.24,0.706 -0.24,1.097 0,0.391 0.076,0.757 0.227,1.084 0.416,0.946 1.362,1.602 2.459,1.602 1.097,0 2.055,-0.656 2.472,-1.602 C 0.164,1.854 0.24,1.488 0.24,1.097 0.24,0.706 0.151,0.328 0,0 M 2.295,2.181 C 1.803,4.35 -0.139,5.977 -2.459,5.977 -4.767,5.977 -6.708,4.35 -7.2,2.181 h -19.899 v 0.278 c -0.302,7.238 -2.963,13.871 -7.263,19.142 -0.328,-0.732 -0.693,-1.438 -1.097,-2.106 2.005,-2.648 3.594,-5.637 4.666,-8.865 v -0.012 c 0.618,-1.854 1.059,-3.771 1.273,-5.763 0.013,-0.013 0.013,-0.025 0.013,-0.038 0.113,-0.87 0.189,-1.74 0.214,-2.636 0.013,-0.353 0.025,-0.718 0.025,-1.084 0,-0.366 -0.012,-0.731 -0.025,-1.097 -0.025,-0.757 -0.075,-1.501 -0.164,-2.245 v -0.012 l 1.816,-2.636 c 0.24,1.211 0.404,2.459 0.479,3.72 v 0.038 c 0.038,0.378 0.063,0.757 0.063,1.135 L -7.2,0 c 0.492,-2.169 2.433,-3.783 4.741,-3.783 2.32,0 4.262,1.614 4.754,3.783 0.088,0.353 0.126,0.719 0.126,1.097 0,0.378 -0.038,0.731 -0.126,1.084"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,376.93653,151.53053)"
       clip-path="url(#clipPath28)" />
    <path
       id="path29"
       d="m 0,0 c -0.416,-0.946 -1.349,-1.601 -2.446,-1.601 -1.097,0 -2.043,0.655 -2.459,1.601 -0.151,0.328 -0.24,0.706 -0.24,1.097 0,0.391 0.076,0.757 0.227,1.084 0.416,0.946 1.375,1.615 2.472,1.615 1.097,0 2.043,-0.669 2.459,-1.615 C 0.164,1.854 0.24,1.488 0.24,1.097 0.24,0.706 0.151,0.328 0,0 M 29.457,21.601 C 25.169,16.33 22.509,9.697 22.206,2.459 V 2.181 H 2.295 C 1.803,4.35 -0.139,5.977 -2.446,5.977 -4.766,5.977 -6.708,4.35 -7.2,2.181 -7.288,1.828 -7.326,1.475 -7.326,1.097 -7.326,0.719 -7.288,0.353 -7.2,0 c 0.492,-2.169 2.434,-3.783 4.754,-3.783 2.307,0 4.249,1.614 4.741,3.783 h 19.911 c 0,-0.378 0.025,-0.757 0.063,-1.135 v -0.038 c 0.076,-1.261 0.24,-2.497 0.479,-3.707 l 1.803,2.623 v 0.012 c -0.088,0.744 -0.138,1.488 -0.163,2.245 -0.013,0.366 -0.026,0.731 -0.026,1.097 0,0.366 0.013,0.731 0.026,1.084 0.025,0.883 0.088,1.753 0.201,2.611 0.24,2.005 0.681,3.947 1.287,5.8 1.071,3.241 2.66,6.242 4.678,8.903 -0.404,0.668 -0.769,1.374 -1.097,2.106"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,224.2896,151.53053)"
       clip-path="url(#clipPath30)" />
    <path
       id="path31"
       d="m 0,0 c -0.378,-0.643 -0.921,-1.173 -1.551,-1.551 -0.681,-0.404 -1.463,-0.643 -2.308,-0.643 -2.484,0 -4.501,2.017 -4.501,4.502 0,0.844 0.227,1.639 0.643,2.307 0.378,0.643 0.908,1.173 1.551,1.551 0.668,0.416 1.462,0.643 2.307,0.643 2.472,0 4.502,-2.017 4.502,-4.501 C 0.643,1.463 0.416,0.668 0,0 m -41.638,-14.451 c 0,-1.097 -0.656,-2.043 -1.601,-2.459 -0.328,-0.151 -0.706,-0.239 -1.097,-0.239 -0.391,0 -0.757,0.075 -1.085,0.227 -0.946,0.416 -1.614,1.374 -1.614,2.471 0,1.097 0.668,2.043 1.614,2.459 0.328,0.151 0.694,0.227 1.085,0.227 0.391,0 0.769,-0.088 1.097,-0.24 0.945,-0.416 1.601,-1.362 1.601,-2.446 m -43.176,12.257 c -0.845,0 -1.627,0.239 -2.308,0.643 -0.63,0.378 -1.172,0.908 -1.551,1.551 -0.416,0.668 -0.643,1.463 -0.643,2.308 0,2.484 2.03,4.501 4.502,4.501 0.845,0 1.639,-0.227 2.308,-0.643 0.643,-0.378 1.172,-0.908 1.551,-1.551 0.416,-0.668 0.643,-1.463 0.643,-2.307 0,-2.485 -2.018,-4.502 -4.502,-4.502 M -3.859,10.34 c -1.828,0 -3.505,-0.605 -4.854,-1.627 l -11.816,11.816 -0.025,0.025 c -0.656,-0.391 -1.337,-0.756 -2.043,-1.059 -1.185,-0.542 -2.434,-0.958 -3.732,-1.236 l -0.013,-0.013 c -4.767,-3.505 -10.592,-5.661 -16.897,-5.876 -0.366,-0.012 -0.732,-0.025 -1.097,-0.025 -0.366,0 -0.732,0.013 -1.085,0.025 -6.305,0.215 -12.118,2.371 -16.884,5.876 0,0.013 -0.013,0.013 -0.013,0.013 h -0.013 c -1.311,0.278 -2.559,0.694 -3.745,1.236 -0.706,0.315 -1.387,0.668 -2.043,1.059 L -79.959,8.713 c -1.35,1.022 -3.027,1.627 -4.855,1.627 -4.426,0 -8.02,-3.594 -8.02,-8.032 0,-1.816 0.605,-3.493 1.627,-4.843 0.441,-0.592 0.971,-1.109 1.551,-1.551 1.349,-1.034 3.026,-1.639 4.842,-1.639 4.439,0 8.032,3.594 8.032,8.033 0,1.828 -0.605,3.505 -1.626,4.854 l 11.815,11.816 c 5.586,-5.246 13.001,-8.524 21.172,-8.789 V -9.71 c -2.181,-0.491 -3.795,-2.433 -3.795,-4.741 0,-2.32 1.614,-4.262 3.795,-4.754 0.341,-0.088 0.706,-0.126 1.085,-0.126 0.378,0 0.744,0.038 1.097,0.126 2.169,0.492 3.783,2.434 3.783,4.754 0,2.308 -1.614,4.25 -3.783,4.741 v 19.899 c 8.158,0.265 15.586,3.543 21.159,8.789 L -10.264,7.162 c -1.022,-1.349 -1.627,-3.026 -1.627,-4.854 0,-4.439 3.594,-8.033 8.032,-8.033 1.816,0 3.493,0.605 4.843,1.639 0.58,0.442 1.109,0.959 1.551,1.551 1.021,1.35 1.626,3.027 1.626,4.843 0,4.438 -3.594,8.032 -8.02,8.032"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,356.458,207.11493)"
       clip-path="url(#clipPath32)" />
    <path
       id="path33"
       d="M 0,0 C 0,1.097 0.668,2.055 1.614,2.471 1.942,2.623 2.308,2.698 2.698,2.698 3.089,2.698 3.468,2.61 3.796,2.459 4.741,2.043 5.397,1.097 5.397,0 c 0,-1.097 -0.656,-2.043 -1.601,-2.459 -0.328,-0.151 -0.707,-0.24 -1.098,-0.24 -0.39,0 -0.756,0.076 -1.084,0.227 C 0.668,-2.055 0,-1.097 0,0 m 11.538,-25.85 h -0.013 c -0.189,0.05 -0.378,0.113 -0.567,0.151 -0.757,0.214 -1.538,0.391 -2.32,0.517 -1.513,0.29 -3.064,0.467 -4.641,0.53 H 3.796 v 0.29 l -0.013,0.025 H 3.796 V -4.754 C 5.964,-4.25 7.578,-2.308 7.578,0 7.578,2.32 5.964,4.262 3.796,4.754 3.442,4.842 3.077,4.88 2.698,4.88 2.32,4.88 1.955,4.842 1.614,4.754 -0.567,4.262 -2.182,2.32 -2.182,0 c 0,-2.308 1.615,-4.262 3.796,-4.754 V -24.652 H 1.4 c -1.577,-0.063 -3.128,-0.24 -4.641,-0.53 -0.782,-0.126 -1.563,-0.303 -2.32,-0.517 -0.189,-0.038 -0.378,-0.101 -0.567,-0.151 h -0.013 c -0.643,-0.77 -1.122,-1.69 -1.337,-2.711 0.164,0.063 0.328,0.113 0.492,0.164 0.631,0.226 1.286,0.416 1.942,0.592 1.576,0.416 3.203,0.706 4.867,0.858 0.467,0.05 0.934,0.075 1.4,0.101 0.126,0.012 0.265,0.012 0.391,0.012 0.353,0.013 0.719,0.025 1.084,0.025 0.366,0 0.732,-0.012 1.098,-0.025 0.126,0 0.252,0 0.378,-0.012 0.466,-0.026 0.933,-0.051 1.4,-0.101 1.664,-0.152 3.291,-0.442 4.867,-0.858 0.656,-0.176 1.311,-0.366 1.942,-0.592 0.164,-0.051 0.328,-0.101 0.492,-0.164 -0.215,1.021 -0.694,1.941 -1.337,2.711"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,293.74493,73.752667)"
       clip-path="url(#clipPath34)" />
    <path
       id="path35"
       d="m 0,0 v -4.123 l -0.832,-0.707 -9.407,-7.893 -2.371,1.109 -2.371,-1.109 -9.394,7.893 -0.845,0.707 V 0 l 5.801,-2.598 6.809,3.052 6.809,-3.052 z"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,314.156,151.92213)"
       clip-path="url(#clipPath36)" />
    <path
       id="path37"
       d="m 0,0 v -2.85 l -10.252,-8.612 h -4.716 L -25.22,-2.85 V 0 l 10.202,-8.562 2.408,2.03 2.421,-2.03 z"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,314.156,162.83387)"
       clip-path="url(#clipPath38)" />
    <path
       id="path39"
       d="m 0,0 c -2.787,-5.599 -8.285,-9.609 -14.779,-10.34 v 24.425 c 1.211,0.719 2.371,1.513 3.468,2.396 l 2.635,-3.833 c -0.945,-2.396 -2.61,-4.426 -4.716,-5.839 2.358,0.757 4.414,2.182 5.94,4.048 l 0.264,-0.378 2.85,-4.149 c -1.286,-3.606 -4.161,-6.469 -7.793,-7.704 3.947,0.113 7.428,2.042 9.659,4.981 z"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,338.5688,164.16227)"
       clip-path="url(#clipPath40)" />
    <path
       id="path41"
       d="m 0,0 v -24.425 c -6.494,0.731 -11.992,4.754 -14.779,10.352 l 2.472,3.594 c 2.232,-2.938 5.712,-4.867 9.659,-4.981 -3.632,1.236 -6.507,4.098 -7.793,7.705 l 2.85,4.149 0.265,0.378 c 1.525,-1.866 3.581,-3.291 5.939,-4.048 -2.106,1.412 -3.77,3.443 -4.716,5.838 l 2.635,3.834 C -2.371,1.513 -1.211,0.706 0,0"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,275.822,145.38187)"
       clip-path="url(#clipPath42)" />
    <path
       id="path43"
       d="m 0,0 c -0.378,-0.643 -0.908,-1.173 -1.551,-1.551 -0.668,-0.416 -1.463,-0.643 -2.308,-0.643 -2.471,0 -4.501,2.018 -4.501,4.502 0,0.844 0.227,1.639 0.643,2.307 0.378,0.643 0.92,1.173 1.551,1.551 0.681,0.404 1.462,0.643 2.307,0.643 2.484,0 4.502,-2.017 4.502,-4.501 C 0.643,1.463 0.416,0.668 0,0 m 2.547,-2.547 c 1.022,1.349 1.627,3.026 1.627,4.855 0,4.438 -3.594,8.032 -8.033,8.032 -1.815,0 -3.493,-0.605 -4.842,-1.639 -0.58,-0.441 -1.11,-0.959 -1.551,-1.551 -1.021,-1.349 -1.627,-3.027 -1.627,-4.842 0,-4.439 3.594,-8.033 8.02,-8.033 1.829,0 3.506,0.605 4.855,1.627 L 4.83,-7.932 c 0.037,0.013 0.063,0.026 0.1,0.026 0.833,0.1 1.678,0.151 2.611,0.151 h 0.214 z"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,248.51747,85.731733)"
       clip-path="url(#clipPath44)" />
    <path
       id="path45"
       d="M 0,0 C -0.845,0 -1.639,0.227 -2.308,0.643 -2.951,1.021 -3.48,1.551 -3.859,2.194 -4.275,2.862 -4.502,3.657 -4.502,4.502 -4.502,6.986 -2.484,9.003 0,9.003 0.845,9.003 1.627,8.764 2.308,8.36 2.938,7.982 3.48,7.452 3.859,6.809 4.275,6.141 4.502,5.347 4.502,4.502 4.502,2.018 2.472,0 0,0 M 7.982,5.233 C 7.969,5.359 7.957,5.485 7.944,5.599 7.919,5.801 7.881,5.99 7.843,6.179 7.793,6.418 7.73,6.658 7.654,6.885 7.604,7.062 7.541,7.226 7.478,7.377 7.44,7.49 7.402,7.591 7.352,7.692 7.276,7.894 7.175,8.083 7.074,8.272 7.024,8.373 6.973,8.474 6.91,8.562 6.835,8.701 6.746,8.84 6.658,8.966 6.582,9.092 6.482,9.218 6.393,9.344 6.368,9.382 6.33,9.42 6.305,9.457 6.217,9.571 6.141,9.672 6.04,9.773 5.687,10.189 5.271,10.567 4.842,10.895 4.678,11.021 4.502,11.147 4.325,11.261 3.922,11.525 3.506,11.74 3.052,11.916 2.825,12.017 2.598,12.106 2.371,12.169 2.169,12.244 1.955,12.295 1.74,12.333 1.652,12.358 1.576,12.383 1.488,12.383 1.337,12.421 1.185,12.446 1.021,12.459 0.694,12.509 0.353,12.534 0,12.534 c -1.11,0 -2.181,-0.227 -3.14,-0.63 -0.479,-0.202 -0.92,-0.454 -1.349,-0.732 h -0.013 c -2.131,-1.462 -3.53,-3.896 -3.53,-6.67 0,-0.227 0.012,-0.454 0.025,-0.681 0.025,-0.227 0.05,-0.442 0.088,-0.656 0.025,-0.189 0.063,-0.366 0.101,-0.529 0.076,-0.29 0.151,-0.568 0.252,-0.845 0.202,-0.58 0.479,-1.123 0.795,-1.627 0.113,-0.177 0.239,-0.353 0.365,-0.517 l -5.208,-5.208 h 0.215 c 0.92,0 1.765,-0.05 2.597,-0.151 0.038,0 0.076,-0.013 0.114,-0.025 l 3.833,3.833 c 0.164,-0.126 0.328,-0.24 0.492,-0.341 0.05,-0.037 0.088,-0.063 0.139,-0.088 0.138,-0.088 0.29,-0.176 0.441,-0.252 0.189,-0.101 0.378,-0.202 0.58,-0.277 0.101,-0.051 0.202,-0.089 0.315,-0.127 0.152,-0.075 0.316,-0.126 0.492,-0.176 0.215,-0.076 0.442,-0.139 0.668,-0.177 0.177,-0.037 0.366,-0.075 0.568,-0.1 0.139,-0.026 0.277,-0.038 0.416,-0.051 0.24,-0.025 0.492,-0.038 0.744,-0.038 0.832,0 1.627,0.126 2.383,0.366 0.505,0.151 0.971,0.353 1.425,0.605 0.681,0.366 1.299,0.82 1.854,1.375 0.542,0.542 1.021,1.172 1.387,1.853 0.618,1.148 0.971,2.447 0.971,3.834 0,0.252 -0.013,0.492 -0.038,0.731"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,351.31307,88.6572)"
       clip-path="url(#clipPath46)" />
    <path
       id="path47"
       d="M 0,0 C -0.845,0 -1.639,0.227 -2.308,0.643 -2.951,1.021 -3.48,1.551 -3.859,2.194 -4.275,2.862 -4.502,3.657 -4.502,4.502 -4.502,6.986 -2.484,9.003 0,9.003 0.845,9.003 1.627,8.764 2.308,8.36 2.938,7.982 3.48,7.452 3.859,6.809 4.275,6.141 4.502,5.347 4.502,4.502 4.502,2.018 2.472,0 0,0 M 7.982,5.233 C 7.969,5.359 7.957,5.485 7.944,5.599 7.919,5.801 7.881,5.99 7.843,6.179 7.793,6.418 7.73,6.658 7.654,6.885 7.604,7.062 7.541,7.226 7.478,7.377 7.44,7.49 7.402,7.591 7.352,7.692 7.276,7.894 7.175,8.083 7.074,8.272 7.024,8.373 6.973,8.474 6.91,8.562 6.835,8.701 6.746,8.84 6.658,8.966 6.582,9.092 6.482,9.218 6.393,9.344 6.368,9.382 6.33,9.42 6.305,9.457 6.217,9.571 6.141,9.672 6.04,9.773 5.687,10.189 5.271,10.567 4.842,10.895 4.678,11.021 4.502,11.147 4.325,11.261 3.922,11.525 3.506,11.74 3.052,11.916 2.825,12.017 2.598,12.106 2.371,12.169 2.169,12.244 1.955,12.295 1.74,12.333 1.652,12.358 1.576,12.383 1.488,12.383 1.337,12.421 1.185,12.446 1.021,12.459 0.694,12.509 0.353,12.534 0,12.534 c -1.11,0 -2.181,-0.227 -3.14,-0.63 -0.479,-0.202 -0.933,-0.442 -1.349,-0.732 0,-0.012 -0.013,0 -0.013,0 -2.143,-1.45 -3.543,-3.896 -3.543,-6.67 0,-0.227 0.013,-0.454 0.038,-0.681 0.025,-0.227 0.05,-0.442 0.088,-0.656 0.025,-0.189 0.063,-0.366 0.101,-0.529 0.076,-0.29 0.151,-0.568 0.252,-0.845 0.189,-0.58 0.467,-1.123 0.795,-1.627 0.113,-0.177 0.239,-0.353 0.365,-0.517 0.442,-0.593 0.959,-1.11 1.551,-1.551 0.164,-0.126 0.328,-0.24 0.492,-0.341 0.05,-0.037 0.088,-0.063 0.139,-0.088 0.138,-0.088 0.29,-0.176 0.441,-0.252 0.189,-0.101 0.378,-0.202 0.58,-0.277 0.101,-0.051 0.202,-0.089 0.315,-0.127 0.152,-0.075 0.316,-0.126 0.492,-0.176 0.215,-0.076 0.442,-0.139 0.668,-0.177 0.177,-0.037 0.366,-0.075 0.568,-0.1 0.139,-0.026 0.277,-0.038 0.416,-0.051 0.24,-0.025 0.492,-0.038 0.744,-0.038 0.832,0 1.627,0.126 2.383,0.366 0.505,0.151 0.971,0.353 1.425,0.605 0.681,0.366 1.299,0.82 1.854,1.375 0.542,0.555 1.009,1.172 1.387,1.853 0.618,1.148 0.971,2.447 0.971,3.834 0,0.252 -0.013,0.492 -0.038,0.731"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,351.31307,88.6572)"
       clip-path="url(#clipPath48)" />
    <path
       id="path49"
       d="M 0,0 C 0,0.845 0.227,1.639 0.643,2.308 1.021,2.951 1.564,3.48 2.194,3.859 2.875,4.262 3.657,4.502 4.502,4.502 6.986,4.502 9.003,2.484 9.003,0 9.003,-0.845 8.776,-1.639 8.36,-2.308 7.982,-2.951 7.452,-3.48 6.809,-3.859 6.141,-4.275 5.347,-4.502 4.502,-4.502 2.03,-4.502 0,-2.484 0,0 m -3.518,0 c 0,-1.387 0.353,-2.686 0.971,-3.833 0.378,-0.681 0.845,-1.299 1.387,-1.854 0.555,-0.555 1.173,-1.009 1.854,-1.374 0.453,-0.253 0.92,-0.454 1.424,-0.606 0.757,-0.239 1.551,-0.365 2.384,-0.365 0.252,0 0.504,0.012 0.744,0.037 0.138,0.013 0.277,0.026 0.416,0.051 0.202,0.025 0.391,0.063 0.567,0.101 0.227,0.037 0.454,0.101 0.669,0.176 0.176,0.051 0.34,0.101 0.491,0.177 0.114,0.038 0.215,0.075 0.316,0.126 0.201,0.076 0.391,0.176 0.58,0.277 0.151,0.076 0.302,0.164 0.441,0.252 0.05,0.026 0.088,0.051 0.139,0.089 0.164,0.101 0.328,0.214 0.492,0.34 0.592,0.442 1.109,0.959 1.551,1.551 0.126,0.164 0.252,0.341 0.365,0.517 0.328,0.505 0.605,1.047 0.795,1.627 0.1,0.277 0.176,0.555 0.252,0.845 0.038,0.164 0.075,0.34 0.101,0.529 0.038,0.215 0.063,0.429 0.088,0.656 0.025,0.227 0.038,0.454 0.038,0.681 0,2.774 -1.4,5.221 -3.544,6.671 0,0 -0.012,-0.013 -0.012,0 C 8.575,6.961 8.121,7.2 7.642,7.402 6.683,7.806 5.611,8.033 4.502,8.033 4.149,8.033 3.808,8.007 3.48,7.957 3.316,7.944 3.165,7.919 3.014,7.881 2.926,7.881 2.85,7.856 2.762,7.831 2.547,7.793 2.333,7.742 2.131,7.667 1.904,7.604 1.677,7.516 1.45,7.415 0.996,7.238 0.58,7.024 0.176,6.759 0,6.645 -0.177,6.519 -0.34,6.393 -0.769,6.065 -1.185,5.687 -1.538,5.271 -1.639,5.17 -1.715,5.069 -1.803,4.956 -1.828,4.918 -1.866,4.88 -1.891,4.842 -1.98,4.716 -2.081,4.59 -2.156,4.464 -2.245,4.338 -2.333,4.199 -2.409,4.06 -2.472,3.972 -2.522,3.871 -2.572,3.77 -2.673,3.581 -2.774,3.392 -2.85,3.19 -2.9,3.089 -2.938,2.989 -2.976,2.875 -3.039,2.724 -3.102,2.56 -3.152,2.383 -3.228,2.156 -3.291,1.917 -3.342,1.677 -3.379,1.488 -3.417,1.299 -3.443,1.097 -3.455,0.984 -3.468,0.857 -3.48,0.731 -3.506,0.492 -3.518,0.252 -3.518,0"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3333333,0,0,-1.3333333,237.37027,82.6548)"
       clip-path="url(#clipPath50)" />
  </g>
</svg>
