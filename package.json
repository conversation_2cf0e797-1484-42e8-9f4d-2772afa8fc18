{"name": "nft-marketplace", "version": "0.1.0", "private": true, "packageManager": "bun@1.2.5", "scripts": {"dev-pro": "bun ./dev-server/index.ts", "dev": "vike dev", "build": "vike build", "lint": "biome lint --write .", "format": "biome format --write .", "analyze": "bunx analyze", "prisma:studio": "prisma studio", "prisma:migrate": "bunx prisma migrate dev --name 01 && bun install && bun run prisma:deploy", "prisma:generate": "prisma generate --no-engine", "prisma:deploy": "prisma migrate deploy && prisma generate --no-engine", "prisma:refresh-metrics": "bun ./scripts/refresh-metrics.ts", "shadcn": "npx shadcn@latest", "preview": "vike build && wrangler dev", "deploy": "vike build && wrangler deploy", "postinstall": "bunx lefthook install"}, "dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@aws-sdk/s3-request-presigner": "^3.782.0", "@codama/nodes-from-anchor": "^1.1.11", "@codama/renderers": "^1.0.19", "@codama/renderers-js-umi": "^1.1.12", "@codama/visitors-core": "^1.2.11", "@coral-xyz/anchor": "^0.30.1", "@hono/node-server": "^1.14.0", "@hono/swagger-ui": "^0.5.1", "@hookform/resolvers": "^4.1.3", "@metaplex-foundation/digital-asset-standard-api": "^1.0.6", "@metaplex-foundation/mpl-core": "^1.3.0", "@metaplex-foundation/mpl-core-das": "^0.0.3", "@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@metaplex-foundation/umi": "^1.1.1", "@metaplex-foundation/umi-bundle-defaults": "^1.1.1", "@metaplex-foundation/umi-signer-wallet-adapters": "^1.1.1", "@prisma/client": "^6.6.0", "@prisma/extension-accelerate": "^1.3.0", "@prisma/extension-optimize": "^1.1.8", "@prisma/instrumentation": "^6.6.0", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.3", "@sentry/react": "^9.8.0", "@solana/kit": "^2.1.0", "@solana/wallet-adapter-base": "^0.9.24", "@solana/wallet-adapter-react": "^0.15.36", "@solana/wallet-adapter-react-ui": "^0.9.36", "@solana/wallet-adapter-wallets": "^0.19.33", "@solana/web3.js": "^1.98.0", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.75.0", "@universal-middleware/core": "^0.4.7", "@universal-middleware/hono": "^0.4.9", "@unpic/react": "^1.0.1", "@vitejs/plugin-react": "^4.3.4", "bs58": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codama": "^1.2.11", "consola": "^3.4.2", "crypto-browserify": "^3.12.1", "dayjs": "^1.11.13", "framer-motion": "^12.7.4", "hono": "^4.7.6", "install": "^0.13.0", "lefthook": "^1.11.8", "jwt-decode": "^4.0.0", "lucide-react": "^0.486.0", "next-themes": "^0.4.6", "ofetch": "^1.4.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "prisma-client": "file:./prisma/client", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-share": "^5.2.2", "readable-stream": "^4.7.0", "recharts": "^2.15.2", "sonner": "^2.0.3", "stream-browserify": "^3.0.0", "swiper": "^11.2.6", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "telefunc": "^0.2.3", "usehooks-ts": "^3.1.1", "vike": "0.4.227", "vike-cloudflare": "^0.1.7", "vike-react": "^0.6.1", "vite-plugin-mcp": "^0.0.3", "vite-plugin-node-polyfills": "^0.23.0", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "^2.0.0-beta.1", "@cloudflare/workers-types": "^4.20250410.0", "@hono/vite-dev-server": "^0.19.0", "@sentry/vite-plugin": "^3.3.1", "@solana/spl-token": "^0.3.8", "@types/bn.js": "^5.1.0", "@types/chai": "^4.3.0", "@types/mocha": "^9.0.0", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "chai": "^4.3.4", "compression": "^1.8.0", "express": "^4.21.2", "mocha": "^9.0.3", "node-fetch": "^3.3.2", "npm-run-all2": "^7.0.2", "prisma": "^6.6.0", "tailwindcss": "^4.1.3", "ts-mocha": "^10.0.0", "typescript": "^5.8.3", "vite": "^6.2.5", "vite-bundle-analyzer": "^0.18.1", "vite-plugin-svgr": "^4.3.0", "wrangler": "^4.9.1"}, "type": "module", "prisma": {"schema": "./prisma/schema"}}