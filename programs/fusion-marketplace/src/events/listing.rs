//! Listing events

use crate::ListingType;
use anchor_lang::prelude::*;

/// Event emitted when a new listing is created
#[event]
pub struct ListingCreatedEvent {
    /// Seller of the NFT
    pub seller: Pubkey,

    /// NFT asset being sold
    pub asset: Pubkey,

    /// Listing account
    pub listing: Pubkey,

    /// Price in lamports
    pub price: u64,

    /// TODO: Create as enum
    /// Listing type (fixed price, auction)
    pub listing_type: ListingType, // 0 = FixedPrice, 1 = Auction

    /// When the listing was created
    pub created_at: i64,

    /// When the listing ends
    pub ends_at: i64,
}

/// Event emitted when a listing is cancelled
#[event]
pub struct ListingCancelledEvent {
    /// Seller of the NFT
    pub seller: Pubkey,

    /// NFT asset being sold
    pub asset: Pubkey,

    /// Listing account
    pub listing: Pubkey,

    /// When the listing was cancelled
    pub cancelled_at: i64,
}

/// Event emitted when a listing is purchased
#[event]
pub struct ListingPurchasedEvent {
    /// Seller of the NFT
    pub seller: Pubkey,

    /// Buyer of the NFT
    pub buyer: Pubkey,

    /// NFT asset being sold
    pub asset: Pubkey,

    /// Listing account
    pub listing: Pubkey,

    /// Price paid in lamports
    pub price: u64,

    /// Protocol fee amount
    pub protocol_fee: u64,

    /// Royalty fee distributed
    pub royalty_payment: u64,

    /// When the purchase occurred
    pub purchased_at: i64,
}

/// Event emitted when a bid is placed on an auction
#[event]
pub struct BidPlacedEvent {
    /// Listing account
    pub listing: Pubkey,

    /// Auction account
    pub auction: Pubkey,

    /// Bidder who placed the bid
    pub bidder: Pubkey,

    /// Bid amount in lamports
    pub bid_amount: u64,

    /// When the bid was placed
    pub timestamp: i64,
}
