//! Events for the offer system

use anchor_lang::prelude::*;

/// Event emitted when an offer is created
#[event]
pub struct OfferCreatedEvent {
    /// The offer account public key
    pub offer: Pubkey,

    /// The buyer who made the offer
    pub buyer: Pubkey,

    /// The asset the offer is for
    pub asset: Pubkey,

    /// The price offered in lamports
    pub price: u64,

    /// Timestamp when the offer expires
    pub expires_at: i64,

    /// Timestamp when the offer was created
    pub created_at: i64,
}

/// Event emitted when an offer is accepted
#[event]
pub struct OfferAcceptedEvent {
    /// The offer account public key
    pub offer: Pubkey,

    /// The buyer who made the offer
    pub buyer: Pubkey,

    /// The seller who accepted the offer
    pub seller: Pubkey,

    /// The asset being sold
    pub asset: Pubkey,

    /// The price in lamports
    pub price: u64,

    /// Protocol fee collected
    pub protocol_fee: u64,

    /// Royalty fee distributed
    pub royalty_payment: u64,

    /// Timestamp when the offer was accepted
    pub accepted_at: i64,
}

/// Event emitted when an offer is cancelled
#[event]
pub struct OfferCancelledEvent {
    /// The offer account public key
    pub offer: Pubkey,

    /// The buyer who cancelled the offer
    pub buyer: Pub<PERSON>,

    /// The asset the offer was for
    pub asset: Pubkey,

    /// Timestamp when the offer was cancelled
    pub cancelled_at: i64,
}
