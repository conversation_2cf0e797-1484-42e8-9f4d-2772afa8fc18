pub mod constants;
pub mod contexts;
pub mod errors;
pub mod events;
pub mod instructions;
pub mod mpl_plugin_management;
pub mod state;

use anchor_lang::prelude::*;

pub use constants::*;
pub use contexts::*;
pub use errors::*;
pub use events::*;
pub use instructions::*;
pub use state::*;

#[cfg(feature = "test-utils")]
use anchor_test_api::test_program;

declare_id!("CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8");

#[program]
#[cfg_attr(feature = "test-utils", test_program)]
pub mod fusion_marketplace {
    use super::*;

    /// Initialize the marketplace with configuration
    pub fn initialize(ctx: Context<Initialize>, config: MarketplaceConfigArgs) -> Result<()> {
        ctx.accounts.handler(config, &ctx.bumps)
    }

    /// Create a fixed price listing for an NFT
    pub fn create_fixed_price_listing(
        ctx: Context<CreateFixedPriceListing>,
        price: u64,
        duration: u64,
    ) -> Result<()> {
        ctx.accounts.handler(price, duration, &ctx.bumps)
    }

    /// Create an auction listing for an NFT
    pub fn create_auction_listing(
        ctx: Context<CreateAuctionListing>,
        start_price: u64,
        start_delay_seconds: u64,
        duration: u64,
        auction_type: AuctionType,
        extension_period: u64,
    ) -> Result<()> {
        ctx.accounts.handler(
            start_price,
            start_delay_seconds,
            duration,
            auction_type,
            extension_period,
            &ctx.bumps,
        )
    }

    /// Cancel fixed price listing
    pub fn cancel_fixed_price_listing(ctx: Context<CancelFixedPriceListing>) -> Result<()> {
        ctx.accounts.handler()
    }

    /// Cancel auction listing
    pub fn cancel_auction_listing(ctx: Context<CancelAuctionListing>) -> Result<()> {
        ctx.accounts.handler()
    }

    /// Purchase a fixed price listing
    pub fn purchase_listing<'a, 'b, 'c, 'info>(
        ctx: Context<'a, 'b, 'c, 'info, PurchaseListing<'info>>,
    ) -> Result<()> {
        ctx.accounts.handler(ctx.remaining_accounts)
    }

    /// Place bidding on an auction listing
    pub fn place_bid(ctx: Context<PlaceBid>, bid_amount: u64) -> Result<()> {
        ctx.accounts.handler(bid_amount)
    }

    /// Claim auction of a completed auction listing
    pub fn claim_auction<'a, 'b, 'c, 'info>(
        ctx: Context<'a, 'b, 'c, 'info, ClaimAuction<'info>>,
    ) -> Result<()> {
        ctx.accounts.handler(ctx.remaining_accounts)
    }

    /// Create an offer on a specific NFT
    pub fn create_offer(ctx: Context<CreateOffer>, price: u64, expires_in: i64) -> Result<()> {
        ctx.accounts.handler(price, expires_in, &ctx.bumps)
    }

    /// Accept an offer on a specific NFT
    pub fn accept_offer<'a, 'b, 'c, 'info>(
        ctx: Context<'a, 'b, 'c, 'info, AcceptOffer<'info>>,
    ) -> Result<()> {
        ctx.accounts.handler(ctx.remaining_accounts)
    }

    /// Cancel an existing offer
    pub fn cancel_offer(ctx: Context<CancelOffer>) -> Result<()> {
        ctx.accounts.handler()
    }
}
