//! Error types for the Fusion Marketplace program

use anchor_lang::prelude::*;

#[error_code]
pub enum ErrorCode {
    #[msg("Invalid marketplace configuration")]
    InvalidMarketplaceConfig,

    #[msg("Invalid listing price")]
    InvalidListingPrice,

    #[msg("Invalid listing duration")]
    InvalidListingDuration,

    #[msg("Invalid auction configuration")]
    InvalidAuctionConfig,

    #[msg("Listing is not active")]
    ListingNotActive,

    #[msg("Auction has not ended")]
    AuctionNotEnded,

    #[msg("Auction has already ended")]
    AuctionAlreadyEnded,

    #[msg("Bid too low")]
    BidTooLow,

    #[msg("Offer expired")]
    OfferExpired,

    #[msg("Offer already filled")]
    OfferAlreadyFilled,

    #[msg("Offer already cancelled")]
    OfferAlreadyCancelled,

    #[msg("Unauthorized")]
    Unauthorized,

    #[msg("Invalid royalty percentage")]
    InvalidRoyaltyPercentage,

    #[msg("Invalid template")]
    InvalidTemplate,

    #[msg("Insufficient funds")]
    InsufficientFunds,

    #[msg("Invalid fee percentage")]
    InvalidFeePercentage,

    #[msg("Not the asset owner")]
    NotAssetOwner,

    #[msg("Insufficient token balance")]
    InsufficientTokenBalance,

    #[msg("Invalid price")]
    InvalidPrice,

    #[msg("Listing duration too short")]
    ListingDurationTooShort,

    #[msg("Listing duration too long")]
    ListingDurationTooLong,

    #[msg("Invalid asset")]
    InvalidAsset,

    #[msg("Invalid listing type")]
    InvalidListingType,

    #[msg("Listing expired")]
    ListingExpired,

    #[msg("Asset is already listed")]
    AssetAlreadyListed,

    #[msg("Invalid fee collector")]
    InvalidFeeCollector,

    #[msg("Asset is already frozen")]
    AssetAlreadyFrozen,

    #[msg("Asset is frozen by an external authority")]
    AssetFrozenByExternalAuthority,

    #[msg("Asset is delegated to an external authority")]
    AssetDelegatedToExternalAuthority,

    #[msg("Unsupported plugin type")]
    UnsupportedPluginType,

    #[msg("Invalid seller account provided")]
    InvalidSeller,

    #[msg("Invalid auction state account provided")]
    InvalidAuctionState,

    #[msg("Auction has not started yet")]
    AuctionNotStarted,

    #[msg("Auction has already ended")]
    AuctionEnded,

    #[msg("Reserve price not met")]
    ReserveNotMet,

    #[msg("Invalid bidder account provided")]
    InvalidBidder,

    #[msg("Invalid buyer account provided")]
    InvalidBuyer,

    #[msg("Overflow occured during arithmetic operation")]
    ArithmeticOverflow,

    #[msg("Invalid creator account provided")]
    InvalidCreatorAccount,
}
