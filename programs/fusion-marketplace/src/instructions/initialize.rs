//! Initialize the marketplace

use crate::{
    contexts::{Initialize, InitializeBumps},
    errors::ErrorCode,
    events::MarketplaceInitializedEvent,
    state::{MarketplaceConfig, MarketplaceConfigArgs},
};
use anchor_lang::prelude::*;

/// Initialize the marketplace with configuration
impl<'info> Initialize<'info> {
    pub fn handler(
        &mut self,
        config: MarketplaceConfigArgs,
        bumps: &InitializeBumps,
    ) -> Result<()> {
        require!(
            config.protocol_fee_bp <= 10000,
            ErrorCode::InvalidFeePercentage
        );

        require!(
            config.min_listing_duration <= config.max_listing_duration,
            ErrorCode::InvalidListingDuration
        );

        // Initialize marketplace configuration
        let marketplace_config = MarketplaceConfig {
            version: 1,
            admin: self.admin.key(),
            fee_collector: self.fee_collector.key(),
            protocol_fee_bp: config.protocol_fee_bp,
            min_bid_increment_bp: config.min_bid_increment_bp,
            min_listing_duration: config.min_listing_duration,
            max_listing_duration: config.max_listing_duration,
            bump: bumps.marketplace_config,
            marketplace_authority_bump: bumps.marketplace_authority,
        };

        self.marketplace_config
            .set_inner(marketplace_config.clone());

        emit!(MarketplaceInitializedEvent {
            admin: marketplace_config.admin,
            fee_collector: marketplace_config.fee_collector,
            protocol_fee_bp: marketplace_config.protocol_fee_bp,
        });

        Ok(())
    }
}
