use crate::{
    constants::get_marketplace_authority_pda, contexts::CreateAuctionListing, errors::ErrorCode,
    events::ListingCreatedEvent, mpl_plugin_management::add_plugin_with_authority,
    state::ListingType, AuctionType, CreateAuctionListingBumps,
};
use anchor_lang::prelude::*;
use mpl_core::{
    instructions::AddPluginV1CpiBuilder,
    types::{FreezeDelegate, Plugin, PluginType, TransferDelegate},
};

/// Create an auction listing for an NFT
impl<'info> CreateAuctionListing<'info> {
    pub fn handler(
        &mut self,
        start_price: u64,
        start_delay_seconds: u64,
        duration: u64,
        auction_type: AuctionType,
        extension_period: u64,
        bumps: &CreateAuctionListingBumps,
    ) -> Result<()> {
        // Validate inputs
        require!(start_price > 0, ErrorCode::InvalidPrice);
        require!(
            duration >= self.marketplace_config.min_listing_duration,
            ErrorCode::ListingDurationTooShort
        );
        require!(
            duration <= self.marketplace_config.max_listing_duration,
            ErrorCode::ListingDurationTooLong
        );

        // Get current timestamp
        let current_time = Clock::get()?.unix_timestamp;

        // Calculate auction start and end times
        let start_time = current_time
            .checked_add(start_delay_seconds as i64)
            .unwrap();
        let end_time = start_time.checked_add(duration as i64).unwrap();

        let authority_seeds = &[
            crate::constants::MARKETPLACE_AUTHORITY_SEED,
            &[self.marketplace_config.marketplace_authority_bump],
        ];

        // Add FreezeDelegate plugin and set authority
        add_plugin_with_authority(
            &self.asset.to_account_info(),
            self.collection
                .as_ref()
                .map(|collection| collection.to_account_info())
                .as_ref(),
            &self.seller,
            &self.system_program,
            &self.mpl_core_program,
            Plugin::FreezeDelegate(FreezeDelegate { frozen: true }),
            PluginType::FreezeDelegate,
            &self.marketplace_authority,
            &[authority_seeds],
        )?;

        // Add TransferDelegate plugin and set authority
        add_plugin_with_authority(
            &self.asset.to_account_info(),
            self.collection
                .as_ref()
                .map(|collection| collection.to_account_info())
                .as_ref(),
            &self.seller,
            &self.system_program,
            &self.mpl_core_program,
            Plugin::TransferDelegate(TransferDelegate {}),
            PluginType::TransferDelegate,
            &self.marketplace_authority,
            &[authority_seeds],
        )?;

        // Initialize the listing account
        self.listing.version = 1;
        self.listing.listing_type = ListingType::Auction(self.auction.key());
        self.listing.seller = self.seller.key();
        self.listing.asset = self.asset.key();
        self.listing.price = start_price; // Starting price
        self.listing.currency = None; // SOL only for now
        self.listing.is_active = true;
        self.listing.created_at = current_time;
        self.listing.updated_at = current_time;
        self.listing.ends_at = Some(end_time);
        self.listing.bump = bumps.listing;

        // Initialize the auction state account
        self.auction.listing = self.listing.key();
        self.auction.auction_type = auction_type;
        self.auction.highest_bid = 0;
        self.auction.highest_bidder = None;
        self.auction.bids_count = 0;
        self.auction.start_time = start_time;
        self.auction.end_time = end_time;
        self.auction.extension_period = extension_period;
        self.auction.escrow_bump = bumps.escrow;
        self.auction.bump = bumps.auction;

        // Emit listing created event
        emit!(ListingCreatedEvent {
            seller: self.seller.key(),
            asset: self.asset.key(),
            listing: self.listing.key(),
            price: start_price,
            listing_type: self.listing.listing_type,
            created_at: current_time,
            ends_at: end_time,
        });

        Ok(())
    }
}
