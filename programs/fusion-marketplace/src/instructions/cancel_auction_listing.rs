use crate::{
    constants::get_marketplace_authority_pda, contexts::CancelAuctionListing, errors::ErrorCode,
    events::ListingCancelledEvent,
};
use anchor_lang::prelude::*;
use mpl_core::{
    instructions::{RemovePluginV1CpiBuilder, UpdatePluginV1CpiBuilder},
    types::{FreezeDelegate, Plugin, PluginType, TransferDelegate},
};

/// Cancel an auction listing
impl<'info> CancelAuctionListing<'info> {
    pub fn handler(&mut self) -> Result<()> {
        // Verify the listing is active
        require!(self.listing.is_active, ErrorCode::ListingNotActive);

        // Get current timestamp
        let current_time = Clock::get()?.unix_timestamp;

        // If there's a highest bidder, refund them
        if let Some(bidder) = &self.auction.highest_bidder {
            if self.auction.highest_bid > 0 {
                // Verify the provided highest_bidder account matches the one in auction state
                if let Some(highest_bidder_info) = &self.highest_bidder {
                    require!(
                        highest_bidder_info.key() == *bidder,
                        ErrorCode::InvalidBidder
                    );

                    // Transfer the bid amount from escrow back to the highest bidder
                    let listing_key = self.listing.key();
                    let escrow_bump = self.auction.escrow_bump;
                    let escrow_seeds = &[
                        crate::constants::ESCROW_PAYMENT_SEED,
                        listing_key.as_ref(),
                        &[escrow_bump],
                    ];
                    let escrow_signer = &[&escrow_seeds[..]];

                    // Transfer funds from escrow to highest bidder
                    anchor_lang::system_program::transfer(
                        CpiContext::new_with_signer(
                            self.system_program.to_account_info(),
                            anchor_lang::system_program::Transfer {
                                from: self.escrow.to_account_info(),
                                to: highest_bidder_info.to_account_info(),
                            },
                            escrow_signer,
                        ),
                        self.auction.highest_bid,
                    )?;
                } else {
                    return Err(ErrorCode::InvalidBidder.into());
                }
            }
        }

        // Prepare authority seeds for signing
        let authority_seeds = &[
            crate::constants::MARKETPLACE_AUTHORITY_SEED,
            &[self.marketplace_config.marketplace_authority_bump],
        ];

        // Unfreeze the asset
        UpdatePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset)
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.marketplace_authority))
            .payer(&self.seller)
            .system_program(&self.system_program)
            .plugin(Plugin::FreezeDelegate(FreezeDelegate { frozen: false }))
            .invoke_signed(&[authority_seeds])?;

        // Remove the FreezeDelegate plugin
        RemovePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset)
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.seller))
            .payer(&self.seller)
            .system_program(&self.system_program)
            .plugin_type(PluginType::FreezeDelegate)
            .invoke()?;

        // Remove the TransferDelegate plugin
        RemovePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset)
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.seller))
            .payer(&self.seller)
            .system_program(&self.system_program)
            .plugin_type(PluginType::TransferDelegate)
            .invoke()?;

        // Mark the listing as inactive
        self.listing.is_active = false;
        self.listing.updated_at = current_time;

        // Emit listing cancelled event
        emit!(ListingCancelledEvent {
            listing: self.listing.key(),
            asset: self.listing.asset,
            seller: self.seller.key(),
            cancelled_at: current_time,
        });

        Ok(())
    }
}
