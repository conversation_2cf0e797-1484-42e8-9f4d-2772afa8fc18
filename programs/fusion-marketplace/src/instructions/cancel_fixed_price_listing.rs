use crate::{
    constants::get_marketplace_authority_pda, contexts::CancelFixedPriceListing,
    events::ListingCancelledEvent,
};
use anchor_lang::prelude::*;
use mpl_core::{
    instructions::{RemovePluginV1CpiBuilder, UpdatePluginV1CpiBuilder},
    types::{FreezeDelegate, Plugin, PluginType, TransferDelegate},
};

/// Cancel a listing
impl<'info> CancelFixedPriceListing<'info> {
    pub fn handler(&mut self) -> Result<()> {
        // Verify the listing belongs to seller
        require!(
            self.listing.seller == self.seller.key(),
            crate::errors::ErrorCode::NotAssetOwner
        );

        // Verify the listing is active
        require!(
            self.listing.is_active,
            crate::errors::ErrorCode::ListingNotActive
        );

        // Get current timestamp
        let current_time = Clock::get()?.unix_timestamp;

        // Prepare authority seeds for signing
        let authority_seeds = &[
            crate::constants::MARKETPLACE_AUTHORITY_SEED,
            &[self.marketplace_config.marketplace_authority_bump],
        ];

        // Unfreeze the asset
        UpdatePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset)
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.marketplace_authority))
            .payer(&self.seller)
            .system_program(&self.system_program)
            .plugin(Plugin::FreezeDelegate(FreezeDelegate { frozen: false }))
            .invoke_signed(&[authority_seeds])?;

        // Remove the FreezeDelegate plugin
        RemovePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset)
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.seller))
            .payer(&self.seller)
            .system_program(&self.system_program)
            .plugin_type(PluginType::FreezeDelegate)
            .invoke()?;

        // Remove the TransferDelegate plugin
        RemovePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset)
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.seller))
            .payer(&self.seller)
            .system_program(&self.system_program)
            .plugin_type(PluginType::TransferDelegate)
            .invoke()?;

        // Mark listing as inactive
        self.listing.is_active = false;
        self.listing.updated_at = current_time;

        // Emit listing cancelled event
        emit!(ListingCancelledEvent {
            seller: self.listing.seller,
            asset: self.listing.asset,
            listing: self.listing.key(),
            cancelled_at: current_time,
        });

        Ok(())
    }
}
