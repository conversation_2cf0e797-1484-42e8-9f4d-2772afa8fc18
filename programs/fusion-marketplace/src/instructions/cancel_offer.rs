use crate::{contexts::CancelOffer, errors::ErrorCode, events::OfferCancelledEvent};
use anchor_lang::prelude::*;

/// Cancel an existing offer and return the escrowed funds to the buyer
impl<'info> CancelOffer<'info> {
    pub fn handler(&mut self) -> Result<()> {
        // Get current timestamp
        let current_time = Clock::get()?.unix_timestamp;

        // Get offer key for seeds
        let offer_key = self.offer.key();

        // Transfer funds from escrow back to buyer
        let escrow_seeds = &[
            crate::constants::ESCROW_PAYMENT_SEED,
            offer_key.as_ref(),
            &[self.offer.escrow_bump],
        ];
        let escrow_signer = &[&escrow_seeds[..]];

        // Get the escrow balance to return to the buyer
        let escrow_balance = self.escrow.lamports();

        // Transfer all funds back to buyer
        anchor_lang::system_program::transfer(
            CpiContext::new_with_signer(
                self.system_program.to_account_info(),
                anchor_lang::system_program::Transfer {
                    from: self.escrow.to_account_info(),
                    to: self.buyer.to_account_info(),
                },
                escrow_signer,
            ),
            escrow_balance,
        )?;

        // The offer account is automatically closed (rent returned to buyer)
        // via the close constraint in the CancelOffer context

        // Emit an event
        emit!(OfferCancelledEvent {
            offer: offer_key,
            buyer: self.buyer.key(),
            asset: self.offer.asset,
            cancelled_at: current_time,
        });

        Ok(())
    }
}
