use crate::mpl_plugin_management::get_royalty_info;
use crate::{
    constants::get_marketplace_authority_pda, contexts::ClaimAuction, errors::ErrorCode,
    events::ListingPurchasedEvent,
};
use anchor_lang::prelude::*;
use mpl_core::{
    instructions::{RemovePluginV1CpiBuilder, TransferV1CpiBuilder, UpdatePluginV1CpiBuilder},
    types::{FreezeDelegate, Plugin, PluginType, TransferDelegate},
};

/// Claim an auction after it has ended
impl<'info> ClaimAuction<'info> {
    pub fn handler(&mut self, creator_accounts: &[AccountInfo<'info>]) -> Result<()> {
        // Verify the auction has ended
        let current_time = Clock::get()?.unix_timestamp;
        require!(
            current_time >= self.auction.end_time,
            ErrorCode::AuctionNotEnded
        );

        // Verify the winner is the highest bidder
        require!(
            self.auction.highest_bidder == Some(self.winner.key()),
            ErrorCode::Unauthorized
        );

        // Prepare authority seeds for signing
        let authority_seeds = &[
            crate::constants::MARKETPLACE_AUTHORITY_SEED,
            &[self.marketplace_config.marketplace_authority_bump],
        ];

        // Unfreeze the asset
        UpdatePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.marketplace_authority))
            .payer(&self.winner)
            .system_program(&self.system_program)
            .plugin(Plugin::FreezeDelegate(FreezeDelegate { frozen: false }))
            .invoke_signed(&[authority_seeds])?;

        // Transfer the asset from seller to winner
        TransferV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.marketplace_authority))
            .payer(&self.winner)
            .system_program(Some(&self.system_program))
            .new_owner(&self.winner)
            .invoke_signed(&[authority_seeds])?;

        // Remove the FreezeDelegate plugin
        RemovePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.winner))
            .payer(&self.winner)
            .system_program(&self.system_program)
            .plugin_type(PluginType::FreezeDelegate)
            .invoke()?;

        // Remove the TransferDelegate plugin
        RemovePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.winner))
            .payer(&self.winner)
            .system_program(&self.system_program)
            .plugin_type(PluginType::TransferDelegate)
            .invoke()?;

        // Calculate marketplace fee
        let winning_bid = self.auction.highest_bid;

        let (royalty_bps, royalty_creators) = get_royalty_info(
            &self.asset.to_account_info(),
            self.collection
                .clone()
                .map(|account| account.to_account_info()),
        )?;
        let royalty_amount = winning_bid
            .checked_mul(royalty_bps.into())
            .unwrap()
            .checked_div(10000)
            .unwrap();

        let fee_percentage = self.marketplace_config.protocol_fee_bp;
        let fee_amount = (winning_bid as u128)
            .checked_sub(royalty_amount as u128)
            .unwrap()
            .checked_mul(fee_percentage as u128)
            .unwrap()
            .checked_div(10000)
            .unwrap() as u64;

        let seller_amount = winning_bid
            .checked_sub(royalty_amount)
            .unwrap()
            .checked_sub(fee_amount)
            .unwrap();

        // Transfer funds from escrow to seller and fee collector
        let listing_key = self.listing.key();
        let escrow_bump = self.auction.escrow_bump;
        let escrow_seeds = &[
            crate::constants::ESCROW_PAYMENT_SEED,
            listing_key.as_ref(),
            &[escrow_bump],
        ];
        let escrow_signer = &[&escrow_seeds[..]];

        // Distribute royalties
        if royalty_amount > 0 {
            if let Some(creators) = royalty_creators {
                // Verify we have enough remaining accounts
                require!(
                    creators.len() == creator_accounts.len(),
                    ErrorCode::InvalidCreatorAccount
                );
                for (i, creator) in creators.iter().enumerate() {
                    let creator_share = royalty_amount
                        .checked_mul(creator.percentage.into())
                        .unwrap()
                        .checked_div(100)
                        .unwrap();

                    // Verify the account matches the expected creator
                    require!(
                        creator.address == *creator_accounts[i].key,
                        ErrorCode::InvalidCreatorAccount
                    );
                    anchor_lang::system_program::transfer(
                        CpiContext::new_with_signer(
                            self.system_program.to_account_info(),
                            anchor_lang::system_program::Transfer {
                                from: self.escrow.to_account_info(),
                                to: creator_accounts[i].clone(),
                            },
                            escrow_signer,
                        ),
                        creator_share,
                    )?;
                }
            }
        }

        // Transfer seller's portion
        anchor_lang::system_program::transfer(
            CpiContext::new_with_signer(
                self.system_program.to_account_info(),
                anchor_lang::system_program::Transfer {
                    from: self.escrow.to_account_info(),
                    to: self.seller.to_account_info(),
                },
                escrow_signer,
            ),
            seller_amount,
        )?;

        // Transfer fee to fee collector
        if fee_amount > 0 {
            anchor_lang::system_program::transfer(
                CpiContext::new_with_signer(
                    self.system_program.to_account_info(),
                    anchor_lang::system_program::Transfer {
                        from: self.escrow.to_account_info(),
                        to: self.fee_collector.to_account_info(),
                    },
                    escrow_signer,
                ),
                fee_amount,
            )?;
        }

        // Mark the listing as inactive
        self.listing.is_active = false;
        self.listing.updated_at = current_time;

        // Emit listing purchased event
        emit!(ListingPurchasedEvent {
            seller: self.seller.key(),
            buyer: self.winner.key(),
            asset: self.asset.key(),
            listing: self.listing.key(),
            price: winning_bid,
            protocol_fee: fee_amount,
            royalty_payment: royalty_amount,
            purchased_at: current_time,
        });

        Ok(())
    }
}
