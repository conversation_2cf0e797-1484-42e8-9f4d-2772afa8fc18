use crate::mpl_plugin_management::get_royalty_info;
use crate::{
    constants::{ESCROW_PAYMENT_SEED, MARKETPLACE_AUTHORITY_SEED},
    contexts::AcceptOffer,
    errors::ErrorCode,
    events::OfferAcceptedEvent,
};
use anchor_lang::prelude::*;
use mpl_core::instructions::{
    RemovePluginV1CpiBuilder, TransferV1CpiBuilder, UpdatePluginV1CpiBuilder,
};
use mpl_core::types::{FreezeDelegate, Plugin, PluginType};

/// Accept an offer on a specific NFT, transferring the NFT to the buyer
/// and the payment funds to the seller
impl<'info> AcceptOffer<'info> {
    pub fn handler(&mut self, creator_accounts: &[AccountInfo<'info>]) -> Result<()> {
        // Get current timestamp
        let current_time = Clock::get()?.unix_timestamp;

        // Calculate fees
        let price = self.offer.price;

        let (royalty_bps, royalty_creators) = get_royalty_info(
            &self.asset.to_account_info(),
            self.collection
                .clone()
                .map(|account| account.to_account_info()),
        )?;
        let royalty_amount = price
            .checked_mul(royalty_bps.into())
            .unwrap()
            .checked_div(10000)
            .unwrap();

        let fee_percentage = self.marketplace_config.protocol_fee_bp;
        let fee_amount = (price as u128)
            .checked_sub(royalty_amount as u128)
            .unwrap()
            .checked_mul(fee_percentage as u128)
            .unwrap()
            .checked_div(10000)
            .unwrap() as u64;

        let seller_amount = price
            .checked_sub(royalty_amount)
            .unwrap()
            .checked_sub(fee_amount)
            .unwrap();

        let authority_seeds = &[
            MARKETPLACE_AUTHORITY_SEED,
            &[self.marketplace_config.marketplace_authority_bump],
        ];

        // Transfer the NFT from seller to buyer
        TransferV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.seller.to_account_info()))
            .payer(&self.seller)
            .system_program(Some(&self.system_program))
            .new_owner(&self.buyer)
            .invoke()?;

        // Transfer funds from escrow to seller
        let offer_key = self.offer.key();
        let escrow_seeds = &[
            ESCROW_PAYMENT_SEED,
            offer_key.as_ref(),
            &[self.offer.escrow_bump],
        ];
        let escrow_signer = &[&escrow_seeds[..]];

        // Distribute royalties
        if royalty_amount > 0 {
            if let Some(creators) = royalty_creators {
                // Verify we have enough remaining accounts
                require!(
                    creators.len() == creator_accounts.len(),
                    ErrorCode::InvalidCreatorAccount
                );
                for (i, creator) in creators.iter().enumerate() {
                    let creator_share = royalty_amount
                        .checked_mul(creator.percentage.into())
                        .unwrap()
                        .checked_div(100)
                        .unwrap();

                    // Verify the account matches the expected creator
                    require!(
                        creator.address == *creator_accounts[i].key,
                        ErrorCode::InvalidCreatorAccount
                    );
                    anchor_lang::system_program::transfer(
                        CpiContext::new_with_signer(
                            self.system_program.to_account_info(),
                            anchor_lang::system_program::Transfer {
                                from: self.escrow.to_account_info(),
                                to: creator_accounts[i].clone(),
                            },
                            escrow_signer,
                        ),
                        creator_share,
                    )?;
                }
            }
        }

        // Transfer seller's portion
        anchor_lang::system_program::transfer(
            CpiContext::new_with_signer(
                self.system_program.to_account_info(),
                anchor_lang::system_program::Transfer {
                    from: self.escrow.to_account_info(),
                    to: self.seller.to_account_info(),
                },
                escrow_signer,
            ),
            seller_amount,
        )?;

        // Transfer fee to fee collector
        if fee_amount > 0 {
            anchor_lang::system_program::transfer(
                CpiContext::new_with_signer(
                    self.system_program.to_account_info(),
                    anchor_lang::system_program::Transfer {
                        from: self.escrow.to_account_info(),
                        to: self.fee_collector.to_account_info(),
                    },
                    escrow_signer,
                ),
                fee_amount,
            )?;
        }

        // The offer account is automatically closed (rent returned to buyer)
        // via the close constraint in the AcceptOffer context

        // Emit an event
        emit!(OfferAcceptedEvent {
            offer: self.offer.key(),
            buyer: self.buyer.key(),
            seller: self.seller.key(),
            asset: self.asset.key(),
            price,
            protocol_fee: fee_amount,
            royalty_payment: royalty_amount,
            accepted_at: current_time,
        });

        Ok(())
    }
}
