use crate::{
    constants::get_marketplace_authority_pda,
    contexts::{CreateFixedPriceListing, CreateFixedPriceListingBumps},
    events::ListingCreatedEvent,
    mpl_plugin_management::add_plugin_with_authority,
    state::{Listing, ListingType},
};
use anchor_lang::prelude::*;
use mpl_core::{
    instructions::{
        AddPluginV1CpiBuilder, ApprovePluginAuthorityV1CpiBuilder, UpdatePluginV1CpiBuilder,
        UpdateV1CpiBuilder,
    },
    types::{
        FreezeDelegate, Plugin, PluginAuthority, PluginType, TransferDelegate, UpdateAuthority,
        UpdateDelegate,
    },
};

/// Create a fixed price listing for an NFT
impl<'info> CreateFixedPriceListing<'info> {
    pub fn handler(
        &mut self,
        price: u64,
        duration: u64,
        bumps: &CreateFixedPriceListingBumps,
    ) -> Result<()> {
        require!(price > 0, crate::errors::ErrorCode::InvalidPrice);

        // Ensure duration is within marketplace config limits
        require!(
            duration >= self.marketplace_config.min_listing_duration,
            crate::errors::ErrorCode::ListingDurationTooShort
        );

        require!(
            duration <= self.marketplace_config.max_listing_duration,
            crate::errors::ErrorCode::ListingDurationTooLong
        );

        // Get current timestamp
        let current_time = Clock::get()?.unix_timestamp;

        let authority_seeds = &[
            crate::constants::MARKETPLACE_AUTHORITY_SEED,
            &[self.marketplace_config.marketplace_authority_bump],
        ];

        // Add FreezeDelegate plugin and set authority
        add_plugin_with_authority(
            &self.asset.to_account_info(),
            self.collection
                .as_ref()
                .map(|collection| collection.to_account_info())
                .as_ref(),
            &self.seller,
            &self.system_program,
            &self.mpl_core_program,
            Plugin::FreezeDelegate(FreezeDelegate { frozen: true }),
            PluginType::FreezeDelegate,
            &self.marketplace_authority,
            &[authority_seeds],
        )?;

        // Add TransferDelegate plugin and set authority
        add_plugin_with_authority(
            &self.asset.to_account_info(),
            self.collection
                .as_ref()
                .map(|collection| collection.to_account_info())
                .as_ref(),
            &self.seller,
            &self.system_program,
            &self.mpl_core_program,
            Plugin::TransferDelegate(TransferDelegate {}),
            PluginType::TransferDelegate,
            &self.marketplace_authority,
            &[authority_seeds],
        )?;

        // Set end time
        let ends_at = current_time + duration as i64;

        // Initialize listing account
        let listing = &mut self.listing;
        listing.version = 1;
        listing.listing_type = ListingType::FixedPrice;
        listing.seller = self.seller.key();
        listing.asset = self.asset.key();
        listing.price = price;
        listing.currency = None; // SOL payment
        listing.is_active = true;
        listing.created_at = current_time;
        listing.updated_at = current_time;
        listing.ends_at = Some(ends_at);
        listing.bump = bumps.listing;

        // Emit listing created event
        emit!(ListingCreatedEvent {
            seller: listing.seller,
            asset: listing.asset,
            listing: listing.key(),
            price,
            listing_type: ListingType::FixedPrice,
            created_at: listing.created_at,
            ends_at,
        });

        Ok(())
    }
}
