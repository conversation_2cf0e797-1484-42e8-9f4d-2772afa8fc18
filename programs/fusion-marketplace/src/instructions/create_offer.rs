use crate::{
    contexts::CreateOffer, errors::ErrorCode, events::OfferCreatedEvent, state::Offer,
    CreateOfferBumps,
};
use anchor_lang::prelude::*;

/// Create a direct offer on a specific NFT
impl<'info> CreateOffer<'info> {
    pub fn handler(&mut self, price: u64, expires_in: i64, bumps: &CreateOfferBumps) -> Result<()> {
        // Get current timestamp
        let current_time = Clock::get()?.unix_timestamp;

        // Calculate expiration time
        let expires_at = current_time
            .checked_add(expires_in)
            .ok_or(ErrorCode::ArithmeticOverflow)?;

        // Validate offer price is greater than zero
        require!(price > 0, ErrorCode::InvalidPrice);

        // Validate expiration period (must be reasonable, e.g., not too short or too long)
        // We'll use marketplace listing durations as reference
        require!(
            expires_in >= self.marketplace_config.min_listing_duration as i64,
            ErrorCode::ListingDurationTooShort
        );
        require!(
            expires_in <= self.marketplace_config.max_listing_duration as i64,
            ErrorCode::ListingDurationTooLong
        );

        // Initialize the offer account
        let offer = &mut self.offer;
        offer.version = 1;
        offer.buyer = self.buyer.key();
        offer.asset = self.asset.key();
        offer.price = price;
        offer.currency = None;
        offer.expires_at = expires_at;
        offer.created_at = current_time;
        offer.is_filled = false;
        offer.is_cancelled = false;
        offer.escrow_bump = bumps.escrow;
        offer.bump = bumps.offer;

        // Transfer the offer amount to the escrow account
        anchor_lang::system_program::transfer(
            CpiContext::new(
                self.system_program.to_account_info(),
                anchor_lang::system_program::Transfer {
                    from: self.buyer.to_account_info(),
                    to: self.escrow.to_account_info(),
                },
            ),
            price,
        )?;

        // Emit an event
        emit!(OfferCreatedEvent {
            offer: offer.key(),
            buyer: offer.buyer,
            asset: offer.asset,
            price: offer.price,
            expires_at: offer.expires_at,
            created_at: offer.created_at,
        });

        Ok(())
    }
}
