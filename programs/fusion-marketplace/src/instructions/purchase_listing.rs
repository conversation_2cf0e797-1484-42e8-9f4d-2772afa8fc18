use crate::mpl_plugin_management::get_royalty_info;
use crate::{
    constants::get_marketplace_authority_pda, contexts::PurchaseListing, errors::ErrorCode,
    events::ListingPurchasedEvent,
};
use anchor_lang::prelude::*;
use mpl_core::{
    instructions::{
        RemovePluginV1CpiBuilder, TransferV1CpiBuilder, UpdatePluginV1CpiBuilder,
        UpdateV1CpiBuilder,
    },
    types::{FreezeDelegate, Plugin, PluginType, UpdateAuthority},
};

/// Purchase a fixed price listing
impl<'info> PurchaseListing<'info> {
    pub fn handler(&mut self, creator_accounts: &[AccountInfo<'info>]) -> Result<()> {
        // Verify listing is active
        require!(
            self.listing.is_active,
            crate::errors::ErrorCode::ListingNotActive
        );

        // Get current timestamp
        let current_time = Clock::get()?.unix_timestamp;

        // Check if listing is expired
        if let Some(ends_at) = self.listing.ends_at {
            require!(
                current_time <= ends_at,
                crate::errors::ErrorCode::ListingExpired
            );
        }

        // Get listing price
        let price = self.listing.price;

        let (royalty_bps, royalty_creators) = get_royalty_info(
            &self.asset.to_account_info(),
            self.collection
                .clone()
                .map(|account| account.to_account_info()),
        )?;
        let royalty_amount = price
            .checked_mul(royalty_bps.into())
            .unwrap()
            .checked_div(10000)
            .unwrap();

        // Calculate protocol fee
        let fee_percentage = self.marketplace_config.protocol_fee_bp;
        let fee_amount = (price as u128)
            .checked_sub(royalty_amount as u128)
            .unwrap()
            .checked_mul(fee_percentage as u128)
            .unwrap()
            .checked_div(10000)
            .unwrap() as u64;

        // Calculate seller amount
        let seller_amount = price
            .checked_sub(royalty_amount)
            .unwrap()
            .checked_sub(fee_amount)
            .unwrap();

        // Distribute royalties
        if royalty_amount > 0 {
            if let Some(creators) = royalty_creators {
                // Verify we have enough remaining accounts
                require!(
                    creators.len() == creator_accounts.len(),
                    ErrorCode::InvalidCreatorAccount
                );
                for (i, creator) in creators.iter().enumerate() {
                    let creator_share = royalty_amount
                        .checked_mul(creator.percentage.into())
                        .unwrap()
                        .checked_div(100)
                        .unwrap();

                    // Verify the account matches the expected creator
                    require!(
                        creator.address == *creator_accounts[i].key,
                        ErrorCode::InvalidCreatorAccount
                    );
                    anchor_lang::system_program::transfer(
                        CpiContext::new(
                            self.system_program.to_account_info(),
                            anchor_lang::system_program::Transfer {
                                from: self.buyer.to_account_info(),
                                to: creator_accounts[i].clone(),
                            },
                        ),
                        creator_share,
                    )?;
                }
            }
        }

        // Transfer SOL from buyer to seller and fee collector
        // Transfer to seller
        anchor_lang::system_program::transfer(
            CpiContext::new(
                self.system_program.to_account_info(),
                anchor_lang::system_program::Transfer {
                    from: self.buyer.to_account_info(),
                    to: self.seller.to_account_info(),
                },
            ),
            seller_amount,
        )?;

        // Transfer protocol fee
        if fee_amount > 0 {
            anchor_lang::system_program::transfer(
                CpiContext::new(
                    self.system_program.to_account_info(),
                    anchor_lang::system_program::Transfer {
                        from: self.buyer.to_account_info(),
                        to: self.fee_collector.to_account_info(),
                    },
                ),
                fee_amount,
            )?;
        }

        // Prepare authority seeds for signing
        let authority_seeds = &[
            crate::constants::MARKETPLACE_AUTHORITY_SEED,
            &[self.marketplace_config.marketplace_authority_bump],
        ];

        // Unfreeze the asset
        UpdatePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.marketplace_authority))
            .payer(&self.buyer)
            .system_program(&self.system_program)
            .plugin(Plugin::FreezeDelegate(FreezeDelegate { frozen: false }))
            .invoke_signed(&[authority_seeds])?;

        // Transfer MPL Core asset from seller to buyer
        TransferV1CpiBuilder::new(&self.mpl_core_program.to_account_info())
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .payer(&self.buyer.to_account_info())
            .authority(Some(&self.marketplace_authority))
            .new_owner(&self.buyer.to_account_info())
            .system_program(Some(&self.system_program.to_account_info()))
            .invoke_signed(&[authority_seeds])?;

        // Remove FreezeDelegate plugin
        RemovePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.buyer))
            .payer(&self.buyer)
            .system_program(&self.system_program)
            .plugin_type(PluginType::FreezeDelegate)
            .invoke()?;

        // Remove TransferDelegate plugin
        RemovePluginV1CpiBuilder::new(&self.mpl_core_program)
            .asset(&self.asset.to_account_info())
            .collection(
                self.collection
                    .as_ref()
                    .map(|collection| collection.to_account_info())
                    .as_ref(),
            )
            .authority(Some(&self.buyer))
            .payer(&self.buyer)
            .system_program(&self.system_program)
            .plugin_type(PluginType::TransferDelegate)
            .invoke()?;

        // Mark listing as inactive
        self.listing.is_active = false;
        self.listing.updated_at = current_time;

        // Emit purchase event
        emit!(ListingPurchasedEvent {
            seller: self.seller.key(),
            buyer: self.buyer.key(),
            asset: self.asset.key(),
            listing: self.listing.key(),
            price,
            protocol_fee: fee_amount,
            royalty_payment: royalty_amount,
            purchased_at: current_time,
        });

        Ok(())
    }
}
