use crate::{
    contexts::PlaceBid,
    errors::ErrorCode,
    events::BidPlacedEvent,
};
use anchor_lang::prelude::*;

/// Place a bid on an auction listing
impl<'info> PlaceBid<'info> {
    pub fn handler(&mut self, bid_amount: u64) -> Result<()> {
        // Validate bid amount
        require!(
            bid_amount > 0,
            ErrorCode::InvalidPrice
        );

        // Check if this is the first bid or if the bid is higher than the current highest bid
        let min_bid = if self.auction.highest_bid == 0 {
            // First bid must be at least the starting price
            self.auction.highest_bid
        } else {
            // Calculate minimum bid based on marketplace config's min_bid_increment_bp
            let min_increment = (self.auction.highest_bid as u128)
                .checked_mul(self.marketplace_config.min_bid_increment_bp as u128)
                .unwrap()
                .checked_div(10000)
                .unwrap();
            
            self.auction.highest_bid
                .checked_add(min_increment as u64)
                .ok_or(ErrorCode::BidTooLow)?
        };

        // Ensure bid is high enough
        require!(
            bid_amount >= min_bid,
            ErrorCode::BidTooLow
        );

        // If there's a previous highest bidder, refund them
        if let Some(previous_bidder) = &self.previous_bidder {
            if self.auction.highest_bid > 0 {
                // Transfer the previous bid amount from escrow back to the previous bidder
                let listing_key = self.listing.key();
                let escrow_bump = self.auction.escrow_bump;
                let escrow_seeds = &[
                    crate::constants::ESCROW_PAYMENT_SEED,
                    listing_key.as_ref(),
                    &[escrow_bump],
                ];
                let escrow_signer = &[&escrow_seeds[..]];

                // Transfer funds from escrow to previous bidder
                anchor_lang::system_program::transfer(
                    CpiContext::new_with_signer(
                        self.system_program.to_account_info(),
                        anchor_lang::system_program::Transfer {
                            from: self.escrow.to_account_info(),
                            to: previous_bidder.to_account_info(),
                        },
                        escrow_signer,
                    ),
                    self.auction.highest_bid,
                )?;
            }
        }

        // Transfer the new bid amount from bidder to escrow
        anchor_lang::system_program::transfer(
            CpiContext::new(
                self.system_program.to_account_info(),
                anchor_lang::system_program::Transfer {
                    from: self.bidder.to_account_info(),
                    to: self.escrow.to_account_info(),
                },
            ),
            bid_amount,
        )?;

        // Update auction state
        self.auction.highest_bid = bid_amount;
        self.auction.highest_bidder = Some(self.bidder.key());
        self.auction.bids_count = self.auction.bids_count.checked_add(1).unwrap();

        // Check if we need to extend the auction (if bid is placed near the end)
        let now = Clock::get()?.unix_timestamp;
        let time_remaining = self.auction.end_time - now;
        
        // If bid is placed within extension_period of end time, extend the auction
        if time_remaining < self.auction.extension_period as i64 {
            self.auction.end_time = now + self.auction.extension_period as i64;
        }

        // Emit bid placed event
        emit!(BidPlacedEvent {
            listing: self.listing.key(),
            auction: self.auction.key(),
            bidder: self.bidder.key(),
            bid_amount,
            timestamp: now,
        });

        Ok(())
    }
}
