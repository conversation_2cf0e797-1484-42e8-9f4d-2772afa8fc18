use crate::errors::ErrorCode;
use anchor_lang::prelude::*;
use mpl_core::{
    accounts::{BaseAssetV1, BaseCollectionV1},
    fetch_plugin,
    instructions::{
        AddPluginV1CpiBuilder, ApprovePluginAuthorityV1CpiBuilder,
        RevokePluginAuthorityV1CpiBuilder, UpdatePluginV1CpiBuilder,
    },
    types::{
        Creator, FreezeDelegate, Plugin, PluginAuthority, PluginType, Royalties, TransferDelegate,
        UpdateDelegate,
    },
};

/// Represents the current state of a plugin
enum PluginState {
    /// Plugin doesn't exist
    NotExists,
    /// Plugin exists with marketplace as authority
    ExistsWithMarketplaceAuthority,
    /// Plugin exists with seller as authority
    ExistsWithSellerAuthority,
    /// Plugin exists with external authority
    ExistsWithExternalAuthority,
    /// Plugin exists with seller as authority and is frozen
    ExistsWithSellerAuthorityFrozen,
    /// Plugin exists with marketplace as authority and is frozen
    ExistsWithMarketplaceAuthorityFrozen,
    /// Plugin exists with external authority and is frozen
    ExistsWithExternalAuthorityFrozen,
}

/// Get the current state of a plugin
fn get_plugin_state<'a>(
    asset: &AccountInfo<'a>,
    plugin_type: &PluginType,
    seller_key: &Pubkey,
    marketplace_authority: &Pubkey,
) -> Result<PluginState> {
    match plugin_type {
        PluginType::UpdateDelegate => {
            match fetch_plugin::<BaseAssetV1, UpdateDelegate>(asset, plugin_type.clone()) {
                Ok((authority, _, _)) => match authority {
                    PluginAuthority::Address { address } => {
                        if &address == seller_key {
                            Ok(PluginState::ExistsWithSellerAuthority)
                        } else if &address == marketplace_authority {
                            Ok(PluginState::ExistsWithMarketplaceAuthority)
                        } else {
                            Ok(PluginState::ExistsWithExternalAuthority)
                        }
                    }
                    PluginAuthority::Owner => Ok(PluginState::ExistsWithSellerAuthority),
                    _ => Ok(PluginState::ExistsWithExternalAuthority),
                },
                Err(_) => Ok(PluginState::NotExists),
            }
        }
        PluginType::FreezeDelegate => {
            match fetch_plugin::<BaseAssetV1, FreezeDelegate>(asset, plugin_type.clone()) {
                Ok((authority, freeze_delegate, _)) => match authority {
                    PluginAuthority::Address { address } => {
                        if freeze_delegate.frozen {
                            if &address == seller_key {
                                Ok(PluginState::ExistsWithSellerAuthorityFrozen)
                            } else if &address == marketplace_authority {
                                Ok(PluginState::ExistsWithMarketplaceAuthorityFrozen)
                            } else {
                                Ok(PluginState::ExistsWithExternalAuthorityFrozen)
                            }
                        } else {
                            if &address == seller_key {
                                Ok(PluginState::ExistsWithSellerAuthority)
                            } else if &address == marketplace_authority {
                                Ok(PluginState::ExistsWithMarketplaceAuthority)
                            } else {
                                Ok(PluginState::ExistsWithExternalAuthority)
                            }
                        }
                    }
                    PluginAuthority::Owner => Ok(PluginState::ExistsWithSellerAuthority),
                    _ => Ok(PluginState::ExistsWithExternalAuthority),
                },
                Err(_) => Ok(PluginState::NotExists),
            }
        }
        PluginType::TransferDelegate => {
            match fetch_plugin::<BaseAssetV1, TransferDelegate>(asset, plugin_type.clone()) {
                Ok((authority, _, _)) => match authority {
                    PluginAuthority::Address { address } => {
                        if &address == seller_key {
                            Ok(PluginState::ExistsWithSellerAuthority)
                        } else if &address == marketplace_authority {
                            Ok(PluginState::ExistsWithMarketplaceAuthority)
                        } else {
                            Ok(PluginState::ExistsWithExternalAuthority)
                        }
                    }
                    PluginAuthority::Owner => Ok(PluginState::ExistsWithSellerAuthority),
                    _ => Ok(PluginState::ExistsWithExternalAuthority),
                },
                Err(_) => Ok(PluginState::NotExists),
            }
        }
        _ => Err(ErrorCode::UnsupportedPluginType.into()),
    }
}

/// Update an existing plugin
fn update_plugin<'a>(
    asset: &AccountInfo<'a>,
    collection: Option<&AccountInfo<'a>>,
    seller: &AccountInfo<'a>,
    authority: &AccountInfo<'a>,
    system_program: &Program<'a, System>,
    mpl_core_program: &AccountInfo<'a>,
    plugin: &Plugin,
    signers_seeds: &[&[&[u8]]],
) -> Result<()> {
    Ok(UpdatePluginV1CpiBuilder::new(mpl_core_program)
        .asset(asset)
        .collection(collection)
        .authority(Some(authority))
        .payer(seller)
        .system_program(system_program)
        .plugin(plugin.clone())
        .invoke_signed(signers_seeds)?)
}

/// Add a new plugin
fn add_plugin<'a>(
    asset: &AccountInfo<'a>,
    collection: Option<&AccountInfo<'a>>,
    seller: &AccountInfo<'a>,
    system_program: &Program<'a, System>,
    mpl_core_program: &AccountInfo<'a>,
    plugin: &Plugin,
    marketplace_authority: &Pubkey,
) -> Result<()> {
    Ok(AddPluginV1CpiBuilder::new(mpl_core_program)
        .asset(asset)
        .collection(collection)
        .authority(Some(seller))
        .payer(seller)
        .system_program(system_program)
        .plugin(plugin.clone())
        .init_authority(PluginAuthority::Address {
            address: *marketplace_authority,
        })
        .invoke()?)
}

/// Revoke plugin authority
fn revoke_plugin_authority<'a>(
    asset: &AccountInfo<'a>,
    collection: Option<&AccountInfo<'a>>,
    seller: &AccountInfo<'a>,
    system_program: &Program<'a, System>,
    mpl_core_program: &AccountInfo<'a>,
    plugin_type: &PluginType,
) -> Result<()> {
    Ok(RevokePluginAuthorityV1CpiBuilder::new(mpl_core_program)
        .asset(asset)
        .collection(collection)
        .payer(&seller)
        .authority(Some(seller))
        .system_program(system_program)
        .plugin_type(plugin_type.clone())
        .invoke()?)
}

/// Approve plugin authority
fn approve_plugin_authority<'a>(
    asset: &AccountInfo<'a>,
    collection: Option<&AccountInfo<'a>>,
    seller: &AccountInfo<'a>,
    system_program: &Program<'a, System>,
    mpl_core_program: &AccountInfo<'a>,
    plugin_type: &PluginType,
    marketplace_authority: &Pubkey,
) -> Result<()> {
    Ok(ApprovePluginAuthorityV1CpiBuilder::new(mpl_core_program)
        .asset(asset)
        .collection(collection)
        .authority(Some(seller))
        .payer(seller)
        .system_program(system_program)
        .plugin_type(plugin_type.clone())
        .new_authority(PluginAuthority::Address {
            address: *marketplace_authority,
        })
        .invoke()?)
}

/// Helper function to add a plugin and set its authority
pub fn add_plugin_with_authority<'a>(
    asset: &AccountInfo<'a>,
    collection: Option<&AccountInfo<'a>>,
    seller: &AccountInfo<'a>,
    system_program: &Program<'a, System>,
    mpl_core_program: &AccountInfo<'a>,
    plugin: Plugin,
    plugin_type: PluginType,
    marketplace_authority: &AccountInfo<'a>,
    signers_seeds: &[&[&[u8]]],
) -> Result<()> {
    // Get the current state of the plugin
    let plugin_state = get_plugin_state(
        asset,
        &plugin_type,
        &seller.key(),
        &marketplace_authority.key(),
    )?;

    // Handle the plugin based on its current state
    match plugin_state {
        PluginState::NotExists => {
            // Add a new plugin if it doesn't exist
            add_plugin(
                asset,
                collection,
                seller,
                system_program,
                mpl_core_program,
                &plugin,
                &marketplace_authority.key(),
            )?;
        }
        PluginState::ExistsWithMarketplaceAuthority
        | PluginState::ExistsWithMarketplaceAuthorityFrozen => {
            // Update the plugin if it exists with marketplace authority
            update_plugin(
                asset,
                collection,
                seller,
                marketplace_authority,
                system_program,
                mpl_core_program,
                &plugin,
                signers_seeds,
            )?;
            // No need to change authority as it's already set to marketplace
        }
        PluginState::ExistsWithSellerAuthority | PluginState::ExistsWithSellerAuthorityFrozen => {
            // First revoke the existing authority
            revoke_plugin_authority(
                asset,
                collection,
                seller,
                system_program,
                mpl_core_program,
                &plugin_type,
            )?;

            // Change authority from seller to marketplace
            // Then set the new authority
            approve_plugin_authority(
                asset,
                collection,
                seller,
                system_program,
                mpl_core_program,
                &plugin_type,
                &marketplace_authority.key(),
            )?;

            // Update the plugin
            update_plugin(
                asset,
                collection,
                seller,
                marketplace_authority,
                system_program,
                mpl_core_program,
                &plugin,
                signers_seeds,
            )?;
        }
        PluginState::ExistsWithExternalAuthority => {
            // Cannot modify a plugin with external authority
            return Err(ErrorCode::AssetDelegatedToExternalAuthority.into());
        }
        PluginState::ExistsWithExternalAuthorityFrozen => {
            // Cannot modify a frozen plugin with external authority
            return Err(ErrorCode::AssetFrozenByExternalAuthority.into());
        }
    }

    Ok(())
}

pub fn get_royalty_info<'a>(
    asset: &AccountInfo<'a>,
    collection: Option<AccountInfo<'a>>,
) -> Result<(u16, Option<Vec<Creator>>)> {
    // Check collection first if provided
    let mut royalties = if let Some(col) = collection {
        fetch_plugin::<BaseCollectionV1, Royalties>(&col, PluginType::Royalties)
            .ok()
            .map(|(_, r, _)| r)
    } else {
        None
    };
    // Check asset (overrides collection)
    royalties = fetch_plugin::<BaseAssetV1, Royalties>(asset, PluginType::Royalties)
        .ok()
        .map(|(_, r, _)| r)
        .or(royalties);
    Ok((
        royalties.as_ref().map(|r| r.basis_points).unwrap_or(0),
        royalties.map(|r| r.creators),
    ))
}
