use anchor_lang::prelude::*;

use crate::{
    constants::{MARKETPLACE_CONFIG_SEED, MARKETPLACE_AUTHORITY_SEED, ESCROW_PAYMENT_SEED, OFFER_SEED, get_marketplace_authority_pda},
    errors::ErrorCode,
    state::{MarketplaceConfig, Offer}
};
use mpl_core::accounts::BaseAssetV1;
use mpl_core::ID as MPL_CORE_ID;

#[derive(Accounts)]
#[instruction(price: u64, expires_in: i64)]
pub struct CreateOffer<'info> {
    /// The buyer creating the offer
    #[account(mut)]
    pub buyer: Signer<'info>,
    
    /// The NFT asset being offered on
    pub asset: Account<'info, BaseAssetV1>,
    
    /// The offer account to be created
    #[account(
        init_if_needed,
        payer = buyer,
        space = 8 + Offer::SPACE,
        seeds = [
            OFFER_SEED, 
            buyer.key().as_ref(), 
            asset.key().as_ref()
        ],
        bump
    )]
    pub offer: Account<'info, Offer>,
    
    /// Escrow account to hold the payment
    #[account(
        mut,
        seeds = [
            ESCROW_PAYMENT_SEED,
            offer.key().as_ref()
        ],
        bump,
        owner = system_program.key()
    )]
    /// CHECK: This is just an escrow account to hold SOL
    pub escrow: SystemAccount<'info>,
    
    /// Marketplace config for validation rules
    #[account(
        seeds = [
            MARKETPLACE_CONFIG_SEED
        ],
        bump = marketplace_config.bump,
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,
    
    /// System program for CPI
    pub system_program: Program<'info, System>,

    /// Clock for timestamp validation
    pub clock: Sysvar<'info, Clock>,
}

#[derive(Accounts)]
pub struct AcceptOffer<'info> {
    /// The seller accepting the offer
    #[account(mut)]
    pub seller: Signer<'info>,
    
    /// The buyer who made the offer
    #[account(mut)]
    /// CHECK: Will be validated against offer.buyer
    pub buyer: UncheckedAccount<'info>,
    
    /// The NFT asset being sold
    #[account(
        mut,
        constraint = asset.owner == seller.key() @ ErrorCode::NotAssetOwner
    )]
    pub asset: Account<'info, BaseAssetV1>,
    
    /// CHECK: validated on instruction handler
    pub collection: Option<UncheckedAccount<'info>>,

    /// The offer being accepted
    #[account(
        mut,
        seeds = [
            OFFER_SEED, 
            buyer.key().as_ref(), 
            asset.key().as_ref()
        ],
        bump = offer.bump,
        constraint = offer.buyer == buyer.key() @ ErrorCode::InvalidBuyer,
        constraint = offer.asset == asset.key() @ ErrorCode::InvalidAsset,
        constraint = !offer.is_cancelled @ ErrorCode::OfferAlreadyCancelled,
        constraint = !offer.is_filled @ ErrorCode::OfferAlreadyFilled,
        constraint = clock.unix_timestamp <= offer.expires_at @ ErrorCode::OfferExpired,
        close = buyer
    )]
    pub offer: Account<'info, Offer>,
    
    /// Escrow account holding the payment
    #[account(
        mut,
        seeds = [
            ESCROW_PAYMENT_SEED,
            offer.key().as_ref()
        ],
        bump = offer.escrow_bump
    )]
    pub escrow: SystemAccount<'info>,
    
    /// Marketplace configuration
    #[account(
        seeds = [crate::constants::MARKETPLACE_CONFIG_SEED],
        bump = marketplace_config.bump
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,
    
    /// Fee collector account
    #[account(
        mut,
        constraint = fee_collector.key() == marketplace_config.fee_collector @ ErrorCode::InvalidFeeCollector
    )]
    /// CHECK: Fee collector account from marketplace config
    pub fee_collector: UncheckedAccount<'info>,
    
    /// Marketplace authority PDA
    #[account(
        seeds = [crate::constants::MARKETPLACE_AUTHORITY_SEED],
        bump = marketplace_config.marketplace_authority_bump,
    )]
    /// CHECK: PDA that serves as the marketplace authority for managing asset transfers
    pub marketplace_authority: UncheckedAccount<'info>,
    
    /// CHECK: MPL Core program ID
    #[account(address = mpl_core::ID)]
    pub mpl_core_program: UncheckedAccount<'info>,
    
    /// System program for transfers
    pub system_program: Program<'info, System>,
    
    /// Clock for timestamp validation
    pub clock: Sysvar<'info, Clock>,
}

#[derive(Accounts)]
pub struct CancelOffer<'info> {
    /// The buyer who created the offer
    #[account(
        mut,
        constraint = buyer.key() == offer.buyer @ ErrorCode::Unauthorized
    )]
    pub buyer: Signer<'info>,
    
    /// The NFT asset being sold
    #[account(
        mut,
        constraint = asset.key() == offer.asset @ ErrorCode::InvalidAsset
    )]
    pub asset: Account<'info, BaseAssetV1>,

    /// The offer being cancelled
    #[account(
        mut,
        seeds = [
            OFFER_SEED, 
            buyer.key().as_ref(), 
            asset.key().as_ref()
        ],
        bump = offer.bump,
        constraint = !offer.is_cancelled @ ErrorCode::OfferAlreadyCancelled,
        constraint = !offer.is_filled @ ErrorCode::OfferAlreadyFilled,
        close = buyer
    )]
    pub offer: Account<'info, Offer>,
    
    /// Escrow account holding the payment
    #[account(
        mut,
        seeds = [
            ESCROW_PAYMENT_SEED, 
            offer.key().as_ref()
        ],
        bump = offer.escrow_bump,
    )]
    pub escrow: SystemAccount<'info>,
    
    /// System program for transfers
    pub system_program: Program<'info, System>,
    
    /// Clock for timestamp updates
    pub clock: Sysvar<'info, Clock>,
}
