//! Initialize marketplace context

use anchor_lang::prelude::*;
use crate::{
    constants::{
        MARKETPLACE_CONFIG_SEED,
        MARKETPLACE_AUTHORITY_SEED
    },
    state::MarketplaceConfig,
};

/// Accounts for initializing the marketplace
#[derive(Accounts)]
pub struct Initialize<'info> {
    /// Payer for the transaction
    #[account(mut)]
    pub payer: Signer<'info>,
    
    /// Admin that can update the marketplace configuration
    #[account(mut)]
    pub admin: Signer<'info>,
    
    /// Account that will receive marketplace fees
    /// CHECK: This is just a pubkey that receives fees
    pub fee_collector: UncheckedAccount<'info>,
    
    /// Marketplace configuration account
    #[account(
        init,
        payer = payer,
        space = 8 + MarketplaceConfig::SPACE,
        seeds = [MARKETPLACE_CONFIG_SEED],
        bump
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,

    /// Marketplace authority account
    #[account(
        seeds = [MARKETPLACE_AUTHORITY_SEED],
        bump,
    )]
    /// CHECK: This is the marketplace authority PDA
    pub marketplace_authority: AccountInfo<'info>,
    
    /// System program
    pub system_program: Program<'info, System>,
}