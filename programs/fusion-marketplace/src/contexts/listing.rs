//! Listing contexts

use anchor_lang::prelude::*;

use crate::{
    constants::{
        AUCTION_SEED, ESCROW_PAYMENT_SEED, LISTING_SEED, MARKETPLACE_AUTHORITY_SEED,
        MARKETPLACE_CONFIG_SEED,
    },
    state::{Auction, AuctionType, Listing, ListingType, MarketplaceConfig},
};
use mpl_core::accounts::BaseAssetV1;

/// Accounts for creating a fixed price listing
#[derive(Accounts)]
#[instruction(price: u64, duration: u64)]
pub struct CreateFixedPriceListing<'info> {
    /// Seller creating the listing
    #[account(mut)]
    pub seller: Signer<'info>,

    /// The NFT asset being listed
    #[account(
        mut,
        constraint = asset.owner == seller.key() @ crate::errors::ErrorCode::NotAssetOwner
    )]
    pub asset: Account<'info, BaseAssetV1>,

    #[account(mut)]
    /// CHECK: validated on instruction handler
    pub collection: Option<UncheckedAccount<'info>>,

    /// Listing account to be created or updated
    #[account(
        init_if_needed,
        payer = seller,
        space = 8 + Listing::SPACE,
        seeds = [
            LISTING_SEED,
            asset.key().as_ref(),
            seller.key().as_ref()
        ],
        bump,
        // If account exists, verify it's either not active or matches the seller's key
        constraint = !listing.is_active @ crate::errors::ErrorCode::AssetAlreadyListed
    )]
    pub listing: Account<'info, Listing>,

    /// Marketplace configuration
    #[account(
        seeds = [MARKETPLACE_CONFIG_SEED],
        bump = marketplace_config.bump
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,

    /// Marketplace authority PDA
    #[account(
        seeds = [MARKETPLACE_AUTHORITY_SEED],
        bump = marketplace_config.marketplace_authority_bump,
    )]
    /// CHECK: This is the marketplace authority PDA
    pub marketplace_authority: AccountInfo<'info>,

    /// MPL Core program
    #[account(address = mpl_core::ID)]
    /// CHECK: MPL Core program ID constraint
    pub mpl_core_program: UncheckedAccount<'info>,

    /// System program
    pub system_program: Program<'info, System>,

    /// Rent sysvar
    pub rent: Sysvar<'info, Rent>,

    /// Clock for timestamps
    pub clock: Sysvar<'info, Clock>,
}

/// Accounts for purchasing a fixed price listing
#[derive(Accounts)]
pub struct PurchaseListing<'info> {
    /// Buyer purchasing the NFT
    #[account(mut)]
    pub buyer: Signer<'info>,

    /// Seller who created the listing
    #[account(mut)]
    /// CHECK: The seller account to receive funds
    pub seller: UncheckedAccount<'info>,

    /// The NFT asset being purchased
    #[account(mut)]
    pub asset: Account<'info, BaseAssetV1>,

    #[account(mut)]
    /// CHECK: validated on instruction handler
    pub collection: Option<UncheckedAccount<'info>>,

    /// Listing being purchased
    #[account(
        mut,
        seeds = [
            LISTING_SEED,
            asset.key().as_ref(),
            seller.key().as_ref()
        ],
        bump = listing.bump,
        constraint = listing.seller == seller.key() @ crate::errors::ErrorCode::Unauthorized,
        constraint = listing.asset == asset.key() @ crate::errors::ErrorCode::InvalidAsset,
        constraint = listing.is_active @ crate::errors::ErrorCode::ListingNotActive,
        constraint = listing.listing_type == ListingType::FixedPrice @ crate::errors::ErrorCode::InvalidListingType,
        constraint = (Clock::get()?.unix_timestamp <= listing.ends_at.unwrap_or(i64::MAX)) @ crate::errors::ErrorCode::ListingExpired,
    )]
    pub listing: Account<'info, Listing>,

    /// Marketplace configuration
    #[account(
        seeds = [MARKETPLACE_CONFIG_SEED],
        bump = marketplace_config.bump
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,

    /// Marketplace authority account
    #[account(
        seeds = [MARKETPLACE_AUTHORITY_SEED],
        bump = marketplace_config.marketplace_authority_bump,
    )]
    /// CHECK: This is the marketplace authority PDA
    pub marketplace_authority: AccountInfo<'info>,

    /// Fee collector account
    #[account(
        mut,
        constraint = fee_collector.key() == marketplace_config.fee_collector @ crate::errors::ErrorCode::InvalidFeeCollector,
    )]
    /// CHECK: Fee collector account from marketplace config
    pub fee_collector: AccountInfo<'info>,

    /// MPL Core program
    #[account(address = mpl_core::ID)]
    /// CHECK: this account is checked by the address constraint
    pub mpl_core_program: UncheckedAccount<'info>,

    /// System program
    pub system_program: Program<'info, System>,

    /// Clock for timestamps
    pub clock: Sysvar<'info, Clock>,
}

#[derive(Accounts)]
#[instruction(
    start_price: u64,
    start_delay_seconds: u64,
    duration: u64,
    auction_type: AuctionType,
    extension_period: u64,
)]
pub struct CreateAuctionListing<'info> {
    #[account(mut)]
    pub seller: Signer<'info>,

    /// The NFT asset being auctioned
    #[account(
        mut,
        constraint = asset.owner == seller.key() @ crate::errors::ErrorCode::NotAssetOwner
    )]
    pub asset: Account<'info, BaseAssetV1>,

    #[account(mut)]
    /// CHECK: validated on instruction handler
    pub collection: Option<UncheckedAccount<'info>>,

    /// Listing being auctioned
    #[account(
        init_if_needed,
        payer = seller,
        space = 8 + Listing::SPACE,
        seeds = [
            LISTING_SEED,
            asset.key().as_ref(),
            seller.key().as_ref()
        ],
        bump,
    )]
    pub listing: Account<'info, Listing>,

    /// Auction-specific state account
    #[account(
        init,
        payer = seller,
        space = 8 + Auction::SPACE,
        seeds = [
            AUCTION_SEED,
            listing.key().as_ref()
        ],
        bump
    )]
    pub auction: Account<'info, Auction>,

    /// Escrow account to hold the highest bid (SOL)
    #[account(
        seeds = [
            ESCROW_PAYMENT_SEED,
            listing.key().as_ref()
        ],
        bump,
        owner = system_program.key()
    )]
    pub escrow: SystemAccount<'info>,

    /// Marketplace configuration
    #[account(
            seeds = [MARKETPLACE_CONFIG_SEED],
            bump = marketplace_config.bump,
        )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,

    /// Marketplace authority account
    #[account(
            seeds = [MARKETPLACE_AUTHORITY_SEED],
            bump = marketplace_config.marketplace_authority_bump,
    )]
    /// CHECK: This is the marketplace authority PDA
    pub marketplace_authority: AccountInfo<'info>,

    /// MPL Core program
    #[account(address = mpl_core::ID)]
    /// CHECK: MPL Core program ID constraint
    pub mpl_core_program: UncheckedAccount<'info>,

    /// System program
    pub system_program: Program<'info, System>,

    /// Rent sysvar
    pub rent: Sysvar<'info, Rent>,

    /// Clock for timestamps
    pub clock: Sysvar<'info, Clock>,
}

/// Accounts for cancelling a listing
#[derive(Accounts)]
pub struct CancelFixedPriceListing<'info> {
    /// Seller who created the listing
    #[account(mut)]
    pub seller: Signer<'info>,

    #[account(mut)]
    /// CHECK: Asset account needed for CPIs and seeds
    pub asset: AccountInfo<'info>,

    #[account(mut)]
    /// CHECK: validated on instruction handler
    pub collection: Option<UncheckedAccount<'info>>,

    /// Listing to cancel
    #[account(
        mut,
        seeds = [
            LISTING_SEED,
            asset.key().as_ref(),
            seller.key().as_ref()
        ],
        bump = listing.bump,
        constraint = listing.seller == seller.key() @ crate::errors::ErrorCode::Unauthorized,
        constraint = listing.is_active @ crate::errors::ErrorCode::ListingNotActive
    )]
    pub listing: Account<'info, Listing>,

    /// Marketplace configuration
    #[account(
            seeds = [MARKETPLACE_CONFIG_SEED],
            bump = marketplace_config.bump,
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,

    /// CHECK: This is the marketplace authority PDA (needed for CPI calls to remove plugins)
    #[account(
            seeds = [MARKETPLACE_AUTHORITY_SEED],
            bump = marketplace_config.marketplace_authority_bump,
    )]
    pub marketplace_authority: AccountInfo<'info>,

    /// MPL Core program
    #[account(address = mpl_core::ID)]
    /// CHECK: MPL Core program ID constraint
    pub mpl_core_program: UncheckedAccount<'info>,

    /// System program
    pub system_program: Program<'info, System>,

    /// Clock for timestamps
    pub clock: Sysvar<'info, Clock>,
}

/// Accounts for cancelling an auction listing
#[derive(Accounts)]
pub struct CancelAuctionListing<'info> {
    /// Seller who created the listing
    #[account(mut)]
    pub seller: Signer<'info>,

    #[account(mut)]
    /// CHECK: Asset account needed for CPIs and seeds
    pub asset: AccountInfo<'info>,

    #[account(mut)]
    /// CHECK: validated on instruction handler
    pub collection: Option<UncheckedAccount<'info>>,

    /// Listing to cancel
    #[account(
        mut,
        seeds = [
            LISTING_SEED,
            asset.key().as_ref(),
            seller.key().as_ref()
        ],
        bump = listing.bump,
        constraint = listing.seller == seller.key() @ crate::errors::ErrorCode::Unauthorized,
        constraint = listing.is_active @ crate::errors::ErrorCode::ListingNotActive
    )]
    pub listing: Account<'info, Listing>,

    /// Auction state account
    #[account(
        mut,
        close = seller,
        seeds = [
            AUCTION_SEED,
            listing.key().as_ref()
        ],
        bump = auction.bump,
    )]
    pub auction: Account<'info, Auction>,

    /// Escrow account holding the highest bid (if auction)
    /// Closing/refund logic handled in instruction handler.
    #[account(
        mut,
        seeds = [
            ESCROW_PAYMENT_SEED,
            listing.key().as_ref()
        ],
        bump = auction.escrow_bump
    )]
    pub escrow: SystemAccount<'info>,

    /// CHECK: The highest bidder to potentially refund (if auction cancelled mid-bid).
    /// Passed in by the client if auction exists and highest_bidder is Some.
    #[account(
        mut,
        constraint = highest_bidder.key() == auction.highest_bidder.unwrap() @ crate::errors::ErrorCode::InvalidBidder,
    )]
    pub highest_bidder: Option<AccountInfo<'info>>,

    /// Marketplace configuration
    #[account(
        seeds = [MARKETPLACE_CONFIG_SEED],
        bump = marketplace_config.bump,
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,

    /// CHECK: This is the marketplace authority PDA (needed for CPI calls to remove plugins)
    #[account(
        seeds = [MARKETPLACE_AUTHORITY_SEED],
        bump = marketplace_config.marketplace_authority_bump,
    )]
    pub marketplace_authority: AccountInfo<'info>,

    /// MPL Core program
    #[account(address = mpl_core::ID)]
    /// CHECK: MPL Core program ID constraint
    pub mpl_core_program: UncheckedAccount<'info>,

    /// System program
    pub system_program: Program<'info, System>,

    /// Clock for timestamps
    pub clock: Sysvar<'info, Clock>,
}

#[derive(Accounts)]
#[instruction(bid_amount: u64)]
pub struct PlaceBid<'info> {
    /// Bidder placing the bid
    #[account(mut)]
    pub bidder: Signer<'info>,

    #[account(mut)]
    /// CHECK: Asset account (needed for listing seeds)
    pub asset: AccountInfo<'info>,

    /// Auction Listing being bid on
    #[account(
        mut, // Listing might be updated (e.g., updated_at)
        seeds = [
            LISTING_SEED,
            asset.key().as_ref(),
            listing.seller.as_ref() // Seller from listing needed for seeds
        ],
        bump = listing.bump,
        constraint = listing.is_active @ crate::errors::ErrorCode::ListingNotActive,
        // Constraint to ensure it's the right type (Auction) and links to the auction PDA
        constraint = matches!(listing.listing_type, ListingType::Auction(key) if key == auction.key()) @ crate::errors::ErrorCode::InvalidListingType,
    )]
    pub listing: Account<'info, Listing>,

    /// Auction-specific state to update
    #[account(
        mut,
        seeds = [
            AUCTION_SEED,
            listing.key().as_ref()
        ],
        bump = auction.bump,
        // Constraint: Ensure auction corresponds to the listing
        constraint = auction.listing == listing.key() @ crate::errors::ErrorCode::InvalidAuctionState,
        // Constraint: Ensure auction has started
        constraint = clock.unix_timestamp >= auction.start_time @ crate::errors::ErrorCode::AuctionNotStarted,
        // Constraint: Ensure auction has not ended
        constraint = clock.unix_timestamp < auction.end_time @ crate::errors::ErrorCode::AuctionEnded,
    )]
    pub auction: Account<'info, Auction>,

    /// CHECK: The account of the *previous* highest bidder (to refund them).
    /// Needs to be mutable to receive funds. Required if auction.highest_bidder is Some.
    #[account(
        mut,
        constraint = previous_bidder.key() == auction.highest_bidder.unwrap() @ crate::errors::ErrorCode::InvalidBidder,
    )]
    pub previous_bidder: Option<AccountInfo<'info>>,

    /// CHECK: The escrow PDA for this auction.
    #[account(
        mut,
        seeds = [
            ESCROW_PAYMENT_SEED,
            listing.key().as_ref()
        ],
        bump = auction.escrow_bump
    )]
    pub escrow: SystemAccount<'info>,

    /// Marketplace configuration (needed for min bid increment)
    #[account(
        seeds = [MARKETPLACE_CONFIG_SEED],
        bump = marketplace_config.bump,
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,

    /// System program
    pub system_program: Program<'info, System>,

    /// Clock for timestamps
    pub clock: Sysvar<'info, Clock>,
}

#[derive(Accounts)]
pub struct ClaimAuction<'info> {
    #[account(mut)]
    /// CHECK: Asset account
    pub asset: Account<'info, BaseAssetV1>,

    #[account(mut)]
    /// CHECK: validated on instruction handler
    pub collection: Option<UncheckedAccount<'info>>,

    /// The highest bidder who is claiming the NFT
    #[account(mut)]
    pub winner: Signer<'info>,

    /// Listing being claimed
    #[account(
        mut,
        seeds = [
            LISTING_SEED,
            asset.key().as_ref(),
            listing.seller.as_ref()
        ],
        bump = listing.bump,
        constraint = listing.is_active @ crate::errors::ErrorCode::ListingNotActive,
        constraint = matches!(listing.listing_type, ListingType::Auction(key) if key == auction.key()) @ crate::errors::ErrorCode::InvalidListingType,
    )]
    pub listing: Account<'info, Listing>,

    /// Auction state to close
    #[account(
        mut,
        seeds = [
            AUCTION_SEED,
            listing.key().as_ref()
        ],
        bump = auction.bump,
        constraint = auction.highest_bidder == Some(winner.key()) @ crate::errors::ErrorCode::Unauthorized,
        // Constraint: Ensure auction has actually ended
        constraint = clock.unix_timestamp >= auction.end_time @ crate::errors::ErrorCode::AuctionNotEnded,
        close = seller
    )]
    pub auction: Account<'info, Auction>,

    /// CHECK: The single escrow PDA holding the winning bid funds.
    #[account(
        mut,
        seeds = [
            ESCROW_PAYMENT_SEED,
            listing.key().as_ref()
        ],
        bump = auction.escrow_bump,
    )]
    pub escrow: SystemAccount<'info>,

    /// CHECK: The original seller account (to receive funds)
    #[account(mut)]
    pub seller: AccountInfo<'info>,

    /// CHECK: Marketplace authority PDA
    #[account(
        seeds = [MARKETPLACE_AUTHORITY_SEED],
        bump = marketplace_config.marketplace_authority_bump,
    )]
    pub marketplace_authority: AccountInfo<'info>,

    /// Marketplace configuration
    #[account(
        seeds = [MARKETPLACE_CONFIG_SEED],
        bump = marketplace_config.bump,
    )]
    pub marketplace_config: Account<'info, MarketplaceConfig>,

    /// Fee collector account from MarketplaceConfig
    #[account(
        mut,
        constraint = fee_collector.key() == marketplace_config.fee_collector @ crate::errors::ErrorCode::InvalidFeeCollector
    )]
    /// CHECK: Fee collector account, validated against config in handler if needed
    pub fee_collector: AccountInfo<'info>,

    /// CHECK: MPL Core program ID
    #[account(address = mpl_core::ID)]
    pub mpl_core_program: UncheckedAccount<'info>,

    /// System program
    pub system_program: Program<'info, System>,

    /// Clock for timestamps
    pub clock: Sysvar<'info, Clock>,
}
