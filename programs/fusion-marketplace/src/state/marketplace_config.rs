//! Marketplace configuration state

use crate::constants::{
    DEFAULT_MAX_LISTING_DURATION, DEFAULT_MIN_BID_INCREMENT_BP, DEFAULT_MIN_LISTING_DURATION,
    DEFAULT_PROTOCOL_FEE_BP,
};
use anchor_lang::prelude::*;

/// Arguments for initializing or updating marketplace configuration
#[derive(AnchorSerialize, AnchorDeserialize, <PERSON>lone, Debug)]
pub struct MarketplaceConfigArgs {
    /// Protocol fee in basis points (e.g., 250 = 2.5%)
    pub protocol_fee_bp: u16,

    /// Minimum bid increment in basis points (e.g., 500 = 5%)
    pub min_bid_increment_bp: u16,

    /// Minimum listing duration in seconds
    pub min_listing_duration: u64,

    /// Maximum listing duration in seconds
    pub max_listing_duration: u64,
}

impl Default for MarketplaceConfigArgs {
    fn default() -> Self {
        Self {
            protocol_fee_bp: DEFAULT_PROTOCOL_FEE_BP,
            min_bid_increment_bp: DEFAULT_MIN_BID_INCREMENT_BP,
            min_listing_duration: DEFAULT_MIN_LISTING_DURATION,
            max_listing_duration: DEFAULT_MAX_LISTING_DURATION,
        }
    }
}

/// Marketplace configuration account
#[account]
#[derive(Debug)]
pub struct MarketplaceConfig {
    /// Version for future upgrades
    pub version: u8,

    /// admin that can update the configuration
    pub admin: Pubkey,

    /// Account that receives marketplace fees
    pub fee_collector: Pubkey,

    /// Protocol fee in basis points (e.g., 250 = 2.5%)
    pub protocol_fee_bp: u16,

    /// Minimum bid increment in basis points (e.g., 500 = 5%)
    pub min_bid_increment_bp: u16,

    /// Minimum listing duration in seconds
    pub min_listing_duration: u64,

    /// Maximum listing duration in seconds
    pub max_listing_duration: u64,

    /// PDA bump
    pub bump: u8,

    /// PDA bump for marketplace authority
    pub marketplace_authority_bump: u8,
}

impl MarketplaceConfig {
    /// Space required for the marketplace config account
    pub const SPACE: usize = 
        1 +     // version
        32 +    // admin
        32 +    // fee_collector
        2 +     // protocol_fee_bp
        2 +     // min_bid_increment_bp
        8 +     // min_listing_duration
        8 +     // max_listing_duration
        1 +     // marketplace authority bump
        1;      // bump
}
