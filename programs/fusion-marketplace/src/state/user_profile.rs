//! User profile state definitions

use anchor_lang::prelude::*;

/// User profile account
#[account]
pub struct UserProfile {
    /// Version for future upgrades
    pub version: u8,
    
    /// Owner of the profile
    pub owner: <PERSON><PERSON>,
    
    /// Optional username (empty string if not set)
    pub username: String,
    
    /// Total volume sold in lamports
    pub total_volume_sold: u64,
    
    /// Total volume bought in lamports
    pub total_volume_bought: u64,
    
    /// When the profile was created
    pub created_at: i64,
    
    /// PDA bump
    pub bump: u8,
}

impl UserProfile {
    /// Space required for the user profile account
    pub const SPACE: usize = 
        1 +     // version
        32 +    // owner
        4 + 50 + // username (max 50 chars)
        8 +     // total_volume_sold
        8 +     // total_volume_bought
        8 +     // created_at
        1;      // bump
}
