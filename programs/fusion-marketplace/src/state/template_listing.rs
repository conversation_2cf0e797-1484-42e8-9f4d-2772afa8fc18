//! Template listing state definitions

use anchor_lang::prelude::*;

/// Template listing account
#[account]
pub struct TemplateListing {
    /// Version for future upgrades
    pub version: u8,
    
    /// Owner of the template
    pub owner: Pubkey,
    
    /// The fusion template being listed
    pub template: Pubkey,
    
    /// Price in lamports
    pub price: u64,
    
    /// Whether the listing is active
    pub is_active: bool,
    
    /// Royalty percentage in basis points (e.g., 1000 = 10%)
    pub royalty_bp: u16,
    
    /// Number of times this template has been used
    pub uses_count: u32,
    
    /// When the listing was created
    pub created_at: i64,
    
    /// PDA bump
    pub bump: u8,
}

impl TemplateListing {
    /// Space required for the template listing account
    pub const SPACE: usize = 
        1 +     // version
        32 +    // owner
        32 +    // template
        8 +     // price
        1 +     // is_active
        2 +     // royalty_bp
        4 +     // uses_count
        8 +     // created_at
        1;      // bump
}
