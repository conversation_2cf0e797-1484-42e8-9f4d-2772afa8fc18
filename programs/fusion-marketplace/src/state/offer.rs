//! Offer state definitions

use anchor_lang::prelude::*;

/// Offer account
#[account]
pub struct Offer {
    /// Version for future upgrades
    pub version: u8,
    
    /// Buyer making the offer
    pub buyer: Pubkey,
    
    /// Specific NFT asset
    pub asset: Pubkey,
    
    /// Price offered in lamports
    pub price: u64,
    
    /// Optional SPL token for payment (None = SOL)
    pub currency: Option<Pubkey>,
    
    /// When the offer expires
    pub expires_at: i64,
    
    /// When the offer was created
    pub created_at: i64,
    
    /// Whether the offer has been filled
    pub is_filled: bool,
    
    /// Whether the offer has been cancelled
    pub is_cancelled: bool,
    
    /// PDA bump
    pub escrow_bump: u8,

    /// PDA bump
    pub bump: u8,
}

impl Offer {
    /// Space required for the offer account
    pub const SPACE: usize = 
        1 +      // version
        32 +     // buyer
        32 +     // asset
        8 +      // price
        1 + 32 + // Option<currency>
        8 +      // expires_at
        8 +      // created_at
        1 +      // is_filled
        1 +      // is_cancelled
        1 +      // escrow_bump
        1;       // bump
}
