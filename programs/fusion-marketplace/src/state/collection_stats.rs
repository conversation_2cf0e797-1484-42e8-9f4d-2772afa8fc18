//! Collection statistics state definitions

use anchor_lang::prelude::*;

/// Collection statistics account
#[account]
pub struct CollectionStats {
    /// Version for future upgrades
    pub version: u8,
    
    /// Collection pubkey
    pub collection: Pubkey,
    
    /// Floor price in lamports
    pub floor_price: u64,
    
    /// Total volume traded in lamports
    pub total_volume: u64,
    
    /// Total number of active listings
    pub total_listings: u32,
    
    /// Total number of items in the collection
    pub total_items: u32,
    
    /// Whether the collection is verified
    pub is_verified: bool,
    
    /// When the stats were last updated
    pub last_updated_at: i64,
    
    /// PDA bump
    pub bump: u8,
}

impl CollectionStats {
    /// Space required for the collection stats account
    pub const SPACE: usize = 
        1 +     // version
        32 +    // collection
        8 +     // floor_price
        8 +     // total_volume
        4 +     // total_listings
        4 +     // total_items
        1 +     // is_verified
        8 +     // last_updated_at
        1;      // bump
}
