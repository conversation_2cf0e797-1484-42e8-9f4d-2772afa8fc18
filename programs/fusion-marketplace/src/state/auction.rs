//! Auction state definitions

use anchor_lang::prelude::*;

/// Auction types
#[derive(Anchor<PERSON>erialize, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq)]
pub enum AuctionType {
    /// English auction (ascending price)
    English,
}

/// Stores the dynamic state of an active auction, linked to a Listing account.
#[account]
#[derive(Debug)]
pub struct Auction {
    /// The parent Listing account of this auction.
    pub listing: Pubkey,

    /// The type of auction, determines bidding rules.
    pub auction_type: AuctionType,

    /// The amount of the current highest bid in lamports.
    pub highest_bid: u64,

    /// The public key of the current highest bidder.
    pub highest_bidder: Option<Pubkey>,

    /// The total number of bids placed on this auction.
    pub bids_count: u32,

    /// The timestamp when bidding is scheduled to start.
    pub start_time: i64,

    /// The timestamp when the auction is scheduled to end.
    /// This can be extended by the `extension_period` if bids occur near the end.
    pub end_time: i64,

    /// The duration (in seconds) by which the auction `end_time` is extended
    /// if a new highest bid is placed within this period before the current `end_time`.
    pub extension_period: u64,

    /// Escrow bump
    pub escrow_bump: u8,

    /// PDA bump seed.
    pub bump: u8,
}

impl Auction {
    /// Space required for the auction account
    pub const SPACE: usize = 
        32 +        // listing (Pubkey)
        1 +         // auction_type (enum discriminator)
        8 +         // highest_bid (u64)
        (1 + 32) +  // highest_bidder (Option<Pubkey>)
        4 +         // bids_count (u32)
        8 +         // start_time (i64)
        8 +         // end_time (i64)
        8 +         // extension_period (u64)
        1 +          // escrow bump
        1;          // bump (u8)
}
