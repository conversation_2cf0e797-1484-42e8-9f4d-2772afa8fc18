//! Listing state definitions

use anchor_lang::prelude::*;

/// Listing types
#[derive(Anchor<PERSON>erialize, Anchor<PERSON><PERSON><PERSON>ize, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>Eq, Eq)]
pub enum ListingType {
    /// Fixed price listing
    FixedPrice,
    /// Auction listing, associated Auction PDA key included
    Auction(Pubkey),
}

/// NFT listing account.
#[account]
#[derive(Debug)]
pub struct Listing {
    /// Version for future upgrades
    pub version: u8,

    /// Type of listing and link to Auction if applicable
    pub listing_type: ListingType,

    /// Seller of the NFT
    pub seller: Pubkey,

    /// NFT asset being sold
    pub asset: Pubkey,

    /// Price in lamports
    pub price: u64,

    /// Optional SPL token for payment (None = SOL)
    pub currency: Option<Pubkey>,

    /// Whether the listing is active
    pub is_active: bool,

    /// Timestamp when the listing was created
    pub created_at: i64,

    /// Timestamp when the listing was last updated (e.g., cancelled)
    pub updated_at: i64,

    /// When the listing ends (for auctions)
    pub ends_at: Option<i64>,

    /// PDA bump
    pub bump: u8,
}

impl Listing {
    /// Space required for the listing account
    pub const SPACE: usize =
        1 +         // version
        (1 + 32) +  // listing_type (Auction(Pubkey) is largest variant)
        32 +        // seller
        32 +        // asset
        8 +         // price
        (1 + 32) +  // currency Option<Pubkey>
        1 +         // is_active
        8 +         // created_at
        8 +         // updated_at
        1 + 8 +     // Option<ends_at>
        1;          // bump
}
