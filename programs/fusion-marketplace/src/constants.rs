//! Constants for the Fusion Marketplace program

use anchor_lang::prelude::*;

/// Seed for the marketplace authority PDA
pub const MARKETPLACE_AUTHORITY_SEED: &[u8] = b"marketplace-authority";

/// Helper function for the marketplace authority PDA
pub fn get_marketplace_authority_pda() -> (Pubkey, u8) {
    Pubkey::find_program_address(&[MARKETPLACE_AUTHORITY_SEED], &crate::id())
}

/// Seed for the marketplace configuration PDA
pub const MARKETPLACE_CONFIG_SEED: &[u8] = b"marketplace-config";

/// Seed for listing PDAs
pub const LISTING_SEED: &[u8] = b"listing";

/// Seed for auction PDAs
pub const AUCTION_SEED: &[u8] = b"auction";

/// Seed for offer PDAs
pub const OFFER_SEED: &[u8] = b"offer";

/// Seed for collection stats PDAs
pub const COLLECTION_STATS_SEED: &[u8] = b"collection-stats";

/// Seed for user profile PDAs
pub const USER_PROFILE_SEED: &[u8] = b"user-profile";

/// Seed for template listing PDAs
pub const TEMPLATE_LISTING_SEED: &[u8] = b"template-listing";

/// Seed for escrow payment PDAs
pub const ESCROW_PAYMENT_SEED: &[u8] = b"escrow-payment";

// Define constants for default values
pub const DEFAULT_PROTOCOL_FEE_BP: u16 = 250; // 2.5%
pub const DEFAULT_MIN_BID_INCREMENT_BP: u16 = 500; // 5%
pub const DEFAULT_MIN_LISTING_DURATION: u64 = 3600; // 1 hour
pub const DEFAULT_MAX_LISTING_DURATION: u64 = 604800; // 1 week
