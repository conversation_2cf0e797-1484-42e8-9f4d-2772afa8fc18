[package]
name = "fusion-marketplace"
version = "0.1.0"
description = "Created with Anchor"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "fusion_marketplace"

[features]
default = []
cpi = ["no-entrypoint"]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
idl-build = ["anchor-lang/idl-build"]
test-utils = ["solana-sdk", "anchor-test-api", "workspace", "async-trait"]

[dependencies]
anchor-lang = { version = "0.30.1", features = ["init-if-needed"] }
mpl-core = { version = "0.9.1", features = ["anchor"] }
anchor-test-api = { path = "../../test-utils/rs/solana/anchor-test-api", optional = true }
workspace = { path = "../../test-utils/rs/solana/workspace", optional = true }
solana-sdk = { version = "1.18.26", optional = true }
async-trait = { version = "0.1.68", optional = true }

[dev-dependencies]
fusion-marketplace = { path = ".", features = ["test-utils"] }
tokio = { version = "1.36", features = ["full", "test-util"] }
workspace = { path = "../../test-utils/rs/solana/workspace", features = ["account-deserialize"] }