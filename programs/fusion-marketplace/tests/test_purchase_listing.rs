use fusion_marketplace::{
    accounts::{CreateFixedPriceListing, PurchaseListing},
    constants::{LISTING_SEED, MARKETPLACE_CONFIG_SEED},
    state::{Listing, ListingType, MarketplaceConfig, MarketplaceConfigArgs},
    testing::FusionMarketplaceProgramTestBed,
};
mod fixtures;
use anchor_lang::error::ErrorCode;
use fixtures::{
    marketplace::{marketplace_authority_pda, setup_marketplace},
    mpl_core::{MplCoreProgram, MplCoreProgramTestBed},
};
use mpl_core::types::UpdateAuthority;
use solana_sdk::sysvar::clock::Clock;
use solana_sdk::sysvar::rent::Rent;
use solana_sdk::{
    instruction::AccountMeta, pubkey::Pubkey, signature::Keypair, signer::Signer, system_program,
    sysvar::SysvarId,
};
use workspace::{TestBed, TestBedError};

#[tokio::test]
async fn test_purchase_listing() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    let marketplace_authority = marketplace_authority_pda();

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller account
    let seller = testbed.create_funded_user(1_000_000_000)?;

    // Create buyer account
    let buyer = testbed.create_funded_user(10_000_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Create PDAs for listings
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price,
            duration,
        )
        .await?;

    // Get seller's initial balance
    let seller_initial_balance = testbed
        .get_account(&seller.pubkey())
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    // Get fee collector's initial balance
    let fee_collector_initial_balance = testbed
        .get_account(&fee_collector)
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    // Purchase the listing
    let tx = testbed
        .fusion_marketplace()
        .purchase_listing(
            &mut testbed,
            PurchaseListing {
                buyer: buyer.pubkey(),
                seller: seller.pubkey(),
                marketplace_authority,
                fee_collector,
                asset,
                collection: None,
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            &[],
        )
        .await?;

    // Verify the listing is no longer active
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;

    assert!(
        !listing_account.is_active,
        "Listing should be inactive after purchase"
    );

    // Verify the seller received the payment (minus fee)
    let protocol_fee_bp = 250; // 2.5%
    let protocol_fee = price * protocol_fee_bp as u64 / 10000;
    let seller_amount = price - protocol_fee;

    let seller_final_balance = testbed
        .get_account(&seller.pubkey())
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    assert!(
        seller_final_balance > seller_initial_balance,
        "Seller balance should increase"
    );

    assert_eq!(
        seller_final_balance - seller_initial_balance,
        seller_amount,
        "Seller should receive the correct amount"
    );

    // Verify the fee collector received the fee
    let fee_collector_final_balance = testbed
        .get_account(&fee_collector)
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    assert!(
        fee_collector_final_balance > fee_collector_initial_balance,
        "Fee collector balance should increase"
    );

    assert_eq!(
        fee_collector_final_balance - fee_collector_initial_balance,
        protocol_fee,
        "Fee collector should receive the correct fee"
    );

    Ok(())
}

#[tokio::test]
async fn test_purchase_listing_asset_in_collection() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    let marketplace_authority = marketplace_authority_pda();

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller account
    let seller = testbed.create_funded_user(1_000_000_000)?;

    // Create buyer account
    let buyer = testbed.create_funded_user(10_000_000_000)?;

    // Create a mock collection
    let collection = testbed
        .mpl_core()
        .create_mock_collection(&mut testbed, &seller)
        .await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft_with_collection(&mut testbed, &seller, collection)
        .await?;

    // Create PDAs for listings
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: Some(collection),
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price,
            duration,
        )
        .await?;

    // Get seller's initial balance
    let seller_initial_balance = testbed
        .get_account(&seller.pubkey())
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    // Get fee collector's initial balance
    let fee_collector_initial_balance = testbed
        .get_account(&fee_collector)
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    // Purchase the listing
    let tx = testbed
        .fusion_marketplace()
        .purchase_listing(
            &mut testbed,
            PurchaseListing {
                buyer: buyer.pubkey(),
                seller: seller.pubkey(),
                marketplace_authority,
                fee_collector,
                asset,
                collection: Some(collection),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            &[],
        )
        .await?;

    // Verify the listing is no longer active
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;

    assert!(
        !listing_account.is_active,
        "Listing should be inactive after purchase"
    );

    // Verify the seller received the payment (minus fee)
    let protocol_fee_bp = 250; // 2.5%
    let protocol_fee = price * protocol_fee_bp as u64 / 10000;
    let seller_amount = price - protocol_fee;

    let seller_final_balance = testbed
        .get_account(&seller.pubkey())
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    assert!(
        seller_final_balance > seller_initial_balance,
        "Seller balance should increase"
    );

    assert_eq!(
        seller_final_balance - seller_initial_balance,
        seller_amount,
        "Seller should receive the correct amount"
    );

    // Verify the fee collector received the fee
    let fee_collector_final_balance = testbed
        .get_account(&fee_collector)
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    assert!(
        fee_collector_final_balance > fee_collector_initial_balance,
        "Fee collector balance should increase"
    );

    assert_eq!(
        fee_collector_final_balance - fee_collector_initial_balance,
        protocol_fee,
        "Fee collector should receive the correct fee"
    );

    Ok(())
}

#[tokio::test]
async fn test_purchase_listing_with_asset_with_royalty() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    let marketplace_authority = marketplace_authority_pda();

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller account
    let seller = testbed.create_funded_user(1_000_000_000)?;

    // Create buyer account
    let buyer = testbed.create_funded_user(10_000_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    let creator_2 = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    testbed
        .mpl_core()
        .add_royalty_plugin(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            500,
            vec![(seller.pubkey(), 70), (creator_2.pubkey(), 30)],
            &[&seller],
        )
        .await?;

    // Create PDAs for listings
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price,
            duration,
        )
        .await?;

    // Get seller's initial balance
    let seller_initial_balance = testbed
        .get_account(&seller.pubkey())
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    let creator_2_initial_balance = testbed.get_account(&creator_2.pubkey()).unwrap().lamports;
    // Get fee collector's initial balance
    let fee_collector_initial_balance = testbed
        .get_account(&fee_collector)
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    // Purchase the listing
    let tx = testbed
        .fusion_marketplace()
        .purchase_listing(
            &mut testbed,
            PurchaseListing {
                buyer: buyer.pubkey(),
                seller: seller.pubkey(),
                marketplace_authority,
                fee_collector,
                asset,
                collection: None,
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            &[
                AccountMeta {
                    pubkey: seller.pubkey(),
                    is_signer: false,
                    is_writable: true,
                },
                AccountMeta {
                    pubkey: creator_2.pubkey(),
                    is_signer: false,
                    is_writable: true,
                },
            ],
        )
        .await?;

    // Verify the listing is no longer active
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;

    assert!(
        !listing_account.is_active,
        "Listing should be inactive after purchase"
    );

    let royalty_amount = price.checked_mul(500).unwrap().checked_div(10000).unwrap();

    // Verify the seller received the payment (minus fee)
    let protocol_fee_bp = 250; // 2.5%
    let protocol_fee = (price - royalty_amount) * protocol_fee_bp as u64 / 10000;
    let seller_amount = price
        - protocol_fee
        - royalty_amount
            .checked_mul(30)
            .unwrap()
            .checked_div(100)
            .unwrap();

    let seller_final_balance = testbed
        .get_account(&seller.pubkey())
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    assert!(
        seller_final_balance > seller_initial_balance,
        "Seller balance should increase"
    );

    assert_eq!(
        seller_final_balance - seller_initial_balance,
        seller_amount,
        "Seller should receive the correct amount"
    );

    let creator_2_royalty_amount = royalty_amount
        .checked_mul(30)
        .unwrap()
        .checked_div(100)
        .unwrap();

    let creator_2_final_balance = testbed.get_account(&creator_2.pubkey()).unwrap().lamports;

    assert_eq!(
        creator_2_final_balance,
        creator_2_initial_balance + creator_2_royalty_amount,
        "Creator 2 should receive payment royalty amount"
    );

    // Verify the fee collector received the fee
    let fee_collector_final_balance = testbed
        .get_account(&fee_collector)
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    assert!(
        fee_collector_final_balance > fee_collector_initial_balance,
        "Fee collector balance should increase"
    );

    assert_eq!(
        fee_collector_final_balance - fee_collector_initial_balance,
        protocol_fee,
        "Fee collector should receive the correct fee"
    );

    Ok(())
}

#[ignore]
#[tokio::test]
async fn test_purchase_listing_with_update_authority() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    let marketplace_authority = marketplace_authority_pda();

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller account
    let seller = testbed.create_funded_user(1_000_000_000)?;

    // Create buyer account
    let buyer = testbed.create_funded_user(10_000_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Create PDAs for listings
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price,
            duration,
        )
        .await?;

    // Get seller's initial balance
    let seller_initial_balance = testbed
        .get_account(&seller.pubkey())
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    // Get fee collector's initial balance
    let fee_collector_initial_balance = testbed
        .get_account(&fee_collector)
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    // Purchase the listing
    let tx = testbed
        .fusion_marketplace()
        .purchase_listing(
            &mut testbed,
            PurchaseListing {
                buyer: buyer.pubkey(),
                seller: seller.pubkey(),
                marketplace_authority,
                fee_collector,
                asset,
                collection: None,
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            &[],
        )
        .await?;

    let asset_data = testbed.mpl_core().query_asset(&mut testbed, asset).await?;

    assert_eq!(
        asset_data.base.update_authority,
        UpdateAuthority::Address(buyer.pubkey()),
        "Update authority should be the buyer"
    );

    // Verify the listing is no longer active
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;

    assert!(
        !listing_account.is_active,
        "Listing should be inactive after purchase"
    );

    // Verify the seller received the payment (minus fee)
    let protocol_fee_bp = 250; // 2.5%
    let protocol_fee = price * protocol_fee_bp as u64 / 10000;
    let seller_amount = price - protocol_fee;

    let seller_final_balance = testbed
        .get_account(&seller.pubkey())
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    assert!(
        seller_final_balance > seller_initial_balance,
        "Seller balance should increase"
    );

    assert_eq!(
        seller_final_balance - seller_initial_balance,
        seller_amount,
        "Seller should receive the correct amount"
    );

    // Verify the fee collector received the fee
    let fee_collector_final_balance = testbed
        .get_account(&fee_collector)
        .map(|acc| acc.lamports)
        .unwrap_or(0);

    assert!(
        fee_collector_final_balance > fee_collector_initial_balance,
        "Fee collector balance should increase"
    );

    assert_eq!(
        fee_collector_final_balance - fee_collector_initial_balance,
        protocol_fee,
        "Fee collector should receive the correct fee"
    );

    Ok(())
}

#[tokio::test]
async fn test_purchase_expired_listing() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    let marketplace_authority = marketplace_authority_pda();

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller account
    let seller = testbed.create_funded_user(1_000_000_000)?;

    // Create buyer account
    let buyer = testbed.create_funded_user(10_000_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Create PDAs for listings
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    let price = 1_000_000_000; // 1 SOL
    let duration = 3600; // 1 Hour
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price,
            duration,
        )
        .await?;

    // Simulating time advancement
    testbed.advance_clock_by_seconds(3601)?;

    // Try to purchase the expired listing (should fail)
    let result = testbed
        .fusion_marketplace()
        .purchase_listing(
            &mut testbed,
            PurchaseListing {
                buyer: buyer.pubkey(),
                seller: seller.pubkey(),
                marketplace_authority,
                fee_collector,
                asset,
                collection: None,
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            &[],
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error but transaction succeeded");
    println!("Error (expected): {:?}", result.unwrap_err());

    Ok(())
}
