use fusion_marketplace::{
    accounts::CreateFixedPriceListing,
    constants::{LISTING_SEED, MARKETPLACE_CONFIG_SEED},
    state::MarketplaceConfigArgs,
    testing::FusionMarketplaceProgramTestBed,
};

use solana_sdk::sysvar::clock::Clock;
use solana_sdk::sysvar::rent::Rent;
use solana_sdk::{
    pubkey::Pubkey, signature::Keypair, signer::Signer, system_program, sysvar::SysvarId,
};
use workspace::{TestBed, TestBedError};

mod fixtures;
use fixtures::{
    marketplace::{marketplace_authority_pda, setup_marketplace},
    mpl_core::{MplCoreProgram, MplCoreProgramTestBed},
};

#[tokio::test]
async fn test_listing_with_zero_price() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await.unwrap();
    testbed.register_fusion_marketplace().await.unwrap();
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing with zero price (should fail)
    let price = 0; // Invalid price
    let duration = 86400; // 1 day

    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error but transaction succeeded");
    println!("Error (expected): {:?}", result.unwrap_err());

    // Verify the listing account was not created
    let account = testbed.get_account(&listing);
    assert!(
        account.is_none(),
        "Listing account was created despite invalid price"
    );

    Ok(())
}

#[tokio::test]
async fn test_listing_with_duration_too_short() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await.unwrap();
    testbed.register_fusion_marketplace().await.unwrap();
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing with duration too short (should fail)
    let price = 1_000_000_000; // 1 SOL
    let duration = 1800; // 30 minutes (less than min_listing_duration of 1 hour)

    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error but transaction succeeded");
    println!("Error (expected): {:?}", result.unwrap_err());

    // Verify the listing account was not created
    let account = testbed.get_account(&listing);
    assert!(
        account.is_none(),
        "Listing account was created despite invalid duration"
    );

    Ok(())
}

#[tokio::test]
async fn test_listing_with_duration_too_long() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await.unwrap();
    testbed.register_fusion_marketplace().await.unwrap();
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing with duration too long (should fail)
    let price = 1_000_000_000; // 1 SOL
    let duration = 1_209_600; // 2 weeks (more than max_listing_duration of 1 week)

    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error but transaction succeeded");
    println!("Error (expected): {:?}", result.unwrap_err());

    // Verify the listing account was not created
    let account = testbed.get_account(&listing);
    assert!(
        account.is_none(),
        "Listing account was created despite invalid duration"
    );

    Ok(())
}

#[tokio::test]
async fn test_listing_not_owned_by_seller() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await.unwrap();
    testbed.register_fusion_marketplace().await.unwrap();
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create another user who will try to list an NFT they don't own
    let other_user = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT owned by admin (not other_user)
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), other_user.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing for an NFT not owned by the seller (should fail)
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day

    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: other_user.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&other_user],
            price,
            duration,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error but transaction succeeded");
    println!("Error (expected): {:?}", result.unwrap_err());

    // Verify the listing account was not created
    let account = testbed.get_account(&listing);
    assert!(
        account.is_none(),
        "Listing account was created despite not being the asset owner"
    );

    Ok(())
}
