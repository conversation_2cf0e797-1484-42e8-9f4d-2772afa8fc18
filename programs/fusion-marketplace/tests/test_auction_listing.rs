use fusion_marketplace::{
    accounts::{CancelAuctionListing, ClaimAuction, CreateAuctionListing, PlaceBid},
    constants::{
        AUCTION_SEED, DEFAULT_MAX_LISTING_DURATION, DEFAULT_MIN_LISTING_DURATION,
        ESCROW_PAYMENT_SEED, LISTING_SEED, MARKETPLACE_CONFIG_SEED,
    },
    errors::ErrorCode,
    state::{Auction, AuctionType, Listing, ListingType, MarketplaceConfig, MarketplaceConfigArgs},
    testing::FusionMarketplaceProgramTestBed,
};
use solana_sdk::{
    instruction::AccountMeta, pubkey::Pubkey, signature::Keypair, signer::Signer, system_program,
    sysvar::Sysvar, sysvar::SysvarId,
};
use workspace::{TestBed, TestBedError};
mod fixtures;
use anchor_lang::AnchorSerialize;
use fixtures::{
    marketplace::{marketplace_authority_pda, setup_marketplace},
    mpl_core::{MplCoreProgram, MplCoreProgramTestBed},
};
use solana_sdk::sysvar::clock::Clock;
use solana_sdk::sysvar::rent::Rent;

/// Test creating an auction listing
#[tokio::test]
async fn test_create_auction_listing() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs for listing and auction
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing
    let start_price = 1_000_000_000; // 1 SOL
    let start_delay_seconds = 60; // Start in 1 minute
    let duration = 86400; // 1 day
    let extension_period = 300; // 5 minutes
    let auction_type = AuctionType::English;

    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            start_price,
            start_delay_seconds,
            duration,
            auction_type,
            extension_period,
        )
        .await?;

    // Verify the listing was created correctly
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;

    assert_eq!(listing_account.seller, admin.pubkey());
    assert_eq!(listing_account.asset, asset);
    assert_eq!(listing_account.price, start_price);

    if let ListingType::Auction(auction_key) = listing_account.listing_type {
        assert_eq!(auction_key, auction);
    } else {
        panic!("Expected Auction listing type");
    }

    assert!(listing_account.is_active);
    assert_eq!(listing_account.currency, None);

    // Verify auction details
    assert_eq!(auction_account.listing, listing);
    assert_eq!(auction_account.auction_type, auction_type);
    assert_eq!(auction_account.highest_bid, 0);
    assert_eq!(auction_account.highest_bidder, None);
    assert_eq!(auction_account.bids_count, 0);
    assert_eq!(auction_account.extension_period, extension_period);

    // Verify timestamps
    let clock = testbed.get_clock()?;
    let expected_start_time = clock.unix_timestamp + start_delay_seconds as i64;
    let expected_end_time = expected_start_time + duration as i64;

    assert_eq!(auction_account.start_time, expected_start_time);
    assert_eq!(auction_account.end_time, expected_end_time);

    Ok(())
}

/// Test placing a bid on an auction
#[tokio::test]
async fn test_place_bid() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create bidders
    let bidder1 = testbed.create_funded_user(10_000_000_000)?; // 10 SOL
    let bidder2 = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs for listing and auction
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing (with no start delay so we can bid immediately)
    let start_price = 1_000_000_000; // 1 SOL
    let start_delay_seconds = 0; // Start immediately
    let duration = 86400; // 1 day
    let extension_period = 300; // 5 minutes
    let auction_type = AuctionType::English;

    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            start_price,
            start_delay_seconds,
            duration,
            auction_type,
            extension_period,
        )
        .await?;

    // Place the first bid
    let bid_amount1 = 1_500_000_000; // 1.5 SOL

    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder1.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None, // No previous bidder
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder1],
            bid_amount1,
        )
        .await?;

    // Verify the auction state after the first bid
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;
    assert_eq!(auction_account.highest_bid, bid_amount1);
    assert_eq!(auction_account.highest_bidder, Some(bidder1.pubkey()));
    assert_eq!(auction_account.bids_count, 1);

    // Check escrow has the bid amount
    let escrow_balance = testbed.get_account(&escrow).unwrap().lamports;
    assert_eq!(escrow_balance, bid_amount1);

    // Place a second bid
    let bid_amount2 = 2_000_000_000; // 2 SOL

    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder2.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: Some(bidder1.pubkey()),
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder2],
            bid_amount2,
        )
        .await?;

    // Verify the auction state after the second bid
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;
    assert_eq!(auction_account.highest_bid, bid_amount2);
    assert_eq!(auction_account.highest_bidder, Some(bidder2.pubkey()));
    assert_eq!(auction_account.bids_count, 2);

    // Check escrow has the new bid amount
    let escrow_balance = testbed.get_account(&escrow).unwrap().lamports;
    assert_eq!(escrow_balance, bid_amount2);

    // Check bidder1 got refunded
    let bidder1_balance = testbed.get_account(&bidder1.pubkey()).unwrap().lamports;
    // Should have close to initial balance minus any transaction fees
    assert!(bidder1_balance > 9_000_000_000); // Allowing for some transaction fees

    Ok(())
}

/// Test bid increment validation in auctions
#[tokio::test]
async fn test_bid_increment_validation() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace with 10% min bid increment
    let min_bid_increment_bp = 1000; // 10%
    let config_args = MarketplaceConfigArgs {
        protocol_fee_bp: 250, // 2.5%
        min_bid_increment_bp,
        min_listing_duration: DEFAULT_MIN_LISTING_DURATION,
        max_listing_duration: DEFAULT_MAX_LISTING_DURATION,
    };

    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, Some(config_args)).await?;

    // Create bidders
    let bidder1 = testbed.create_funded_user(10_000_000_000)?; // 10 SOL
    let bidder2 = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs for listing and auction
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing
    let start_price = 1_000_000_000; // 1 SOL

    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            start_price,
            0,     // Start immediately
            86400, // 1 day
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Place first bid
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder1.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder1],
            start_price, // Equal to starting price
        )
        .await?;

    // Try to place a bid below the minimum increment
    let invalid_bid_amount = start_price + (start_price / 20); // 5% increase, less than the 10% requirement

    let result = testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder2.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: Some(bidder1.pubkey()),
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder2],
            invalid_bid_amount,
        )
        .await;

    // Should fail with BidTooLow error
    assert!(
        result.is_err(),
        "Expected error for bid below minimum increment"
    );

    // Now place a valid bid with sufficient increment
    let valid_bid_amount = start_price + (start_price / 10) + 100; // Just over 10% increase

    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder2.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: Some(bidder1.pubkey()),
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder2],
            valid_bid_amount,
        )
        .await?;

    // Verify the auction state after the valid bid
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;
    assert_eq!(auction_account.highest_bid, valid_bid_amount);
    assert_eq!(auction_account.highest_bidder, Some(bidder2.pubkey()));
    assert_eq!(auction_account.bids_count, 2);

    Ok(())
}

/// Test auction time extension when bids are placed near the end
#[tokio::test]
async fn test_auction_time_extension() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create bidders
    let bidder1 = testbed.create_funded_user(10_000_000_000)?;
    let bidder2 = testbed.create_funded_user(10_000_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs for listing and auction
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing with a short duration for testing
    let start_price = 1_000_000_000; // 1 SOL
    let duration = 3600; // 1 hour
    let extension_period = 300; // 5 minutes

    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            start_price,
            0, // Start immediately
            duration,
            AuctionType::English,
            extension_period,
        )
        .await?;

    // Get the original end time
    let auction_before = testbed.get_account_data::<Auction>(&auction).await?;
    let original_end_time = auction_before.end_time;

    // Place a first bid
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder1.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder1],
            start_price,
        )
        .await?;

    // Advance the clock to 1 minute before auction end
    let mut clock = testbed.get_clock()?;
    clock.unix_timestamp = original_end_time - 60; // 1 minute before end
    testbed.set_clock(clock)?;

    // Place another bid near the end
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder2.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: Some(bidder1.pubkey()),
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder2],
            start_price + 200_000_000, // 1.2 SOL
        )
        .await?;

    // Check that the auction end time was extended
    let auction_after = testbed.get_account_data::<Auction>(&auction).await?;
    assert!(
        auction_after.end_time > original_end_time,
        "Auction end time should be extended"
    );
    assert_eq!(
        auction_after.end_time,
        testbed.get_clock()?.unix_timestamp + extension_period as i64,
        "Auction should be extended by the extension period"
    );

    Ok(())
}

/// Test cancelling an auction listing
#[tokio::test]
async fn test_cancel_auction_listing_asset_in_collection() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock collection
    let collection = testbed
        .mpl_core()
        .create_mock_collection(&mut testbed, &admin)
        .await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft_with_collection(&mut testbed, &admin, collection)
        .await?;

    // Calculate PDAs
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing
    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: Some(collection),
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            1_000_000_000, // 1 SOL
            0,             // Start immediately
            86400,         // 1 day
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Verify the listing is active before cancellation
    let listing_before = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_before.is_active);

    // Cancel the auction listing
    testbed
        .fusion_marketplace()
        .cancel_auction_listing(
            &mut testbed,
            CancelAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: Some(collection),
                listing,
                auction,
                escrow,
                highest_bidder: None, // No bids yet
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&admin],
        )
        .await?;

    // Verify the listing was cancelled
    let listing_after = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_after.is_active);

    // Verify asset can now be transferred
    let buyer = testbed.create_funded_user(10_000_000)?;
    let transfer_result = testbed
        .mpl_core()
        .transfer(
            &mut testbed,
            asset,
            Some(collection),
            admin.pubkey(),
            buyer.pubkey(),
            &[&admin],
        )
        .await;

    assert!(
        transfer_result.is_ok(),
        "Should be able to transfer asset after cancellation"
    );

    Ok(())
}

/// Test cancelling an auction listing
#[tokio::test]
async fn test_cancel_auction_listing() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing
    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            1_000_000_000, // 1 SOL
            0,             // Start immediately
            86400,         // 1 day
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Verify the listing is active before cancellation
    let listing_before = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_before.is_active);

    // Cancel the auction listing
    testbed
        .fusion_marketplace()
        .cancel_auction_listing(
            &mut testbed,
            CancelAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                highest_bidder: None, // No bids yet
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&admin],
        )
        .await?;

    // Verify the listing was cancelled
    let listing_after = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_after.is_active);

    // Verify asset can now be transferred
    let buyer = testbed.create_funded_user(10_000_000)?;
    let transfer_result = testbed
        .mpl_core()
        .transfer(
            &mut testbed,
            asset,
            None,
            admin.pubkey(),
            buyer.pubkey(),
            &[&admin],
        )
        .await;

    assert!(
        transfer_result.is_ok(),
        "Should be able to transfer asset after cancellation"
    );

    Ok(())
}

/// Test cancelling an auction with active bids
#[tokio::test]
async fn test_cancel_auction_with_bids() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a bidder
    let bidder = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing
    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            1_000_000_000, // 1 SOL
            0,             // Start immediately
            86400,         // 1 day
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Place a bid
    let bid_amount = 1_500_000_000; // 1.5 SOL
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder],
            bid_amount,
        )
        .await?;

    // Record bidder balance before cancellation
    let bidder_balance_before = testbed.get_account(&bidder.pubkey()).unwrap().lamports;

    // Cancel the auction listing with refund to bidder
    testbed
        .fusion_marketplace()
        .cancel_auction_listing(
            &mut testbed,
            CancelAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                highest_bidder: Some(bidder.pubkey()),
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&admin],
        )
        .await?;

    // Check the bidder was refunded (minus tx fees)
    let bidder_balance_after = testbed.get_account(&bidder.pubkey()).unwrap().lamports;
    assert!(
        bidder_balance_after > bidder_balance_before + bid_amount - 100_000, // Allow for tx fees
        "Bidder should be refunded"
    );

    // Verify listing is inactive and auction account is closed
    let listing_after = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_after.is_active);

    // Escrow should be emptied
    let escrow_balance = testbed.get_account(&escrow).unwrap().lamports;
    assert_eq!(escrow_balance, 0, "Escrow should be empty");

    Ok(())
}

/// Test claiming an auction after it has ended
#[tokio::test]
async fn test_claim_auction() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a bidder
    let bidder = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a winner
    let winner = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing with a short duration
    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            1_000_000_000, // 1 SOL start price
            0,             // Start immediately
            3600,          // 1 hour duration
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Place a winning bid
    let bid_amount = 1_900_000_000; // 1.9 SOL
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder],
            bid_amount,
        )
        .await?;

    // Place a winning bid
    let bid_amount = 2_000_000_000; // 2 SOL
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: winner.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: Some(bidder.pubkey()),
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            bid_amount,
        )
        .await?;

    // Record balances before claiming
    let seller_balance_before = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    let fee_collector_balance_before = testbed.get_account(&fee_collector).unwrap().lamports;

    // Advance clock past auction end time
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;
    let mut clock = testbed.get_clock()?;
    clock.unix_timestamp = auction_account.end_time + 10; // 10 seconds after end
    testbed.set_clock(clock)?;

    // Claim the auction as the winner
    testbed
        .fusion_marketplace()
        .claim_auction(
            &mut testbed,
            ClaimAuction {
                asset,
                collection: None,
                winner: winner.pubkey(),
                listing,
                auction,
                escrow,
                seller: admin.pubkey(),
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                fee_collector,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            &[],
        )
        .await?;

    // Verify the listing is now inactive
    let listing_after = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_after.is_active);

    // Escrow should be emptied
    let escrow_balance = testbed.get_account(&escrow).unwrap().lamports;
    assert_eq!(escrow_balance, 0, "Escrow should be empty");

    // Verify asset now belongs to the winner
    let asset_account = testbed
        .get_account_data::<mpl_core::accounts::BaseAssetV1>(&asset)
        .await?;
    assert_eq!(asset_account.owner, winner.pubkey());

    // Verify seller received funds (minus protocol fee)
    let seller_balance_after = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    let config = testbed
        .get_account_data::<MarketplaceConfig>(&marketplace_config)
        .await?;
    let expected_fee = (bid_amount as u128 * config.protocol_fee_bp as u128 / 10000) as u64;
    let expected_seller_amount = bid_amount - expected_fee;

    assert!(
        seller_balance_after > seller_balance_before + expected_seller_amount - 100_000, // Allow for tx fees
        "Seller should receive funds"
    );

    // Verify fee collector received the protocol fee
    let fee_collector_balance_after = testbed.get_account(&fee_collector).unwrap().lamports;
    assert!(
        fee_collector_balance_after >= fee_collector_balance_before + expected_fee,
        "Fee collector should receive protocol fee"
    );

    Ok(())
}

/// Test claiming an auction after it has ended
#[tokio::test]
async fn test_claim_auction_asset_in_collection() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a bidder
    let bidder = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a winner
    let winner = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a mock collection
    let collection = testbed
        .mpl_core()
        .create_mock_collection(&mut testbed, &admin)
        .await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft_with_collection(&mut testbed, &admin, collection)
        .await?;

    // Calculate PDAs
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing with a short duration
    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: Some(collection),
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            1_000_000_000, // 1 SOL start price
            0,             // Start immediately
            3600,          // 1 hour duration
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Place a winning bid
    let bid_amount = 1_900_000_000; // 1.9 SOL
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder],
            bid_amount,
        )
        .await?;

    // Place a winning bid
    let bid_amount = 2_000_000_000; // 2 SOL
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: winner.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: Some(bidder.pubkey()),
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            bid_amount,
        )
        .await?;

    // Record balances before claiming
    let seller_balance_before = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    let fee_collector_balance_before = testbed.get_account(&fee_collector).unwrap().lamports;

    // Advance clock past auction end time
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;
    let mut clock = testbed.get_clock()?;
    clock.unix_timestamp = auction_account.end_time + 10; // 10 seconds after end
    testbed.set_clock(clock)?;

    // Claim the auction as the winner
    testbed
        .fusion_marketplace()
        .claim_auction(
            &mut testbed,
            ClaimAuction {
                asset,
                collection: Some(collection),
                winner: winner.pubkey(),
                listing,
                auction,
                escrow,
                seller: admin.pubkey(),
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                fee_collector,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            &[],
        )
        .await?;

    // Verify the listing is now inactive
    let listing_after = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_after.is_active);

    // Escrow should be emptied
    let escrow_balance = testbed.get_account(&escrow).unwrap().lamports;
    assert_eq!(escrow_balance, 0, "Escrow should be empty");

    // Verify asset now belongs to the winner
    let asset_account = testbed
        .get_account_data::<mpl_core::accounts::BaseAssetV1>(&asset)
        .await?;
    assert_eq!(asset_account.owner, winner.pubkey());

    // Verify seller received funds (minus protocol fee)
    let seller_balance_after = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    let config = testbed
        .get_account_data::<MarketplaceConfig>(&marketplace_config)
        .await?;
    let expected_fee = (bid_amount as u128 * config.protocol_fee_bp as u128 / 10000) as u64;
    let expected_seller_amount = bid_amount - expected_fee;

    assert!(
        seller_balance_after > seller_balance_before + expected_seller_amount - 100_000, // Allow for tx fees
        "Seller should receive funds"
    );

    // Verify fee collector received the protocol fee
    let fee_collector_balance_after = testbed.get_account(&fee_collector).unwrap().lamports;
    assert!(
        fee_collector_balance_after >= fee_collector_balance_before + expected_fee,
        "Fee collector should receive protocol fee"
    );

    Ok(())
}

/// Test claiming an auction after it has ended with assets with royalty
#[tokio::test]
async fn test_claim_auction_asset_with_royalty() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a bidder
    let bidder = testbed.create_funded_user(10_000_000_000)?; // 10 SOL
    let creator_2 = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a winner
    let winner = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    testbed
        .mpl_core()
        .add_royalty_plugin(
            &mut testbed,
            asset,
            None,
            admin.pubkey(),
            500,
            vec![(admin.pubkey(), 70), (creator_2.pubkey(), 30)],
            &[&admin],
        )
        .await?;

    // Calculate PDAs
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing with a short duration
    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            1_000_000_000, // 1 SOL start price
            0,             // Start immediately
            3600,          // 1 hour duration
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Place a winning bid
    let bid_amount = 1_900_000_000; // 1.9 SOL
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: bidder.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&bidder],
            bid_amount,
        )
        .await?;

    // Place a winning bid
    let bid_amount = 2_000_000_000; // 2 SOL
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: winner.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: Some(bidder.pubkey()),
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            bid_amount,
        )
        .await?;

    // Record balances before claiming
    let seller_balance_before = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    let creator_2_balance_before = testbed.get_account(&creator_2.pubkey()).unwrap().lamports;
    let fee_collector_balance_before = testbed.get_account(&fee_collector).unwrap().lamports;

    // Advance clock past auction end time
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;
    let mut clock = testbed.get_clock()?;
    clock.unix_timestamp = auction_account.end_time + 10; // 10 seconds after end
    testbed.set_clock(clock)?;

    // Claim the auction as the winner
    testbed
        .fusion_marketplace()
        .claim_auction(
            &mut testbed,
            ClaimAuction {
                asset,
                collection: None,
                winner: winner.pubkey(),
                listing,
                auction,
                escrow,
                seller: admin.pubkey(),
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                fee_collector,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            &[
                AccountMeta {
                    pubkey: admin.pubkey(),
                    is_signer: false,
                    is_writable: true,
                },
                AccountMeta {
                    pubkey: creator_2.pubkey(),
                    is_signer: false,
                    is_writable: true,
                },
            ],
        )
        .await?;

    // Verify the listing is now inactive
    let listing_after = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_after.is_active);

    // Escrow should be emptied
    let escrow_balance = testbed.get_account(&escrow).unwrap().lamports;
    assert_eq!(escrow_balance, 0, "Escrow should be empty");

    // Verify asset now belongs to the winner
    let asset_account = testbed
        .get_account_data::<mpl_core::accounts::BaseAssetV1>(&asset)
        .await?;
    assert_eq!(asset_account.owner, winner.pubkey());

    // Verify seller received funds (minus protocol fee)
    let seller_balance_after = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    let royalty_amount = bid_amount
        .checked_mul(500)
        .unwrap()
        .checked_div(10000)
        .unwrap();

    let config = testbed
        .get_account_data::<MarketplaceConfig>(&marketplace_config)
        .await?;
    let expected_fee =
        ((bid_amount - royalty_amount) as u128 * config.protocol_fee_bp as u128 / 10000) as u64;
    let expected_seller_amount = bid_amount - expected_fee - ((royalty_amount * 30) / 100);

    assert!(
        seller_balance_after > seller_balance_before + expected_seller_amount - 100_000, // Allow for tx fees
        "Seller should receive funds"
    );

    let creator_2_royalty_amount = royalty_amount
        .checked_mul(30)
        .unwrap()
        .checked_div(100)
        .unwrap();
    let creator_2_final_balance = testbed.get_account(&creator_2.pubkey()).unwrap().lamports;

    assert_eq!(
        creator_2_final_balance,
        creator_2_balance_before + creator_2_royalty_amount,
        "Creator 2 should receive payment royalty amount"
    );

    // Verify fee collector received the protocol fee
    let fee_collector_balance_after = testbed.get_account(&fee_collector).unwrap().lamports;
    assert!(
        fee_collector_balance_after >= fee_collector_balance_before + expected_fee,
        "Fee collector should receive protocol fee"
    );

    Ok(())
}

/// Test attempting to claim an auction before it ends (should fail)
#[tokio::test]
async fn test_claim_auction_too_early() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a winner
    let winner = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing
    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            1_000_000_000, // 1 SOL
            0,             // Start immediately
            3600,          // 1 hour
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Place a winning bid
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: winner.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            1_500_000_000, // 1.5 SOL
        )
        .await?;

    // Try to claim the auction before it ends
    let result = testbed
        .fusion_marketplace()
        .claim_auction(
            &mut testbed,
            ClaimAuction {
                asset,
                collection: None,
                winner: winner.pubkey(),
                listing,
                auction,
                escrow,
                seller: admin.pubkey(),
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                fee_collector,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            &[],
        )
        .await;

    // Should fail with AuctionNotEnded error
    assert!(
        result.is_err(),
        "Should not be able to claim auction before it ends"
    );

    // Auction should still be active
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);
    assert_eq!(auction_account.highest_bidder, Some(winner.pubkey()));

    Ok(())
}

/// Test attempting to claim by non-winner (should fail)
#[tokio::test]
async fn test_claim_auction_by_non_winner() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create bidders
    let winner = testbed.create_funded_user(10_000_000_000)?;
    let non_winner = testbed.create_funded_user(10_000_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDAs
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    let (auction, _) =
        Pubkey::find_program_address(&[AUCTION_SEED, listing.as_ref()], &fusion_marketplace::ID);

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, listing.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create an auction listing with short duration
    testbed
        .fusion_marketplace()
        .create_auction_listing(
            &mut testbed,
            CreateAuctionListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                listing,
                auction,
                escrow,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            1_000_000_000, // 1 SOL
            0,             // Start immediately
            3600,          // 1 hour
            AuctionType::English,
            300, // 5 minutes extension
        )
        .await?;

    // Place a winning bid
    testbed
        .fusion_marketplace()
        .place_bid(
            &mut testbed,
            PlaceBid {
                bidder: winner.pubkey(),
                asset,
                listing,
                auction,
                previous_bidder: None,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&winner],
            1_500_000_000, // 1.5 SOL
        )
        .await?;

    // Advance clock past auction end time
    let auction_account = testbed.get_account_data::<Auction>(&auction).await?;
    let mut clock = testbed.get_clock()?;
    clock.unix_timestamp = auction_account.end_time + 10; // 10 seconds after end
    testbed.set_clock(clock)?;

    // Try to claim as the non-winner
    let result = testbed
        .fusion_marketplace()
        .claim_auction(
            &mut testbed,
            ClaimAuction {
                asset,
                collection: None,
                winner: non_winner.pubkey(),
                listing,
                auction,
                escrow,
                seller: admin.pubkey(),
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                fee_collector,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&non_winner],
            &[],
        )
        .await;

    // Should fail with Unauthorized error
    assert!(
        result.is_err(),
        "Non-winner should not be able to claim auction"
    );

    // Auction should still be active
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);

    Ok(())
}
