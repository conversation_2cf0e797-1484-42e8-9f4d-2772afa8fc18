use fusion_marketplace::{
    accounts::{A<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>},
    constants::*,
    errors::ErrorCode,
    id as fusion_marketplace_id,
    testing::FusionMarketplaceProgramTestBed,
};
use workspace::{TestBed, TestBedError};
mod fixtures;
use fixtures::{
    marketplace::{marketplace_authority_pda, setup_marketplace},
    mpl_core::{MplCoreProgram, MplCoreProgramTestBed},
};
use solana_sdk::sysvar::clock::Clock;
use solana_sdk::sysvar::rent::Rent;
use solana_sdk::{
    instruction::AccountMeta, pubkey::Pubkey, signature::Keypair, signer::Signer, system_program,
    sysvar::Sysvar, sysvar::SysvarId, transaction::TransactionError,
};
use std::time::{Duration, SystemTime, UNIX_EPOCH};

#[tokio::test]
async fn test_offer_create_and_cancel() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Create an offer from buyer to seller
    let price = 10_000_000_000; // 10 SOL
    let expires_in = 3600; // 1 hour expiration

    // Create buyer
    let buyer = testbed.create_funded_user(15_000_000_000)?; // 15 SOL

    let (offer, _) = Pubkey::find_program_address(
        &[OFFER_SEED, buyer.pubkey().as_ref(), asset.as_ref()],
        &fusion_marketplace::ID,
    );

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, offer.as_ref()],
        &fusion_marketplace::ID,
    );

    // 1. Create offer
    testbed
        .fusion_marketplace()
        .create_offer(
            &mut testbed,
            CreateOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            price,
            expires_in,
        )
        .await?;

    // Verify funds were moved to escrow
    let escrow_account = testbed.get_account(&escrow).unwrap();
    assert_eq!(
        escrow_account.lamports, price,
        "Escrow should contain the offer amount"
    );

    // 2. Cancel offer
    testbed
        .fusion_marketplace()
        .cancel_offer(
            &mut testbed,
            CancelOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
        )
        .await?;

    Ok(())
}

#[tokio::test]
async fn test_offer_accept() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT owned by seller
    let seller = testbed.create_funded_user(10_000_000_000)?; // 10 SOL
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Create buyer
    let buyer = testbed.create_funded_user(15_000_000_000)?; // 15 SOL

    // Create an offer
    let price = 5_000_000_000; // 5 SOL
    let expires_in = 3600; // 1 hour expiration

    let (offer, _) = Pubkey::find_program_address(
        &[OFFER_SEED, buyer.pubkey().as_ref(), asset.as_ref()],
        &fusion_marketplace::ID,
    );

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, offer.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create offer
    testbed
        .fusion_marketplace()
        .create_offer(
            &mut testbed,
            CreateOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            price,
            expires_in,
        )
        .await?;

    // Get seller's initial balance
    let seller_initial_balance = testbed.get_account(&seller.pubkey()).unwrap().lamports;

    // Accept the offer
    testbed
        .fusion_marketplace()
        .accept_offer(
            &mut testbed,
            AcceptOffer {
                seller: seller.pubkey(),
                buyer: buyer.pubkey(),
                asset,
                collection: None,
                offer,
                escrow,
                marketplace_config,
                fee_collector,
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&seller],
            &[],
        )
        .await?;

    // Verify asset was transferred to buyer
    let asset_info = testbed.mpl_core().query_asset(&mut testbed, asset).await?;
    assert_eq!(
        asset_info.base.owner,
        buyer.pubkey(),
        "Asset should be transferred to the buyer"
    );

    // Verify seller received payment (minus fee)
    let seller_final_balance = testbed.get_account(&seller.pubkey()).unwrap().lamports;
    let fee_percentage = 250; // 2.5% default

    let fee_amount = (price as u128)
        .checked_mul(fee_percentage as u128)
        .unwrap()
        .checked_div(10000)
        .unwrap() as u64;
    let seller_amount = price - fee_amount;

    assert!(
        seller_final_balance > seller_initial_balance,
        "Seller should receive payment"
    );
    assert_eq!(
        seller_final_balance,
        seller_initial_balance + seller_amount,
        "Seller should receive payment minus fee"
    );

    // Verify fee collector received fee
    let fee_collector_balance = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    assert!(
        fee_collector_balance >= fee_amount,
        "Fee collector should receive fee"
    );

    Ok(())
}

#[tokio::test]
async fn test_offer_accept_asset_in_collection() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT owned by seller
    let seller = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    // Create a mock collection
    let collection = testbed
        .mpl_core()
        .create_mock_collection(&mut testbed, &seller)
        .await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft_with_collection(&mut testbed, &seller, collection)
        .await?;

    // Create buyer
    let buyer = testbed.create_funded_user(15_000_000_000)?; // 15 SOL

    // Create an offer
    let price = 5_000_000_000; // 5 SOL
    let expires_in = 3600; // 1 hour expiration

    let (offer, _) = Pubkey::find_program_address(
        &[OFFER_SEED, buyer.pubkey().as_ref(), asset.as_ref()],
        &fusion_marketplace::ID,
    );

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, offer.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create offer
    testbed
        .fusion_marketplace()
        .create_offer(
            &mut testbed,
            CreateOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            price,
            expires_in,
        )
        .await?;

    // Get seller's initial balance
    let seller_initial_balance = testbed.get_account(&seller.pubkey()).unwrap().lamports;

    // Accept the offer
    testbed
        .fusion_marketplace()
        .accept_offer(
            &mut testbed,
            AcceptOffer {
                seller: seller.pubkey(),
                buyer: buyer.pubkey(),
                asset,
                collection: Some(collection),
                offer,
                escrow,
                marketplace_config,
                fee_collector,
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&seller],
            &[],
        )
        .await?;

    // Verify asset was transferred to buyer
    let asset_info = testbed.mpl_core().query_asset(&mut testbed, asset).await?;
    assert_eq!(
        asset_info.base.owner,
        buyer.pubkey(),
        "Asset should be transferred to the buyer"
    );

    // Verify seller received payment (minus fee)
    let seller_final_balance = testbed.get_account(&seller.pubkey()).unwrap().lamports;
    let fee_percentage = 250; // 2.5% default

    let fee_amount = (price as u128)
        .checked_mul(fee_percentage as u128)
        .unwrap()
        .checked_div(10000)
        .unwrap() as u64;
    let seller_amount = price - fee_amount;

    assert!(
        seller_final_balance > seller_initial_balance,
        "Seller should receive payment"
    );
    assert_eq!(
        seller_final_balance,
        seller_initial_balance + seller_amount,
        "Seller should receive payment minus fee"
    );

    // Verify fee collector received fee
    let fee_collector_balance = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    assert!(
        fee_collector_balance >= fee_amount,
        "Fee collector should receive fee"
    );

    Ok(())
}

#[tokio::test]
async fn test_offer_accept_for_asset_with_royalty() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT owned by seller
    let seller = testbed.create_funded_user(10_000_000_000)?; // 10 SOL
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    testbed
        .mpl_core()
        .add_royalty_plugin(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            500,
            vec![(seller.pubkey(), 100)],
            &[&seller],
        )
        .await?;

    // Create buyer
    let buyer = testbed.create_funded_user(15_000_000_000)?; // 15 SOL

    // Create an offer
    let price: u64 = 5_000_000_000; // 5 SOL
    let expires_in = 3600; // 1 hour expiration

    let (offer, _) = Pubkey::find_program_address(
        &[OFFER_SEED, buyer.pubkey().as_ref(), asset.as_ref()],
        &fusion_marketplace::ID,
    );

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, offer.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create offer
    testbed
        .fusion_marketplace()
        .create_offer(
            &mut testbed,
            CreateOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            price,
            expires_in,
        )
        .await?;

    // Get seller's initial balance
    let seller_initial_balance = testbed.get_account(&seller.pubkey()).unwrap().lamports;

    // Accept the offer
    testbed
        .fusion_marketplace()
        .accept_offer(
            &mut testbed,
            AcceptOffer {
                seller: seller.pubkey(),
                buyer: buyer.pubkey(),
                asset,
                collection: None,
                offer,
                escrow,
                marketplace_config,
                fee_collector,
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&seller],
            &[AccountMeta {
                pubkey: seller.pubkey(),
                is_signer: false,
                is_writable: true,
            }],
        )
        .await?;

    // Verify asset was transferred to buyer
    let asset_info = testbed.mpl_core().query_asset(&mut testbed, asset).await?;
    assert_eq!(
        asset_info.base.owner,
        buyer.pubkey(),
        "Asset should be transferred to the buyer"
    );

    // Verify seller received payment (minus fee)
    let seller_final_balance = testbed.get_account(&seller.pubkey()).unwrap().lamports;

    let royalty_amount = price.checked_mul(500).unwrap().checked_div(10000).unwrap();

    let fee_percentage = 250; // 2.5% default
    let fee_amount = (price as u128)
        .checked_sub(royalty_amount as u128)
        .unwrap()
        .checked_mul(fee_percentage as u128)
        .unwrap()
        .checked_div(10000)
        .unwrap() as u64;

    let seller_amount = price - fee_amount;

    assert!(
        seller_final_balance > seller_initial_balance,
        "Seller should receive payment"
    );
    assert_eq!(
        seller_final_balance,
        seller_initial_balance + seller_amount,
        "Seller should receive payment minus fee"
    );

    // Verify fee collector received fee
    let fee_collector_balance = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    assert!(
        fee_collector_balance >= fee_amount,
        "Fee collector should receive fee"
    );

    Ok(())
}

#[tokio::test]
async fn test_offer_accept_for_asset_with_multiple_creators_split_royalty(
) -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT owned by seller
    let seller = testbed.create_funded_user(10_000_000_000)?; // 10 SOL
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    let creator_2 = testbed.create_funded_user(10_000_000_000)?; // 10 SOL

    testbed
        .mpl_core()
        .add_royalty_plugin(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            500,
            vec![(seller.pubkey(), 70), (creator_2.pubkey(), 30)],
            &[&seller],
        )
        .await?;

    // Create buyer
    let buyer = testbed.create_funded_user(15_000_000_000)?; // 15 SOL

    // Create an offer
    let price: u64 = 5_000_000_000; // 5 SOL
    let expires_in = 3600; // 1 hour expiration

    let (offer, _) = Pubkey::find_program_address(
        &[OFFER_SEED, buyer.pubkey().as_ref(), asset.as_ref()],
        &fusion_marketplace::ID,
    );

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, offer.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create offer
    testbed
        .fusion_marketplace()
        .create_offer(
            &mut testbed,
            CreateOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            price,
            expires_in,
        )
        .await?;

    // Get seller's initial balance
    let seller_initial_balance = testbed.get_account(&seller.pubkey()).unwrap().lamports;
    let creator_2_initial_balance = testbed.get_account(&creator_2.pubkey()).unwrap().lamports;

    // Accept the offer
    testbed
        .fusion_marketplace()
        .accept_offer(
            &mut testbed,
            AcceptOffer {
                seller: seller.pubkey(),
                buyer: buyer.pubkey(),
                asset,
                collection: None,
                offer,
                escrow,
                marketplace_config,
                fee_collector,
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&seller],
            &[
                AccountMeta {
                    pubkey: seller.pubkey(),
                    is_signer: false,
                    is_writable: true,
                },
                AccountMeta {
                    pubkey: creator_2.pubkey(),
                    is_signer: false,
                    is_writable: true,
                },
            ],
        )
        .await?;

    // Verify asset was transferred to buyer
    let asset_info = testbed.mpl_core().query_asset(&mut testbed, asset).await?;
    assert_eq!(
        asset_info.base.owner,
        buyer.pubkey(),
        "Asset should be transferred to the buyer"
    );

    // Verify seller received payment (minus fee)
    let seller_final_balance = testbed.get_account(&seller.pubkey()).unwrap().lamports;

    let royalty_amount = price.checked_mul(500).unwrap().checked_div(10000).unwrap();

    let fee_percentage = 250; // 2.5% default
    let fee_amount = (price as u128)
        .checked_sub(royalty_amount as u128)
        .unwrap()
        .checked_mul(fee_percentage as u128)
        .unwrap()
        .checked_div(10000)
        .unwrap() as u64;

    let seller_amount = price
        - fee_amount
        - royalty_amount
            .checked_mul(30)
            .unwrap()
            .checked_div(100)
            .unwrap();

    let creator_2_royalty_amount = royalty_amount
        .checked_mul(30)
        .unwrap()
        .checked_div(100)
        .unwrap();

    assert!(
        seller_final_balance > seller_initial_balance,
        "Seller should receive payment"
    );
    assert_eq!(
        seller_final_balance,
        seller_initial_balance + seller_amount,
        "Seller should receive payment minus fee"
    );

    let creator_2_final_balance = testbed.get_account(&creator_2.pubkey()).unwrap().lamports;

    assert_eq!(
        creator_2_final_balance,
        creator_2_initial_balance + creator_2_royalty_amount,
        "Creator 2 should receive payment royalty amount"
    );
    // Verify fee collector received fee
    let fee_collector_balance = testbed.get_account(&admin.pubkey()).unwrap().lamports;
    assert!(
        fee_collector_balance >= fee_amount,
        "Fee collector should receive fee"
    );

    Ok(())
}

#[tokio::test]
async fn test_expired_offer() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT owned by seller
    let seller = testbed.create_funded_user(10_000_000_000)?;
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Create buyer
    let buyer = testbed.create_funded_user(15_000_000_000)?;

    // Create an offer with short expiration
    let price = 5_000_000_000; // 5 SOL
    let expires_in = 3600; // 1 minute expiration

    let (offer, _) = Pubkey::find_program_address(
        &[OFFER_SEED, buyer.pubkey().as_ref(), asset.as_ref()],
        &fusion_marketplace::ID,
    );

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, offer.as_ref()],
        &fusion_marketplace::ID,
    );

    // Create offer
    testbed
        .fusion_marketplace()
        .create_offer(
            &mut testbed,
            CreateOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            price,
            expires_in,
        )
        .await?;

    // Advance clock past expiration
    testbed.advance_clock_by_seconds(expires_in + 10)?;

    // Try to accept the expired offer - should fail
    let accept_result = testbed
        .fusion_marketplace()
        .accept_offer(
            &mut testbed,
            AcceptOffer {
                seller: seller.pubkey(),
                buyer: buyer.pubkey(),
                asset,
                collection: None,
                offer,
                escrow,
                marketplace_config,
                fee_collector: admin.pubkey(),
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&seller],
            &[],
        )
        .await;

    // Verify offer rejection due to expiration
    assert!(
        accept_result.is_err(),
        "Accepting expired offer should fail"
    );

    // The buyer should still be able to cancel the expired offer
    testbed
        .fusion_marketplace()
        .cancel_offer(
            &mut testbed,
            CancelOffer {
                asset,
                buyer: buyer.pubkey(),
                offer,
                escrow,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
        )
        .await?;

    Ok(())
}

#[tokio::test]
async fn test_offer_validation() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let seller = testbed.create_funded_user(10_000_000_000)?;
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Create buyers
    let buyer = testbed.create_funded_user(15_000_000_000)?;
    let other_user = testbed.create_funded_user(15_000_000_000)?;

    // Test 1: Try to create offer with zero price (should fail)
    let zero_price = 0;
    let expires_in = 3600;

    let (offer, _) = Pubkey::find_program_address(
        &[OFFER_SEED, buyer.pubkey().as_ref(), asset.as_ref()],
        &fusion_marketplace::ID,
    );

    let (escrow, _) = Pubkey::find_program_address(
        &[ESCROW_PAYMENT_SEED, offer.as_ref()],
        &fusion_marketplace::ID,
    );

    let zero_price_result = testbed
        .fusion_marketplace()
        .create_offer(
            &mut testbed,
            CreateOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            zero_price,
            expires_in,
        )
        .await;

    assert!(
        zero_price_result.is_err(),
        "Creating offer with zero price should fail"
    );

    // Create valid offer for further tests
    let valid_price = 5_000_000_000;
    testbed
        .fusion_marketplace()
        .create_offer(
            &mut testbed,
            CreateOffer {
                buyer: buyer.pubkey(),
                asset,
                offer,
                escrow,
                marketplace_config,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            valid_price,
            expires_in,
        )
        .await?;

    // Test 2: Try to cancel someone else's offer (should fail)
    let cancel_result = testbed
        .fusion_marketplace()
        .cancel_offer(
            &mut testbed,
            CancelOffer {
                asset,
                buyer: other_user.pubkey(),
                offer,
                escrow,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&other_user],
        )
        .await;

    assert!(
        cancel_result.is_err(),
        "Cancelling someone else's offer should fail"
    );

    // Test 3: Try to accept own offer (technically possible, but doesn't make sense)
    // This might fail due to NFT ownership constraint if the buyer doesn't own the NFT
    let accept_own_result = testbed
        .fusion_marketplace()
        .accept_offer(
            &mut testbed,
            AcceptOffer {
                seller: buyer.pubkey(),
                buyer: buyer.pubkey(),
                asset,
                collection: None,
                offer,
                escrow,
                marketplace_config,
                fee_collector: admin.pubkey(),
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
            &[],
        )
        .await;

    assert!(
        accept_own_result.is_err(),
        "Accepting own offer should fail if you don't own the NFT"
    );

    // Test 4: Try to accept another user's offer when you don't own the NFT
    let accept_unowned_result = testbed
        .fusion_marketplace()
        .accept_offer(
            &mut testbed,
            AcceptOffer {
                seller: other_user.pubkey(),
                buyer: buyer.pubkey(),
                asset,
                collection: None,
                offer,
                escrow,
                marketplace_config,
                fee_collector: admin.pubkey(),
                marketplace_authority: marketplace_authority_pda(),
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&other_user],
            &[],
        )
        .await;

    assert!(
        accept_unowned_result.is_err(),
        "Accepting offer for NFT you don't own should fail"
    );

    // Cancel offer to clean up
    testbed
        .fusion_marketplace()
        .cancel_offer(
            &mut testbed,
            CancelOffer {
                asset,
                buyer: buyer.pubkey(),
                offer,
                escrow,
                system_program: system_program::ID,
                clock: Clock::id(),
            },
            &[&buyer],
        )
        .await?;

    Ok(())
}
