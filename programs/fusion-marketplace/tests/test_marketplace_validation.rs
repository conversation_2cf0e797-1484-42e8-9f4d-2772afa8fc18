use fusion_marketplace::{
    accounts::Initialize,
    constants::MARKETPLACE_CONFIG_SEED,
    errors::<PERSON>rrorCode,
    state::MarketplaceConfigArgs,
    testing::FusionMarketplaceProgramTestBed,
};
use solana_sdk::{pubkey::Pubkey, signature::Keypair, signer::Signer, system_program};
use workspace::{TestBed, TestBedError};
mod fixtures;
use fixtures::{
    marketplace::{marketplace_authority_pda, setup_marketplace},
    mpl_core::{MplCoreProgram, MplCoreProgramTestBed},
};

#[tokio::test]
async fn test_initialize_with_excessive_fee() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await.unwrap();
    testbed.register_fusion_marketplace().await.unwrap();

    // Create and fund user accounts
    let admin = testbed.create_funded_user(10_000_000).unwrap();
    let fee_collector = Keypair::new().pubkey();

    // Initialize the marketplace with invalid fee (>100%)
    let config = MarketplaceConfigArgs {
        protocol_fee_bp: 10001, // 100.01% (invalid)
        min_bid_increment_bp: 500,
        min_listing_duration: 3600,
        max_listing_duration: 604800,
    };

    // Calculate PDA for marketplace config
    let (marketplace_config, _) =
        Pubkey::find_program_address(&[MARKETPLACE_CONFIG_SEED], &fusion_marketplace::ID);

    // Execute initialize instruction - should fail with InvalidFeePercentage
    let result = testbed
        .fusion_marketplace()
        .initialize(
            &mut testbed,
            Initialize {
                payer: admin.pubkey(),
                admin: admin.pubkey(),
                fee_collector,
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                system_program: system_program::ID,
            },
            &[&admin],
            config,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error but transaction succeeded");
    
    // Check for specific error code
    let error = result.unwrap_err();
    
    // The account should not have been created
    let account = testbed.get_account(&marketplace_config);
    assert!(account.is_none(), "Account was created despite invalid fee");

    Ok(())
}

#[tokio::test]
async fn test_initialize_with_invalid_duration() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await.unwrap();
    testbed.register_fusion_marketplace().await.unwrap();

    // Create and fund user accounts
    let admin = testbed.create_funded_user(10_000_000).unwrap();
    let fee_collector = Keypair::new().pubkey();

    // Initialize the marketplace with invalid duration (min > max)
    let config = MarketplaceConfigArgs {
        protocol_fee_bp: 250,
        min_bid_increment_bp: 500,
        min_listing_duration: 604800, // 1 week
        max_listing_duration: 3600,   // 1 hour (invalid: min > max)
    };

    // Calculate PDA for marketplace config
    let (marketplace_config, _) =
        Pubkey::find_program_address(&[MARKETPLACE_CONFIG_SEED], &fusion_marketplace::ID);

    // Execute initialize instruction - should fail with InvalidListingDuration
    let result = testbed
        .fusion_marketplace()
        .initialize(
            &mut testbed,
            Initialize {
                payer: admin.pubkey(),
                admin: admin.pubkey(),
                fee_collector,
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                system_program: system_program::ID,
            },
            &[&admin],
            config,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error but transaction succeeded");
    
    // Check for specific error code
    let error = result.unwrap_err();
    println!("Error: {:?}", error);
    
    // The account should not have been created
    let account = testbed.get_account(&marketplace_config);
    assert!(account.is_none(), "Account was created despite invalid duration");

    Ok(())
}

#[tokio::test]
async fn test_initialize_marketplace_twice() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await.unwrap();
    testbed.register_fusion_marketplace().await.unwrap();

    // Create and fund user accounts
    let admin = testbed.create_funded_user(10_000_000).unwrap();
    let fee_collector = Keypair::new().pubkey();

    // Valid config
    let config = MarketplaceConfigArgs {
        protocol_fee_bp: 250,
        min_bid_increment_bp: 500,
        min_listing_duration: 3600,
        max_listing_duration: 604800,
    };

    // Calculate PDA for marketplace config
    let (marketplace_config, _) =
        Pubkey::find_program_address(&[MARKETPLACE_CONFIG_SEED], &fusion_marketplace::ID);

    // First initialization should succeed
    let tx = testbed
        .fusion_marketplace()
        .initialize(
            &mut testbed,
            Initialize {
                payer: admin.pubkey(),
                admin: admin.pubkey(),
                fee_collector,
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                system_program: system_program::ID,
            },
            &[&admin],
            config.clone(),
        )
        .await?;

    println!("First Initialize Tx: {:?}", tx);

    // Second initialization should fail
    let result = testbed
        .fusion_marketplace()
        .initialize(
            &mut testbed,
            Initialize {
                payer: admin.pubkey(),
                admin: admin.pubkey(),
                fee_collector,
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                system_program: system_program::ID,
            },
            &[&admin],
            config,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error but transaction succeeded");
    
    // Check for specific error code (likely an Anchor constraint error)
    let error = result.unwrap_err();
    println!("Error: {:?}", error);

    Ok(())
}
