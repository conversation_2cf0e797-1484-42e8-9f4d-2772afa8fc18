use fusion_marketplace::{
    accounts::{CancelFixedPriceListing, CreateFixedPriceListing},
    constants::{
        DEFAULT_MAX_LISTING_DURATION, DEFAULT_MIN_LISTING_DURATION, LISTING_SEED,
        MARKETPLACE_CONFIG_SEED,
    },
    errors::ErrorCode,
    state::{Listing, ListingType, MarketplaceConfig, MarketplaceConfigArgs},
    testing::FusionMarketplaceProgramTestBed,
};
use solana_sdk::{
    pubkey::Pubkey, signature::Keypair, signer::Signer, system_program, sysvar::Sysvar,
    sysvar::SysvarId,
};
use workspace::{TestBed, TestBedError};
mod fixtures;
use anchor_lang::AnchorSerialize;
use fixtures::{
    marketplace::{marketplace_authority_pda, setup_marketplace},
    mpl_core::{MplCoreProgram, MplCoreProgramTestBed},
};
use solana_sdk::sysvar::clock::Clock;
use solana_sdk::sysvar::rent::Rent;

/// Test creating a fixed price listing
#[tokio::test]
async fn test_create_fixed_price_listing() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a fixed price listing
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day

    let tx = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await?;

    // Verify the listing was created correctly
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;

    assert_eq!(listing_account.seller, admin.pubkey());
    assert_eq!(listing_account.asset, asset);
    assert_eq!(listing_account.price, price);
    assert_eq!(listing_account.listing_type, ListingType::FixedPrice);
    assert!(listing_account.is_active);
    assert_eq!(listing_account.currency, None);

    // Verify the ends_at field is set correctly
    let clock = testbed.get_clock()?;
    let expected_end_time = clock.unix_timestamp + duration as i64;
    assert_eq!(listing_account.ends_at, Some(expected_end_time));

    Ok(())
}

/// Test cancelling a listing
#[tokio::test]
async fn test_cancel_listing() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // First create a listing to cancel
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day

    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await?;

    // Verify the listing is active before cancellation
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);

    // Simulating time advancement
    testbed.advance_clock_by_seconds(100)?;

    // Cancel the listing
    let tx = testbed
        .fusion_marketplace()
        .cancel_fixed_price_listing(
            &mut testbed,
            CancelFixedPriceListing {
                seller: admin.pubkey(),
                listing,
                clock: Clock::id(),
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                asset,
                collection: None,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
            },
            &[&admin],
        )
        .await?;

    // Verify the listing was cancelled
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_account.is_active);

    // Verify the updated_at timestamp was updated
    assert!(listing_account.updated_at > listing_account.created_at);

    Ok(())
}

/// Test cancelling a listing
#[tokio::test]
async fn test_cancel_listing_asset_in_collection() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Initialize marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock collection
    let collection = testbed
        .mpl_core()
        .create_mock_collection(&mut testbed, &admin)
        .await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft_with_collection(&mut testbed, &admin, collection)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // First create a listing to cancel
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day

    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: Some(collection),
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await?;

    // Verify the listing is active before cancellation
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);

    // Simulating time advancement
    testbed.advance_clock_by_seconds(100)?;

    // Cancel the listing
    let tx = testbed
        .fusion_marketplace()
        .cancel_fixed_price_listing(
            &mut testbed,
            CancelFixedPriceListing {
                seller: admin.pubkey(),
                listing,
                clock: Clock::id(),
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                asset,
                collection: Some(collection),
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
            },
            &[&admin],
        )
        .await?;

    // Verify the listing was cancelled
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_account.is_active);

    // Verify the updated_at timestamp was updated
    assert!(listing_account.updated_at > listing_account.created_at);

    Ok(())
}

/// Test creating multiple listings from the same seller
#[tokio::test]
async fn test_multiple_listings_same_seller() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    let (seller, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create multiple NFTs
    let asset1 = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;
    let asset2 = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;
    let asset3 = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Create PDAs for listings
    let (listing1, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset1.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );
    let (listing2, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset2.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );
    let (listing3, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset3.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create listings with different prices
    let price1 = 1_000_000_000; // 1 SOL
    let price2 = 2_000_000_000; // 2 SOL
    let price3 = 3_000_000_000; // 3 SOL
    let duration = 86400; // 1 day

    // Create listing 1
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset: asset1,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing: listing1,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price1,
            duration,
        )
        .await?;

    // Create listing 2
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset: asset2,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing: listing2,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price2,
            duration,
        )
        .await?;

    // Create listing 3
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset: asset3,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing: listing3,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price3,
            duration,
        )
        .await?;

    // Verify all listings were created correctly
    let listing1_account = testbed.get_account_data::<Listing>(&listing1).await?;
    let listing2_account = testbed.get_account_data::<Listing>(&listing2).await?;
    let listing3_account = testbed.get_account_data::<Listing>(&listing3).await?;

    assert_eq!(listing1_account.price, price1);
    assert_eq!(listing2_account.price, price2);
    assert_eq!(listing3_account.price, price3);

    assert!(listing1_account.is_active);
    assert!(listing2_account.is_active);
    assert!(listing3_account.is_active);

    Ok(())
}

/// Test listing for assets across different owners
#[tokio::test]
async fn test_listings_different_sellers() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create additional users
    let seller1 = testbed.create_funded_user(10_000_000)?;
    let seller2 = testbed.create_funded_user(10_000_000)?;

    // Create assets for each seller
    let asset1 = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller1)
        .await?;
    let asset2 = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller2)
        .await?;

    // Create listings for each seller
    let (listing1, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset1.as_ref(), seller1.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );
    let (listing2, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset2.as_ref(), seller2.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create listing for seller 1
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller1.pubkey(),
                asset: asset1,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing: listing1,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller1],
            1_000_000_000,
            86400,
        )
        .await?;

    // Create listing for seller 2
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller2.pubkey(),
                asset: asset2,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing: listing2,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller2],
            2_000_000_000,
            172800, // 2 days
        )
        .await?;

    // Verify listings
    let listing1_account = testbed.get_account_data::<Listing>(&listing1).await?;
    let listing2_account = testbed.get_account_data::<Listing>(&listing2).await?;

    assert_eq!(listing1_account.seller, seller1.pubkey());
    assert_eq!(listing2_account.seller, seller2.pubkey());

    assert!(listing1_account.is_active);
    assert!(listing2_account.is_active);

    Ok(())
}

/// Test unauthorized cancellation (only seller should be able to cancel)
#[tokio::test]
async fn test_unauthorized_listing_cancellation() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a seller and a different user
    let seller = testbed.create_funded_user(10_000_000)?;
    let other_user = testbed.create_funded_user(10_000_000)?;

    // Create an asset
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000,
            86400,
        )
        .await?;

    // Try to cancel the listing as a different user (should fail)
    let result = testbed
        .fusion_marketplace()
        .cancel_fixed_price_listing(
            &mut testbed,
            CancelFixedPriceListing {
                seller: other_user.pubkey(),
                listing,
                clock: Clock::id(),
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                asset,
                collection: None,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
            },
            &[&other_user],
        )
        .await;

    // Verify the error
    assert!(
        result.is_err(),
        "Expected error when non-seller tries to cancel listing"
    );

    // Verify the listing is still active
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);

    Ok(())
}

/// Test creating a listing with minimum allowed duration
#[tokio::test]
async fn test_listing_minimum_duration() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing with minimum allowed duration
    let price = 1_000_000_000; // 1 SOL
    let duration = DEFAULT_MIN_LISTING_DURATION; // Minimum allowed

    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await?;

    // Verify the listing was created
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);

    // Verify the end time is correctly set
    let clock = testbed.get_clock()?;
    let expected_end_time = clock.unix_timestamp + duration as i64;
    assert_eq!(listing_account.ends_at, Some(expected_end_time));

    Ok(())
}

/// Test creating a listing with maximum allowed duration
#[tokio::test]
async fn test_listing_maximum_duration() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing with maximum allowed duration
    let price = 1_000_000_000; // 1 SOL
    let duration = DEFAULT_MAX_LISTING_DURATION; // Maximum allowed

    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await?;

    // Verify the listing was created
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);

    // Verify the end time is correctly set
    let clock = testbed.get_clock()?;
    let expected_end_time = clock.unix_timestamp + duration as i64;
    assert_eq!(listing_account.ends_at, Some(expected_end_time));

    Ok(())
}

/// Test listing with too short duration (should fail)
#[tokio::test]
async fn test_listing_duration_too_short() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing with too short duration
    let price = 1_000_000_000; // 1 SOL
    let duration = DEFAULT_MIN_LISTING_DURATION - 60; // Too short (1 minute less than minimum)

    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error for duration too short");

    // Verify the listing account was not created
    let account = testbed.get_account(&listing);
    assert!(account.is_none(), "Listing account should not exist");

    Ok(())
}

/// Test listing with too long duration (should fail)
#[tokio::test]
async fn test_listing_duration_too_long() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing with too long duration
    let price = 1_000_000_000; // 1 SOL
    let duration = DEFAULT_MAX_LISTING_DURATION + 3600; // Too long (1 hour more than maximum)

    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error for duration too long");

    // Verify the listing account was not created
    let account = testbed.get_account(&listing);
    assert!(account.is_none(), "Listing account should not exist");

    Ok(())
}

/// Test listing with zero price (should fail)
#[tokio::test]
async fn test_listing_zero_price() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (admin, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &admin)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), admin.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing with zero price
    let price = 0; // Invalid price
    let duration = 86400; // 1 day

    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: admin.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&admin],
            price,
            duration,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error for zero price");

    // Verify the listing account was not created
    let account = testbed.get_account(&listing);
    assert!(account.is_none(), "Listing account should not exist");

    Ok(())
}

/// Test listing by non-owner (should fail)
#[tokio::test]
async fn test_listing_by_non_owner() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create users
    let owner = testbed.create_funded_user(10_000_000)?;
    let non_owner = testbed.create_funded_user(10_000_000)?;

    // Create asset owned by owner
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &owner)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), non_owner.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing by non-owner
    let price = 1_000_000_000; // 1 SOL
    let duration = 86400; // 1 day

    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: non_owner.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&non_owner],
            price,
            duration,
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error for non-owner listing");

    // Verify the listing account was not created
    let account = testbed.get_account(&listing);
    assert!(account.is_none(), "Listing account should not exist");

    Ok(())
}

/// Test listing expiration and behavior
#[tokio::test]
async fn test_listing_expiration() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create a user
    let seller = testbed.create_funded_user(10_000_000)?;

    // Create asset
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Create listing with minimum allowed duration
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Get current time
    let mut clock = testbed.get_clock()?;
    let now = clock.unix_timestamp;

    // Create listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000,
            DEFAULT_MIN_LISTING_DURATION,
        )
        .await?;

    // Verify listing created successfully
    let listing_data = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_data.is_active);
    assert_eq!(
        listing_data.ends_at,
        Some(now + DEFAULT_MIN_LISTING_DURATION as i64)
    );

    // Advance the clock to simulate expiration
    clock.unix_timestamp = listing_data.ends_at.unwrap() + 1;
    let now = clock.unix_timestamp;
    testbed.set_clock(clock)?;

    // Verify the listing appears expired but is still active
    let listing_data = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_data.is_active); // is_active is still true (just expired)
    assert!(listing_data.ends_at.unwrap() < now); // But it has expired

    Ok(())
}

/// Test that a listed NFT cannot be transferred
#[tokio::test]
async fn test_listed_nft_transfer_prevention() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller and buyer
    let seller = testbed.create_funded_user(10_000_000)?;
    let buyer = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000, // 1 SOL
            86400,         // 1 day
        )
        .await?;

    // Attempt to transfer the NFT directly (should fail because it's frozen)
    let transfer_result = testbed
        .mpl_core()
        .transfer(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            buyer.pubkey(),
            &[&seller],
        )
        .await;

    // Verify transfer was rejected
    assert!(
        transfer_result.is_err(),
        "Expected transfer of listed NFT to fail"
    );

    Ok(())
}

/// Test NFT can be transferred after listing is cancelled
#[tokio::test]
async fn test_transfer_after_cancellation() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller and buyer
    let seller = testbed.create_funded_user(10_000_000)?;
    let buyer = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000, // 1 SOL
            86400,         // 1 day
        )
        .await?;

    // Cancel the listing
    testbed
        .fusion_marketplace()
        .cancel_fixed_price_listing(
            &mut testbed,
            CancelFixedPriceListing {
                seller: seller.pubkey(),
                listing,
                clock: Clock::id(),
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                asset,
                collection: None,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
            },
            &[&seller],
        )
        .await?;

    // Now try to transfer the NFT (should succeed)
    let transfer_result = testbed
        .mpl_core()
        .transfer(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            buyer.pubkey(),
            &[&seller],
        )
        .await;

    // Verify transfer succeeded
    assert!(
        transfer_result.is_ok(),
        "Transfer after cancellation should succeed"
    );

    // Verify buyer now owns the NFT
    // We'd need to load the asset account and check the owner field
    let asset_account = testbed
        .get_account_data::<mpl_core::accounts::BaseAssetV1>(&asset)
        .await?;
    assert_eq!(
        asset_account.owner,
        buyer.pubkey(),
        "Buyer should now own the NFT"
    );

    Ok(())
}

/// Test ability to re-list an NFT after cancellation
#[tokio::test]
async fn test_relist_after_cancellation() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller
    let seller = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // First listing
    let price1 = 1_000_000_000; // 1 SOL
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price1,
            86400, // 1 day
        )
        .await?;

    // Cancel the listing
    testbed
        .fusion_marketplace()
        .cancel_fixed_price_listing(
            &mut testbed,
            CancelFixedPriceListing {
                seller: seller.pubkey(),
                listing,
                clock: Clock::id(),
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                asset,
                collection: None,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
            },
            &[&seller],
        )
        .await?;

    // Verify the new listing data
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(!listing_account.is_active);

    // Re-list at a different price
    let price2 = 2_000_000_000; // 2 SOL
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            price2,
            86400, // 1 day
        )
        .await?;

    // Verify the new listing data
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);
    assert_eq!(listing_account.price, price2);

    Ok(())
}

/// Test the plugins are correctly added when creating a listing
#[tokio::test]
async fn test_plugins_added_on_listing() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller
    let seller = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Get marketplace authority
    let marketplace_authority = marketplace_authority_pda();

    // Create a listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000, // 1 SOL
            86400,         // 1 day
        )
        .await?;

    // Verify the FreezeDelegate plugin is added and frozen
    let asset_account = testbed.get_account(&asset).unwrap();

    // In a real test, you might need to inspect the asset account data structure
    // For example, finding the plugin registry and checking that FreezeDelegate plugin exists
    // and is set to frozen: true, with the correct authority

    // For now, a simplified check might be to verify we can't transfer it
    let buyer = testbed.create_funded_user(10_000_000)?;
    let transfer_result = testbed
        .mpl_core()
        .transfer(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            buyer.pubkey(),
            &[&seller],
        )
        .await;

    assert!(
        transfer_result.is_err(),
        "Expected transfer to fail due to freeze plugin"
    );

    Ok(())
}

/// Test the plugins are correctly removed when cancelling a listing
#[tokio::test]
async fn test_plugins_removed_on_cancellation() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller and buyer
    let seller = testbed.create_funded_user(10_000_000)?;
    let buyer = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000, // 1 SOL
            86400,         // 1 day
        )
        .await?;

    // Cancel the listing
    testbed
        .fusion_marketplace()
        .cancel_fixed_price_listing(
            &mut testbed,
            CancelFixedPriceListing {
                seller: seller.pubkey(),
                listing,
                clock: Clock::id(),
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                asset,
                collection: None,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
            },
            &[&seller],
        )
        .await?;

    // Verify we can now transfer the NFT (proving plugins were removed)
    let transfer_result = testbed
        .mpl_core()
        .transfer(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            buyer.pubkey(),
            &[&seller],
        )
        .await;

    assert!(
        transfer_result.is_ok(),
        "Transfer should succeed after cancellation"
    );

    // Verify buyer now owns the NFT
    let asset_account = testbed
        .get_account_data::<mpl_core::accounts::BaseAssetV1>(&asset)
        .await?;
    assert_eq!(
        asset_account.owner,
        buyer.pubkey(),
        "Buyer should now own the NFT"
    );

    Ok(())
}

/// Test creating a duplicate listing for the same asset (should fail)
#[tokio::test]
async fn test_duplicate_listing() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Create seller
    let seller = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000, // 1 SOL
            86400,         // 1 day
        )
        .await?;

    // Try to create a duplicate listing
    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            2_000_000_000, // Different price
            86400,         // 1 day
        )
        .await;

    // Verify the error
    assert!(result.is_err(), "Expected error for duplicate listing");

    Ok(())
}

/// Test creating a listing for an asset that already has plugins
#[tokio::test]
async fn test_listing_with_existing_plugins() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;
    let seller = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Manually add a plugin to the asset before listing
    testbed
        .mpl_core()
        .add_freeze_delegate_plugin(
            &mut testbed,
            asset,
            seller.pubkey(),
            false, // Not frozen initially
            &[&seller],
        )
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Now try to create a listing - should succeed despite existing plugin
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000,
            86400,
        )
        .await?;

    // Verify the listing was created and asset is frozen
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);

    // Verify transfer fails (asset is frozen)
    let buyer = testbed.create_funded_user(10_000_000)?;
    let transfer_result = testbed
        .mpl_core()
        .transfer(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            buyer.pubkey(),
            &[&seller],
        )
        .await;

    assert!(
        transfer_result.is_err(),
        "Transfer should fail for listed NFT"
    );

    Ok(())
}

/// Test attempting to list an asset with plugins owned by external authority
#[tokio::test]
async fn test_listing_with_external_authority() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;
    let seller = testbed.create_funded_user(10_000_000)?;
    let external_auth = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Add a plugin with external authority
    testbed
        .mpl_core()
        .add_freeze_delegate_plugin_with_auth(
            &mut testbed,
            asset,
            seller.pubkey(),
            external_auth.pubkey(), // External authority
            true,                   // Frozen
            &[&seller],
        )
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Try to create a listing - should fail due to external authority
    let result = testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000,
            86400,
        )
        .await;

    // Verify the error - should be our custom AssetFrozenByExternalAuthority error
    assert!(
        result.is_err(),
        "Listing with external plugin authority should fail"
    );

    Ok(())
}

/// Test listing an asset that already has marketplace authority plugins
#[tokio::test]
async fn test_listing_with_marketplace_plugins() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;
    let seller = testbed.create_funded_user(10_000_000)?;
    let marketplace_authority = marketplace_authority_pda();

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Add a plugin with marketplace authority
    testbed
        .mpl_core()
        .add_freeze_delegate_plugin_with_auth(
            &mut testbed,
            asset,
            seller.pubkey(),
            marketplace_authority, // Marketplace authority
            false,                 // Not frozen
            &[&seller],
        )
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create a listing - should succeed with existing marketplace authority
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000,
            86400,
        )
        .await?;

    // Verify the listing was created and is active
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);

    Ok(())
}

/// Test complex scenario of listing, cancelling, changing plugins, and re-listing
#[tokio::test]
async fn test_complex_relisting_scenario() -> Result<(), TestBedError> {
    let mut testbed = TestBed::new().await?;
    testbed.register_fusion_marketplace().await?;
    testbed.register_mpl_core().await?;

    // Setup marketplace
    let (_, _, marketplace_config) = setup_marketplace(&mut testbed, None).await?;
    let seller = testbed.create_funded_user(10_000_000)?;
    let buyer = testbed.create_funded_user(10_000_000)?;

    // Create a mock NFT
    let asset = testbed
        .mpl_core()
        .create_mock_nft(&mut testbed, &seller)
        .await?;

    // Calculate PDA for listing
    let (listing, _) = Pubkey::find_program_address(
        &[LISTING_SEED, asset.as_ref(), seller.pubkey().as_ref()],
        &fusion_marketplace::ID,
    );

    // Create first listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            1_000_000_000,
            86400,
        )
        .await?;

    // Cancel the listing
    testbed
        .fusion_marketplace()
        .cancel_fixed_price_listing(
            &mut testbed,
            CancelFixedPriceListing {
                seller: seller.pubkey(),
                listing,
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                asset,
                collection: None,
                system_program: system_program::ID,
                mpl_core_program: mpl_core::ID,
                clock: Clock::id(),
            },
            &[&seller],
        )
        .await?;

    // Add some custom plugin with seller authority
    testbed
        .mpl_core()
        .add_freeze_delegate_plugin(&mut testbed, asset, seller.pubkey(), false, &[&seller])
        .await?;

    // Now try to create a new listing
    testbed
        .fusion_marketplace()
        .create_fixed_price_listing(
            &mut testbed,
            CreateFixedPriceListing {
                seller: seller.pubkey(),
                asset,
                collection: None,
                marketplace_authority: marketplace_authority_pda(),
                marketplace_config,
                listing,
                mpl_core_program: mpl_core::ID,
                system_program: system_program::ID,
                rent: Rent::id(),
                clock: Clock::id(),
            },
            &[&seller],
            2_000_000_000, // New price
            86400,
        )
        .await?;

    // Verify the re-listing was successful
    let listing_account = testbed.get_account_data::<Listing>(&listing).await?;
    assert!(listing_account.is_active);
    assert_eq!(listing_account.price, 2_000_000_000);

    // Verify transfer fails (asset should be frozen again)
    let transfer_result = testbed
        .mpl_core()
        .transfer(
            &mut testbed,
            asset,
            None,
            seller.pubkey(),
            buyer.pubkey(),
            &[&seller],
        )
        .await;

    assert!(
        transfer_result.is_err(),
        "Transfer should fail for listed NFT"
    );

    Ok(())
}
