use fusion_marketplace::{
    accounts::Initialize,
    constants::MARKETPLACE_CONFIG_SEED,
    state::{MarketplaceConfig, MarketplaceConfigArgs},
    testing::FusionMarketplaceProgramTestBed,
};
use solana_sdk::{pubkey::Pubkey, signature::Keypair, signer::Signer, system_program};
use workspace::{TestBed, TestBedError};

mod fixtures;
use fixtures::marketplace::setup_marketplace;

#[tokio::test]
async fn test_initialize_marketplace() -> Result<(), TestBedError> {
    // Setup testbed
    let mut testbed = TestBed::new().await.unwrap();
    testbed.register_fusion_marketplace().await.unwrap();

    let (admin, fee_collector, marketplace_config) = setup_marketplace(&mut testbed, None).await?;

    // Deserialize and verify all fields
    let marketplace_config = testbed
        .get_account_data::<MarketplaceConfig>(&marketplace_config).await?;
    
    assert_eq!(marketplace_config.admin, admin.pubkey());
    assert_eq!(marketplace_config.fee_collector, fee_collector);
    assert_eq!(marketplace_config.protocol_fee_bp, 250);

    Ok(())
}
