use mpl_core::types::{
    FreezeDelegate, Plugin, PluginAuthority, PluginType, Royalties, RuleSet, TransferDelegate,
};
use mpl_core::Asset;
use solana_sdk::{pubkey::Pubkey, signature::Keypair, signer::Signer, system_program};
use workspace::TestBedError;

#[derive(Debug, <PERSON>lone)]
pub struct MplCoreProgram {
    pub program_id: solana_sdk::pubkey::Pubkey,
}

impl MplCoreProgram {
    pub fn with_program_id(program_id: ::solana_sdk::pubkey::Pubkey) -> Self {
        Self { program_id }
    }

    pub async fn transfer(
        &mut self,
        testbed: &mut workspace::TestBed,
        asset: Pubkey,
        collection: Option<Pubkey>,
        seller: Pubkey,
        buyer: Pubkey,
        signers: &[&solana_sdk::signature::Keypair],
    ) -> std::result::Result<solana_sdk::signature::Signature, TestBedError> {
        let ix = mpl_core::instructions::TransferV1Builder::new()
            .asset(asset)
            .collection(collection)
            .authority(Some(seller))
            .new_owner(buyer)
            .payer(seller)
            .system_program(Some(system_program::ID))
            .instruction();

        testbed.execute_transaction(&[ix], signers).await
    }

    pub async fn create(
        &mut self,
        testbed: &mut workspace::TestBed,
        asset: Pubkey,
        name: String,
        uri: String,
        owner: Pubkey,
        signers: &[&solana_sdk::signature::Keypair],
        collection: Option<Pubkey>,
    ) -> std::result::Result<solana_sdk::signature::Signature, TestBedError> {
        let ix = mpl_core::instructions::CreateV1Builder::new()
            .asset(asset)
            .name(name)
            .uri(uri)
            .owner(Some(owner))
            .payer(owner)
            .system_program(system_program::ID)
            .collection(collection)
            .instruction();

        testbed.execute_transaction(&[ix], signers).await
    }

    pub async fn create_collection(
        &mut self,
        testbed: &mut workspace::TestBed,
        collection: Pubkey,
        name: String,
        uri: String,
        owner: Pubkey,
        signers: &[&solana_sdk::signature::Keypair],
    ) -> std::result::Result<solana_sdk::signature::Signature, TestBedError> {
        let ix = mpl_core::instructions::CreateCollectionV1Builder::new()
            .collection(collection)
            .name(name)
            .uri(uri)
            .payer(owner)
            .system_program(system_program::ID)
            .instruction();

        testbed.execute_transaction(&[ix], signers).await
    }

    pub async fn create_mock_nft(
        &mut self,
        testbed: &mut workspace::TestBed,
        owner: &solana_sdk::signature::Keypair,
    ) -> Result<Pubkey, TestBedError> {
        let asset = solana_sdk::signature::Keypair::new();

        self.create(
            testbed,
            asset.pubkey(),
            "Mock NFT".to_string(),
            "https://example.com/metadata/mock-nft.json".to_string(),
            owner.pubkey(),
            &[&owner, &asset],
            None,
        )
        .await?;

        Ok(asset.pubkey())
    }

    pub async fn create_mock_collection(
        &mut self,
        testbed: &mut workspace::TestBed,
        owner: &solana_sdk::signature::Keypair,
    ) -> Result<Pubkey, TestBedError> {
        let asset = solana_sdk::signature::Keypair::new();

        self.create_collection(
            testbed,
            asset.pubkey(),
            "Mock Collection".to_string(),
            "https://example.com/metadata/mock-collection.json".to_string(),
            owner.pubkey(),
            &[&owner, &asset],
        )
        .await?;

        Ok(asset.pubkey())
    }

    pub async fn create_mock_nft_with_collection(
        &mut self,
        testbed: &mut workspace::TestBed,
        owner: &solana_sdk::signature::Keypair,
        collection: Pubkey,
    ) -> Result<Pubkey, TestBedError> {
        let asset = solana_sdk::signature::Keypair::new();

        self.create(
            testbed,
            asset.pubkey(),
            "Mock NFT".to_string(),
            "https://example.com/metadata/mock-nft.json".to_string(),
            owner.pubkey(),
            &[&owner, &asset],
            Some(collection),
        )
        .await?;

        Ok(asset.pubkey())
    }

    pub async fn query_asset(
        &mut self,
        testbed: &mut workspace::TestBed,
        asset: Pubkey,
    ) -> Result<Box<mpl_core::Asset>, TestBedError> {
        let asset_account = testbed
            .get_account(&asset)
            .ok_or(TestBedError::AccountNotFound(asset))?;

        Asset::from_bytes(&asset_account.data)
            .map_err(|e| TestBedError::DeserializationError(e.to_string()))
    }

    /// Add a Royalty plugin to an asset
    pub async fn add_royalty_plugin(
        &mut self,
        testbed: &mut workspace::TestBed,
        asset: Pubkey,
        collection: Option<Pubkey>,
        owner: Pubkey,
        basis_points: u16,
        creators: Vec<(Pubkey, u8)>,
        signers: &[&Keypair],
    ) -> Result<solana_sdk::signature::Signature, TestBedError> {
        let royalty_plugin = Plugin::Royalties(Royalties {
            basis_points,
            creators: creators
                .into_iter()
                .map(|(address, percentage)| mpl_core::types::Creator {
                    address,
                    percentage,
                })
                .collect(),
            rule_set: RuleSet::None,
        });

        let ix = mpl_core::instructions::AddPluginV1Builder::new()
            .asset(asset)
            .collection(collection)
            .payer(owner)
            .plugin(royalty_plugin)
            .instruction();

        testbed.execute_transaction(&[ix], signers).await
    }

    /// Add a FreezeDelegate plugin to an asset
    pub async fn add_freeze_delegate_plugin(
        &mut self,
        testbed: &mut workspace::TestBed,
        asset: Pubkey,
        owner: Pubkey,
        frozen: bool,
        signers: &[&Keypair],
    ) -> Result<solana_sdk::signature::Signature, TestBedError> {
        let plugin = Plugin::FreezeDelegate(FreezeDelegate { frozen });

        let ix = mpl_core::instructions::AddPluginV1Builder::new()
            .asset(asset)
            .authority(Some(owner))
            .payer(owner)
            .system_program(system_program::ID)
            .plugin(plugin)
            .instruction();

        testbed.execute_transaction(&[ix], signers).await
    }

    /// Add a FreezeDelegate plugin with a specific authority
    pub async fn add_freeze_delegate_plugin_with_auth(
        &mut self,
        testbed: &mut workspace::TestBed,
        asset: Pubkey,
        owner: Pubkey,
        authority: Pubkey,
        frozen: bool,
        signers: &[&Keypair],
    ) -> Result<(), TestBedError> {
        // First add the plugin
        let plugin = Plugin::FreezeDelegate(FreezeDelegate { frozen });

        let add_ix = mpl_core::instructions::AddPluginV1Builder::new()
            .asset(asset)
            .authority(Some(owner))
            .payer(owner)
            .system_program(system_program::ID)
            .plugin(plugin)
            .init_authority(PluginAuthority::Address { address: authority })
            .instruction();

        testbed.execute_transaction(&[add_ix], signers).await?;

        Ok(())
    }

    /// Add a TransferDelegate plugin to an asset
    pub async fn add_transfer_delegate_plugin(
        &mut self,
        testbed: &mut workspace::TestBed,
        asset: Pubkey,
        owner: Pubkey,
        signers: &[&Keypair],
    ) -> Result<solana_sdk::signature::Signature, TestBedError> {
        let plugin = Plugin::TransferDelegate(TransferDelegate {});

        let ix = mpl_core::instructions::AddPluginV1Builder::new()
            .asset(asset)
            .authority(Some(owner))
            .payer(owner)
            .system_program(system_program::ID)
            .plugin(plugin)
            .instruction();

        testbed.execute_transaction(&[ix], signers).await
    }

    /// Add a TransferDelegate plugin with a specific authority
    pub async fn add_transfer_delegate_plugin_with_auth(
        &mut self,
        testbed: &mut workspace::TestBed,
        asset: Pubkey,
        owner: Pubkey,
        authority: Pubkey,
        signers: &[&Keypair],
    ) -> Result<(), TestBedError> {
        // First add the plugin
        let plugin = Plugin::TransferDelegate(TransferDelegate {});

        let add_ix = mpl_core::instructions::AddPluginV1Builder::new()
            .asset(asset)
            .authority(Some(owner))
            .payer(owner)
            .system_program(system_program::ID)
            .plugin(plugin)
            .instruction();

        testbed.execute_transaction(&[add_ix], signers).await?;

        // Then set the authority
        let auth_ix = mpl_core::instructions::ApprovePluginAuthorityV1Builder::new()
            .asset(asset)
            .authority(Some(owner))
            .payer(owner)
            .system_program(system_program::ID)
            .plugin_type(PluginType::TransferDelegate)
            .new_authority(PluginAuthority::Address { address: authority })
            .instruction();

        testbed.execute_transaction(&[auth_ix], signers).await?;

        Ok(())
    }
}

#[::async_trait::async_trait]
impl ::workspace::TestProgram for MplCoreProgram {
    fn program_id(&self) -> ::solana_sdk::pubkey::Pubkey {
        self.program_id
    }

    fn as_any(&self) -> &dyn ::std::any::Any {
        self
    }
}

#[async_trait::async_trait]
pub trait MplCoreProgramTestBed {
    async fn register_mpl_core(&mut self) -> std::result::Result<(), TestBedError>;
    fn mpl_core(&self) -> MplCoreProgram;
}

#[async_trait::async_trait]
impl MplCoreProgramTestBed for workspace::TestBed {
    async fn register_mpl_core(&mut self) -> std::result::Result<(), TestBedError> {
        let program = MplCoreProgram::with_program_id(mpl_core::ID);

        let workspace_root =
            find_workspace_root().unwrap_or_else(|| std::env::current_dir().unwrap_or_default());

        // Build absolute path to the program binary
        let program_path = workspace_root
            .join("tests/programs")
            .join(format!("{}.so", "mpl_core"))
            .to_string_lossy()
            .to_string();

        // Register the program with the TestBed
        self.register_program("mpl_core", program, &program_path)
            .await
    }

    fn mpl_core(&self) -> MplCoreProgram {
        self.get_program::<MplCoreProgram>("mpl_core")
            .expect("Program not registered")
            .clone()
    }
}

fn find_workspace_root() -> Option<std::path::PathBuf> {
    let mut current = std::env::current_dir().ok()?;

    // Walk up the directory tree until we find Anchor.toml
    loop {
        if current.join("Anchor.toml").exists() {
            return Some(current);
        }

        if !current.pop() {
            return None;
        }
    }
}
