use solana_sdk::{pubkey::Pubkey, signature::Keypair, signer::Signer, system_program, sysvar::SysvarId};
use workspace::{TestBed, TestBedError};
use fusion_marketplace::{
    constants::{LISTING_SEED, MARKETPLACE_AUTHORITY_SEED, MARKETPLACE_CONFIG_SEED},
    accounts::CreateFixedPriceListing,
    state::MarketplaceConfigArgs,
    testing::FusionMarketplaceProgramTestBed,
};

/// Helper function to initialize the marketplace
pub async fn setup_marketplace(
    testbed: &mut TestBed,
    config: Option<MarketplaceConfigArgs>
) -> Result<(Keypair, Pubkey, Pubkey), TestBedError> {
    // Create and fund user accounts
    let admin = testbed.create_funded_user(10_000_000_000)?;
    let fee_collector = testbed.create_funded_user(1_000_000_000)?.pubkey();

    // Initialize the marketplace with config
    let config = config.unwrap_or_default();

    // Calculate PDA for marketplace config
    let (marketplace_config, _) =
        Pubkey::find_program_address(&[MARKETPLACE_CONFIG_SEED], &fusion_marketplace::ID);

    // Execute initialize instruction
    let tx = testbed
        .fusion_marketplace()
        .initialize(
            testbed,
            fusion_marketplace::accounts::Initialize {
                payer: admin.pubkey(),
                admin: admin.pubkey(),
                fee_collector,
                marketplace_config,
                marketplace_authority: marketplace_authority_pda(),
                system_program: system_program::ID,
            },
            &[&admin],
            config,
        )
        .await?;

    Ok((admin, fee_collector, marketplace_config))
}

pub fn marketplace_authority_pda() -> Pubkey {
    let (marketplace_config, _) =
    Pubkey::find_program_address(&[MARKETPLACE_AUTHORITY_SEED], &fusion_marketplace::ID);

    marketplace_config
}