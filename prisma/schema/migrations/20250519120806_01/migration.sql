/*
  Warnings:

  - A unique constraint covering the columns `[nftId]` on the table `Order` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[collectionId]` on the table `Order` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "ActivityLog" ADD COLUMN     "amount" DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "Collection" ADD COLUMN     "nftTraitFilters" JSONB;

-- AlterTable
ALTER TABLE "Order" ADD COLUMN     "collectionId" TEXT,
ADD COLUMN     "nftId" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Order_nftId_key" ON "Order"("nftId");

-- CreateIndex
CREATE UNIQUE INDEX "Order_collectionId_key" ON "Order"("collectionId");

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_nftId_fkey" FOREIGN KEY ("nftId") REFERENCES "NFT"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_collectionId_fkey" FOREIGN KEY ("collectionId") REFERENCES "Collection"("id") ON DELETE SET NULL ON UPDATE CASCADE;
