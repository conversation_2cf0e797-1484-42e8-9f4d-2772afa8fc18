-- Create function to update user sales and purchase volumes
CREATE OR REPLACE FUNCTION update_user_transaction_volumes()
RETURNS TRIGGER AS $$
BEGIN
  -- Only process completed orders
  IF NEW."status" = 'COMPLETED' THEN
    -- Update seller's sales volume
    UPDATE "users"
    SET "totalSalesVolume" = "totalSalesVolume" + NEW."price"
    WHERE "id" = NEW."sellerId";

    -- Update buyer's purchase volume
    UPDATE "users"
    SET "totalPurchaseVolume" = "totalPurchaseVolume" + NEW."price"
    WHERE "id" = NEW."buyerId";

    -- Update collection's total volume and recent sales count
    IF NEW."listingId" IS NOT NULL THEN
      -- Get the collection ID from the NFT in the listing
      WITH listing_info AS (
        SELECT n."collectionId"
        FROM "Listing" l
        JOIN "NFT" n ON l."nftId" = n."id"
        WHERE l."id" = NEW."listingId"
      )
      UPDATE "Collection" c
      SET
        "totalVolume" = "totalVolume" + NEW."price",
        "recentSalesCount" = (
          SELECT COUNT(*)
          FROM "Order" o
          JOIN "Listing" l ON o."listingId" = l."id"
          JOIN "NFT" n ON l."nftId" = n."id"
          WHERE n."collectionId" = c."id"
          AND o."status" = 'COMPLETED'
          AND o."createdAt" > NOW() - INTERVAL '7 days'
        )
      FROM listing_info
      WHERE c."id" = listing_info."collectionId";
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update transaction volumes when an order is updated
DROP TRIGGER IF EXISTS update_user_transaction_volumes_trigger ON "Order";
CREATE TRIGGER update_user_transaction_volumes_trigger
AFTER INSERT OR UPDATE OF "status" ON "Order"
FOR EACH ROW
EXECUTE FUNCTION update_user_transaction_volumes();

-- Create function to update user activity score
CREATE OR REPLACE FUNCTION update_user_activity_score()
RETURNS TRIGGER AS $$
BEGIN
  -- Update user's activity score and last active timestamp
  IF NEW."userId" IS NOT NULL THEN
    UPDATE "users"
    SET
      "activityScore" = (
        -- Basic formula: weight different activities
        (SELECT COUNT(*) FROM "NFT" WHERE "creatorId" = NEW."userId") * 10 + -- Creating NFTs (weight: 10)
        (SELECT COUNT(*) FROM "Listing" WHERE "sellerId" = NEW."userId") * 5 + -- Listings (weight: 5)
        (SELECT COUNT(*) FROM "Bid" WHERE "bidderId" = NEW."userId") * 3 + -- Bids (weight: 3)
        (SELECT COUNT(*) FROM "Offer" WHERE "buyerId" = NEW."userId") * 3 + -- Offers (weight: 3)
        (SELECT COUNT(*) FROM "Order" WHERE "buyerId" = NEW."userId" OR "sellerId" = NEW."userId") * 8 + -- Transactions (weight: 8)
        (SELECT COUNT(*) FROM "ActivityLog" WHERE "userId" = NEW."userId") -- General activities (weight: 1)
      ),
      "lastActiveAt" = NOW()
    WHERE "id" = NEW."userId";
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update user activity score when a new activity is logged
DROP TRIGGER IF EXISTS update_user_activity_score_trigger ON "ActivityLog";
CREATE TRIGGER update_user_activity_score_trigger
AFTER INSERT ON "ActivityLog"
FOR EACH ROW
EXECUTE FUNCTION update_user_activity_score();

-- Create a scheduled job to refresh time-based metrics daily
CREATE OR REPLACE FUNCTION refresh_time_based_metrics()
RETURNS VOID AS $$
BEGIN
  -- Update NFT time-based activity counts
  UPDATE "NFT"
  SET
    "totalActivities7Days" = (
      SELECT COUNT(*)
      FROM "ActivityLog"
      WHERE "ActivityLog"."nftId" = "NFT"."id"
      AND "ActivityLog"."createdAt" > NOW() - INTERVAL '7 days'
    ),
    "totalActivities30Days" = (
      SELECT COUNT(*)
      FROM "ActivityLog"
      WHERE "ActivityLog"."nftId" = "NFT"."id"
      AND "ActivityLog"."createdAt" > NOW() - INTERVAL '30 days'
    );

  -- Update collection recent sales count
  UPDATE "Collection"
  SET "recentSalesCount" = (
    SELECT COUNT(*)
    FROM "Order" o
    JOIN "Listing" l ON o."listingId" = l."id"
    JOIN "NFT" n ON l."nftId" = n."id"
    WHERE n."collectionId" = "Collection"."id"
    AND o."status" = 'COMPLETED'
    AND o."createdAt" > NOW() - INTERVAL '7 days'
  );

  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Note: We're skipping pg_cron and will run the refresh function manually
-- The refresh_time_based_metrics() function can be called from:
-- 1. Application code using the refresh-metrics.ts script
-- 2. Directly in SQL using: SELECT refresh_time_based_metrics();
-- 3. An external scheduler like Heroku Scheduler or GitHub Actions
