-- Enable required extensions for the NFT marketplace
-- Note: Some extensions might require superuser privileges on Prisma Postgres

-- Enable btree_gin for better GIN index support
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- Try to enable pg_stat_statements for query performance monitoring
-- Using D<PERSON> block to handle cases where the user doesn't have superuser privileges
DO $$
BEGIN
  -- Check if we have permission to create the extension
  IF EXISTS (
    SELECT 1 FROM pg_roles 
    WHERE rolname = current_user 
    AND rolsuper = true
  ) THEN
    EXECUTE 'CREATE EXTENSION IF NOT EXISTS pg_stat_statements';
  ELSE
    RAISE NOTICE 'Skipping pg_stat_statements extension - requires superuser privileges';
  END IF;
END
$$;
