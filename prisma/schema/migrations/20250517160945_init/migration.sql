/*
  Warnings:

  - Made the column `floorPrice` on table `Collection` required. This step will fail if there are existing NULL values in that column.
  - Made the column `totalVolume` on table `Collection` required. This step will fail if there are existing NULL values in that column.
  - Made the column `totalItems` on table `Collection` required. This step will fail if there are existing NULL values in that column.
  - Made the column `recentSalesCount` on table `Collection` required. This step will fail if there are existing NULL values in that column.
  - Made the column `isVerified` on table `Collection` required. This step will fail if there are existing NULL values in that column.
  - Made the column `totalSalesVolume` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `totalPurchaseVolume` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `ownedNFTCount` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdNFTCount` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `collectionCount` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `followerCount` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `followingCount` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `isVerified` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `activityScore` on table `users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `lastActiveAt` on table `users` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterEnum
ALTER TYPE "ActivityType" ADD VALUE 'ROYALTY_RECEIVED';

-- DropIndex
DROP INDEX "Listing_status_price_idx";

-- DropIndex
DROP INDEX "NFT_attributes_gin_idx";

-- DropIndex
DROP INDEX "NFT_lastBiddingPrice_idx";

-- AlterTable
ALTER TABLE "Collection" ALTER COLUMN "floorPrice" SET NOT NULL,
ALTER COLUMN "totalVolume" SET NOT NULL,
ALTER COLUMN "totalItems" SET NOT NULL,
ALTER COLUMN "recentSalesCount" SET NOT NULL,
ALTER COLUMN "isVerified" SET NOT NULL;

-- AlterTable
ALTER TABLE "NFT" ADD COLUMN     "lastActivityAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "totalActivities" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "totalActivities30Days" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "totalActivities7Days" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "users" ALTER COLUMN "totalSalesVolume" SET NOT NULL,
ALTER COLUMN "totalPurchaseVolume" SET NOT NULL,
ALTER COLUMN "ownedNFTCount" SET NOT NULL,
ALTER COLUMN "createdNFTCount" SET NOT NULL,
ALTER COLUMN "collectionCount" SET NOT NULL,
ALTER COLUMN "followerCount" SET NOT NULL,
ALTER COLUMN "followingCount" SET NOT NULL,
ALTER COLUMN "isVerified" SET NOT NULL,
ALTER COLUMN "activityScore" SET NOT NULL,
ALTER COLUMN "lastActiveAt" SET NOT NULL;

-- CreateIndex
CREATE INDEX "ActivityLog_userId_idx" ON "ActivityLog"("userId");

-- CreateIndex
CREATE INDEX "ActivityLog_nftId_idx" ON "ActivityLog"("nftId");

-- CreateIndex
CREATE INDEX "ActivityLog_collectionId_idx" ON "ActivityLog"("collectionId");

-- CreateIndex
CREATE INDEX "ActivityLog_listingId_idx" ON "ActivityLog"("listingId");

-- CreateIndex
CREATE INDEX "ActivityLog_type_idx" ON "ActivityLog"("type");

-- CreateIndex
CREATE INDEX "ActivityLog_createdAt_idx" ON "ActivityLog"("createdAt");

-- CreateIndex
CREATE INDEX "Bid_listingId_idx" ON "Bid"("listingId");

-- CreateIndex
CREATE INDEX "Bid_bidderId_idx" ON "Bid"("bidderId");

-- CreateIndex
CREATE INDEX "Bid_amount_idx" ON "Bid"("amount");

-- CreateIndex
CREATE INDEX "Bid_createdAt_idx" ON "Bid"("createdAt");

-- CreateIndex
CREATE INDEX "Collection_ownerId_idx" ON "Collection"("ownerId");

-- CreateIndex
CREATE INDEX "Collection_createdAt_idx" ON "Collection"("createdAt");

-- CreateIndex
CREATE INDEX "Collection_updatedAt_idx" ON "Collection"("updatedAt");

-- CreateIndex
CREATE INDEX "Collection_totalLikes_idx" ON "Collection"("totalLikes");

-- CreateIndex
CREATE INDEX "Collection_totalViews_idx" ON "Collection"("totalViews");

-- CreateIndex
CREATE INDEX "Collection_name_idx" ON "Collection"("name");

-- CreateIndex
CREATE INDEX "Collection_floorPrice_idx" ON "Collection"("floorPrice");

-- CreateIndex
CREATE INDEX "Collection_totalVolume_idx" ON "Collection"("totalVolume");

-- CreateIndex
CREATE INDEX "Collection_totalItems_idx" ON "Collection"("totalItems");

-- CreateIndex
CREATE INDEX "Collection_recentSalesCount_idx" ON "Collection"("recentSalesCount");

-- CreateIndex
CREATE INDEX "Collection_isVerified_idx" ON "Collection"("isVerified");

-- CreateIndex
CREATE INDEX "Collection_category_idx" ON "Collection"("category");

-- CreateIndex
CREATE INDEX "Collection_symbol_idx" ON "Collection"("symbol");

-- CreateIndex
CREATE INDEX "Collection_isVerified_totalVolume_idx" ON "Collection"("isVerified", "totalVolume");

-- CreateIndex
CREATE INDEX "Collection_category_floorPrice_idx" ON "Collection"("category", "floorPrice");

-- CreateIndex
CREATE INDEX "Collection_category_totalVolume_idx" ON "Collection"("category", "totalVolume");

-- CreateIndex
CREATE INDEX "Listing_nftId_idx" ON "Listing"("nftId");

-- CreateIndex
CREATE INDEX "Listing_sellerId_idx" ON "Listing"("sellerId");

-- CreateIndex
CREATE INDEX "Listing_status_listingType_idx" ON "Listing"("status", "listingType");

-- CreateIndex
CREATE INDEX "Listing_createdAt_idx" ON "Listing"("createdAt");

-- CreateIndex
CREATE INDEX "Listing_endTime_idx" ON "Listing"("endTime");

-- CreateIndex
CREATE INDEX "NFT_creatorId_idx" ON "NFT"("creatorId");

-- CreateIndex
CREATE INDEX "NFT_ownerId_idx" ON "NFT"("ownerId");

-- CreateIndex
CREATE INDEX "NFT_collectionId_idx" ON "NFT"("collectionId");

-- CreateIndex
CREATE INDEX "NFT_createdAt_idx" ON "NFT"("createdAt");

-- CreateIndex
CREATE INDEX "NFT_totalLikes_idx" ON "NFT"("totalLikes");

-- CreateIndex
CREATE INDEX "NFT_totalViews_idx" ON "NFT"("totalViews");

-- CreateIndex
CREATE INDEX "NFT_totalActivities_idx" ON "NFT"("totalActivities");

-- CreateIndex
CREATE INDEX "NFT_totalActivities7Days_idx" ON "NFT"("totalActivities7Days");

-- CreateIndex
CREATE INDEX "NFT_totalActivities30Days_idx" ON "NFT"("totalActivities30Days");

-- CreateIndex
CREATE INDEX "NFT_lastActivityAt_idx" ON "NFT"("lastActivityAt");

-- CreateIndex
CREATE INDEX "NFT_name_idx" ON "NFT"("name");

-- CreateIndex
CREATE INDEX "Offer_nftId_idx" ON "Offer"("nftId");

-- CreateIndex
CREATE INDEX "Offer_buyerId_idx" ON "Offer"("buyerId");

-- CreateIndex
CREATE INDEX "Offer_sellerId_idx" ON "Offer"("sellerId");

-- CreateIndex
CREATE INDEX "Offer_status_idx" ON "Offer"("status");

-- CreateIndex
CREATE INDEX "Offer_createdAt_idx" ON "Offer"("createdAt");

-- CreateIndex
CREATE INDEX "Offer_expiresAt_idx" ON "Offer"("expiresAt");

-- CreateIndex
CREATE INDEX "Order_buyerId_idx" ON "Order"("buyerId");

-- CreateIndex
CREATE INDEX "Order_sellerId_idx" ON "Order"("sellerId");

-- CreateIndex
CREATE INDEX "Order_status_idx" ON "Order"("status");

-- CreateIndex
CREATE INDEX "Order_createdAt_idx" ON "Order"("createdAt");

-- CreateIndex
CREATE INDEX "collection_likes_userId_idx" ON "collection_likes"("userId");

-- CreateIndex
CREATE INDEX "collection_likes_createdAt_idx" ON "collection_likes"("createdAt");

-- CreateIndex
CREATE INDEX "follows_followerId_idx" ON "follows"("followerId");

-- CreateIndex
CREATE INDEX "follows_followingId_idx" ON "follows"("followingId");

-- CreateIndex
CREATE INDEX "follows_createdAt_idx" ON "follows"("createdAt");

-- CreateIndex
CREATE INDEX "minting_sessions_userId_idx" ON "minting_sessions"("userId");

-- CreateIndex
CREATE INDEX "minting_sessions_collectionId_idx" ON "minting_sessions"("collectionId");

-- CreateIndex
CREATE INDEX "minting_sessions_status_idx" ON "minting_sessions"("status");

-- CreateIndex
CREATE INDEX "minting_sessions_createdAt_idx" ON "minting_sessions"("createdAt");

-- CreateIndex
CREATE INDEX "nft_likes_userId_idx" ON "nft_likes"("userId");

-- CreateIndex
CREATE INDEX "nft_likes_createdAt_idx" ON "nft_likes"("createdAt");

-- CreateIndex
CREATE INDEX "sessions_userId_idx" ON "sessions"("userId");

-- CreateIndex
CREATE INDEX "sessions_active_idx" ON "sessions"("active");

-- CreateIndex
CREATE INDEX "sessions_createdAt_idx" ON "sessions"("createdAt");

-- CreateIndex
CREATE INDEX "users_username_idx" ON "users"("username");

-- CreateIndex
CREATE INDEX "users_publicKey_idx" ON "users"("publicKey");

-- CreateIndex
CREATE INDEX "users_createdAt_idx" ON "users"("createdAt");

-- CreateIndex
CREATE INDEX "users_updatedAt_idx" ON "users"("updatedAt");

-- CreateIndex
CREATE INDEX "users_totalSalesVolume_idx" ON "users"("totalSalesVolume");

-- CreateIndex
CREATE INDEX "users_totalPurchaseVolume_idx" ON "users"("totalPurchaseVolume");

-- CreateIndex
CREATE INDEX "users_ownedNFTCount_idx" ON "users"("ownedNFTCount");

-- CreateIndex
CREATE INDEX "users_createdNFTCount_idx" ON "users"("createdNFTCount");

-- CreateIndex
CREATE INDEX "users_collectionCount_idx" ON "users"("collectionCount");

-- CreateIndex
CREATE INDEX "users_followerCount_idx" ON "users"("followerCount");

-- CreateIndex
CREATE INDEX "users_isVerified_idx" ON "users"("isVerified");

-- CreateIndex
CREATE INDEX "users_activityScore_idx" ON "users"("activityScore");

-- CreateIndex
CREATE INDEX "users_lastActiveAt_idx" ON "users"("lastActiveAt");

-- CreateIndex
CREATE INDEX "users_isVerified_followerCount_idx" ON "users"("isVerified", "followerCount");

-- CreateIndex
CREATE INDEX "users_isVerified_totalSalesVolume_idx" ON "users"("isVerified", "totalSalesVolume");
