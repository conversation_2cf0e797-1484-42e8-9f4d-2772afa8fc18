-- Create function to update NFT activity counts
CREATE OR REPLACE FUNCTION update_nft_activity_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update total activity count
  UPDATE "NFT"
  SET 
    "totalActivities" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = NEW."nftId"),
    "totalActivities7Days" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = NEW."nftId" AND "createdAt" > NOW() - INTERVAL '7 days'),
    "totalActivities30Days" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = NEW."nftId" AND "createdAt" > NOW() - INTERVAL '30 days'),
    "lastActivityAt" = NOW()
  WHERE "id" = NEW."nftId";
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update NFT activity counts when a new activity is added
CREATE TRIGGER update_nft_activity_counts_trigger
AFTER INSERT ON "ActivityLog"
FOR EACH ROW
WHEN (NEW."nftId" IS NOT NULL)
EXECUTE FUNCTION update_nft_activity_counts();

-- Create function to update NFT activity counts when an activity is deleted
CREATE OR REPLACE FUNCTION update_nft_activity_counts_on_delete()
RETURNS TRIGGER AS $$
BEGIN
  -- Update total activity count
  UPDATE "NFT"
  SET 
    "totalActivities" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = OLD."nftId"),
    "totalActivities7Days" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = OLD."nftId" AND "createdAt" > NOW() - INTERVAL '7 days'),
    "totalActivities30Days" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = OLD."nftId" AND "createdAt" > NOW() - INTERVAL '30 days')
  WHERE "id" = OLD."nftId";
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update NFT activity counts when an activity is deleted
CREATE TRIGGER update_nft_activity_counts_on_delete_trigger
AFTER DELETE ON "ActivityLog"
FOR EACH ROW
WHEN (OLD."nftId" IS NOT NULL)
EXECUTE FUNCTION update_nft_activity_counts_on_delete();

-- Create a scheduled job to update the time-based activity counts daily
-- This ensures the 7-day and 30-day counts remain accurate even without new activities
CREATE OR REPLACE FUNCTION refresh_time_based_activity_counts()
RETURNS VOID AS $$
BEGIN
  UPDATE "NFT"
  SET 
    "totalActivities7Days" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "ActivityLog"."nftId" = "NFT"."id" AND "ActivityLog"."createdAt" > NOW() - INTERVAL '7 days'),
    "totalActivities30Days" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "ActivityLog"."nftId" = "NFT"."id" AND "ActivityLog"."createdAt" > NOW() - INTERVAL '30 days');
  
  RETURN;
END;
$$ LANGUAGE plpgsql;
-- Create a cron job to run the refresh function every 10 minutes
-- Note: This requires the pg_cron extension to be enabled
-- If pg_cron is not available, you'll need to set up an external scheduler
-- or implement this in your application code
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
    PERFORM cron.schedule('*/10 * * * *', 'SELECT refresh_time_based_activity_counts()');
  END IF;
END
$$;
