-- Add GIN index for JSON attributes field
CREATE INDEX IF NOT EXISTS "NFT_attributes_gin_idx" ON "NFT" USING GIN (attributes);

-- Add GIN index for JSON attributes field in ActivityLog
CREATE INDEX IF NOT EXISTS "ActivityLog_data_gin_idx" ON "ActivityLog" USING GIN (data);
-- FOR EACH ROW

-- Add index for price filtering
CREATE INDEX IF NOT EXISTS "NFT_lastBiddingPrice_idx" ON "NFT" ("lastBiddingPrice");

-- Add index for Listing price
CREATE INDEX IF NOT EXISTS "Listing_price_idx" ON "Listing" ("price");

-- Add composite index for active listings by price
CREATE INDEX IF NOT EXISTS "Listing_status_price_idx" ON "Listing" ("status", "price");

-- Add index for Offer price
CREATE INDEX IF NOT EXISTS "Offer_price_idx" ON "Offer" ("price");
