-- AlterTable
ALTER TABLE "ActivityLog" ADD COLUMN     "fromUserId" TEXT,
ADD COLUMN     "toUserId" TEXT;

-- CreateIndex
CREATE INDEX "ActivityLog_fromUserId_idx" ON "ActivityLog"("fromUserId");

-- CreateIndex
CREATE INDEX "ActivityLog_toUserId_idx" ON "ActivityLog"("toUserId");

-- AddForeignKey
ALTER TABLE "ActivityLog" ADD CONSTRAINT "ActivityLog_fromUserId_fkey" FOREIGN KEY ("fromUserId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ActivityLog" ADD CONSTRAINT "ActivityLog_toUserId_fkey" FOREIGN KEY ("toUserId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
