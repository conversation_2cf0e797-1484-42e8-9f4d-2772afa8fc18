-- Add new denormalized fields to Collection model
ALTER TABLE "Collection" ADD COLUMN IF NOT EXISTS "floorPrice" DOUBLE PRECISION DEFAULT 0;
ALTER TABLE "Collection" ADD COLUMN IF NOT EXISTS "totalVolume" DOUBLE PRECISION DEFAULT 0;
ALTER TABLE "Collection" ADD COLUMN IF NOT EXISTS "totalItems" INTEGER DEFAULT 0;
ALTER TABLE "Collection" ADD COLUMN IF NOT EXISTS "recentSalesCount" INTEGER DEFAULT 0;
ALTER TABLE "Collection" ADD COLUMN IF NOT EXISTS "isVerified" BOOLEAN DEFAULT false;
ALTER TABLE "Collection" ADD COLUMN IF NOT EXISTS "category" TEXT;

-- Add new denormalized fields to User model
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "totalSalesVolume" DOUBLE PRECISION DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "totalPurchaseVolume" DOUBLE PRECISION DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "ownedNFTCount" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "createdNFTCount" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "collectionCount" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "followerCount" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "followingCount" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "isVerified" BOOLEAN DEFAULT false;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "activityScore" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "lastActiveAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP;

-- Create function to update NFT activity counts
CREATE OR REPLACE FUNCTION update_nft_activity_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update total activity count
  UPDATE "NFT"
  SET 
    "totalActivities" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = NEW."nftId"),
    "totalActivities7Days" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = NEW."nftId" AND "createdAt" > NOW() - INTERVAL '7 days'),
    "totalActivities30Days" = (SELECT COUNT(*) FROM "ActivityLog" WHERE "nftId" = NEW."nftId" AND "createdAt" > NOW() - INTERVAL '30 days'),
    "lastActivityAt" = NOW()
  WHERE "id" = NEW."nftId";
  
  -- Update user activity score for the owner of the NFT, if applicable
  IF NEW."nftId" IS NOT NULL THEN
    -- Get the owner ID of the NFT that has activity
    UPDATE "users" u
    SET "activityScore" = "activityScore" + 1,
        "lastActiveAt" = NOW()
    FROM "NFT" n
    WHERE n."id" = NEW."nftId" AND n."ownerId" = u."id" AND n."ownerId" IS NOT NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update NFT activity counts when a new activity is added
DROP TRIGGER IF EXISTS update_nft_activity_counts_trigger ON "ActivityLog";
CREATE TRIGGER update_nft_activity_counts_trigger
AFTER INSERT ON "ActivityLog"
FOR EACH ROW
WHEN (NEW."nftId" IS NOT NULL)
EXECUTE FUNCTION update_nft_activity_counts();

-- Create function to update collection statistics
CREATE OR REPLACE FUNCTION update_collection_statistics()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Update collection item count for INSERT
    UPDATE "Collection"
    SET "totalItems" = "totalItems" + 1
    WHERE "id" = NEW."collectionId";
  ELSIF TG_OP = 'UPDATE' THEN
    -- If collection ID changed, update both old and new collections
    IF NEW."collectionId" <> OLD."collectionId" OR 
       (NEW."collectionId" IS NOT NULL AND OLD."collectionId" IS NULL) OR
       (NEW."collectionId" IS NULL AND OLD."collectionId" IS NOT NULL) THEN
      
      -- Decrement count for old collection if it exists
      IF OLD."collectionId" IS NOT NULL THEN
        UPDATE "Collection"
        SET "totalItems" = "totalItems" - 1
        WHERE "id" = OLD."collectionId";
      END IF;
      
      -- Increment count for new collection if it exists
      IF NEW."collectionId" IS NOT NULL THEN
        UPDATE "Collection"
        SET "totalItems" = "totalItems" + 1
        WHERE "id" = NEW."collectionId";
      END IF;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrement count for collection when NFT is deleted
    IF OLD."collectionId" IS NOT NULL THEN
      UPDATE "Collection"
      SET "totalItems" = "totalItems" - 1
      WHERE "id" = OLD."collectionId";
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update collection statistics when an NFT is added, updated or deleted
DROP TRIGGER IF EXISTS update_collection_statistics_trigger ON "NFT";
CREATE TRIGGER update_collection_statistics_trigger
AFTER INSERT OR UPDATE OF "collectionId" ON "NFT"
FOR EACH ROW
EXECUTE FUNCTION update_collection_statistics();

-- Create separate trigger for DELETE operations on NFT
DROP TRIGGER IF EXISTS update_collection_statistics_delete_trigger ON "NFT";
CREATE TRIGGER update_collection_statistics_delete_trigger
AFTER DELETE ON "NFT"
FOR EACH ROW
EXECUTE FUNCTION update_collection_statistics();

-- Create function to update collection floor price
CREATE OR REPLACE FUNCTION update_collection_floor_price()
RETURNS TRIGGER AS $$
DECLARE
  collection_id TEXT;
BEGIN
  -- Get the collection ID from the NFT
  SELECT "collectionId" INTO collection_id FROM "NFT" WHERE "id" = NEW."nftId";
  
  -- Update collection floor price if this is an active listing
  IF NEW."status" = 'ACTIVE' AND collection_id IS NOT NULL THEN
    UPDATE "Collection"
    SET "floorPrice" = (
      SELECT MIN(l."price")
      FROM "Listing" l
      JOIN "NFT" n ON l."nftId" = n."id"
      WHERE n."collectionId" = collection_id
      AND l."status" = 'ACTIVE'
    )
    WHERE "id" = collection_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update collection floor price when a listing is added or updated
DROP TRIGGER IF EXISTS update_collection_floor_price_trigger ON "Listing";
CREATE TRIGGER update_collection_floor_price_trigger
AFTER INSERT OR UPDATE OF "status", "price" ON "Listing"
FOR EACH ROW
EXECUTE FUNCTION update_collection_floor_price();

-- Create function to update user NFT counts
CREATE OR REPLACE FUNCTION update_user_nft_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update owned NFT count for the new owner
  IF NEW."ownerId" IS NOT NULL THEN
    UPDATE "users"
    SET "ownedNFTCount" = (SELECT COUNT(*) FROM "NFT" WHERE "ownerId" = NEW."ownerId")
    WHERE "id" = NEW."ownerId";
  END IF;
  
  -- Update created NFT count
  UPDATE "users"
  SET "createdNFTCount" = (SELECT COUNT(*) FROM "NFT" WHERE "creatorId" = NEW."creatorId")
  WHERE "id" = NEW."creatorId";
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update user NFT counts when an NFT is added or ownership changes
DROP TRIGGER IF EXISTS update_user_nft_counts_trigger ON "NFT";
CREATE TRIGGER update_user_nft_counts_trigger
AFTER INSERT OR UPDATE OF "ownerId" ON "NFT"
FOR EACH ROW
EXECUTE FUNCTION update_user_nft_counts();

-- Create function to update user collection count
CREATE OR REPLACE FUNCTION update_user_collection_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE "users"
  SET "collectionCount" = (SELECT COUNT(*) FROM "Collection" WHERE "ownerId" = NEW."ownerId")
  WHERE "id" = NEW."ownerId";
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update user collection count when a collection is added
DROP TRIGGER IF EXISTS update_user_collection_count_trigger ON "Collection";
CREATE TRIGGER update_user_collection_count_trigger
AFTER INSERT ON "Collection"
FOR EACH ROW
EXECUTE FUNCTION update_user_collection_count();

-- Create function to update user follower and following counts
CREATE OR REPLACE FUNCTION update_user_follow_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update follower count for the user being followed
  UPDATE "users"
  SET "followerCount" = (SELECT COUNT(*) FROM "follows" WHERE "followingId" = NEW."followingId")
  WHERE "id" = NEW."followingId";
  
  -- Update following count for the follower
  UPDATE "users"
  SET "followingCount" = (SELECT COUNT(*) FROM "follows" WHERE "followerId" = NEW."followerId")
  WHERE "id" = NEW."followerId";
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update user follow counts when a follow relationship is created
DROP TRIGGER IF EXISTS update_user_follow_counts_trigger ON "follows";
CREATE TRIGGER update_user_follow_counts_trigger
AFTER INSERT ON "follows"
FOR EACH ROW
EXECUTE FUNCTION update_user_follow_counts();
