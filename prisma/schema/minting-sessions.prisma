model MintingSession {
  id                 String        @id @default(uuid())
  sessionKey         String        @unique
  status             MintingStatus @default(PENDING)
  transactionHash    String?
  metadataUri        String?
  royaltyBasisPoints Int           @default(0)
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  active             Boolean       @default(true)
  userId             String
  collectionId       String?
  collection         Collection?   @relation(fields: [collectionId], references: [id])
  user               User          @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([collectionId])
  @@index([status])
  @@index([createdAt])
  @@map("minting_sessions")
}

enum MintingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
