model Offer {
  id           String      @id @default(uuid())
  publicKey    String
  escrow       String
  price        Float
  status       OfferStatus
  expiresAt    DateTime?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  collectionId String?
  nftId        String
  buyerId      String
  sellerId     String
  buyer        User        @relation("buyerOffers", fields: [buyerId], references: [id])
  nft          NFT         @relation(fields: [nftId], references: [id])
  collection   Collection? @relation(fields: [collectionId], references: [id])
  seller       User        @relation("sellerOffers", fields: [sellerId], references: [id])
  orders       Order[]

  @@index([nftId])
  @@index([buyerId])
  @@index([sellerId])
  @@index([status])
  @@index([price])
  @@index([createdAt])
  @@index([expiresAt])
}

enum OfferStatus {
  PENDING
  ACCEPTED
  REJECTED
  CANCELLED
  EXPIRED
}
