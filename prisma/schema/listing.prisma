model Listing {
  id              String        @id @default(uuid())
  listKey         String
  auctionKey      String?
  escrow          String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  price           Float
  currency        String        @default("SOL")
  status          ListingStatus
  listingType     ListingType
  startTime       DateTime      @default(now())
  endTime         DateTime?
  minBidIncrement Float?
  reservePrice    Float?
  instantBuyPrice Float?
  lastBidId       String?       @unique
  nftId           String
  collectionId    String?
  sellerId        String
  userId          String?
  activityLogs    ActivityLog[]
  bids            Bid[]         @relation("listingBids")
  lastBid         Bid?          @relation("lastBidRelation", fields: [lastBidId], references: [id])
  nft             NFT           @relation(fields: [nftId], references: [id])
  collection      Collection?   @relation(fields: [collectionId], references: [id])
  seller          User          @relation("sellerListings", fields: [sellerId], references: [id])
  User            User?         @relation(fields: [userId], references: [id])
  order           Order?

  @@index([nftId])
  @@index([sellerId])
  @@index([status, listingType])
  @@index([price])
  @@index([createdAt])
  @@index([endTime])
}

enum ListingStatus {
  ACTIVE
  SOLD
  CANCELLED
  EXPIRED
}

enum ListingType {
  FIXED_PRICE
  AUCTION
}
