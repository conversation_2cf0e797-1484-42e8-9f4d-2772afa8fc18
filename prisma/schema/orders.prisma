model Order {
  id              String        @id @default(uuid())
  publicKey       String
  price           Float
  currency        String        @default("SOL")
  status          OrderStatus
  transactionHash String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  listingId       String?       @unique
  nftId           String?
  offerId         String?
  collectionId    String?
  buyerId         String
  sellerId        String
  activityLogs    ActivityLog[]
  buyer           User          @relation("buyerOrders", fields: [buyerId], references: [id])
  listing         Listing?      @relation(fields: [listingId], references: [id])
  nft             NFT?          @relation(fields: [nftId], references: [id])
  offer           Offer?        @relation(fields: [offerId], references: [id])
  collection      Collection?   @relation(fields: [collectionId], references: [id])
  seller          User          @relation("sellerOrders", fields: [sellerId], references: [id])

  @@index([buyerId])
  @@index([sellerId])
  @@index([status])
  @@index([createdAt])
  @@index([nftId])
}

enum OrderStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}
