model ActivityLog {
  id              String       @id @default(uuid())
  transactionHash String
  type            ActivityType
  amount          Float?
  data            Json?
  createdAt       DateTime     @default(now())
  userId          String?
  fromUserId      String?
  toUserId        String?
  nftId           String?
  collectionId    String?
  listingId       String?
  bidId           String?
  orderId         String?
  bid             Bid?         @relation(fields: [bidId], references: [id])
  collection      Collection?  @relation(fields: [collectionId], references: [id])
  listing         Listing?     @relation(fields: [listingId], references: [id])
  nft             NFT?         @relation(fields: [nftId], references: [id])
  order           Order?       @relation(fields: [orderId], references: [id])
  user            User?        @relation(fields: [userId], references: [id])
  fromUser        User?        @relation("fromUserActivities", fields: [fromUserId], references: [id])
  toUser          User?        @relation("toUserActivities", fields: [toUserId], references: [id])

  @@index([userId])
  @@index([fromUserId])
  @@index([toUserId])
  @@index([nftId])
  @@index([collectionId])
  @@index([listingId])
  @@index([type])
  @@index([createdAt])
}

enum ActivityType {
  ACCOUNT_CREATED
  NFT_MINTED
  NFT_LISTED
  NFT_PRICE_UPDATED
  NFT_TRANSFERRED
  COLLECTION_CREATED
  METADATA_UPDATED
  NFT_BID_PLACED
  NFT_LISTING_CANCELLED
  NFT_OFFER_CREATED
  NFT_OFFER_ACCEPTED
  NFT_OFFER_CANCELLED
  ROYALTY_RECEIVED
}
