model Collection {
  id                 String           @id @default(uuid())
  name               String
  xUrl               String?
  websiteUrl         String?
  publicKey          String           @unique
  description        String
  logoUrl            String
  tags               String[]
  bannerUrl          String
  nftTraitFilters    Json?
  metadataUri        String
  royaltyBasisPoints Int              @default(0)
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  totalLikes         Int              @default(0)
  totalViews         Int              @default(0)
  floorPrice         Float            @default(0) // Lowest active listing price
  totalVolume        Float            @default(0) // Total sales volume
  totalItems         Int              @default(0) // Number of NFTs
  recentSalesCount   Int              @default(0) // Sales in last 7 days
  isVerified         Boolean          @default(false) // Verification status
  category           String? // Collection category/type
  ownerId            String
  activityLogs       ActivityLog[]
  owner              User             @relation(fields: [ownerId], references: [id])
  nfts               NFT[]
  listings           Listing[]
  orders             Order[]
  mintingSessions    MintingSession[]
  offers             Offer[]

  @@index([tags])
  @@index([ownerId])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([totalLikes])
  @@index([totalViews])
  @@index([name])
  @@index([floorPrice])
  @@index([totalVolume])
  @@index([totalItems])
  @@index([recentSalesCount])
  @@index([isVerified])
  @@index([category])
  @@index([isVerified, totalVolume])
  @@index([category, floorPrice])
  @@index([category, totalVolume])
}

model CollectionLike {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  userId    String
  publicKey String
  user      User     @relation(fields: [userId], references: [id])

  @@unique([userId, publicKey])
  @@index([userId])
  @@index([createdAt])
  @@map("collection_likes")
}
