model Bid {
  id             String        @id @default(uuid())
  amount         Float
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  listingId      String
  bidderId       String
  activityLogs   ActivityLog[]
  bidder         User          @relation("bidderBids", fields: [bidderId], references: [id])
  listing        Listing       @relation("listingBids", fields: [listingId], references: [id])
  lastBidListing Listing?      @relation("lastBidRelation")

  @@index([listingId])
  @@index([bidderId])
  @@index([amount])
  @@index([createdAt])
}
