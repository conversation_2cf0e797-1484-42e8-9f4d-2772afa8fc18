model User {
  id                  String           @id @default(uuid())
  publicKey           String           @unique
  email               String?
  imageUrl            String?
  bannerUrl           String?
  instagramId         String?
  telegramId          String?
  twitterId           String?
  websiteId           String?
  bio                 String?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  userName            String?
  facebookId          String?
  username            String           @unique
  totalSalesVolume    Float            @default(0) // Total volume sold
  totalPurchaseVolume Float            @default(0) // Total volume purchased
  ownedNFTCount       Int              @default(0) // Number of NFTs owned
  createdNFTCount     Int              @default(0) // Number of NFTs created
  collectionCount     Int              @default(0) // Number of collections created
  followerCount       Int              @default(0) // Number of followers
  followingCount      Int              @default(0) // Number of users followed
  isVerified          Boolean          @default(false) // Verification status
  activityScore       Int              @default(0) // Overall activity score
  lastActiveAt        DateTime         @default(now()) // Last activity timestamp
  activityLogs        ActivityLog[]
  fromUserActivities  ActivityLog[]    @relation("fromUserActivities")
  toUserActivities    ActivityLog[]    @relation("toUserActivities")
  bids                Bid[]            @relation("bidderBids")
  collections         Collection[]
  listings            Listing[]        @relation("sellerListings")
  Listing             Listing[]
  createdNFTs         NFT[]            @relation("creator")
  ownedNFTs           NFT[]            @relation("owner")
  offers              Offer[]          @relation("buyerOffers")
  receivedOffers      Offer[]          @relation("sellerOffers")
  buyerOrders         Order[]          @relation("buyerOrders")
  sellerOrders        Order[]          @relation("sellerOrders")
  collectionLikes     CollectionLike[]
  following           Follow[]         @relation("follower")
  followers           Follow[]         @relation("following")
  MintingSession      MintingSession[]
  nftLikes            NftLike[]
  Session             Session[]

  @@index([username])
  @@index([publicKey])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([totalSalesVolume])
  @@index([totalPurchaseVolume])
  @@index([ownedNFTCount])
  @@index([createdNFTCount])
  @@index([collectionCount])
  @@index([followerCount])
  @@index([isVerified])
  @@index([activityScore])
  @@index([lastActiveAt])
  @@index([isVerified, followerCount])
  @@index([isVerified, totalSalesVolume])
  @@map("users")
}

model Follow {
  id          String   @id @default(uuid())
  followerId  String
  followingId String
  createdAt   DateTime @default(now())
  follower    User     @relation("follower", fields: [followerId], references: [id])
  following   User     @relation("following", fields: [followingId], references: [id])

  @@unique([followerId, followingId])
  @@index([followerId])
  @@index([followingId])
  @@index([createdAt])
  @@map("follows")
}
