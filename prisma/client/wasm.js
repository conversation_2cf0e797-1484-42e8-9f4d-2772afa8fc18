
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.ActivityLogScalarFieldEnum = {
  id: 'id',
  transactionHash: 'transactionHash',
  type: 'type',
  amount: 'amount',
  data: 'data',
  createdAt: 'createdAt',
  userId: 'userId',
  fromUserId: 'fromUserId',
  toUserId: 'toUserId',
  nftId: 'nftId',
  collectionId: 'collectionId',
  listingId: 'listingId',
  bidId: 'bidId',
  orderId: 'orderId'
};

exports.Prisma.BidScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  listingId: 'listingId',
  bidderId: 'bidderId'
};

exports.Prisma.CollectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  xUrl: 'xUrl',
  websiteUrl: 'websiteUrl',
  publicKey: 'publicKey',
  description: 'description',
  logoUrl: 'logoUrl',
  tags: 'tags',
  bannerUrl: 'bannerUrl',
  nftTraitFilters: 'nftTraitFilters',
  metadataUri: 'metadataUri',
  royaltyBasisPoints: 'royaltyBasisPoints',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  totalLikes: 'totalLikes',
  totalViews: 'totalViews',
  floorPrice: 'floorPrice',
  totalVolume: 'totalVolume',
  totalItems: 'totalItems',
  recentSalesCount: 'recentSalesCount',
  isVerified: 'isVerified',
  category: 'category',
  ownerId: 'ownerId'
};

exports.Prisma.CollectionLikeScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  userId: 'userId',
  publicKey: 'publicKey'
};

exports.Prisma.ListingScalarFieldEnum = {
  id: 'id',
  listKey: 'listKey',
  auctionKey: 'auctionKey',
  escrow: 'escrow',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  price: 'price',
  currency: 'currency',
  status: 'status',
  listingType: 'listingType',
  startTime: 'startTime',
  endTime: 'endTime',
  minBidIncrement: 'minBidIncrement',
  reservePrice: 'reservePrice',
  instantBuyPrice: 'instantBuyPrice',
  lastBidId: 'lastBidId',
  nftId: 'nftId',
  collectionId: 'collectionId',
  sellerId: 'sellerId',
  userId: 'userId'
};

exports.Prisma.MintingSessionScalarFieldEnum = {
  id: 'id',
  sessionKey: 'sessionKey',
  status: 'status',
  transactionHash: 'transactionHash',
  metadataUri: 'metadataUri',
  royaltyBasisPoints: 'royaltyBasisPoints',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  active: 'active',
  userId: 'userId',
  collectionId: 'collectionId'
};

exports.Prisma.NFTScalarFieldEnum = {
  id: 'id',
  name: 'name',
  publicKey: 'publicKey',
  description: 'description',
  imageUrl: 'imageUrl',
  metadataUrl: 'metadataUrl',
  tags: 'tags',
  attributes: 'attributes',
  royaltyBasisPoints: 'royaltyBasisPoints',
  isLocked: 'isLocked',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastBiddingPrice: 'lastBiddingPrice',
  totalLikes: 'totalLikes',
  totalViews: 'totalViews',
  totalActivities: 'totalActivities',
  totalActivities7Days: 'totalActivities7Days',
  totalActivities30Days: 'totalActivities30Days',
  lastActivityAt: 'lastActivityAt',
  ownerId: 'ownerId',
  creatorId: 'creatorId',
  collectionId: 'collectionId'
};

exports.Prisma.NftLikeScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  userId: 'userId',
  publicKey: 'publicKey'
};

exports.Prisma.OfferScalarFieldEnum = {
  id: 'id',
  publicKey: 'publicKey',
  escrow: 'escrow',
  price: 'price',
  status: 'status',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  collectionId: 'collectionId',
  nftId: 'nftId',
  buyerId: 'buyerId',
  sellerId: 'sellerId'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  publicKey: 'publicKey',
  price: 'price',
  currency: 'currency',
  status: 'status',
  transactionHash: 'transactionHash',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  listingId: 'listingId',
  nftId: 'nftId',
  offerId: 'offerId',
  collectionId: 'collectionId',
  buyerId: 'buyerId',
  sellerId: 'sellerId'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  refreshToken: 'refreshToken',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  active: 'active'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  publicKey: 'publicKey',
  email: 'email',
  imageUrl: 'imageUrl',
  bannerUrl: 'bannerUrl',
  instagramId: 'instagramId',
  telegramId: 'telegramId',
  twitterId: 'twitterId',
  websiteId: 'websiteId',
  bio: 'bio',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userName: 'userName',
  facebookId: 'facebookId',
  username: 'username',
  totalSalesVolume: 'totalSalesVolume',
  totalPurchaseVolume: 'totalPurchaseVolume',
  ownedNFTCount: 'ownedNFTCount',
  createdNFTCount: 'createdNFTCount',
  collectionCount: 'collectionCount',
  followerCount: 'followerCount',
  followingCount: 'followingCount',
  isVerified: 'isVerified',
  activityScore: 'activityScore',
  lastActiveAt: 'lastActiveAt'
};

exports.Prisma.FollowScalarFieldEnum = {
  id: 'id',
  followerId: 'followerId',
  followingId: 'followingId',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.ActivityType = exports.$Enums.ActivityType = {
  ACCOUNT_CREATED: 'ACCOUNT_CREATED',
  NFT_MINTED: 'NFT_MINTED',
  NFT_LISTED: 'NFT_LISTED',
  NFT_PRICE_UPDATED: 'NFT_PRICE_UPDATED',
  NFT_TRANSFERRED: 'NFT_TRANSFERRED',
  COLLECTION_CREATED: 'COLLECTION_CREATED',
  METADATA_UPDATED: 'METADATA_UPDATED',
  NFT_BID_PLACED: 'NFT_BID_PLACED',
  NFT_LISTING_CANCELLED: 'NFT_LISTING_CANCELLED',
  NFT_OFFER_CREATED: 'NFT_OFFER_CREATED',
  NFT_OFFER_ACCEPTED: 'NFT_OFFER_ACCEPTED',
  NFT_OFFER_CANCELLED: 'NFT_OFFER_CANCELLED',
  ROYALTY_RECEIVED: 'ROYALTY_RECEIVED'
};

exports.ListingStatus = exports.$Enums.ListingStatus = {
  ACTIVE: 'ACTIVE',
  SOLD: 'SOLD',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED'
};

exports.ListingType = exports.$Enums.ListingType = {
  FIXED_PRICE: 'FIXED_PRICE',
  AUCTION: 'AUCTION'
};

exports.MintingStatus = exports.$Enums.MintingStatus = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED'
};

exports.OfferStatus = exports.$Enums.OfferStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED'
};

exports.Prisma.ModelName = {
  ActivityLog: 'ActivityLog',
  Bid: 'Bid',
  Collection: 'Collection',
  CollectionLike: 'CollectionLike',
  Listing: 'Listing',
  MintingSession: 'MintingSession',
  NFT: 'NFT',
  NftLike: 'NftLike',
  Offer: 'Offer',
  Order: 'Order',
  Session: 'Session',
  User: 'User',
  Follow: 'Follow'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
