model ActivityLog {
  id              String       @id @default(uuid())
  transactionHash String
  type            ActivityType
  amount          Float?
  data            Json?
  createdAt       DateTime     @default(now())
  userId          String?
  fromUserId      String?
  toUserId        String?
  nftId           String?
  collectionId    String?
  listingId       String?
  bidId           String?
  orderId         String?
  bid             Bid?         @relation(fields: [bidId], references: [id])
  collection      Collection?  @relation(fields: [collectionId], references: [id])
  listing         Listing?     @relation(fields: [listingId], references: [id])
  nft             NFT?         @relation(fields: [nftId], references: [id])
  order           Order?       @relation(fields: [orderId], references: [id])
  user            User?        @relation(fields: [userId], references: [id])
  fromUser        User?        @relation("fromUserActivities", fields: [fromUserId], references: [id])
  toUser          User?        @relation("toUserActivities", fields: [toUserId], references: [id])

  @@index([userId])
  @@index([fromUserId])
  @@index([toUserId])
  @@index([nftId])
  @@index([collectionId])
  @@index([listingId])
  @@index([type])
  @@index([createdAt])
}

enum ActivityType {
  ACCOUNT_CREATED
  NFT_MINTED
  NFT_LISTED
  NFT_PRICE_UPDATED
  NFT_TRANSFERRED
  COLLECTION_CREATED
  METADATA_UPDATED
  NFT_BID_PLACED
  NFT_LISTING_CANCELLED
  NFT_OFFER_CREATED
  NFT_OFFER_ACCEPTED
  NFT_OFFER_CANCELLED
  ROYALTY_RECEIVED
}

model Bid {
  id             String        @id @default(uuid())
  amount         Float
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  listingId      String
  bidderId       String
  activityLogs   ActivityLog[]
  bidder         User          @relation("bidderBids", fields: [bidderId], references: [id])
  listing        Listing       @relation("listingBids", fields: [listingId], references: [id])
  lastBidListing Listing?      @relation("lastBidRelation")

  @@index([listingId])
  @@index([bidderId])
  @@index([amount])
  @@index([createdAt])
}

model Collection {
  id                 String           @id @default(uuid())
  name               String
  xUrl               String?
  websiteUrl         String?
  publicKey          String           @unique
  description        String
  logoUrl            String
  tags               String[]
  bannerUrl          String
  nftTraitFilters    Json?
  metadataUri        String
  royaltyBasisPoints Int              @default(0)
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  totalLikes         Int              @default(0)
  totalViews         Int              @default(0)
  floorPrice         Float            @default(0) // Lowest active listing price
  totalVolume        Float            @default(0) // Total sales volume
  totalItems         Int              @default(0) // Number of NFTs
  recentSalesCount   Int              @default(0) // Sales in last 7 days
  isVerified         Boolean          @default(false) // Verification status
  category           String? // Collection category/type
  ownerId            String
  activityLogs       ActivityLog[]
  owner              User             @relation(fields: [ownerId], references: [id])
  nfts               NFT[]
  listings           Listing[]
  orders             Order[]
  mintingSessions    MintingSession[]
  offers             Offer[]

  @@index([tags])
  @@index([ownerId])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([totalLikes])
  @@index([totalViews])
  @@index([name])
  @@index([floorPrice])
  @@index([totalVolume])
  @@index([totalItems])
  @@index([recentSalesCount])
  @@index([isVerified])
  @@index([category])
  @@index([isVerified, totalVolume])
  @@index([category, floorPrice])
  @@index([category, totalVolume])
}

model CollectionLike {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  userId    String
  publicKey String
  user      User     @relation(fields: [userId], references: [id])

  @@unique([userId, publicKey])
  @@index([userId])
  @@index([createdAt])
  @@map("collection_likes")
}

model Listing {
  id              String        @id @default(uuid())
  listKey         String
  auctionKey      String?
  escrow          String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  price           Float
  currency        String        @default("SOL")
  status          ListingStatus
  listingType     ListingType
  startTime       DateTime      @default(now())
  endTime         DateTime?
  minBidIncrement Float?
  reservePrice    Float?
  instantBuyPrice Float?
  lastBidId       String?       @unique
  nftId           String
  collectionId    String?
  sellerId        String
  userId          String?
  activityLogs    ActivityLog[]
  bids            Bid[]         @relation("listingBids")
  lastBid         Bid?          @relation("lastBidRelation", fields: [lastBidId], references: [id])
  nft             NFT           @relation(fields: [nftId], references: [id])
  collection      Collection?   @relation(fields: [collectionId], references: [id])
  seller          User          @relation("sellerListings", fields: [sellerId], references: [id])
  User            User?         @relation(fields: [userId], references: [id])
  order           Order?

  @@index([nftId])
  @@index([sellerId])
  @@index([status, listingType])
  @@index([price])
  @@index([createdAt])
  @@index([endTime])
}

enum ListingStatus {
  ACTIVE
  SOLD
  CANCELLED
  EXPIRED
}

enum ListingType {
  FIXED_PRICE
  AUCTION
}

model MintingSession {
  id                 String        @id @default(uuid())
  sessionKey         String        @unique
  status             MintingStatus @default(PENDING)
  transactionHash    String?
  metadataUri        String?
  royaltyBasisPoints Int           @default(0)
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  active             Boolean       @default(true)
  userId             String
  collectionId       String?
  collection         Collection?   @relation(fields: [collectionId], references: [id])
  user               User          @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([collectionId])
  @@index([status])
  @@index([createdAt])
  @@map("minting_sessions")
}

enum MintingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

model NFT {
  id                    String   @id @default(uuid())
  name                  String
  publicKey             String   @unique
  description           String?
  imageUrl              String
  metadataUrl           String
  tags                  String[]
  attributes            Json?
  royaltyBasisPoints    Int      @default(0)
  isLocked              Boolean  @default(false) // For fusion/merging
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  lastBiddingPrice      Float?   @default(0)
  totalLikes            Int      @default(0)
  totalViews            Int      @default(0)
  totalActivities       Int      @default(0) // Denormalized field for activity count
  totalActivities7Days  Int      @default(0) // Activity count for last 7 days
  totalActivities30Days Int      @default(0) // Activity count for last 30 days
  lastActivityAt        DateTime @default(now()) // Timestamp of the last activity

  // Relations
  ownerId      String
  owner        User          @relation("owner", fields: [ownerId], references: [id])
  creatorId    String
  creator      User          @relation("creator", fields: [creatorId], references: [id])
  collectionId String?
  collection   Collection?   @relation(fields: [collectionId], references: [id])
  orders       Order[]
  listings     Listing[]
  offers       Offer[]
  activityLogs ActivityLog[]

  @@index([tags])
  @@index([creatorId])
  @@index([ownerId])
  @@index([collectionId])
  @@index([createdAt])
  @@index([totalLikes])
  @@index([totalViews])
  @@index([totalActivities]) // Index for sorting by activity count
  @@index([totalActivities7Days]) // Index for sorting by recent activity count
  @@index([totalActivities30Days]) // Index for sorting by monthly activity count
  @@index([lastActivityAt]) // Index for sorting by most recent activity
  @@index([name])
}

model NftLike {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  userId    String
  publicKey String
  user      User     @relation(fields: [userId], references: [id])

  @@unique([userId, publicKey])
  @@index([userId])
  @@index([createdAt])
  @@map("nft_likes")
}

model Offer {
  id           String      @id @default(uuid())
  publicKey    String
  escrow       String
  price        Float
  status       OfferStatus
  expiresAt    DateTime?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  collectionId String?
  nftId        String
  buyerId      String
  sellerId     String
  buyer        User        @relation("buyerOffers", fields: [buyerId], references: [id])
  nft          NFT         @relation(fields: [nftId], references: [id])
  collection   Collection? @relation(fields: [collectionId], references: [id])
  seller       User        @relation("sellerOffers", fields: [sellerId], references: [id])
  orders       Order[]

  @@index([nftId])
  @@index([buyerId])
  @@index([sellerId])
  @@index([status])
  @@index([price])
  @@index([createdAt])
  @@index([expiresAt])
}

enum OfferStatus {
  PENDING
  ACCEPTED
  REJECTED
  CANCELLED
  EXPIRED
}

model Order {
  id              String        @id @default(uuid())
  publicKey       String
  price           Float
  currency        String        @default("SOL")
  status          OrderStatus
  transactionHash String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  listingId       String?       @unique
  nftId           String?
  offerId         String?
  collectionId    String?
  buyerId         String
  sellerId        String
  activityLogs    ActivityLog[]
  buyer           User          @relation("buyerOrders", fields: [buyerId], references: [id])
  listing         Listing?      @relation(fields: [listingId], references: [id])
  nft             NFT?          @relation(fields: [nftId], references: [id])
  offer           Offer?        @relation(fields: [offerId], references: [id])
  collection      Collection?   @relation(fields: [collectionId], references: [id])
  seller          User          @relation("sellerOrders", fields: [sellerId], references: [id])

  @@index([buyerId])
  @@index([sellerId])
  @@index([status])
  @@index([createdAt])
  @@index([nftId])
}

enum OrderStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

generator client {
  provider        = "prisma-client-js"
  output          = "../client"
  moduleFormat    = "esm"
  previewFeatures = ["prismaSchemaFolder"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id           String   @id @default(uuid())
  refreshToken String   @unique
  userId       String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  active       Boolean
  user         User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([active])
  @@index([createdAt])
  @@map("sessions")
}

model User {
  id                  String           @id @default(uuid())
  publicKey           String           @unique
  email               String?
  imageUrl            String?
  bannerUrl           String?
  instagramId         String?
  telegramId          String?
  twitterId           String?
  websiteId           String?
  bio                 String?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  userName            String?
  facebookId          String?
  username            String           @unique
  totalSalesVolume    Float            @default(0) // Total volume sold
  totalPurchaseVolume Float            @default(0) // Total volume purchased
  ownedNFTCount       Int              @default(0) // Number of NFTs owned
  createdNFTCount     Int              @default(0) // Number of NFTs created
  collectionCount     Int              @default(0) // Number of collections created
  followerCount       Int              @default(0) // Number of followers
  followingCount      Int              @default(0) // Number of users followed
  isVerified          Boolean          @default(false) // Verification status
  activityScore       Int              @default(0) // Overall activity score
  lastActiveAt        DateTime         @default(now()) // Last activity timestamp
  activityLogs        ActivityLog[]
  fromUserActivities  ActivityLog[]    @relation("fromUserActivities")
  toUserActivities    ActivityLog[]    @relation("toUserActivities")
  bids                Bid[]            @relation("bidderBids")
  collections         Collection[]
  listings            Listing[]        @relation("sellerListings")
  Listing             Listing[]
  createdNFTs         NFT[]            @relation("creator")
  ownedNFTs           NFT[]            @relation("owner")
  offers              Offer[]          @relation("buyerOffers")
  receivedOffers      Offer[]          @relation("sellerOffers")
  buyerOrders         Order[]          @relation("buyerOrders")
  sellerOrders        Order[]          @relation("sellerOrders")
  collectionLikes     CollectionLike[]
  following           Follow[]         @relation("follower")
  followers           Follow[]         @relation("following")
  MintingSession      MintingSession[]
  nftLikes            NftLike[]
  Session             Session[]

  @@index([username])
  @@index([publicKey])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([totalSalesVolume])
  @@index([totalPurchaseVolume])
  @@index([ownedNFTCount])
  @@index([createdNFTCount])
  @@index([collectionCount])
  @@index([followerCount])
  @@index([isVerified])
  @@index([activityScore])
  @@index([lastActiveAt])
  @@index([isVerified, followerCount])
  @@index([isVerified, totalSalesVolume])
  @@map("users")
}

model Follow {
  id          String   @id @default(uuid())
  followerId  String
  followingId String
  createdAt   DateTime @default(now())
  follower    User     @relation("follower", fields: [followerId], references: [id])
  following   User     @relation("following", fields: [followingId], references: [id])

  @@unique([followerId, followingId])
  @@index([followerId])
  @@index([followingId])
  @@index([createdAt])
  @@map("follows")
}
