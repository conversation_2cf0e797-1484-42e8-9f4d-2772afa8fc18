import type { OnPageTransitionEndAsync } from 'vike/types'
import { toggleOverlay } from '@/lib/utils'

const protectedPages = ['/create', '/user', '/edit-profile', '/studio']

export const onPageTransitionEnd: OnPageTransitionEndAsync = async (
	pageContext,
) => {
	document
		.getElementById('globalLoader')
		?.classList.remove('page-is-transitioning')
	document.getElementsByTagName('body')[0].classList.remove('overflow-hidden')

	const accessToken = localStorage.getItem('accessToken')
	const pathname = pageContext.urlParsed.pathname
	if (accessToken) {
		return
	}

	if (protectedPages.includes(pathname)) {
		toggleOverlay(true)
	} else {
		toggleOverlay(false)
	}
}
