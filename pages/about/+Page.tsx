'use client'

import React from 'react'
import MyBreadcrumb from '@/components/MyBreadcrumb'
import CreateAndSellNFTBanner from '@/pages/index/CreateAndSellNFTBanner'

const breadcrumbs = [
	{ id: 1, title: 'Home', url: '/' },
	{ id: 2, title: 'About Us' },
]

export default function Page() {
	return (
		<div className='containerPadding pt-4'>
			<MyBreadcrumb items={breadcrumbs} />

			{/* Hero Section */}
			<div className='text-center mt-12 mb-16'>
				<h1 className='text-2xl lg:text-5xl font-semibold font-squada'>
					About Shogun Fusion
				</h1>
				<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
					Revolutionizing the NFT Marketplace Experience
				</span>
			</div>

			{/* Mission Section */}
			<div className='py-8 px-6 lg:px-10 rounded-[20px] bg-[#191919] mb-16'>
				<h2 className='text-xl lg:text-3xl font-semibold mb-6'>Our Mission</h2>
				<p className='text-[#ECECEC] lg:text-lg'>
					At Shogun Fusion, we're building the next generation NFT marketplace
					where creators and collectors can seamlessly connect, trade, and
					discover unique digital assets. Our platform combines cutting-edge
					blockchain technology with an intuitive user experience to make NFT
					trading accessible to everyone.
				</p>
				<div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-10'>
					<div className='bg-[#131313] p-6 rounded-[15px]'>
						<div className='w-12 h-12 bg-[#2A2A2A] rounded-full flex items-center justify-center mb-4'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								width='24'
								height='24'
								viewBox='0 0 24 24'
								fill='none'
								stroke='currentColor'
								strokeWidth='2'
								strokeLinecap='round'
								strokeLinejoin='round'
								className='text-primary'
							>
								<path d='M20.42 4.58a5.4 5.4 0 0 0-7.65 0l-.77.78-.77-.78a5.4 5.4 0 0 0-7.65 0C1.46 6.7 1.33 10.28 4 13l8 8 8-8c2.67-2.72 2.54-6.3.42-8.42z' />
							</svg>
						</div>
						<h3 className='text-lg font-medium mb-2'>Community First</h3>
						<p className='text-[#ACACAC]'>
							We prioritize our community of creators and collectors, building
							features that empower artistic expression and fair compensation.
						</p>
					</div>
					<div className='bg-[#131313] p-6 rounded-[15px]'>
						<div className='w-12 h-12 bg-[#2A2A2A] rounded-full flex items-center justify-center mb-4'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								width='24'
								height='24'
								viewBox='0 0 24 24'
								fill='none'
								stroke='currentColor'
								strokeWidth='2'
								strokeLinecap='round'
								strokeLinejoin='round'
								className='text-primary'
							>
								<path d='M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z' />
							</svg>
						</div>
						<h3 className='text-lg font-medium mb-2'>Security & Trust</h3>
						<p className='text-[#ACACAC]'>
							Our platform is built with robust security measures to ensure safe
							transactions and protect your digital assets.
						</p>
					</div>
					<div className='bg-[#131313] p-6 rounded-[15px]'>
						<div className='w-12 h-12 bg-[#2A2A2A] rounded-full flex items-center justify-center mb-4'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								width='24'
								height='24'
								viewBox='0 0 24 24'
								fill='none'
								stroke='currentColor'
								strokeWidth='2'
								strokeLinecap='round'
								strokeLinejoin='round'
								className='text-primary'
							>
								<path d='M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16' />
							</svg>
						</div>
						<h3 className='text-lg font-medium mb-2'>Innovation</h3>
						<p className='text-[#ACACAC]'>
							We continuously push the boundaries of what's possible in the NFT
							space, introducing new features like Fusion NFTs.
						</p>
					</div>
				</div>
			</div>

			{/* Technology Section */}
			<div className='py-8 px-6 lg:px-10 rounded-[20px] bg-[#191919] mb-16'>
				<h2 className='text-xl lg:text-3xl font-semibold mb-6'>
					Our Technology
				</h2>
				<p className='text-[#ECECEC] lg:text-lg mb-8'>
					Shogun Fusion is built on cutting-edge blockchain technology,
					supporting multiple standards including ERC-721, ERC-1155, and Solana
					NFTs. Our platform features:
				</p>
				<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
					<div className='flex gap-4'>
						<div className='w-12 h-12 bg-[#2A2A2A] rounded-full flex items-center justify-center flex-shrink-0'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								width='24'
								height='24'
								viewBox='0 0 24 24'
								fill='none'
								stroke='currentColor'
								strokeWidth='2'
								strokeLinecap='round'
								strokeLinejoin='round'
								className='text-primary'
							>
								<path d='M12 2L2 7l10 5 10-5-10-5z' />
								<path d='M2 17l10 5 10-5' />
								<path d='M2 12l10 5 10-5' />
							</svg>
						</div>
						<div>
							<h3 className='text-lg font-medium mb-2'>Solana Support</h3>
							<p className='text-[#ACACAC]'>
								Trade NFTs on Solana blockchain with ease.
							</p>
						</div>
					</div>
					<div className='flex gap-4'>
						<div className='w-12 h-12 bg-[#2A2A2A] rounded-full flex items-center justify-center flex-shrink-0'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								width='24'
								height='24'
								viewBox='0 0 24 24'
								fill='none'
								stroke='currentColor'
								strokeWidth='2'
								strokeLinecap='round'
								strokeLinejoin='round'
								className='text-primary'
							>
								<rect x='2' y='7' width='20' height='14' rx='2' ry='2' />
								<path d='M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16' />
							</svg>
						</div>
						<div>
							<h3 className='text-lg font-medium mb-2'>Gas Optimization</h3>
							<p className='text-[#ACACAC]'>
								Our smart contracts are designed to minimize gas fees while
								maintaining security.
							</p>
						</div>
					</div>
					<div className='flex gap-4'>
						<div className='w-12 h-12 bg-[#2A2A2A] rounded-full flex items-center justify-center flex-shrink-0'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								width='24'
								height='24'
								viewBox='0 0 24 24'
								fill='none'
								stroke='currentColor'
								strokeWidth='2'
								strokeLinecap='round'
								strokeLinejoin='round'
								className='text-primary'
							>
								<path d='M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z' />
							</svg>
						</div>
						<div>
							<h3 className='text-lg font-medium mb-2'>Fusion NFTs</h3>
							<p className='text-[#ACACAC]'>
								Our innovative Fusion technology allows creators to combine
								multiple NFTs into unique new assets.
							</p>
						</div>
					</div>
					<div className='flex gap-4'>
						<div className='w-12 h-12 bg-[#2A2A2A] rounded-full flex items-center justify-center flex-shrink-0'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								width='24'
								height='24'
								viewBox='0 0 24 24'
								fill='none'
								stroke='currentColor'
								strokeWidth='2'
								strokeLinecap='round'
								strokeLinejoin='round'
								className='text-primary'
							>
								<circle cx='12' cy='12' r='10' />
								<path d='M16 12h-6.5' />
								<path d='m12.5 8.5-4 3.5 4 3.5' />
							</svg>
						</div>
						<div>
							<h3 className='text-lg font-medium mb-2'>Cross-Chain</h3>
							<p className='text-[#ACACAC]'>
								Seamlessly trade assets across different blockchain networks.
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Team Section */}
			{/* <div className='py-8 px-6 lg:px-10 rounded-[20px] bg-[#191919] mb-16'>
				<h2 className='text-xl lg:text-3xl font-semibold mb-6'>Our Team</h2>
				<p className='text-[#ECECEC] lg:text-lg mb-8'>
					Shogun Fusion is built by a team of passionate blockchain developers,
					designers, and NFT enthusiasts who are committed to creating the best
					possible platform for digital asset trading.
				</p>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
					{[1, 2, 3, 4].map((item) => (
						<div
							key={item}
							className='bg-[#131313] p-6 rounded-[15px] text-center'
						>
							<div className='w-24 h-24 mx-auto bg-[#2A2A2A] rounded-full mb-4 overflow-hidden flex items-center justify-center'>
								<svg
									xmlns='http://www.w3.org/2000/svg'
									width='40'
									height='40'
									viewBox='0 0 24 24'
									fill='none'
									stroke='currentColor'
									strokeWidth='1'
									strokeLinecap='round'
									strokeLinejoin='round'
									className='text-primary'
								>
									<path d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2' />
									<circle cx='12' cy='7' r='4' />
								</svg>
							</div>
							<h3 className='text-lg font-medium mb-1'>Team Member {item}</h3>
							<p className='text-[#ACACAC] text-sm'>
								Co-Founder &{' '}
								{item === 1
									? 'CEO'
									: item === 2
										? 'CTO'
										: item === 3
											? 'Creative Director'
											: 'Lead Developer'}
							</p>
						</div>
					))}
				</div>
			</div> */}

			<CreateAndSellNFTBanner />
		</div>
	)
}
