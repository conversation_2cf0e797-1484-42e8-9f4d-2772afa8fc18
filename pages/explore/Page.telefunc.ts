import { Buffer } from 'node:buffer'
import { <PERSON>Key } from '@solana/web3.js'
import { Prisma } from 'prisma-client/edge'
import { FUSION_MARKETPLACE_PROGRAM_ID } from '@/lib/generated'
import { prisma } from '@/server/lib/prismaClient'

// Constants for marketplace program
const MARKETPLACE_PROGRAM_ID = FUSION_MARKETPLACE_PROGRAM_ID
const LISTING_SEED = 'listing'

// Define types for filtering parameters
export type TimeFilter = '1H' | '1D' | '7D' | '30D'
export type ExploreParams = {
	timeFilter?: TimeFilter
	searchQuery?: string
	page?: number
	limit?: number
}

/**
 * Get NFTs with filtering and pagination
 */
export async function onGetNFTs(params: ExploreParams = {}) {
	try {
		const { timeFilter, searchQuery, page = 1, limit = 20 } = params

		// Calculate time range based on filter
		const timeRange = getTimeRangeFromFilter(timeFilter)

		// Build where clause for NFTs
		const where: Prisma.NFTWhereInput = {
			...(timeRange && { createdAt: { gte: timeRange } }),
			...(searchQuery && {
				OR: [
					{
						name: { contains: searchQuery, mode: Prisma.QueryMode.insensitive },
					},
					{
						description: {
							contains: searchQuery,
							mode: Prisma.QueryMode.insensitive,
						},
					},
				],
			}),
		}

		// Fetch NFTs with pagination and include owner
		const nfts = await prisma.nFT.findMany({
			where,
			include: {
				owner: true,
				collection: {
					select: { name: true },
				},
			},
			skip: (page - 1) * limit,
			take: limit,
			orderBy: { createdAt: 'desc' },
		})

		// Use the LISTING_SEED constant defined at the top of the file

		// Map to the expected NFT type
		return nfts.map((nft) => {
			// Calculate listing address for each NFT
			const [listing] = PublicKey.findProgramAddressSync(
				[
					Buffer.from(LISTING_SEED),
					new PublicKey(nft.publicKey).toBuffer(),
					new PublicKey(nft.owner.publicKey).toBuffer(),
				],
				new PublicKey(MARKETPLACE_PROGRAM_ID),
			)

			return {
				name: nft.name,
				collectionName: nft.collection?.name ?? '',
				publicKey: nft.publicKey,
				description: nft.description || '',
				logoUrl: nft.imageUrl,
				symbol: nft.name.substring(0, 3).toUpperCase(),
				biddingPrice: nft.lastBiddingPrice || 0,
				owner: {
					id: nft.owner.id,
					username: nft.owner.username,
					imageUrl: nft.owner.imageUrl || '',
					publicKey: nft.owner.publicKey,
				},
				listing: listing.toString(),
			}
		})
	} catch (error) {
		console.error('Error fetching NFTs:', error)
		return []
	}
}

/**
 * Get collections with filtering and pagination
 */
export async function onGetCollection(params: ExploreParams = {}) {
	try {
		const { timeFilter, searchQuery, page = 1, limit = 20 } = params

		// Calculate time range based on filter
		const timeRange = getTimeRangeFromFilter(timeFilter)

		// Build where clause for collections
		const where: Prisma.CollectionWhereInput = {
			...(timeRange && { createdAt: { gte: timeRange } }),
			...(searchQuery && {
				OR: [
					{
						name: { contains: searchQuery, mode: Prisma.QueryMode.insensitive },
					},
					{
						description: {
							contains: searchQuery,
							mode: Prisma.QueryMode.insensitive,
						},
					},
				],
			}),
		}

		// Fetch collections with pagination and include owner
		const collections = await prisma.collection.findMany({
			where,
			include: {
				owner: true,
				nfts: {
					orderBy: { lastBiddingPrice: 'asc' },
					take: 1,
				},
			},
			skip: (page - 1) * limit,
			take: limit,
			orderBy: { createdAt: 'desc' },
		})

		// Map to the expected Collection type
		return collections.map((collection) => {
			const lowestBiddingNFT = collection.nfts[0]

			return {
				symbol: collection.symbol,
				name: collection.name,
				publicKey: collection.publicKey,
				description: collection.description,
				logoUrl: collection.logoUrl,
				bannerUrl: collection.bannerUrl,
				metadataUri: collection.metadataUri,
				creator: {
					id: collection.owner.id,
					username: collection.owner.username,
					imageUrl: collection.owner.imageUrl || '',
					publicKey: collection.owner.publicKey,
				},
				lowestBiddingPrice: lowestBiddingNFT?.lastBiddingPrice ?? 0,
			}
		})
	} catch (error) {
		console.error('Error fetching collections:', error)
		return []
	}
}

/**
 * Get fusion NFTs with efficient database queries
 */
export async function onGetFusionNFTs(params: ExploreParams = {}) {
	try {
		const { timeFilter, searchQuery, page = 1, limit = 20 } = params

		// Calculate time range based on filter
		const timeRange = getTimeRangeFromFilter(timeFilter)

		// Build where clause for fusion NFTs (locked NFTs)
		const where: Prisma.NFTWhereInput = {
			isLocked: true,
			...(timeRange && { createdAt: { gte: timeRange } }),
			...(searchQuery && {
				OR: [
					{
						name: { contains: searchQuery, mode: Prisma.QueryMode.insensitive },
					},
					{
						description: {
							contains: searchQuery,
							mode: Prisma.QueryMode.insensitive,
						},
					},
				],
			}),
		}

		// Fetch fusion NFTs with pagination and include owner
		const fusionNfts = await prisma.nFT.findMany({
			where,
			include: {
				owner: true,
			},
			skip: (page - 1) * limit,
			take: limit,
			orderBy: { createdAt: 'desc' },
		})

		// Map to the expected NFT type
		return fusionNfts.map((nft) => {
			// Calculate listing address for each NFT
			const [listing] = PublicKey.findProgramAddressSync(
				[
					Buffer.from(LISTING_SEED),
					Buffer.from(nft.publicKey),
					Buffer.from(nft.owner.publicKey),
				],
				new PublicKey(MARKETPLACE_PROGRAM_ID),
			)

			return {
				name: nft.name,
				publicKey: nft.publicKey,
				description: nft.description || '',
				logoUrl: nft.imageUrl,
				symbol: nft.name.substring(0, 3).toUpperCase(),
				biddingPrice: nft.lastBiddingPrice || 0,
				owner: {
					id: nft.owner.id,
					username: nft.owner.username,
					imageUrl: nft.owner.imageUrl || '',
					publicKey: nft.owner.publicKey,
				},
				listing: listing.toString(),
			}
		})
	} catch (error) {
		console.error('Error fetching fusion NFTs:', error)
		return []
	}
}

/**
 * Helper function to convert time filter to Date object
 */
function getTimeRangeFromFilter(timeFilter?: TimeFilter): Date | null {
	if (!timeFilter) return null

	const now = new Date()

	switch (timeFilter) {
		case '1H':
			return new Date(now.getTime() - 60 * 60 * 1000) // 1 hour ago
		case '1D':
			return new Date(now.getTime() - 24 * 60 * 60 * 1000) // 1 day ago
		case '7D':
			return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
		case '30D':
			return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
		default:
			return null
	}
}
