import { publicKey } from '@metaplex-foundation/umi'
import { useCallback, useContext, useEffect, useState } from 'react'
import { usePageContext } from 'vike-react/usePageContext'
import CardDataLoader from '@/components/CardDataLoader'
import NFTCard from '@/components/cards/NFTCard'
import TrendingCollection<PERSON>ardExplore from '@/components/cards/TrendingCollectionCardExplore'
import TrendingCreatorCard from '@/components/cards/TrendingCreatorCard'
import MyBreadcrumb from '@/components/MyBreadcrumb'
import { PaginationComp } from '@/components/MyPagination'
import { Button } from '@/components/ui/button'
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import {
	type ParamType as ActiveTabType,
	type TimeFilter as ActiveTimeType,
	type CollectionData,
	type NFTWithListing,
	useExplore,
} from '@/hooks/useExplore'
import { fetchListing, type Listing } from '@/lib/generated'
import { UmiContext } from '@/lib/umi'
import { cn } from '@/lib/utils'
import type { CreatorData } from '@/server/app/explore/helper'

const breadcrumbs = [
	{ id: 1, title: 'Home', url: '/' },
	{ id: 2, title: "Trending NFT's" },
]

const sortOptionsConfig: Record<
	ActiveTabType,
	Array<{ value: string; label: string }>
> = {
	nft: [
		{ value: 'recently_created', label: 'Recently Created' },
		{ value: 'price_high_to_low', label: 'Price: High to Low' },
		{ value: 'price_low_to_high', label: 'Price: Low to High' },
		//{ value: 'most_viewed', label: 'Most Viewed' },
		//{ value: 'high_activity', label: 'High Activity' },
		//{ value: 'recently_sold', label: 'Recently Sold' },
		//{ value: 'auction_first', label: 'Auction First' },
		//{ value: 'most_liked', label: 'Most Liked' },
	],
	fusion: [
		{ value: 'recently_created', label: 'Recently Created' },
		{ value: 'price_high_to_low', label: 'Price: High to Low' },
		{ value: 'price_low_to_high', label: 'Price: Low to High' },
		//{ value: 'most_viewed', label: 'Most Viewed' },
		//{ value: 'high_activity', label: 'High Activity' },
		//{ value: 'recently_sold', label: 'Recently Sold' },
		//{ value: 'auction_first', label: 'Auction First' },
		//{ value: 'most_liked', label: 'Most Liked' },
	],
	collection: [
		//{ value: 'most_viewed', label: 'Most Viewed' },
		//{ value: 'high_activity', label: 'High Activity' },
		//{ value: 'most_liked', label: 'Most Liked' },
		// { value : 'recently_created', label: 'Recently Created'}
	],
	creator: [], // No sorting for creators
}

// debounce hook
function useDebounce<T>(value: T, delay: number): T {
	const [debouncedValue, setDebouncedValue] = useState<T>(value)

	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedValue(value)
		}, delay)

		return () => {
			clearTimeout(timer)
		}
	}, [value, delay])

	return debouncedValue
}

// Custom hook for URL params management
function useUrlParams() {
	const { urlParsed } = usePageContext()

	const getInitialParams = () => ({
		tab: (urlParsed.search?.tab as ActiveTabType) || 'nft',
		time: (urlParsed.search?.time as ActiveTimeType) || 'ALL',
		search: (urlParsed.search?.search as string) || '',
		sort: (urlParsed.search?.sort as string) || '', // Added sort
	})

	const updateUrlParams = useCallback((params: Record<string, string>) => {
		const url = new URL(window.location.href)

		Object.entries(params).forEach(([key, value]) => {
			if (value) {
				url.searchParams.set(key, value)
			} else {
				url.searchParams.delete(key)
			}
		})

		window.history.pushState({}, '', url.toString())
	}, [])

	return { getInitialParams, updateUrlParams }
}

export default function Page() {
	const { getInitialParams, updateUrlParams } = useUrlParams()
	const initialParams = getInitialParams()
	const [activePage, setActivePage] = useState<number>(1)

	// State management
	const [activeTab, setActiveTab] = useState<ActiveTabType>(initialParams.tab)
	const [activeTime, setActiveTime] = useState<ActiveTimeType>(
		initialParams.time,
	)
	const [searchInput, setSearchInput] = useState<string>(initialParams.search)

	const [sortOption, setSortOption] = useState<string>(() => {
		let sortValue = initialParams.sort
		const tabValue = initialParams.tab

		if (sortValue === 'popularity-desc') {
			sortValue = 'most_viewed' // Map legacy value
		}

		const currentTabSortOptions = sortOptionsConfig[tabValue] || []

		if (tabValue === 'creator') {
			return '' // Creators have no sort
		}

		if (
			sortValue &&
			currentTabSortOptions.some((opt) => opt.value === sortValue)
		) {
			return sortValue // Valid sort from URL for the initial tab
		}

		// Default to first option of the initial tab if sort is invalid or not present
		if (currentTabSortOptions.length > 0) {
			return currentTabSortOptions[0].value
		}
		// Fallback if misconfigured or tab has no options (e.g. new tab not in config yet)
		return 'most_viewed'
	})
	const [isDropdownOpen, setIsDropdownOpen] = useState(false)

	// Apply debounce to search input
	const debouncedSearchQuery = useDebounce<string>(searchInput, 500)

	// State for storing NFT listings
	const [nftListings, setNftListings] = useState<Record<string, Listing>>({})

	// Use our new Tanstack Query hook
	const { data, isFetching } = useExplore({
		tab: activeTab,
		timeFilter: activeTime,
		searchQuery: debouncedSearchQuery,
		page: activePage,
		limit: 20,
		sort: sortOption,
	})

	// Extract NFTs and collections from the response
	const nfts = data?.data?.nfts || []
	const collections = data?.data?.collections || []
	const creators = data?.data?.creators || []

	const umi = useContext(UmiContext)

	useEffect(() => {
		const fetchListingData = async () => {
			if (umi && nfts.length > 0) {
				try {
					const listingsMap: Record<string, Listing> = {}
					const listingPromises = nfts.map(async (nft: NFTWithListing) => {
						let listingAddress = null
						if ('listing' in nft) {
							listingAddress = nft.listing
						} else if ('listingData' in nft && nft.listingData) {
							listingAddress = nft.publicKey
						}

						if (listingAddress && typeof listingAddress === 'string') {
							try {
								const listing = await fetchListing(
									umi,
									publicKey(listingAddress),
								)
								if (listing) {
									listingsMap[nft.publicKey] = listing
								}
							} catch (listingError) {
								console.error(
									`Error fetching listing for NFT ${nft.publicKey}:`,
									listingError,
								)
							}
						}
						return null
					})
					await Promise.all(listingPromises)
					setNftListings(listingsMap)
				} catch (error) {
					console.error('Error in listing fetch process:', error)
				}
			}
		}
		fetchListingData()
	}, [umi, nfts])

	// Event handlers
	const handleTabChange = (tab: ActiveTabType) => {
		setActiveTab(tab)
		setActivePage(1) // Reset page on tab change

		const newTabSortOptions = sortOptionsConfig[tab] || []
		let newSortOption = sortOption // Assume current sort option initially

		if (tab === 'creator') {
			newSortOption = ''
		} else if (
			newTabSortOptions.length > 0 &&
			!newTabSortOptions.some((opt) => opt.value === sortOption)
		) {
			// If tab has options, but current sort is not one of them, pick the first
			newSortOption = newTabSortOptions[0].value
		} else if (newTabSortOptions.length === 0) {
			// Tab is not 'creator' (due to first if) but has no sort options (misconfiguration?)
			newSortOption = '' // Default to no sort for such cases
		}
		// If current sort option is valid for the new tab (and tab is not creator and has options), it remains unchanged.

		setSortOption(newSortOption)
		updateUrlParams({ tab, sort: newSortOption, page: '1' })
	}

	const handleTimeFilterChange = (time: ActiveTimeType) => {
		setActiveTime(time)
		setActivePage(1)
		updateUrlParams({ time, page: '1' })
	}

	const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const query = e.target.value
		setSearchInput(query)
		setActivePage(1)
		updateUrlParams({ search: query, page: '1' })
	}

	const handleSortChange = (value: string) => {
		setSortOption(value)
		setActivePage(1)
		updateUrlParams({ sort: value, page: '1' })
		setIsDropdownOpen(false)
	}

	const currentSortOptions = sortOptionsConfig[activeTab] || []
	const selectedSortOptionDetails = currentSortOptions.find(
		(opt) => opt.value === sortOption,
	)
	const displaySortLabel =
		selectedSortOptionDetails?.label ||
		(currentSortOptions.length > 0 ? currentSortOptions[0].label : 'Sort')

	return (
		<div className='containerPadding pt-4'>
			<MyBreadcrumb items={breadcrumbs} />
			<div className='text-center mt-12'>
				<h1 className='text-2xl lg:text-5xl font-semibold font-squada'>
					Trending NFTs
				</h1>
				<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
					Most Appreciated NFT's of the Day
				</span>
			</div>
			<div className='py-4 px-5 rounded-[10px] bg-[#191919] mt-10'>
				<div className='flex items-center gap-x-4 lg:gap-x-9 font-bold text-[#ACACAC]'>
					<span
						tabIndex={0}
						role='tab'
						aria-selected={activeTab === 'nft'}
						className={`cursor-pointer ${activeTab === 'nft' ? 'text-white' : ''}`}
						onClick={() => handleTabChange('nft')}
					>
						NFTs
					</span>
					<span
						tabIndex={0}
						role='tab'
						aria-selected={activeTab === 'collection'}
						className={`cursor-pointer ${activeTab === 'collection' ? 'text-white' : ''}`}
						onClick={() => handleTabChange('collection')}
					>
						Collections
					</span>
					<span
						tabIndex={0}
						role='tab'
						aria-selected={activeTab === 'creator'}
						className={`cursor-pointer ${activeTab === 'creator' ? 'text-white' : ''}`}
						onClick={() => handleTabChange('creator')}
					>
						Creators
					</span>
				</div>
				<div className='flex flex-col lg:flex-row items-center gap-x-5 mt-4'>
					<div className='w-full lg:max-w-[250px] p-1 lg:p-2 h-[40px] lg:h-[60px] flex items-center gap-x-1 rounded-[10px] bg-[#131313]'>
						<TimeButton
							activeTime={activeTime}
							text='ALL'
							setActiveTime={handleTimeFilterChange}
						/>
						<TimeButton
							activeTime={activeTime}
							text='30D'
							setActiveTime={handleTimeFilterChange}
						/>
						<TimeButton
							activeTime={activeTime}
							text='7D'
							setActiveTime={handleTimeFilterChange}
						/>
						<TimeButton
							activeTime={activeTime}
							text='1D'
							setActiveTime={handleTimeFilterChange}
						/>
						<TimeButton
							activeTime={activeTime}
							text='1H'
							setActiveTime={handleTimeFilterChange}
						/>
					</div>
					{activeTab !== 'creator' && currentSortOptions.length > 0 && (
						<DropdownMenu
							open={isDropdownOpen}
							onOpenChange={setIsDropdownOpen}
						>
							<DropdownMenuTrigger asChild>
								<Button
									variant='outline'
									className='w-full lg:w-[180px] h-[40px] lg:h-[54px] bg-black/60 backdrop-blur-[10px] text-white 
                 hover:bg-white/10 rounded-full flex items-center justify-between px-4 
                 transition-all duration-200 text-center'
								>
									<span className='truncate w-full text-center text-xs '>
										{displaySortLabel}
									</span>

									<svg
										className={`w-4 h-4 ml-2 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
										fill='none'
										stroke='currentColor'
										viewBox='0 0 24 24'
										xmlns='http://www.w3.org/2000/svg'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth='2'
											d='M19 9l-7 7-7-7'
										/>
									</svg>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent
								align='end'
								className='w-[200px] bg-black/60 backdrop-blur-[10px] border border-white/10 shadow-2xl rounded-xl p-1 z-50'
								sideOffset={8}
							>
								{currentSortOptions.map((option) => (
									<DropdownMenuItem
										key={option.value}
										onClick={() => handleSortChange(option.value)}
										className={cn(
											'cursor-pointer text-white hover:bg-white/10 focus:bg-white/10 py-2 px-3 rounded-lg transition-all duration-200 ease-in-out',
											sortOption === option.value && 'bg-white/20 font-medium',
										)}
									>
										{option.label}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					)}
					<Input
						placeholder='Search'
						className='mt-3 lg:mt-0 grow h-[40px] lg:h-[54px] rounded-full bg-black/60 backdrop-blur-[10px]'
						value={searchInput}
						onChange={handleSearchInputChange}
					/>
				</div>
			</div>
			{isFetching ? (
				<CardDataLoader className='pt-[100px]' />
			) : (
				<>
					{activeTab === 'nft' && (
						<div className='mt-7 lg:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 3xl:grid-cols-7 gap-5 lg:gap-7'>
							{nfts?.length ? (
								nfts.map((nft: NFTWithListing) => {
									const listing = nftListings[nft.publicKey]
									const listingPrice = listing
										? Number(listing.price) / 1_000_000_000
										: nft.listingData?.price || null

									return (
										<NFTCard
											name={nft.name}
											ownerName={nft.owner.username}
											profileImg={nft.owner.imageUrl || ''}
											img={nft.logoUrl || ''}
											key={nft.publicKey}
											variant='TrendingNFT'
											ownerPublicKey={nft.owner.publicKey}
											biddingPrice={listingPrice || nft.biddingPrice}
											publicKey={nft.publicKey}
											isListed={
												listing?.isActive ||
												(nft.listingData?.isActive ?? false)
											}
											isAuction={nft.listingData?.listingType === 'AUCTION'}
											listingEndsAt={
												nft.listingData?.endTime
													? new Date(nft.listingData.endTime).getTime()
													: null
											}
											collectionName={nft?.collectionName ?? ''}
										/>
									)
								})
							) : (
								<div className='col-span-full text-center py-10 text-gray-400'>
									No NFTs found
								</div>
							)}
						</div>
					)}
					{activeTab === 'fusion' && (
						<div className='mt-7 lg:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 3xl:grid-cols-7 gap-5 lg:gap-7'>
							{nfts?.length ? (
								nfts.map((nft: NFTWithListing) => {
									const listing = nftListings[nft.publicKey]
									const listingPrice = listing
										? Number(listing.price) / 1_000_000_000
										: nft.listingData?.price || null

									return (
										<NFTCard
											name={nft.name}
											ownerName={nft.owner.username}
											profileImg={nft.owner.imageUrl || ''}
											img={nft.logoUrl || ''}
											key={nft.publicKey}
											variant='TrendingFusionNFT'
											biddingPrice={listingPrice || nft.biddingPrice}
											publicKey={nft.publicKey}
											isListed={
												!!listing || (nft.listingData?.isActive ?? false)
											}
											listingEndsAt={
												nft.listingData?.endTime
													? new Date(nft.listingData.endTime).getTime()
													: null
											}
											collectionName={nft.collectionName}
										/>
									)
								})
							) : (
								<div className='col-span-full text-center py-10 text-gray-400'>
									No fusion NFTs found
								</div>
							)}
						</div>
					)}
					{activeTab === 'collection' && (
						<div className='grid mt-7 lg:mt-14 grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4 gap-5 lg:gap-7'>
							{collections?.length ? (
								collections.map((collection: CollectionData) => (
									<TrendingCollectionCardExplore
										creater={collection.creator}
										key={collection.publicKey}
										publicKey={collection.publicKey}
										name={collection.name}
										collectionImgs={[
											{ img: collection.logoUrl },
											{ img: collection.bannerUrl },
										]}
									/>
								))
							) : (
								<div className='col-span-full text-center py-10 text-gray-400'>
									No collections found
								</div>
							)}
						</div>
					)}
					{activeTab === 'creator' && (
						<div className='grid mt-7 lg:mt-14 grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4 gap-5 lg:gap-7'>
							{creators?.length ? (
								creators.map((creator: CreatorData) => (
									<TrendingCreatorCard
										key={creator.id}
										img={creator.nfts[0]?.imageUrl}
										creator={{
											name: creator.userName,
											caption: `@${creator.userName.toLowerCase().replace(/\s+/g, '')}`,
											profileImg: creator.imageUrl,
										}}
										username={creator.userName}
									/>
								))
							) : (
								<div className='col-span-full text-center py-10 text-gray-400'>
									No creators found
								</div>
							)}
						</div>
					)}
					<PaginationComp
						className='mt-7'
						currentPage={activePage}
						setCurrentPage={(page) => {
							setActivePage(page)
							updateUrlParams({ page: page.toString() })
						}}
						totalPages={data?.data?.pagination?.totalPages || 1}
					/>
				</>
			)}
		</div>
	)
}

const TimeButton = ({
	text,
	activeTime,
	setActiveTime,
}: {
	text: ActiveTimeType
	activeTime: ActiveTimeType
	setActiveTime: (time: ActiveTimeType) => void
}) => {
	return (
		<button
			type='button'
			aria-pressed={activeTime === text}
			className={cn(
				'flex-center text-sm grow h-full rounded-[7px] cursor-pointer',
				activeTime === text && 'bgActiveBtn',
			)}
			onClick={(e) => {
				e.preventDefault()
				setActiveTime(text)
			}}
		>
			{text}
		</button>
	)
}
