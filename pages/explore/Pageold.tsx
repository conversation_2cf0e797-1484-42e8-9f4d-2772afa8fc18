import { publicKey } from '@metaplex-foundation/umi'
import { useCallback, useContext, useEffect, useState } from 'react'
import { useData } from 'vike-react/useData'
import { usePageContext } from 'vike-react/usePageContext'
import CardDataLoader from '@/components/CardDataLoader'
import NFTCard from '@/components/cards/NFTCard'
import TrendingCollectionCardExplore from '@/components/cards/TrendingCollectionCardExplore'
import MyBreadcrumb from '@/components/MyBreadcrumb'
import { Input } from '@/components/ui/input'
import { fetchListing, type Listing } from '@/lib/generated'
import { UmiContext } from '@/lib/umi'
import { cn } from '@/lib/utils'
import type { Collection, NFT } from '@/types/enhanced-assets'
import type { ExploreData } from './+data'
import {
	type ExploreParams,
	onGetCollection,
	onGetFusionNFTs,
	onGetNFTs,
} from './Page.telefunc'

type ActiveTimeType = '1H' | '1D' | '7D' | '30D'
type ActiveTabType = 'nft' | 'fusion' | 'collection'

const breadcrumbs = [
	{ id: 1, title: 'Home', url: '/' },
	{ id: 2, title: "Trending NFT's" },
]

// debounce hook
function useDebounce<T>(value: T, delay: number): T {
	const [debouncedValue, setDebouncedValue] = useState<T>(value)

	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedValue(value)
		}, delay)

		return () => {
			clearTimeout(timer)
		}
	}, [value, delay])

	return debouncedValue
}

// Custom hook for URL params management
function useUrlParams() {
	const { urlParsed } = usePageContext()

	const getInitialParams = () => ({
		tab: (urlParsed.search?.tab as ActiveTabType) || 'nft',
		time: (urlParsed.search?.time as ActiveTimeType) || '1H',
		search: (urlParsed.search?.search as string) || '',
	})

	const updateUrlParams = useCallback((params: Record<string, string>) => {
		const url = new URL(window.location.href)

		Object.entries(params).forEach(([key, value]) => {
			if (value) {
				url.searchParams.set(key, value)
			} else {
				url.searchParams.delete(key)
			}
		})

		window.history.pushState({}, '', url.toString())
	}, [])

	return { getInitialParams, updateUrlParams }
}

// Helper function to safely extract endsAt value from listing
function getListingEndTime(listing: Listing | undefined): number | null {
	if (!listing || !listing.endsAt) return null

	// For Option type, we need to check if it has a value
	// The structure depends on the UMI serialization
	try {
		// Try to access the value - this will work if it's present
		const endsAtValue = listing.endsAt as unknown

		// Check if it's a Some variant with __kind property
		if (
			typeof endsAtValue === 'object' &&
			endsAtValue !== null &&
			'__kind' in endsAtValue &&
			endsAtValue.__kind === 'Some' &&
			'value' in endsAtValue
		) {
			return Number(endsAtValue.value)
		}

		// If it's directly a bigint
		if (typeof endsAtValue === 'bigint') {
			return Number(endsAtValue)
		}
	} catch (error) {
		console.error('Error extracting endsAt value:', error)
	}

	return null
}

// Custom hook for data fetching
function useNftDataFetching(initialData: ExploreData | null) {
	const [nfts, setNFTs] = useState<NFT[]>(initialData?.nfts || [])
	const [collections, setCollections] = useState<Collection[]>(
		initialData?.collections || [],
	)
	const [isLoading, setIsLoading] = useState<boolean>(false)

	const fetchData = useCallback(
		async (
			activeTab: ActiveTabType,
			timeFilter: ActiveTimeType,
			searchQuery: string,
		) => {
			setIsLoading(true)

			const params: ExploreParams = {
				timeFilter,
				searchQuery,
				page: 1,
				limit: 20,
			}

			try {
				if (activeTab === 'nft') {
					const fetchedNfts = await onGetNFTs(params)
					setNFTs(fetchedNfts)
					setCollections([])
				} else if (activeTab === 'fusion') {
					const fetchedNfts = await onGetFusionNFTs(params)
					setNFTs(fetchedNfts)
					setCollections([])
				} else if (activeTab === 'collection') {
					const fetchedCollections = await onGetCollection(params)
					setCollections(fetchedCollections)
					setNFTs([])
				}
			} catch (error) {
				console.error('Error fetching data:', error)
			} finally {
				setIsLoading(false)
			}
		},
		[],
	)

	return { nfts, collections, isLoading, fetchData }
}

export default function Page() {
	const data = useData<ExploreData>()
	const { getInitialParams, updateUrlParams } = useUrlParams()
	const initialParams = getInitialParams()

	// State management
	const [activeTab, setActiveTab] = useState<ActiveTabType>(initialParams.tab)
	const [activeTime, setActiveTime] = useState<ActiveTimeType>(
		initialParams.time,
	)
	const [searchInput, setSearchInput] = useState<string>(initialParams.search)

	// Apply debounce to search input
	const debouncedSearchQuery = useDebounce<string>(searchInput, 500)

	// State for storing NFT listings
	const [nftListings, setNftListings] = useState<Record<string, Listing>>({})

	// Data fetching hook
	const { nfts, collections, isLoading, fetchData } = useNftDataFetching(data)

	const umi = useContext(UmiContext)

	useEffect(() => {
		const fetchListingData = async () => {
			if (umi && nfts.length > 0) {
				try {
					// Create a map of NFT public key to its listing data
					const listingsMap: Record<string, Listing> = {}

					// Fetch listings for each NFT that has a listing property
					const listingPromises = nfts.map(async (nft) => {
						if (nft.listing) {
							try {
								const listing = await fetchListing(umi, publicKey(nft.listing))

								if (listing) {
									listingsMap[nft.publicKey] = listing
									console.log(`Fetched listing for NFT ${nft.name}:`, listing)
								}
							} catch (listingError) {
								console.error(
									`Error fetching listing for NFT ${nft.publicKey}:`,
									listingError,
								)
							}
						}
						return null
					})

					// Wait for all listing fetch operations to complete
					await Promise.all(listingPromises)

					// Update state with all fetched listings
					setNftListings(listingsMap)
					console.log('All listings fetched:', listingsMap)
				} catch (error) {
					console.error('Error in listing fetch process:', error)
				}
			}
		}
		fetchListingData()
	}, [umi, nfts])

	// Event handlers
	const handleTabChange = (tab: ActiveTabType) => {
		setActiveTab(tab)
		updateUrlParams({ tab })
	}

	const handleTimeFilterChange = (time: ActiveTimeType) => {
		setActiveTime(time)
		updateUrlParams({ time })
	}

	const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const query = e.target.value
		setSearchInput(query)
		updateUrlParams({ search: query })
	}

	// Fetch data when dependencies change
	useEffect(() => {
		fetchData(activeTab, activeTime, debouncedSearchQuery)
	}, [activeTab, activeTime, debouncedSearchQuery, fetchData])

	return (
		<div className='containerPadding pt-4'>
			<MyBreadcrumb items={breadcrumbs} />
			<div className='text-center mt-12'>
				<h1 className='text-2xl lg:text-5xl font-semibold font-squada'>
					Trending NFTs
				</h1>
				<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
					Most Appreciated NFT's of the Day
				</span>
			</div>
			<div className='py-4 px-5 rounded-[10px] bg-[#191919] mt-10'>
				<div className='flex items-center gap-x-4 lg:gap-x-9 font-bold text-[#ACACAC]'>
					<span
						tabIndex={0}
						role='tab'
						aria-selected={activeTab === 'nft'}
						className={cn(
							'cursor-pointer',
							activeTab === 'nft' && 'text-white',
						)}
						onClick={() => handleTabChange('nft')}
					>
						NFTs
					</span>
					{/* <span
						tabIndex={0}
						role='tab'
						aria-selected={activeTab === 'fusion'}
						className={cn(
							'cursor-pointer',
							activeTab === 'fusion' && 'text-white',
						)}
						onClick={() => handleTabChange('fusion')}
					>
						Fusion NFTs
					</span> */}
					<span
						tabIndex={0}
						role='tab'
						aria-selected={activeTab === 'collection'}
						className={cn(
							'cursor-pointer',
							activeTab === 'collection' && 'text-white',
						)}
						onClick={() => handleTabChange('collection')}
					>
						Collections
					</span>
				</div>
				<div className='flex flex-col lg:flex-row items-center gap-x-5 mt-4'>
					<div className='w-full lg:max-w-[250px] p-1 lg:p-2 h-[40px] lg:h-[60px] flex items-center gap-x-1 rounded-[10px] bg-[#131313]'>
						<TimeButton
							activeTime={activeTime}
							text='1H'
							setActiveTime={handleTimeFilterChange}
						/>
						<TimeButton
							activeTime={activeTime}
							text='1D'
							setActiveTime={handleTimeFilterChange}
						/>
						<TimeButton
							activeTime={activeTime}
							text='7D'
							setActiveTime={handleTimeFilterChange}
						/>
						<TimeButton
							activeTime={activeTime}
							text='30D'
							setActiveTime={handleTimeFilterChange}
						/>
					</div>
					<Input
						placeholder='Search'
						className='mt-3 lg:mt-0 grow h-[40px] lg:h-[54px] rounded-full bg-black/60 backdrop-blur-[10px]'
						value={searchInput}
						onChange={handleSearchInputChange}
					/>
				</div>
			</div>

			{isLoading ? (
				<CardDataLoader />
			) : (
				<>
					{activeTab === 'nft' && (
						<div className='mt-7 lg:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 3xl:grid-cols-7 gap-5 lg:gap-7'>
							{nfts?.length ? (
								nfts.map((nft) => {
									// Get listing info if available
									const listing = nftListings[nft.publicKey]
									const listingPrice = listing
										? Number(listing.price) / 1_000_000_000
										: null

									return (
										<NFTCard
											name={nft.name}
											ownerName={nft.owner.username}
											profileImg={nft.owner.imageUrl || ''}
											img={nft.logoUrl || ''}
											key={nft.publicKey}
											variant='TrendingNFT'
											biddingPrice={listingPrice || nft.biddingPrice}
											publicKey={nft.publicKey}
											isListed={!!listing}
											listingEndsAt={getListingEndTime(listing)}
										/>
									)
								})
							) : (
								<div className='col-span-full text-center py-10 text-gray-400'>
									No NFTs found
								</div>
							)}
						</div>
					)}
					{activeTab === 'fusion' && (
						<div className='mt-7 lg:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 3xl:grid-cols-7 gap-5 lg:gap-7'>
							{nfts?.length ? (
								nfts.map((nft) => {
									// Get listing info if available
									const listing = nftListings[nft.publicKey]
									const listingPrice = listing
										? Number(listing.price) / 1_000_000_000
										: null

									return (
										<NFTCard
											name={nft.name}
											ownerName={nft.owner.username}
											profileImg={nft.owner.imageUrl || ''}
											img={nft.logoUrl || ''}
											key={nft.publicKey}
											variant='TrendingFusionNFT'
											biddingPrice={listingPrice || nft.biddingPrice}
											publicKey={nft.publicKey}
											isListed={!!listing}
											listingEndsAt={getListingEndTime(listing)}
										/>
									)
								})
							) : (
								<div className='col-span-full text-center py-10 text-gray-400'>
									No fusion NFTs found
								</div>
							)}
						</div>
					)}
					{activeTab === 'collection' && (
						<div className='grid mt-7 lg:mt-14 grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4 gap-5 lg:gap-7'>
							{collections?.length ? (
								collections.map((collection) => (
									<TrendingCollectionCardExplore
										key={collection.publicKey}
										publicKey={collection.publicKey}
										name={collection.name}
										collectionImgs={[
											{ img: collection.logoUrl },
											{ img: collection.bannerUrl },
										]}
									/>
								))
							) : (
								<div className='col-span-full text-center py-10 text-gray-400'>
									No collections found
								</div>
							)}
						</div>
					)}
				</>
			)}
		</div>
	)
}

const TimeButton = ({
	text,
	activeTime,
	setActiveTime,
}: {
	text: ActiveTimeType
	activeTime: ActiveTimeType
	setActiveTime: (time: ActiveTimeType) => void
}) => {
	return (
		<button
			type='button'
			aria-pressed={activeTime === text}
			className={cn(
				'flex-center text-sm grow h-full rounded-[7px] cursor-pointer',
				activeTime === text && 'bgActiveBtn',
			)}
			onClick={(e) => {
				e.preventDefault()
				setActiveTime(text)
			}}
		>
			{text}
		</button>
	)
}
