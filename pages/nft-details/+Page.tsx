import { Image } from '@unpic/react'
import { Badge<PERSON>heck } from 'lucide-react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { usePageContext } from 'vike-react/usePageContext'
import { Link } from '@/components/atoms/Link'
import { Tab, TabContent } from '@/components/atoms/Tabs'
import CardDataLoader from '@/components/CardDataLoader'
import NFTCard from '@/components/cards/NFTCard'
import { ActivityLineChart } from '@/components/LineChart'
import MyBreadcrumb from '@/components/MyBreadcrumb'
import { PaginationComp } from '@/components/MyPagination'
import NftActivities from '@/components/NftActivities'
import NftListing from '@/components/NftListing'
import NftOffers from '@/components/NftOffers'
import SkeletonNFTDetails from '@/components/SkeletonNFTDetails'
import { Button } from '@/components/ui/button'
import {
	type NFTDetailTab,
	useNFTActivity,
	useNFTBase,
	useNFTDescription,
	useNFTListing,
	useNFTOffers,
	useNFTPriceHistory,
} from '@/hooks/useNFTDetails'
import { useCountViews } from '@/hooks/useViews'
import { cn, handleViewOnSolscan, truncateAddress } from '@/lib/utils'
import NftDetailsBanner from '@/pages/nft-details/NftDetailsBanner'
import type { NFTDetailResponse } from '@/server/app/nft/nft.route'

// Simple tab loading component
const TabLoading = () => (
	<div className='flex flex-col items-center justify-center py-12'>
		<CardDataLoader />
	</div>
)

export default function Page() {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false)
	const [tab, setTab] = useState<NFTDetailTab>('description')
	const [previousTab, setPreviousTab] = useState<NFTDetailTab>('description')
	const [activePage, setActivePage] = useState<number>(1)
	const { routeParams } = usePageContext()
	const publicKey = routeParams.slug

	// Track if initial page load has completed
	const initialLoadCompletedRef = useRef(false)

	// Track views for this NFT
	useCountViews('nft', publicKey)

	// Fetch base NFT data (always needed)
	const {
		data: baseData,
		isLoading: isBaseLoading,
		isError: isBaseError,
		refetch: refetchBase,
	} = useNFTBase(publicKey)

	// Fetch description tab data (needed for initial load)
	const {
		data: descriptionData,
		isLoading: isDescriptionLoading,
		refetch: refetchDescription,
	} = useNFTDescription(publicKey)

	// Only fetch other tab data when needed (lazy loading)
	const {
		data: priceHistoryData,
		isLoading: isPriceHistoryLoading,
		refetch: refetchPriceHistory,
	} = useNFTPriceHistory(publicKey, {
		enabled: tab === 'priceHistory', // Only fetch when tab is active
	})

	const {
		data: listingData,
		isLoading: isListingLoading,
		refetch: refetchListing,
	} = useNFTListing(publicKey, activePage, {
		enabled: tab === 'listing', // Only fetch when tab is active
	})

	const {
		data: offersData,
		isLoading: isOffersLoading,
		refetch: refetchOffers,
	} = useNFTOffers(publicKey, activePage, {
		enabled: tab === 'offers', // Only fetch when tab is active
	})

	const {
		data: activityData,
		isLoading: isActivityLoading,
		refetch: refetchActivity,
	} = useNFTActivity(publicKey, activePage, {
		enabled: tab === 'itemActivity', // Only fetch when tab is active
	})

	// Mark initial load as completed once we have base data
	useEffect(() => {
		if (baseData?.nft && !initialLoadCompletedRef.current) {
			initialLoadCompletedRef.current = true
		}
	}, [baseData])

	// If the tab changes, reset the active page to 1
	useEffect(() => {
		if (tab !== previousTab) {
			setActivePage(1)
			setPreviousTab(tab)
		}
	}, [tab, previousTab])

	// Get the current tab data using useMemo to prevent unnecessary recalculations
	const currentTabData = useMemo(() => {
		switch (tab) {
			case 'description':
				return {
					data: descriptionData,
					isLoading: isDescriptionLoading,
					refetch: refetchDescription,
				}

			case 'priceHistory':
				return {
					data: priceHistoryData,
					isLoading: isPriceHistoryLoading,
					refetch: refetchPriceHistory,
				}
			case 'listing':
				return {
					data: listingData,
					isLoading: isListingLoading,
					refetch: refetchListing,
				}
			case 'offers':
				return {
					data: offersData,
					isLoading: isOffersLoading,
					refetch: refetchOffers,
				}
			case 'itemActivity':
				return {
					data: activityData,
					isLoading: isActivityLoading,
					refetch: refetchActivity,
				}
			default:
				return {
					data: descriptionData,
					isLoading: isDescriptionLoading,
					refetch: refetchDescription,
				}
		}
	}, [
		tab,
		descriptionData,
		isDescriptionLoading,
		refetchDescription,
		priceHistoryData,
		isPriceHistoryLoading,
		refetchPriceHistory,
		listingData,
		isListingLoading,
		refetchListing,
		offersData,
		isOffersLoading,
		refetchOffers,
		activityData,
		isActivityLoading,
		refetchActivity,
	])
	// Memoize derived state values to prevent unnecessary re-renders
	const nft = useMemo(
		() => baseData?.nft || currentTabData.data?.nft,
		[baseData?.nft, currentTabData.data?.nft],
	)
	const isLiked = useMemo(
		() => baseData?.userInfo?.isLiked || false,
		[baseData?.userInfo?.isLiked],
	)
	const isOwner = useMemo(
		() => baseData?.userInfo?.isOwner || false,
		[baseData?.userInfo?.isOwner],
	)
	const activities = useMemo(
		() => activityData?.activities || [],
		[activityData?.activities],
	)
	const relatedNFTs = useMemo(
		() => descriptionData?.relatedNFTs || [],
		[descriptionData?.relatedNFTs],
	)

	const breadcrumbs = useMemo(
		() => [
			{ id: 1, title: 'Home', url: '/' },
			{ id: 2, title: nft?.name || 'NFT Details' },
		],
		[nft?.name],
	)

	if (isBaseLoading && !initialLoadCompletedRef.current) {
		return <SkeletonNFTDetails />
	}

	// Show loading state only on initial page load
	if (isBaseLoading && !initialLoadCompletedRef.current) {
		return (
			<div className='containerPadding pt-4'>
				<MyBreadcrumb items={breadcrumbs} />
				<div className='flex flex-col items-center justify-center min-h-[50vh]'>
					<h1 className='text-2xl font-semibold'>Loading NFT Details</h1>
					<p className='text-[#7E7E7E] mt-4'>
						Please wait while we fetch the NFT information...
					</p>
				</div>
			</div>
		)
	}

	// If NFT not found, show a message
	if (isBaseError || (!isBaseLoading && !nft)) {
		return (
			<div className='containerPadding pt-4'>
				<MyBreadcrumb items={breadcrumbs} />
				<div className='flex flex-col items-center justify-center min-h-[50vh]'>
					<h1 className='text-2xl font-semibold'>NFT Not Found</h1>
					<p className='text-[#7E7E7E] mt-4'>
						The NFT you are looking for does not exist or has been removed.
					</p>
					<div className='flex gap-4 mt-8'>
						<Button onClick={() => refetchBase()}>Refresh</Button>
						<Link href='/'>
							<Button variant='outline'>Back to Home</Button>
						</Link>
					</div>
				</div>
			</div>
		)
	}

	// Type assertion for safety when we know nft is not null
	const safeNft = nft as NFTDetailResponse['nft']
	const safeRelatedNFTs = relatedNFTs || []

	return (
		<div className='containerPadding pt-4'>
			<MyBreadcrumb items={breadcrumbs} />
			<div className='flex justify-between items-center mb-4'>
				<div className='flex-1'>
					<NftDetailsBanner
						lastPrice={
							priceHistoryData?.priceHistory?.[
								priceHistoryData.priceHistory.length - 1
							]?.price || 0
						}
						offerPrice={offersData?.offers?.[0]?.price || 0}
						nft={safeNft}
						refreshData={refetchBase}
						isLiked={isLiked}
						isOwner={isOwner}
					/>
				</div>

				{/* {showRefreshButton && (
					<Button
						onClick={() => currentTabData.refetch()}
						variant='outline'
						size='sm'
						className='ml-2'
					>
						Refresh
					</Button>
				)} */}
			</div>

			<div className='mt-10 w-full flex lg:justify-center gap-x-5 lg:gap-x-10 items-center text-[#8A8A8A] overflow-x-auto py-4 border-b-[1.5px] gradientBorder'>
				<Tab
					value='description'
					currentTab={tab}
					onClickTab={() => setTab('description')}
				>
					Description
				</Tab>
				<Tab
					value='priceHistory'
					currentTab={tab}
					onClickTab={() => setTab('priceHistory')}
				>
					Price History
				</Tab>
				<Tab
					value='listing'
					currentTab={tab}
					onClickTab={() => setTab('listing')}
				>
					Listing
				</Tab>
				<Tab
					currentTab={tab}
					value='offers'
					onClickTab={() => setTab('offers')}
				>
					Offers
				</Tab>
				<Tab
					currentTab={tab}
					value='itemActivity'
					onClickTab={() => setTab('itemActivity')}
				>
					Item Activity
				</Tab>
			</div>
			<TabContent tab={tab} value='description'>
				{isDescriptionLoading ? (
					<TabLoading />
				) : (
					<>
						<div className='flex gap-x-2 font-semibold items-center'>
							<span className='text-[10px] lg:text-sm'>Owned By</span>
							<Link
								href={`/profile/${safeNft.owner.username}`}
								className='text-xs lg:text-xl text-primary hover:underline'
							>
								{safeNft.owner.username}
							</Link>
							<BadgeCheck className='hidden w-5 h-auto stroke-black fill-primary' />
						</div>
						<p className='text-[10px] leading-loose lg:text-lg text-[#7E7E7E] mt-2'>
							{safeNft.description || 'No description available.'}
						</p>
						{safeNft.attributes && safeNft.attributes.length > 0 ? (
							<h4 className='text-xs lg:text-2xl font-semibold mt-7 lg:mt-10'>
								Traits
							</h4>
						) : null}
						<div className='traitsWrapper grid grid-cols-3 lg:flex flex-wrap gap-x-2 gap-y-4 lg:gap-4 pt-4 lg:pt-7'>
							{safeNft.attributes && safeNft.attributes.length > 0 ? (
								safeNft.attributes.map(
									(
										attr: { id: string; traitType: string; value: string },
										index: number,
									) => (
										<div
											className={cn(
												'rounded-[10px] px-3 lg:px-5 lg:min-w-[220px] min-h-[90px] lg:min-h-[196px] text-[10px] lg:text-xl flex justify-evenly text-center flex-col bg-container',
												index >= 3 && 'col-span-1/2',
											)}
											key={attr.id}
										>
											<span className='text-[#8A8A8A] uppercase'>
												{attr.traitType}
											</span>
											<span className='font-semibold capitalize'>
												{attr.value}
											</span>
										</div>
									),
								)
							) : (
								<div className='col-span-3 lg:w-full text-center py-4 text-[#7E7E7E]'>
									No traits available for this NFT.
								</div>
							)}
						</div>
						{safeNft.collectionId && safeNft.collection && (
							<>
								<h4 className='text-xs lg:text-2xl font-semibold mt-7 lg:mt-10'>
									About {safeNft.collection.name}
								</h4>

								<div className='flex gap-x-5 items-start pt-4 lg:pt-7'>
									<Image
										src={
											safeNft.collection.logoUrl ||
											'/assets/temp/trending-nft2.png'
										}
										className='object-cover w-[37px] h-[37px] lg:w-[56px] lg:h-[56px] rounded-full shrink-0'
										layout='fixed'
										width={56}
										height={56}
										alt='collection logo'
									/>
									<div>
										<p className='text-xs lg:text-lg text-[#7E7E7E]'>
											{safeNft.collection.description ||
												'No collection description available.'}
										</p>
										{/* social icons here */}
									</div>
								</div>
							</>
						)}
						<h4 className='text-xs lg:text-2xl font-semibold lg:mt-10 hidden lg:inline-block'>
							Details
						</h4>{' '}
						<table className='text-[10px] lg:text-sm font-semibold mt-7 border-separate border-spacing-y-3 '>
							<tbody>
								<tr>
									<td>Public Key</td>
									<td className='text-primary pl-10'>
										<span
											className='hover:underline cursor-pointer'
											onClick={() =>
												handleViewOnSolscan(safeNft.publicKey, 'account')
											}
										>
											{truncateAddress(safeNft.publicKey)}
										</span>
									</td>
								</tr>
								{/* <tr>
									<td>Symbol</td>
									<td className='text-primary pl-10'>{safeNft.symbol}</td>
								</tr> */}
								<tr>
									<td>Owner</td>
									<td className='text-primary pl-10'>
										<Link
											href={`/profile/${safeNft.owner.username}`}
											className='hover:underline'
										>
											{safeNft.owner.username}
										</Link>
									</td>
								</tr>
								<tr>
									<td>Token Standard</td>
									<td className='text-primary pl-10'>SPL</td>
								</tr>
								<tr>
									<td>Chain</td>
									<td className='text-primary pl-10'>Solana</td>
								</tr>
								<tr>
									<td>Last Updated</td>
									<td className='text-primary pl-10'>
										{new Date(safeNft.updatedAt || '').toLocaleDateString(
											'en-US',
										)}
									</td>
								</tr>
								<tr>
									<td>Creator Earnings</td>
									<td className='text-primary pl-10'>
										{((safeNft.royaltyBasisPoints || 0) / 100).toFixed(2)}%
									</td>
								</tr>
								{safeNft.metadataUrl && (
									<tr>
										<td>Metadata</td>
										<td className='text-primary pl-10'>
											<a
												href={safeNft.metadataUrl}
												target='_blank'
												rel='noopener noreferrer'
												className='hover:underline'
											>
												View Metadata
											</a>
										</td>
									</tr>
								)}
							</tbody>
						</table>
					</>
				)}
			</TabContent>
			<TabContent tab={tab} value='priceHistory'>
				{isPriceHistoryLoading ? (
					<TabLoading />
				) : (
					<ActivityLineChart data={priceHistoryData?.priceHistory || []} />
				)}
			</TabContent>
			<TabContent tab={tab} value='listing'>
				{isListingLoading ? (
					<TabLoading />
				) : (
					<NftListing listings={listingData?.listingHistory} />
				)}
			</TabContent>
			<TabContent tab={tab} value='offers'>
				{isOffersLoading ? (
					<TabLoading />
				) : (
					<NftOffers
						offers={offersData?.offers}
						isOwner={isOwner}
						nftPublicKey={publicKey}
						refreshData={refetchOffers}
					/>
				)}
			</TabContent>
			<TabContent tab={tab} value='itemActivity'>
				{isActivityLoading ? (
					<TabLoading />
				) : (
					<>
						<div className='flex items-center justify-between lg:justify-end'>
							<span className='lg:hidden text-[10px] font-medium'>
								Activity Details
							</span>
							{/* <Button
								className={cn(
									'bg-black text-[#ACACAC] text-[10px] !px-5 lg:text-sm border border-accent/50 hover:bg-[#292929] hover:text-white hover:border-[#292929]',
									isSidebarOpen && 'bg-[#292929] text-white',
								)}
								variant={'ghost'}
								onClick={() => setIsSidebarOpen(!isSidebarOpen)}
							>
								<SlidersHorizontal className='w-4 h-auto' />{' '}
								<span className='text-[10px] lg:text-sm'>Filter</span>
							</Button> */}
						</div>{' '}
						<div className='lg:flex items-start lg:mt-7'>
							{/* <Filters
								isSidebarOpen={isSidebarOpen}
								setIsSidebarOpen={setIsSidebarOpen}
							/> */}
							<NftActivities activities={activities || []} />
						</div>
					</>
				)}
			</TabContent>

			{(() => {
				// Only show pagination for tabs that support it
				if (tab === 'listing' && listingData?.pagination) {
					return (
						<PaginationComp
							className='mt-7'
							currentPage={activePage}
							setCurrentPage={setActivePage}
							totalPages={listingData.pagination.totalPages}
						/>
					)
				}

				if (tab === 'offers' && offersData?.pagination) {
					return (
						<PaginationComp
							className='mt-7'
							currentPage={activePage}
							setCurrentPage={setActivePage}
							totalPages={offersData.pagination.totalPages}
						/>
					)
				}

				if (tab === 'itemActivity' && activityData?.pagination) {
					return (
						<PaginationComp
							className='mt-7'
							currentPage={activePage}
							setCurrentPage={setActivePage}
							totalPages={activityData.pagination.totalPages}
						/>
					)
				}

				return null
			})()}

			{safeNft.collectionId && (
				<>
					<div className='flex items-center justify-between mt-7 lg:mt-10'>
						<h4 className='text-sm lg:text-2xl font-semibold'>
							More from the Collection
						</h4>

						<Link
							className='hover:text-primary hidden lg:inline-block py-2 px-4 text-xs lg:text-sm rounded-full border border-border'
							href={`/collection/${safeNft.collection?.publicKey}`}
						>
							View Collection
						</Link>
					</div>

					<div className='pt-4 lg:pt-7 grid grid-cols-1 sm:grid-cols-2 gap-5 lg:flex lg:pb-2 lg:flex-nowrap overflow-x-auto lg:gap-7'>
						{safeRelatedNFTs.length > 0 ? (
							safeRelatedNFTs.map((relatedNft) => (
								<NFTCard
									key={relatedNft.publicKey}
									profileImg={
										relatedNft.owner.imageUrl || '/assets/temp/user-profile.png'
									}
									img={relatedNft.logoUrl}
									variant='TrendingNFT'
									name={relatedNft.name}
									ownerName={relatedNft.owner.username}
									//biddingPrice={relatedNft.biddingPrice}
									className='lg:w-[300px] 2xl:w-[350px] lg:shrink-0'
									publicKey={relatedNft.publicKey}
									ownerPublicKey={relatedNft.owner.publicKey}
									biddingPrice={
										relatedNft?.latestListing?.price ?? relatedNft.biddingPrice
									}
									isListed={relatedNft?.latestListing?.status === 'ACTIVE'}
									isAuction={
										relatedNft?.latestListing?.listingType === 'AUCTION'
									}
									listingEndsAt={
										relatedNft.latestListing?.endTime
											? new Date(relatedNft.latestListing.endTime).getTime()
											: null
									}
									collectionName={relatedNft?.collectionName ?? ''}
									//
								/>
							))
						) : (
							<div className='col-span-full text-center py-8 text-[#7E7E7E]'>
								No related NFTs found in this collection.
							</div>
						)}
					</div>

					<div className='flex lg:hidden justify-center mt-5'>
						<Link
							className='hover:text-primary py-2 px-4 text-xs lg:text-sm rounded-full border border-border'
							href={`/collection/${safeNft.collection?.publicKey}`}
						>
							View Collection
						</Link>
					</div>
				</>
			)}
		</div>
	)
}
