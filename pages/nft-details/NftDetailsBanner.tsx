import {
	public<PERSON><PERSON>,
	type TransactionBuilder,
	transactionBuilder,
} from '@metaplex-foundation/umi'
import { base58 } from '@metaplex-foundation/umi/serializers'
import { useWallet } from '@solana/wallet-adapter-react'
import { Image } from '@unpic/react'
import { <PERSON>ge<PERSON>he<PERSON>, Check, Eye, Heart, RefreshCcw } from 'lucide-react'
import { useContext, useEffect, useState } from 'react'
import { toast } from 'sonner'
import LikeFeature from '@/components/atoms/LikeFeature'
import ShareFeature from '@/components/atoms/ShareFeature'
import TransferNftFeature from '@/components/atoms/TransferNftFeature'
import customToast from '@/components/CustomToast'
import ConfirmCancel from '@/components/modals/ConfirmCancel'
import {
	acceptOffer,
	cancelAuctionListing,
	cancelFixedPriceListing,
	cancelOffer,
	claimAuction,
	placeBid,
	purchaseListing,
} from '@/lib/generated'
import { useSolanaPrice } from '@/lib/hooks/useSolanaPrice'
import { UmiContext } from '@/lib/umi'
import { getShareUrl } from '@/lib/utils'
import type { NFTDetailResponse } from '@/server/app/nft/nft.route'
import { Link } from '../../components/atoms/Link'
import MakeOfferModal from '../../components/modals/MakeOfferModal'
import PlaceBidModal from '../../components/modals/PlaceBidModal'
import { Button } from '../../components/ui/button'

/**
 * NFT Details Banner Component
 *
 * This component uses the following user-specific flags from the NFT object:
 * - isUserOwnThisNFT: Indicates if the current user owns this NFT
 * - isUserHaveActiveOffer: Indicates if the current user has an active offer on this NFT
 * - isUserHaveActiveListedNFTSale: Indicates if the current user has an active listing for this NFT
 */
interface NftDetailsBannerProps {
	nft: NFTDetailResponse['nft']
	refreshData: () => void
	isLiked: boolean
	isOwner: boolean
	offerPrice: number
	lastPrice: number
}

export default function NftDetailsBanner({
	lastPrice,
	offerPrice,
	nft,
	refreshData,
	isLiked,
	isOwner,
}: NftDetailsBannerProps) {
	const { publicKey: walletPublicKey } = useWallet()
	const wallet = useWallet()
	const umi = useContext(UmiContext)
	const [loading, setLoading] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [transactionHash, setTransactionHash] = useState('')
	const [success, setSuccess] = useState(false)
	const solToUsd = useSolanaPrice()

	// Use the user-specific flag from the NFT object
	const isOwnNft = nft.isUserOwnThisNFT

	useEffect(() => {
		if (transactionHash) {
			refreshData()
		}
	}, [transactionHash, refreshData])

	// Check if the NFT is listed and if it's an auction
	const isListed = nft.latestListing?.status === 'ACTIVE'
	const isAuction = isListed && nft.latestListing?.listingType === 'AUCTION'

	// Use the user-specific flag to check if the user has an active offer
	const isOfferBuyer = nft.isUserHaveActiveOffer

	// Check if there's a pending offer that the owner can accept
	const hasPendingOffer = nft.latestOffer?.status === 'PENDING' && isOwnNft

	// Check if auction has ended and can be claimed
	const now = new Date()
	const auctionEndTime = nft.latestListing?.endTime
		? new Date(nft.latestListing.endTime)
		: null
	const isAuctionEnded = isAuction && auctionEndTime && now > auctionEndTime

	// Check if current user is the highest bidder of an ended auction
	const isAuctionWinner =
		isAuctionEnded && nft.latestBid?.bidder?.publicKey && walletPublicKey
			? walletPublicKey.toBase58() === nft.latestBid.bidder.publicKey
			: false

	// Get the highest price among listing, offer, and bidding price
	const getHighestPrice = () => {
		// Initialize with biddingPrice (always available, but use 0 as fallback)
		let highestPrice = offerPrice || lastPrice || 0

		// Check if latestListing price is higher
		if (
			isListed &&
			nft.latestListing?.price !== undefined &&
			nft.latestListing?.price !== null &&
			Number(nft.latestListing.price) > highestPrice
		) {
			highestPrice = Number(nft.latestListing.price)
		}

		// Check if latestOffer price is higher
		if (
			nft.latestOffer?.price !== undefined &&
			nft.latestOffer?.price !== null &&
			nft.latestOffer.status === 'PENDING' &&
			Number(nft.latestOffer.price) > highestPrice
		) {
			highestPrice = Number(nft.latestOffer.price)
		}

		// Display 0 if the amount is very small (less than 0.000001)
		if (highestPrice < 0.000001) {
			return 0
		}

		// Format to a maximum of 4 decimal places
		return Number(highestPrice.toFixed(4))
	}

	// Get the highest price
	const highestPrice = getHighestPrice()

	// Calculate USD price using real-time SOL to USD conversion rate
	// Round to a maximum of 4 decimal places as per user preference
	const usdPrice = (highestPrice * solToUsd).toFixed(2)

	// Reset success state when component re-renders
	useEffect(() => {
		if (success) setSuccess(false)
	}, [success])

	// Optimistic UI update for buy now
	const handleBuyNow = async () => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction) {
			setError('Wallet not connected.')
			return
		}

		setLoading(true)
		setError(null)

		try {
			// Build the transaction
			const builder = transactionBuilder()
				.add(
					purchaseListing(umi, {
						asset: publicKey(nft.publicKey),
						buyer: umi.identity,
						feeCollector: publicKey(
							'ArGtRWrGMMHoz6ATpuWYzrwNEjMd9bHfCHRcwMNfjZH6',
						),
						collection: nft.collection
							? publicKey(nft.collection.publicKey)
							: undefined,
						seller: publicKey(nft.owner.publicKey),
					}),
				)
				.addRemainingAccounts([
					{
						pubkey: publicKey(nft.creater.publicKey),
						isWritable: false,
						isSigner: false,
					},
				])

			const { signature } = await builder
				.sendAndConfirm(umi, {
					send: {
						skipPreflight: true,
					},
				})
				.catch((err) => {
					console.error('Failed to purchase NFT:', err)
					throw err
				})

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)
			setSuccess(true)
			customToast.success(`NFT Purchased Successfully for ${highestPrice} SOL`)
			refreshData()
		} catch (err: unknown) {
			console.error('Failed to purchase NFT:', err)
			setError(
				`Purchase failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(false)
		}
	}

	// Optimistic UI update for place bid
	const handlePlaceBid = async (bidAmount: number) => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction) {
			setError('Wallet not connected or listing not found.')
			return
		}

		setLoading(true)
		setError(null)

		// Prepare for transaction

		try {
			// Get auction data from the listing
			const auctionPda = nft.latestListing?.auctionKey

			if (!auctionPda) {
				throw new Error('This is not an auction listing')
			}

			// Convert bid amount to lamports (SOL * 10^9)
			const bidAmountLamports = BigInt(bidAmount * 1_000_000_000)

			// Build the transaction
			const builder = transactionBuilder().add(
				placeBid(umi, {
					bidder: umi.identity,
					asset: publicKey(nft.publicKey),
					listing: publicKey(nft.listing),
					bidAmount: bidAmountLamports,
					escrow: publicKey(nft.latestListing?.escrow as string),
					auction: publicKey(nft.latestListing?.auctionKey as string),
				}),
			)

			const { signature } = await builder
				.sendAndConfirm(umi, {
					send: {
						skipPreflight: true,
					},
				})
				.catch((err) => {
					console.error('Failed to place bid:', err)
					throw err
				})

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)
			setSuccess(true)
			customToast.success(`Bid placed successfully for ${bidAmount} SOL`)
			refreshData()
		} catch (err: unknown) {
			console.error('Failed to place bid:', err)
			setError(
				`Bid failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(false)
		}
	}

	// Optimistic UI update for cancel listing
	const cancelListing = async (type: 'fixed' | 'auction') => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction) {
			setError('Wallet not connected.')
			return
		}

		setLoading(true)
		setError(null)

		// Prepare for transaction

		try {
			// Build the transaction
			let builder: TransactionBuilder
			if (type === 'fixed') {
				builder = transactionBuilder().add(
					cancelFixedPriceListing(umi, {
						seller: umi.identity,
						asset: publicKey(nft.publicKey),
						listing: publicKey(nft.listing),
						collection: nft.collection
							? publicKey(nft.collection.publicKey)
							: undefined,
					}),
				)
			} else {
				builder = transactionBuilder().add(
					cancelAuctionListing(umi, {
						seller: umi.identity,
						asset: publicKey(nft.publicKey),
						listing: publicKey(nft.listing),
						collection: nft.collection
							? publicKey(nft.collection.publicKey)
							: undefined,
					}),
				)
			}
			const { signature } = await builder.sendAndConfirm(umi).catch((err) => {
				console.error('Failed to cancel listing:', err)
				throw err
			})

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)
			setSuccess(true)
			customToast.success('Listing Cancelled Successfully')
			refreshData()
		} catch (err: unknown) {
			console.error('Failed to cancel listing:', err)
			setError(
				`Cancel listing failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(false)
		}
	}

	const handleCancelOffer = async () => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction) {
			setError('Wallet not connected.')
			return
		}
		setLoading(true)
		setError(null)
		try {
			const { signature } = await transactionBuilder()
				.add(
					cancelOffer(umi, {
						asset: publicKey(nft.publicKey),
						buyer: umi.identity,
						escrow: publicKey(nft.latestOffer?.escrow as string),
						offer: publicKey(nft.latestOffer?.publicKey as string),
					}),
				)
				.sendAndConfirm(umi, {
					send: {
						skipPreflight: true,
					},
				})
				.catch((err) => {
					console.error('Failed to cancel offer:', err)
					throw err
				})

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)
			setSuccess(true)
			customToast.success('Offer Cancelled Successfully')
			refreshData()
		} catch (err: unknown) {
			console.error('Failed to cancel offer:', err)
			setError(
				`Cancel offer failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(false)
		}
	}

	const handleAcceptOffer = async () => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction) {
			setError('Wallet not connected.')
			return
		}
		setLoading(true)
		setError(null)
		try {
			const { signature } = await transactionBuilder()
				.add(
					acceptOffer(umi, {
						asset: publicKey(nft.publicKey),
						buyer: publicKey(nft.latestOffer?.buyer.publicKey as string),
						offer: publicKey(nft.latestOffer?.publicKey as string),
						escrow: publicKey(nft.latestOffer?.escrow as string),
						seller: umi.identity,
						collection: nft.collection
							? publicKey(nft.collection.publicKey)
							: undefined,
						feeCollector: publicKey(
							'ArGtRWrGMMHoz6ATpuWYzrwNEjMd9bHfCHRcwMNfjZH6',
						),
					}),
				)
				.addRemainingAccounts([
					{
						pubkey: publicKey(nft.creater.publicKey),
						isWritable: false,
						isSigner: false,
					},
				])
				.sendAndConfirm(umi, {
					send: {
						skipPreflight: true,
					},
				})
				.catch((err) => {
					console.error('Failed to accept offer:', err)
					throw err
				})

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)
			setSuccess(true)
			customToast.success('Offer Accepted Successfully')
			refreshData()
		} catch (err: unknown) {
			console.error('Failed to accept offer:', err)
			setError(
				`Accept offer failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(false)
		}
	}

	const handleClaimAuction = async () => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction) {
			setError('Wallet not connected.')
			return
		}
		setLoading(true)
		setError(null)
		try {
			const { signature } = await transactionBuilder()
				.add(
					claimAuction(umi, {
						asset: publicKey(nft.publicKey),
						winner: umi.identity,
						seller: publicKey(nft.owner.publicKey),
						listing: publicKey(nft.latestListing?.publicKey as string),
						auction: publicKey(nft.latestListing?.auctionKey as string),
						escrow: publicKey(nft.latestListing?.escrow as string),
						collection: nft.collection
							? publicKey(nft.collection.publicKey)
							: undefined,
						feeCollector: publicKey(
							'ArGtRWrGMMHoz6ATpuWYzrwNEjMd9bHfCHRcwMNfjZH6',
						),
					}),
				)
				.addRemainingAccounts([
					{
						pubkey: publicKey(nft.creater.publicKey),
						isWritable: false,
						isSigner: false,
					},
				])
				.sendAndConfirm(umi)
				.catch((err) => {
					console.error('Failed to claim auction:', err)
					throw err
				})

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)
			setSuccess(true)
			customToast.success('Auction Claimed Successfully')
			refreshData()
		} catch (err: unknown) {
			console.error('Failed to claim auction:', err)
			setError(
				`Claim auction failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(false)
		}
	}

	function fetchListingData(): void {
		refreshData()
	}

	return (
		<div className='grid grid-cols-1 lg:grid-cols-[clamp(0px,45%,587px)_1fr] items-center gap-x-10 mt-10'>
			<Image
				src={nft.logoUrl}
				className='object-cover aspect-square rounded-[10px] mx-auto'
				layout='fixed'
				width={587}
				height={587}
				alt={nft.name}
			/>
			<div className='mt-7 lg:mt-0'>
				<div className='flex gap-x-2'>
					{nft?.collection?.name && (
						<Link
							href={`/collection/${nft.collection?.publicKey}`}
							className='flex gap-x-2 font-semibold items-center'
						>
							<span className='text-base lg:text-2xl font-semibold'>
								{nft.collection.name}
							</span>
							<BadgeCheck className='hidden w-5 h-auto stroke-black fill-primary' />
						</Link>
					)}
				</div>
				<div className='flex items-center justify-between '>
					<h1 className='text-xs lg:text-3xl font-semibold'>{nft.name}</h1>
					<div className='flex items-center gap-x-3'>
						{/* 
<TransferNftFeature isOwner={isOwner} />
						*/}
						<TransferNftFeature
							isOwner={isOwner}
							nftName={nft.name}
							nftImage={nft.logoUrl}
							collectionName={nft.collection?.name}
							collectionAddress={nft.collection?.publicKey}
							mintAddress={nft.publicKey}
							onTransferComplete={refreshData}
						/>
						<LikeFeature type='nft' itemId={nft.publicKey} isLiked={isLiked} />
						<ShareFeature
							twitter={{
								text: `Check out this NFT: ${nft.name} on Shogun NFT Marketplace`,
								url: getShareUrl('nft', nft.publicKey),
								hashtags: 'shogun, nft, marketplace, solana',
							}}
						/>
						<button
							className='blurredBtn'
							onClick={() => fetchListingData()}
							title='Refresh Listing'
						>
							<RefreshCcw className='w-4 lg:w-6 h-auto text-white fill-transparent' />
						</button>
					</div>
				</div>
				<p className='text-[10px] lg:text-lg line-clamp-3 text-[#7E7E7E] mt-4 lg:mt-7'>
					{nft.description || 'No description available.'}
				</p>

				<div className='py-4 relative bg-container grid grid-cols-2 lg:grid-cols-2 lg:divide-x-[1px] lg:divide-[#3A3939] text-xs lg:text-sm xl:text-base mt-4 lg:mt-10 rounded-[10px] gap-y-7'>
					{/* <span className='inline-flex items-center justify-center gap-x-1'>
						<Hash className='w-4 h-auto' /> 4,169
					</span> */}
					<span className='inline-flex items-center justify-center gap-x-1'>
						<Eye className='w-4 h-auto' /> {nft.totalViews} Views
					</span>
					<span className='absolute w-[1px] h-[80%] inline-block lg:hidden bg-[#3A3939] inset-1/2 -translate-1/2' />
					<span className='inline-flex items-center justify-center gap-x-1'>
						<Heart className='w-4 h-auto' /> {nft.totalLikes} Favorites
					</span>
					{/* <span className='inline-flex items-center justify-center gap-x-1'>
						<Shapes className='w-4 h-auto' /> PFPs
					</span> */}
				</div>

				<div className='mt-5'>
					<span className='block text-sm text-[#6D6D6D]'>Price</span>
					<span className='lg:text-3xl font-bold'>{highestPrice} SOL</span>
					<span className='text-sm text-[#6D6D6D] ml-2'>
						{solToUsd
							? `($${(Number(usdPrice)).toFixed(2)} USD)`
							: '(Price unavailable)'}
					</span>
				</div>
				{isOwnNft ? (
					<div className='flex flex-col gap-4 w-full mt-8'>
						{/* Owner controls */}
						{nft.isUserHaveActiveListedNFTSale && hasPendingOffer ? (
							<div className='grid grid-cols-1 lg:grid-cols-2 gap-y-3 lg:gap-x-5'>
								<Button
									variant='default'
									className='text-xs lg:text-xl font-medium lg:!h-[55px]'
									onClick={handleAcceptOffer}
									disabled={loading || !wallet.publicKey}
								>
									{loading
										? 'Processing...'
										: `Accept Offer (${nft.latestOffer?.price} SOL)`}
								</Button>
								<Button
									variant='destructive'
									className='text-xs lg:text-xl font-medium lg:!h-[55px]'
									onClick={() => cancelListing(isAuction ? 'auction' : 'fixed')}
									disabled={loading || !wallet.publicKey}
								>
									{loading ? 'Processing...' : 'Cancel Listing'}
								</Button>
							</div>
						) : nft.isUserHaveActiveListedNFTSale ? (
							<div className='grid grid-cols-1 lg:grid-cols-2 gap-y-3 lg:gap-x-5'>
								<Button
									variant='destructive'
									className='text-xs lg:text-xl font-medium lg:!h-[55px]'
									onClick={() => cancelListing(isAuction ? 'auction' : 'fixed')}
									disabled={loading || !wallet.publicKey}
								>
									{loading ? 'Processing...' : 'Cancel Listing'}
								</Button>
							</div>
						) : hasPendingOffer ? (
							<div className='grid grid-cols-1 lg:grid-cols-2 gap-y-3 lg:gap-x-5'>
								<Button
									variant='default'
									className='text-xs lg:text-xl font-medium lg:!h-[55px]'
									onClick={handleAcceptOffer}
									disabled={loading || !wallet.publicKey}
								>
									{loading
										? 'Processing...'
										: `Accept Offer (${nft.latestOffer?.price} SOL)`}
								</Button>
								<Link
									href={`/list-for-sale/${nft.publicKey}`}
									className='w-full'
								>
									<Button
										variant='outline'
										className='w-full text-xs lg:text-xl font-medium lg:!h-[55px]'
									>
										List for Sale
									</Button>
								</Link>
							</div>
						) : (
							<div className='flex justify-center'>
								<Link
									href={`/list-for-sale/${nft.publicKey}`}
									className='w-1/2'
								>
									<Button
										variant='default'
										className='w-full text-xs lg:text-xl font-medium lg:!h-[55px]'
									>
										List for Sale
									</Button>
								</Link>
							</div>
						)}
					</div>
				) : (
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-y-3 lg:gap-x-5 mt-8'>
						{/* Non-owner controls */}
						{isOfferBuyer ? (
							<ConfirmCancel
								onConfirm={handleCancelOffer}
								pending={loading}
								para='Are you sure you want to cancel this offer?'
							>
								<Button
									variant='destructive'
									className='text-xs lg:text-xl font-medium lg:!h-[55px]'
									disabled={loading || !wallet.publicKey}
								>
									{loading ? 'Processing...' : 'Cancel Offer'}
								</Button>
							</ConfirmCancel>
						) : (
							<MakeOfferModal
								assetPublicKey={nft.publicKey}
								assetName={nft.name}
								assetImage={nft.logoUrl}
								assetPrice={highestPrice}
							>
								<Button
									variant='outline'
									className='w-full text-xs lg:text-xl font-semibold lg:!h-[55px]'
								>
									Make an offer
								</Button>
							</MakeOfferModal>
						)}

						{isAuctionWinner ? (
							<Button
								variant='default'
								className='text-xs lg:text-xl font-medium lg:!h-[55px]'
								onClick={handleClaimAuction}
								disabled={loading || !wallet.publicKey}
							>
								{loading ? 'Processing...' : 'Claim Auction'}
							</Button>
						) : (
							isListed &&
							(isAuction ? (
								<PlaceBidModal nft={nft} onPlaceBid={handlePlaceBid}>
									<Button
										variant='default'
										className='text-xs lg:text-xl font-medium lg:!h-[55px]'
										disabled={loading || !wallet.publicKey}
									>
										{loading ? 'Processing...' : 'Place a Bid'}
									</Button>
								</PlaceBidModal>
							) : (
								<Button
									variant='default'
									className='text-xs lg:text-xl font-medium lg:!h-[55px]'
									onClick={handleBuyNow}
									disabled={loading || !wallet.publicKey}
								>
									{loading ? 'Processing...' : 'Buy Now'}
								</Button>
							))
						)}
					</div>
				)}
			</div>

			{error && (
				<div className='mt-4 col-span-2 wrap-break-word p-3 bg-red-900/30 border border-red-500 rounded-md text-red-200'>
					{error}
				</div>
			)}

			{success && (
				<div className='mt-4 p-3 col-span-2 wrap-break-word bg-green-900/30 border border-green-500 rounded-md text-green-200 flex items-center'>
					<Check className='w-5 h-5 mr-2' />
					NFT purchased successfully! Transaction hash: {transactionHash}
				</div>
			)}
		</div>
	)
}
