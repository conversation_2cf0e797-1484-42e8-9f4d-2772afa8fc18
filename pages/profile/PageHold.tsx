import { Image } from '@unpic/react'
import { ChevronDown, Share2, SlidersHorizontal } from 'lucide-react'
import { useId, useState } from 'react'
import { useData } from 'vike-react/useData'
import NFTCard from '@/components/cards/NFTCard'
import MyBreadcrumb from '@/components/MyBreadcrumb'
import FollowListingModal from '@/components/modals/follow-listing-modal'
import { Button } from '@/components/ui/button'
import { useFollowStatus } from '@/hooks/useFollowUser'
import type { EnhancedAsset } from '@/types/enhanced-assets'
import type { User } from './+data'
import { type CreatedNFT, onGetUserCreatedNfts } from './Page.telefunc'

const profileBreadcrumb = [
	{ id: 1, title: 'Home', url: '/' },
	{ id: 2, title: 'Profile' },
]

type TabType =
	| 'nfts'
	| 'collections'
	| 'creators'
	| 'offerReceived'
	| 'offerMade'

export default function Page() {
	const { assets: nfts, user } = useData<{
		assets: EnhancedAsset[]
		user: User
	}>()
	const [activeTab, setActiveTab] = useState<TabType>('nfts')
	const [createdNfts, setCreatedNfts] = useState<CreatedNFT[]>([])
	const [isLoading, setIsLoading] = useState(false)
	const key = useId()

	// Get follower and following counts
	const { data: followStatus } = useFollowStatus(user?.id || '')
	const followersCount = followStatus?.followersCount || 0
	const followingCount = followStatus?.followingCount || 0

	const handleTabChange = async (tab: TabType) => {
		setActiveTab(tab)

		if (tab === 'creators' && createdNfts.length === 0) {
			setIsLoading(true)
			try {
				const userCreatedNfts = await onGetUserCreatedNfts({
					userName: user.username,
				})
				setCreatedNfts(userCreatedNfts)
			} catch (error) {
				console.error('Error fetching created NFTs:', error)
			} finally {
				setIsLoading(false)
			}
		}
	}

	const renderNFTs = () => {
		if (isLoading) {
			return (
				<div className='col-span-full text-center py-10'>
					<p className='text-lg text-white/60'>Loading...</p>
				</div>
			)
		}

		if (activeTab === 'nfts') {
			if (!nfts || nfts.length === 0) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-white/60'>
							No NFTs found in this collection
						</p>
					</div>
				)
			}

			return nfts.map((nft, index) => (
				<NFTCard
					key={`${key}-${nft.publicKey}`}
					profileImg={user.imageUrl || '/assets/temp/user-profile.png'}
					img={nft.metadata.image || '/assets/temp/trending-nft2.png'}
					name={nft.metadata.name}
					ownerName={user.username}
					biddingPrice={0.5} // You might want to replace this with actual price data when available
					variant='TrendingNFT'
				/>
			))
		}

		if (activeTab === 'creators') {
			if (!createdNfts || createdNfts.length === 0) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-white/60'>No created NFTs found</p>
					</div>
				)
			}

			return createdNfts.map((nft, index) => (
				<NFTCard
					key={`created-${key}-${nft.publicKey}`}
					profileImg={user.imageUrl || '/assets/temp/user-profile.png'}
					img={nft.imageUrl || '/assets/temp/trending-nft1.png'}
					name={nft.name}
					ownerName={user.username}
					biddingPrice={nft.lastBiddingPrice || 0.5}
					variant='TrendingNFT'
				/>
			))
		}

		return (
			<div className='col-span-full text-center py-10'>
				<p className='text-lg text-white/60'>This feature is coming soon</p>
			</div>
		)
	}

	return (
		<div className='containerPadding pt-4'>
			<MyBreadcrumb items={profileBreadcrumb} />
			<div className='pt-[10svh] lg:pt-[12svh]'>
				<div className='relative userProfileDetails flex flex-col items-center'>
					<div className='relative rounded-full w-auto after:contents-[*] after:absolute after:-inset-2 after:p-1 after:bg-gradient-to-r after:from-[#FF8F1F] after:to-[#DD0003] after:-z-[1] after:rounded-full'>
						<Image
							src={user.imageUrl || '/assets/temp/user-profile.png'}
							className='h-[100px] w-[100px] lg:w-[180px] lg:h-[180px] rounded-full '
							layout='fixed'
							width={180}
							height={180}
							alt='user profile'
						/>
						<div className='absolute -right-12 -bottom-0 flex-center lg:hidden cursor-pointer w-[30px] h-[30px] rounded-full bg-white/20 backdrop-blur-md'>
							<Share2 className='w-4 h-auto' />
						</div>
					</div>
					<div className='flex-center flex-col lg:flex-row gap-y-1 mt-4'>
						<span className='text-xl font-semibold'>{user.username}</span>
						<span className='text-xs lg:text-lg text-white/50 lg:ml-2.5'>
							@{user.username}
						</span>
						<div className='flex lg:hidden gap-x-2 divide-x-2 items-center text-xs font-semibold mt-2'>
							<FollowListingModal userId={user.id}>
								<div className='flex gap-x-2 divide-x-2 cursor-pointer'>
									<span className='pr-2'>Following {followingCount}</span>
									<span>Followers {followersCount}</span>
								</div>
							</FollowListingModal>
						</div>
					</div>
					<p className='text-xs lg:text-lg mt-3 text-center'>{user.bio}</p>
					<div className='absolute hidden lg:flex items-center justify-between w-full top-5'>
						<FollowListingModal userId={user.id}>
							<div className='flex gap-x-4 items-center text-lg font-semibold cursor-pointer'>
								<span>Following {followingCount}</span>
								<span>Followers {followersCount}</span>
							</div>
						</FollowListingModal>
						<div className='flex-center cursor-pointer lg:w-[50px] lg:h-[50px] rounded-full bg-white/20 backdrop-blur-md'>
							<Share2 className='w-6 h-auto' />
						</div>
					</div>
				</div>
				<div className='mt-10 w-full flex lg:justify-center gap-x-5 lg:gap-x-10 items-center text-[#8A8A8A] overflow-x-auto py-4 border-b-[1.5px] gradientBorder'>
					<span
						tabIndex={0}
						role='tab'
						className={`cursor-pointer text-nowrap ${activeTab === 'nfts' ? 'text-white' : ''}`}
						onClick={() => handleTabChange('nfts')}
					>
						NFTs {nfts.length}
					</span>
					<span
						tabIndex={0}
						className={`cursor-pointer text-nowrap ${activeTab === 'collections' ? 'text-white' : ''}`}
						onClick={() => handleTabChange('collections')}
					>
						Collections 22
					</span>
					<span
						tabIndex={0}
						className={`cursor-pointer text-nowrap ${activeTab === 'creators' ? 'text-white' : ''}`}
						onClick={() => handleTabChange('creators')}
					>
						Creators {createdNfts.length > 0 ? createdNfts.length : 10}
					</span>
					<span
						tabIndex={0}
						className={`cursor-pointer text-nowrap ${activeTab === 'offerReceived' ? 'text-white' : ''}`}
						onClick={() => handleTabChange('offerReceived')}
					>
						Offer Received
					</span>
					<span
						tabIndex={0}
						className={`cursor-pointer text-nowrap ${activeTab === 'offerMade' ? 'text-white' : ''}`}
						onClick={() => handleTabChange('offerMade')}
					>
						Offer Made
					</span>
				</div>
				<div className='flex items-center justify-between mt-7'>
					<Button
						className='bg-black text-[#ACACAC] border border-accent/50'
						variant={'ghost'}
					>
						<SlidersHorizontal className='w-4 h-auto' /> Filters
					</Button>
					<Button
						className='bg-black text-[#ACACAC] border border-accent/50'
						variant={'ghost'}
					>
						Sort <ChevronDown className='w-5 h-auto' />
					</Button>
				</div>
				<div className='mt-7 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 3xl:grid-cols-6 gap-5 lg:gap-7'>
					{renderNFTs()}
				</div>
			</div>
		</div>
	)
}
