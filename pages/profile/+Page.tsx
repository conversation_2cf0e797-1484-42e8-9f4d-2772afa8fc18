import { useWallet } from '@solana/wallet-adapter-react'
import { Image } from '@unpic/react'
import { Co<PERSON>, Pencil, Wallet } from 'lucide-react'
import { useEffect, useId, useState } from 'react'
import { useData } from 'vike-react/useData'
import type { z } from 'zod'
import FollowFeature from '@/components/atoms/FollowFeature'
import GroupedSocialButtons from '@/components/atoms/GroupedSocialButtons'
import { Link } from '@/components/atoms/Link'
import ShareFeature from '@/components/atoms/ShareFeature'
import { Tab, TabContent } from '@/components/atoms/Tabs'
import CardDataLoader from '@/components/CardDataLoader'
import customToast from '@/components/CustomToast'
import NFTCard from '@/components/cards/NFTCard'
import TrendingCollectionCardExplore from '@/components/cards/TrendingCollectionCardExplore'
// import Filters from '@/components/Filters'
import MyBreadcrumb from '@/components/MyBreadcrumb'
import { PaginationComp } from '@/components/MyPagination'
import FollowListingModal from '@/components/modals/follow-listing-modal'
import { MyOffersTable, OffersReceivedTable } from '@/components/NftOffers'
import SkeletonProfilePage from '@/components/SkeletonProfilePage'
import { Button } from '@/components/ui/button'
import { useLikedCollections, useLikedNfts } from '@/hooks/useFollowUser'
import { useFollowStatus, useFollowUser } from '@/hooks/useFollowUser'
import { type NftFilterType, useUserNfts } from '@/hooks/useProfile'
import { useOffersMade } from '@/lib/hooks/useOfferCreated'
import { useOffersReceived } from '@/lib/hooks/useOffersReceived'
import { useBannerUrl } from '@/lib/providers/BannerProvider'
import { getShareUrl } from '@/lib/utils'
import { cn, copyText, truncateAddress } from '@/lib/utils'
import type { Collection } from '@/types/enhanced-assets'
import type { collectionInfoSchema, nftInfoSchema } from '@/types/user.types'
import type { Counts, User } from './+data'
import {
	type CreatedNFT,
	onGetUserCollections,
	onGetUserCreatedNfts,
} from './Page.telefunc'

const profileBreadcrumb = [
	{ id: 1, title: 'Home', url: '/' },
	{ id: 2, title: 'Profile' },
]

// Define types for liked items based on the user.types schemas
type LikedNft = z.infer<typeof nftInfoSchema>
type LikedCollection = z.infer<typeof collectionInfoSchema>

export default function Page() {
	const { publicKey: walletPublicKey } = useWallet()
	const [followTab, setFollowTab] = useState<'Following' | 'Followers'>(
		'Followers',
	)
	const { user, counts } = useData<{
		user: User
		counts: Counts
	}>() || { user: null, counts: { totalNFTs: 0, totalCreators: 0 } }

	const [nftFilter, setNftFilter] = useState<NftFilterType>('all')
	const [nftPage, setNftPage] = useState(1)
	const [nftLimit] = useState(20)
	// Use the new hook for NFT data
	const {
		data: userNftsData,
		isLoading: isNftsLoading,
		error: nftsError,
	} = useUserNfts({
		username: user?.username || '',
		filter: nftFilter,
		page: nftPage,
		limit: nftLimit,
	})

	// Add a loading state for initial data fetch
	const [isPageLoading, setIsPageLoading] = useState(true)
	const isCurrentUser = walletPublicKey?.toBase58() === user?.publicKey
	const [isSidebarOpen, setIsSidebarOpen] = useState(false)
	const [tab, setTab] = useState<
		'nft' | 'collection' | 'creators' | 'offerReceived' | 'offerMade' | 'likes'
	>('nft')
	const [createdNfts, setCreatedNfts] = useState<CreatedNFT[]>([])
	const [collections, setCollections] = useState<Collection[]>([])
	const [activeLikesTab, setActiveLikesTab] = useState<'nft' | 'collection'>(
		'nft',
	)
	const [isLoading, setIsLoading] = useState(false)
	const [likedNftPage, setLikedNftPage] = useState(1)
	const [likedNftLimit] = useState(20)

	// Pagination state for created NFTs
	const [createdNftPage, setCreatedNftPage] = useState(1)
	const [createdNftLimit] = useState(20)
	const [createdNftPagination, setCreatedNftPagination] = useState({
		totalItems: 0,
		totalPages: 1,
		currentPage: 1,
		itemsPerPage: 20,
	})

	// Pagination state for collections
	const [collectionPage, setCollectionPage] = useState(1)
	const [collectionLimit] = useState(20)
	const [collectionPagination, setCollectionPagination] = useState({
		totalItems: 0,
		totalPages: 1,
		currentPage: 1,
		itemsPerPage: 20,
	})

	const key = useId()

	// Use the follow hooks instead of local state and useEffect
	const { data: followData } = useFollowStatus(user?.id || '')
	const { toggleFollow } = useFollowUser()
	const { data: likedNfts, isLoading: likedNftsLoading } = useLikedNfts()
	const { data: likedCollections, isLoading: likedCollectionsLoading } =
		useLikedCollections()

	// Determine if the current user is following the profile user
	const isFollowing = followData?.isFollowing || false
	const followingCount = followData?.followingCount || 0
	const followersCount = followData?.followersCount || 0

	const handleTabChange = async (tabValue: typeof tab) => {
		setTab(tabValue)

		if (tabValue === 'nft') {
			// Reset to page 1 and 'all' filter when switching to NFT tab
			setNftPage(1)
			setNftFilter('all')
		} else if (tabValue === 'creators' && user) {
			setIsLoading(true)
			try {
				const response = await onGetUserCreatedNfts({
					userName: user.username,
					page: createdNftPage,
					limit: createdNftLimit,
				})
				setCreatedNfts(response.nfts)
				setCreatedNftPagination(response.pagination)
			} catch (error) {
				console.error('Error fetching created NFTs:', error)
			} finally {
				setIsLoading(false)
			}
		} else if (tabValue === 'collection' && user) {
			setIsLoading(true)
			try {
				const response = await onGetUserCollections({
					userName: user.username,
					page: collectionPage,
					limit: collectionLimit,
				})
				setCollections(response.collections)
				setCollectionPagination(response.pagination)
			} catch (error) {
				console.error('Error fetching collections:', error)
			} finally {
				setIsLoading(false)
			}
		} else if (tabValue === 'likes') {
			// No need for additional loading state, it's handled by the hooks
			setIsLoading(false)
		}
	}

	// Replace handleFollowChange with the new hook's toggleFollow
	const handleFollowChange = (shouldFollow: boolean) => {
		if (!user || !walletPublicKey) {
			customToast.error('User or wallet not found')
			return
		}
		toggleFollow(user.id, !shouldFollow)
	}

	const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

	const {
		data: offersMade,
		isInitialLoading: isOffersMadeInitialLoading,
		isRefetching: isOffersMadeRefetching,
		error: offersMadeError,
		refetch: refetchOffersMade,
	} = useOffersMade({ sort: sortOrder })

	const {
		data: offersReceived,
		isInitialLoading: isOffersReceivedInitialLoading,
		isRefetching: isOffersReceivedRefetching,
		error: offersReceivedError,
		refetch: refetchOffersReceived,
	} = useOffersReceived({ sort: sortOrder })

	useEffect(() => {
		if (tab === 'offerReceived') {
			refetchOffersReceived()
		}
		if (tab === 'offerMade') {
			refetchOffersMade()
		}
	}, [tab, refetchOffersMade, refetchOffersReceived])

	// Function to handle sort order change
	const handleSortChange = (order: 'asc' | 'desc') => {
		setSortOrder(order)
		// Refetch data with new sort order
		if (tab === 'offerMade') {
			refetchOffersMade()
		} else if (tab === 'offerReceived') {
			refetchOffersReceived()
		}
	}

	const renderNFTs = () => {
		if (isLoading) {
			return (
				<div className='col-span-full flex-center'>
					<CardDataLoader />
				</div>
			)
		}

		if (tab === 'nft') {
			// Show loading state while fetching NFTs
			if (isNftsLoading) {
				return (
					<div className='col-span-full flex-center'>
						<CardDataLoader />
					</div>
				)
			}

			// Show error state if there was an error fetching NFTs
			if (nftsError) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-red-500'>
							Error loading NFTs:{' '}
							{nftsError instanceof Error ? nftsError.message : 'Unknown error'}
						</p>
					</div>
				)
			}

			// Show empty state if no NFTs were found
			if (!userNftsData?.nfts || userNftsData.nfts.length === 0) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-white/60'>
							{isCurrentUser
								? "You don't have any NFTs yet"
								: 'No NFTs found in this collection'}
						</p>
					</div>
				)
			}
			// Render NFT cards
			return (
				<>
					<div className='col-span-full grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
						{userNftsData.nfts.map((nft) => (
							<NFTCard
								name={nft.name}
								ownerName={user.username}
								profileImg={user.imageUrl || ''}
								img={nft.metadata.image || '/assets/temp/trending-nft2.png'}
								key={nft.publicKey}
								variant='TrendingNFT'
								ownerPublicKey={user.publicKey}
								biddingPrice={nft.listing.price}
								publicKey={nft.publicKey}
								isListed={nft.listing.isActive}
								isAuction={nft.listing?.listingType === 'AUCTION'}
								listingEndsAt={
									nft.listing?.endTime
										? new Date(nft.listing.endTime).getTime()
										: null
								}
								collectionName={nft?.collectionName ?? ''}
							/>
						))}
					</div>
					<PaginationComp
						className='mt-7 col-span-full'
						currentPage={nftPage}
						setCurrentPage={setNftPage}
						totalPages={userNftsData.pagination?.totalPages || 1}
					/>
				</>
			)
		}

		if (tab === 'creators') {
			if (!createdNfts || createdNfts.length === 0) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-white/60'>
							{isCurrentUser
								? "You haven't created any NFTs yet"
								: 'No created NFTs found'}
						</p>
					</div>
				)
			}

			return (
				<>
					<div className='col-span-full grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
						{createdNfts.map((nft) => (
							<NFTCard
								key={`created-${key}-${nft.publicKey}`}
								profileImg={user?.imageUrl || '/assets/temp/user-profile.png'}
								img={nft.imageUrl || '/assets/temp/trending-nft1.png'}
								name={nft.name || 'Unnamed NFT'}
								ownerName={isCurrentUser ? 'You' : user?.username || ''}
								biddingPrice={nft.lastBiddingPrice || 0}
								variant='TrendingNFT'
								publicKey={nft.publicKey}
							/>
						))}
					</div>
					<PaginationComp
						className='mt-7 col-span-full'
						currentPage={createdNftPagination.currentPage}
						setCurrentPage={(page) => {
							setCreatedNftPage(page)
							if (user) {
								setIsLoading(true)
								onGetUserCreatedNfts({
									userName: user.username,
									page: page,
									limit: createdNftLimit,
								})
									.then((response) => {
										setCreatedNfts(response.nfts)
										setCreatedNftPagination(response.pagination)
									})
									.catch((error) => {
										console.error('Error fetching created NFTs:', error)
									})
									.finally(() => {
										setIsLoading(false)
									})
							}
						}}
						totalPages={createdNftPagination.totalPages || 1}
					/>
				</>
			)
		}

		if (tab === 'collection') {
			if (!collections || collections.length === 0) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-white/60'>
							{isCurrentUser
								? "You don't have any collections yet"
								: 'No collections found'}
						</p>
					</div>
				)
			}

			return (
				<>
					<div className='col-span-full grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
						{collections.map((collection) => (
							<TrendingCollectionCardExplore
								creater={{
									id: collection.owner?.id ?? '',
									imageUrl: collection.owner?.imageUrl ?? '',
									username: collection.owner?.username ?? '',
									publicKey: collection.owner?.publicKey ?? '',
								}}
								key={`collection-${key}-${collection.publicKey}`}
								publicKey={collection.publicKey}
								name={collection.name}
								collectionImgs={[
									{
										img:
											collection.logoUrl ||
											'/assets/img/default-card-image.png',
									},
									{
										img:
											collection.bannerUrl ||
											'/assets/img/default-card-image.png',
									},
								]}
							/>
						))}
					</div>
					<PaginationComp
						className='mt-7 col-span-full'
						currentPage={collectionPagination.currentPage}
						setCurrentPage={(page) => {
							setCollectionPage(page)
							if (user) {
								setIsLoading(true)
								onGetUserCollections({
									userName: user.username,
									page: page,
									limit: collectionLimit,
								})
									.then((response) => {
										setCollections(response.collections)
										setCollectionPagination(response.pagination)
									})
									.catch((error) => {
										console.error('Error fetching collections:', error)
									})
									.finally(() => {
										setIsLoading(false)
									})
							}
						}}
						totalPages={collectionPagination.totalPages || 1}
					/>
				</>
			)
		}

		if (tab === 'offerMade') {
			if (isOffersMadeInitialLoading || isOffersMadeRefetching) {
				return (
					<div className='col-span-full flex-center py-10'>
						<CardDataLoader />
					</div>
				)
			}

			if (offersMadeError) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-red-500'>
							Error loading offers:{' '}
							{offersMadeError instanceof Error
								? offersMadeError.message
								: 'Unknown error'}
						</p>
					</div>
				)
			}

			if (!offersMade?.offers || offersMade.offers.length === 0) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-white/60'>
							{isCurrentUser
								? "You haven't made any offers yet"
								: "This user hasn't made any offers yet"}
						</p>
					</div>
				)
			}

			return (
				<div className='col-span-full w-full'>
					<MyOffersTable offers={offersMade?.offers} />
					<PaginationComp
						className='mt-7'
						currentPage={offersMade.page || 1}
						setCurrentPage={() => {
							refetchOffersMade()
						}}
						totalPages={Math.ceil(
							(offersMade.total || 0) / (offersMade.limit || 10),
						)}
					/>
				</div>
			)
		}
		if (tab === 'offerReceived') {
			if (isOffersReceivedInitialLoading || isOffersReceivedRefetching) {
				return (
					<div className='col-span-full flex-center py-10'>
						<CardDataLoader />
					</div>
				)
			}

			if (offersReceivedError && offersReceivedError instanceof Error) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-red-500'>
							Error loading offers:{' '}
							{offersReceivedError instanceof Error
								? offersReceivedError.message
								: 'Unknown error'}
						</p>
					</div>
				)
			}

			if (
				!offersReceived?.offers ||
				(offersReceived.offers.length === 0 && isCurrentUser)
			) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-white/60'>
							{isCurrentUser
								? "You haven't made any offers yet"
								: "This user hasn't made any offers yet"}
						</p>
					</div>
				)
			}

			return (
				<div className='col-span-full w-full'>
					<OffersReceivedTable offers={offersReceived?.offers} />
					<PaginationComp
						className='mt-7'
						currentPage={offersReceived.page || 1}
						setCurrentPage={() => {
							refetchOffersReceived()
						}}
						totalPages={Math.ceil(
							(offersReceived.total || 0) / (offersReceived.limit || 10),
						)}
					/>
				</div>
			)
		}

		if (tab === 'likes') {
			// Show loading indicator if either NFTs or collections are being loaded
			if (
				(activeLikesTab === 'nft' && likedNftsLoading) ||
				(activeLikesTab === 'collection' && likedCollectionsLoading)
			) {
				return (
					<div className='col-span-full flex-center'>
						<CardDataLoader />
					</div>
				)
			}

			if (activeLikesTab === 'nft') {
				// Using data from the likedNfts hook
				if (!likedNfts?.nfts || likedNfts.nfts.length === 0) {
					return (
						<div className='col-span-full text-center py-10'>
							<p className='text-lg text-white/60'>
								{isCurrentUser
									? "You haven't liked any NFTs yet"
									: 'No liked NFTs found'}
							</p>
						</div>
					)
				}

				return (
					<>
						<div className='col-span-full grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
							{likedNfts.nfts.map((nft: LikedNft) => (
								<NFTCard
									key={`liked-nft-${key}-${nft.publicKey}`}
									profileImg={
										nft.owner?.imageUrl || '/assets/temp/user-profile.png'
									}
									img={nft.logoUrl || '/assets/temp/trending-nft1.png'}
									name={nft.name || 'Unnamed NFT'}
									ownerName={nft.owner?.username || ''}
									biddingPrice={nft.biddingPrice || 0}
									variant='TrendingNFT'
									publicKey={nft.publicKey}
									collectionName={nft.collectionName || ''}
								/>
							))}
						</div>
						<PaginationComp
							className='mt-7 col-span-full'
							currentPage={likedNftPage}
							setCurrentPage={setLikedNftPage}
							totalPages={likedNfts.pagination?.totalPages || 1}
						/>
					</>
				)
			}

			// Collection tab - Using data from the likedCollections hook
			if (
				!likedCollections?.collections ||
				likedCollections.collections.length === 0
			) {
				return (
					<div className='col-span-full text-center py-10'>
						<p className='text-lg text-white/60'>
							{isCurrentUser
								? "You haven't liked any collections yet"
								: 'No liked collections found'}
						</p>
					</div>
				)
			}

			return likedCollections.collections.map((collection: LikedCollection) => (
				<TrendingCollectionCardExplore
					creater={{
						imageUrl: collection.owner?.imageUrl ?? '',
						username: collection.owner?.username ?? '',
					}}
					key={`liked-collection-${key}-${collection.publicKey}`}
					publicKey={collection.publicKey}
					name={collection.name}
					collectionImgs={[
						{ img: collection.logoUrl || '/assets/img/default-card-image.png' },
						{
							img: collection.bannerUrl || '/assets/img/default-card-image.png',
						},
					]}
				/>
			))
		}

		return (
			<div className='col-span-full text-center py-10'>
				<p className='text-lg text-white/60'>
					{isCurrentUser
						? 'Your offers will appear here soon'
						: 'This feature is coming soon'}
				</p>
			</div>
		)
	}

	// User data
	const username = user?.username || ''
	const userBio = user?.bio || ''
	const userImage = user?.imageUrl || '/assets/img/default-profile.png'

	// Only show content after client-side hydration is complete
	const nftCount = userNftsData?.pagination.totalItems || 0
	const totalMintedNfts = counts?.totalMintedNfts
	const { setBannerUrl } = useBannerUrl()

	useEffect(() => {
		if (user?.bannerUrl) {
			setBannerUrl(user?.bannerUrl)
		} else {
			setBannerUrl(null)
		}
	}, [user, setBannerUrl])

	// Set isPageLoading to false once data is loaded
	useEffect(() => {
		// Check if user data is loaded
		if (user) {
			// Add a small delay to ensure a smooth transition
			const timer = setTimeout(() => {
				setIsPageLoading(false)
			}, 500)
			return () => clearTimeout(timer)
		}
	}, [user])

	// If still loading initial data, show skeleton
	if (isPageLoading) {
		return <SkeletonProfilePage />
	}

	return (
		<div className='containerPadding pt-4'>
			<MyBreadcrumb items={profileBreadcrumb} />
			<div className='pt-[3rem] lg:pt-[5rem]'>
				{/* -------------User Profile Details----------- */}
				<div className='relative userProfileDetails flex flex-col lg:flex-row items-center'>
					<div className='relative shrink-0 rounded-full w-auto after:contents-[*] after:absolute after:-inset-2 after:p-1 after:bg-gradient-to-r after:from-[#FF8F1F] after:to-[#DD0003] after:-z-[1] after:rounded-full'>
						<Image
							src={userImage}
							className='h-[100px] w-[100px] lg:w-[180px] lg:h-[180px] rounded-full '
							layout='fixed'
							width={180}
							height={180}
							alt='user profile'
						/>
					</div>
					<div className='lg:ml-7 w-full flex items-center justify-center lg:items-start lg:justify-start flex-col gap-y-1 mt-4 lg:mt-0'>
						<div className='flex flex-col text-sm text-primary lg:text-xl font-semibold items-center lg:flex-row'>
							@ {username} {isCurrentUser && '(You)'}
						</div>

						<div className='flex items-center gap-x-1 text-sm text-white/50'>
							<Wallet className='w-3 h-auto mr-1' />
							{truncateAddress(walletPublicKey?.toBase58() ?? '')}
							<Button
								variant={'ghost'}
								size={'icon'}
								onClick={() =>
									copyText({ data: walletPublicKey?.toBase58() ?? '' })
								}
							>
								<Copy className='w-3 h-auto' />
							</Button>
						</div>
						<p className='text-xs lg:text-lg mt-3 lg:mt-0 text-center lg:text-left'>
							{userBio
								? userBio
								: isCurrentUser
									? 'Add a bio to tell others about yourself'
									: ''}
						</p>

						<div className='flex items-center justify-center flex-col lg:flex-row lg:justify-between w-full mt-5'>
							<div className='flex flex-col lg:flex-row gap-x-4 items-center text-lg font-semibold'>
								<div className='flex items-center gap-x-4'>
									<FollowListingModal
										userId={user?.id || ''}
										tabValue={followTab}
									>
										<span
											onClick={() => setFollowTab('Following')}
											className='lg:flex-center text-xs lg:text-sm lg:px-5 lg:h-[50px] lg:rounded-[10px] lg:hover:bg-white/20 lg:hover:backdrop-blur-md lg:border lg:border-border lg:cursor-pointer'
										>
											Following {followingCount || 0}
										</span>
									</FollowListingModal>
									<span className='inline-block lg:hidden mx-1 shrink-0 mx-1 h-[22px] w-[1.5px] bg-white/30' />
									<FollowListingModal
										userId={user?.id || ''}
										tabValue={followTab}
									>
										<span
											onClick={() => setFollowTab('Followers')}
											className='lg:flex-center text-xs lg:text-sm lg:px-5 lg:h-[50px] lg:rounded-[10px] lg:hover:bg-white/20 lg:hover:backdrop-blur-md lg:border lg:border-border lg:cursor-pointer'
										>
											Followers {followersCount || 0}
										</span>
									</FollowListingModal>
								</div>
								{!isCurrentUser && (
									<FollowFeature
										className='min-w-[100px] mt-7 lg:mt-0'
										isFollowed={isFollowing}
										onChangeFollow={handleFollowChange}
									/>
								)}
							</div>

							<div className='flex items-center justify-center gap-x-3 mt-7 lg:mt-0'>
								<GroupedSocialButtons
									links={{
										twitter: user?.twitterId
											? user.twitterId.startsWith('http')
												? user.twitterId
												: `https://twitter.com/${user.twitterId.replace(/^@/, '')}`
											: undefined,
										instagram: user?.instagramId
											? user.instagramId.startsWith('http')
												? user.instagramId
												: `https://instagram.com/${user.instagramId.replace(/^@/, '')}`
											: undefined,
										website: user?.websiteId
											? user.websiteId.startsWith('http')
												? user.websiteId
												: `https://${user.websiteId}`
											: undefined,
										facebook: user?.facebookId
											? user.facebookId.startsWith('http')
												? user.facebookId
												: `https://facebook.com/${user.facebookId.replace(/^@/, '')}`
											: undefined,
									}}
								/>
								{(user?.twitterId ||
									user?.instagramId ||
									user?.websiteId ||
									user?.facebookId) && (
									<span className='inline-block mx-1 shrink-0 lg:mx-2 h-[22px] lg:h-[33px] w-[1.5px] bg-white/30' />
								)}
								{isCurrentUser && (
									<Link href='/edit-profile'>
										<div className='blurredBtn'>
											<Pencil className='w-4 lg:w-6 h-auto' />
										</div>
									</Link>
								)}
								<ShareFeature
									twitter={{
										text: `Check out this creator profile: ${username} on Shogun NFT Marketplace`,
										url: getShareUrl('profile', user?.id || ''),
										hashtags: 'shogun, nft, marketplace, solana',
									}}
								/>
							</div>
						</div>
					</div>
				</div>

				<div className='mt-10 w-full flex lg:justify-center gap-x-5 lg:gap-x-10 items-center text-[#8A8A8A] overflow-x-auto py-4 border-b-[1.5px] gradientBorder'>
					<Tab
						value='nft'
						currentTab={tab}
						onClickTab={() => handleTabChange('nft')}
					>
						{isCurrentUser ? 'Your NFTs' : 'NFTs'} {nftCount || ''}
					</Tab>
					<Tab
						value='collection'
						currentTab={tab}
						onClickTab={() => handleTabChange('collection')}
					>
						{isCurrentUser ? 'Your Collections' : 'Collection'}{' '}
						{counts.totalNftCollections || ''}
					</Tab>
					<Tab
						value='creators'
						currentTab={tab}
						onClickTab={() => handleTabChange('creators')}
					>
						{isCurrentUser ? 'Your Creations' : 'Creator'}{' '}
						{totalMintedNfts || ''}
					</Tab>
					<Tab
						currentTab={tab}
						value='offerReceived'
						onClickTab={() => handleTabChange('offerReceived')}
					>
						{isCurrentUser ? 'Your Offers Received' : 'Offer Received'}
					</Tab>
					<Tab
						currentTab={tab}
						value='offerMade'
						onClickTab={() => handleTabChange('offerMade')}
					>
						{isCurrentUser ? 'Your Offers Made' : 'Offer Made'}
					</Tab>
					<Tab
						currentTab={tab}
						value='likes'
						onClickTab={() => handleTabChange('likes')}
					>
						{isCurrentUser ? 'Your Likes' : 'Likes'}
					</Tab>
				</div>

				<div className='flex items-center justify-between mt-7'>
					{tab === 'likes' && (
						<div className='w-full lg:max-w-[250px] p-1 lg:p-2 h-[40px] lg:h-[50px] flex items-center gap-x-1 rounded-[10px] bg-[#131313] mr-auto'>
							<TimeButton
								activeLikesTab={activeLikesTab}
								text='nft'
								setActiveLikesTab={setActiveLikesTab}
								label='NFTs'
							/>
							<TimeButton
								activeLikesTab={activeLikesTab}
								text='collection'
								setActiveLikesTab={setActiveLikesTab}
								label='Collections'
							/>
						</div>
					)}

					{tab === 'nft' && (
						<div className='w-full lg:max-w-[350px] p-1 lg:p-2 h-[40px] lg:h-[50px] flex items-center gap-x-1 rounded-[10px] bg-[#131313] mr-auto'>
							<FilterButton
								activeFilter={nftFilter}
								filter='all'
								setFilter={setNftFilter}
								label='All'
							/>
							<FilterButton
								activeFilter={nftFilter}
								filter='fixed-price'
								setFilter={setNftFilter}
								label='Fixed Price'
							/>
							<FilterButton
								activeFilter={nftFilter}
								filter='auction'
								setFilter={setNftFilter}
								label='Auction'
							/>
						</div>
					)}

					{/* <Button
						className={cn(
							'bg-black text-[#ACACAC] text-[10px] !px-5 lg:text-sm border border-accent/50 hover:bg-[#292929] hover:text-white hover:border-[#292929]',
							isSidebarOpen && 'bg-[#292929] text-white',
						)}
						variant={'ghost'}
						onClick={() => setIsSidebarOpen(!isSidebarOpen)}
					>
						<SlidersHorizontal className='w-4 h-auto' />{' '}
						<span className='text-[10px] lg:text-sm'>Filter</span>
					</Button> */}
					{/* Hide Filter button when tab is offerMade or offerReceived */}
					{/* {tab !== 'offerMade' && tab !== 'offerReceived' && (
						<Button
							className={cn(
								'bg-black text-[#ACACAC] text-[10px] !px-5 lg:text-sm border border-accent/50 hover:bg-[#292929] hover:text-white hover:border-[#292929]',
								isSidebarOpen && 'bg-[#292929] text-white',
							)}
							variant={'ghost'}
							onClick={() => setIsSidebarOpen(!isSidebarOpen)}
						>
							<SlidersHorizontal className='w-4 h-auto' />{' '}
							<span className='text-[10px] lg:text-sm'>Filter</span>
						</Button>
					)} */}

					{/* <DropdownMenu modal={false}>
						<DropdownMenuTrigger asChild>
							<Button
								className='bg-black text-[#ACACAC] border border-accent/50 hover:bg-[#292929] hover:text-white hover:border-[#292929]'
								variant={'ghost'}
							>
								Sort
								<ChevronDown className='w-5 h-auto' />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent className='border-border'>
							<DropdownMenuItem
								onClick={() => handleSortChange('asc')}
								className={sortOrder === 'asc' ? 'bg-accent/20' : ''}
							>
								Ascending
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() => handleSortChange('desc')}
								className={sortOrder === 'desc' ? 'bg-accent/20' : ''}
							>
								Descending
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu> */}
				</div>

				<div className='profileContentWrapper mt-7 lg:mt-10 lg:flex'>
					{/* <Filters
						isSidebarOpen={isSidebarOpen}
						setIsSidebarOpen={setIsSidebarOpen}
					/> */}
					<div className='profileMainArea w-full'>
						<TabContent tab='nft' value={tab} className='!pt-0'>
							<div className='@container'>{renderNFTs()}</div>
						</TabContent>
						<TabContent tab='creators' value={tab} className='!pt-0'>
							<div className='@container'>
								<div className='grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
									{renderNFTs()}
								</div>
							</div>
						</TabContent>
						<TabContent tab='collection' value={tab} className='!pt-0'>
							<div className='@container'>
								<div className='grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
									{renderNFTs()}
								</div>
							</div>
						</TabContent>
						<TabContent tab='offerReceived' value={tab} className='!pt-0'>
							<div className='@container'>
								<div className='grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
									{renderNFTs()}
								</div>
							</div>
						</TabContent>
						<TabContent tab='offerMade' value={tab} className='!pt-0'>
							<div className='@container'>
								<div className='grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
									{renderNFTs()}
								</div>
							</div>
						</TabContent>
						<TabContent tab='likes' value={tab} className='!pt-0'>
							<div className='@container'>
								<div className='grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
									{renderNFTs()}
								</div>
							</div>
						</TabContent>
					</div>
				</div>
			</div>
		</div>
	)
}

// Component for Likes tab buttons
const TimeButton = ({
	text,
	activeLikesTab,
	setActiveLikesTab,
	label,
}: {
	text: 'nft' | 'collection'
	activeLikesTab: 'nft' | 'collection'
	setActiveLikesTab: (time: 'nft' | 'collection') => void
	label: string
}) => {
	return (
		<button
			type='button'
			aria-pressed={activeLikesTab === text}
			className={cn(
				'flex-center text-sm grow h-full rounded-[7px] cursor-pointer',
				activeLikesTab === text && 'bgActiveBtn',
			)}
			onClick={(e) => {
				e.preventDefault()
				setActiveLikesTab(text)
			}}
		>
			{label}
		</button>
	)
}

// Component for NFT filter buttons
const FilterButton = ({
	filter,
	activeFilter,
	setFilter,
	label,
}: {
	filter: NftFilterType
	activeFilter: NftFilterType
	setFilter: (filter: NftFilterType) => void
	label: string
}) => {
	return (
		<button
			type='button'
			aria-pressed={activeFilter === filter}
			className={cn(
				'flex-center text-sm grow h-full rounded-[7px] cursor-pointer',
				activeFilter === filter && 'bgActiveBtn',
			)}
			onClick={(e) => {
				e.preventDefault()
				setFilter(filter)
			}}
		>
			{label}
		</button>
	)
}
