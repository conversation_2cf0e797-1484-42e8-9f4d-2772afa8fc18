import { useEffect, useState } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { useData } from 'vike-react/useData'
import TrendingCollectionCard from '@/components/cards/TrendingCollectionCard'
import TrendingCollectionCardSkeleton from '@/components/cards/TrendingCollectionCardSkeleton'
import { Button } from '@/components/ui/button'
import 'swiper/css'
import { Link } from '@/components/atoms/Link'
import type { HomeData } from './+data'
import {
	type CollectionWithImages,
	onGetTrendingCollections,
} from './Page.telefunc'
import TrendingCollectionCardExplore from '@/components/cards/TrendingCollectionCardExplore'

export default function TrendingCollectionSection() {
	const initialData = useData<HomeData>()
	const [collections, setCollections] = useState<CollectionWithImages[]>(
		initialData?.trendingCollections || [],
	)

	const [isLoading, setIsLoading] = useState(false)

	// Define the CollectionData type based on usage in the component
	type CollectionData = CollectionWithImages & {
		creator?: {
			imageUrl?: string | null
			username?: string | null
		}
		publicKey: string
		name: string
		logoUrl: string
		bannerUrl: string
		nftImages?: Array<{ img: string }>
	}
	// Fetch updated data periodically
	useEffect(() => {
		const fetchTrendingCollections = async () => {
			try {
				setIsLoading(true)
				const updatedCollections = await onGetTrendingCollections({
					timeFilter: '30D',
					limit: 6,
				})
				setCollections(updatedCollections as CollectionData[])
			} catch (error) {
				console.error('Error fetching trending collections:', error)
			} finally {
				setIsLoading(false)
			}
		}

		// Initial setup of data from SSR
		if (initialData?.trendingCollections) {
			setCollections(initialData.trendingCollections as CollectionData[])
		}

		// Set up interval for periodic updates
		const intervalId = setInterval(fetchTrendingCollections, 10 * 60 * 1000) // Update every 10 minutes

		// Clean up interval on component unmount
		return () => clearInterval(intervalId)
	}, [initialData])

	if ((!collections || collections?.length === 0) && !isLoading) {
		return null
	}

	return (
		<section className='trendingCollectionSection containerPadding mt-10 lg:mt-20'>
			<div className='flex items-center justify-between pl-2'>
				<div>
					<h1 className='text-2xl lg:text-5xl font-semibold'>
						Trending Collections
					</h1>
					<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
						Most valued fine-arts for sale day
					</span>
				</div>
				<Link href='/explore?tab=collection'>
					<Button variant='outline' className='hidden md:flex !px-6'>
						Explore More
					</Button>
				</Link>
			</div>

			{isLoading && collections.length === 0 ? (
				<div>
					{/* Desktop skeleton loaders */}
					<div className='hidden lg:grid lg:mt-14 grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4 gap-5 lg:gap-7'>
						{Array.from({ length: 3 }).map((_, index) => (
							<TrendingCollectionCardSkeleton
								key={`skeleton-collection-${index}-${Date.now()}`}
							/>
						))}
					</div>

					{/* Mobile skeleton loaders */}
					<div className='w-full lg:hidden mt-7'>
						<Swiper
							slidesPerView={1.12}
							spaceBetween={16}
							navigation={true}
							className='trendingCollectionSwiper'
						>
							{Array.from({ length: 5 }).map((_, index) => (
								<SwiperSlide key={`skeleton-slide-${index}-${Date.now()}`}>
									<TrendingCollectionCardSkeleton />
								</SwiperSlide>
							))}
						</Swiper>
					</div>
				</div>
			) : (
				<div>
					<div className='hidden lg:grid lg:mt-14 grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4 gap-5 lg:gap-7'>
						{/* {collections.length > 0
							? collections.map((collection) => (
									<TrendingCollectionCard
										key={collection.publicKey}
										publicKey={collection.publicKey}
										name={collection.name}
										collectionImgs={
											collection.nftImages && collection.nftImages.length > 0
												? collection.nftImages
												: [
														{ img: '/assets/img/default-card-image.png' },
														{ img: '/assets/img/default-card-image.png' },
														{ img: '/assets/img/default-card-image.png' },
													]
										}
										ownerImageUrl={collection.creator?.imageUrl ?? ''}
										ownerName={collection.creator?.username ?? ''}
									/>
								))
							: // Show skeletons if no collections are available
								Array.from({ length: 3 }).map((_, index) => (
									<TrendingCollectionCardSkeleton
										key={`empty-skeleton-${index}-${Date.now()}`}
									/>
								))} */}

						{collections?.length ? (
							collections.map((collection: CollectionData) => (
								<TrendingCollectionCardExplore
									creater={collection.creator}
									key={collection.publicKey}
									publicKey={collection.publicKey}
									name={collection.name}
									collectionImgs={[
										{
											img:
												collection.logoUrl ||
												'/assets/img/default-card-image.png',
										},
										{
											img:
												collection.bannerUrl ||
												'/assets/img/default-card-image.png',
										},
									]}
								/>
							))
						) : (
							<div className='col-span-full text-center py-10 text-gray-400'>
								No collections found
							</div>
						)}
					</div>
					<div className='w-full lg:hidden mt-7'>
						{/* <Swiper
							slidesPerView={1.12}
							spaceBetween={16}
							navigation={true}
							className='trendingCollectionSwiper'
						>
							{collections.length > 0
								? collections.map((collection) => (
										<SwiperSlide key={collection.publicKey}>
											<TrendingCollectionCard
												publicKey={collection.publicKey}
												name={collection.name}
												collectionImgs={
													collection.nftImages &&
													collection.nftImages.length > 0
														? collection.nftImages
														: [
																{ img: '/assets/img/default-card-image.png' },
																{ img: '/assets/img/default-card-image.png' },
																{ img: '/assets/img/default-card-image.png' },
															]
												}
												ownerImageUrl={collection.creator?.imageUrl ?? ''}
												ownerName={collection.creator?.username ?? ''}
											/>
										</SwiperSlide>
									))
								: // Show skeletons if no collections are available
									Array.from({ length: 5 }).map((_, index) => (
										<SwiperSlide
											key={`empty-mobile-skeleton-${index}-${Date.now()}`}
										>
											<TrendingCollectionCardSkeleton />
										</SwiperSlide>
									))}
						</Swiper> */}
						<Swiper
							slidesPerView={1.12}
							spaceBetween={16}
							navigation={true}
							className='trendingCollectionSwiper'
						>
							{collections.length > 0
								? collections.map((collection) => (
										<SwiperSlide key={collection.publicKey}>
											<TrendingCollectionCardExplore
												creater={collection.creator}
												publicKey={collection.publicKey}
												name={collection.name}
												collectionImgs={[
													{
														img:
															collection.logoUrl ||
															'/assets/img/default-card-image.png',
													},
													{
														img:
															collection.bannerUrl ||
															'/assets/img/default-card-image.png',
													},
												]}
											/>
										</SwiperSlide>
									))
								: Array.from({ length: 5 }).map((_, index) => (
										<SwiperSlide
											key={`empty-mobile-skeleton-${index}-${Date.now()}`}
										>
											<TrendingCollectionCardSkeleton />
										</SwiperSlide>
									))}
						</Swiper>
					</div>

					<Link href='/explore?tab=collection' className='w-full'>
						<Button variant='outline' className='md:hidden mx-auto !px-6 mt-7'>
							Explore More
						</Button>
					</Link>
				</div>
			)}
		</section>
	)
}
