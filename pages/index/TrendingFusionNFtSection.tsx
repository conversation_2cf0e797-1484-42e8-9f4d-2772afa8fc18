import { useEffect, useState } from 'react'
import GroupFilterButtons from '@/components/atoms/GroupFilterButtons'
import { Link } from '@/components/atoms/Link'
import { Button } from '@/components/ui/button'
import NFTCard from '../../components/cards/NFTCard'
import SkeletonNFTCard from '../../components/SkeletonNFTCard' // Import SkeletonNFTCard

const filterBtnValues: readonly string[] = [
	'Today',
	'This Week',
	'This Month',
	'This Year',
]

export default function TrendingFusionNFTSection() {
	const [isLoading, setIsLoading] = useState(true)
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	const [nftData, setNftData] = useState<any[] | null>(null)
	const [activeFilter, setActiveFilter] = useState(filterBtnValues[0])

	useEffect(() => {
		// Simulate data fetching
		const fetchData = async () => {
			try {
				// Simulate a delay to mimic API call
				await new Promise((resolve) => setTimeout(resolve, 2000))

				// Mock data (based on the original static NFTs)
				setNftData([
					{
						profileImg: '/assets/temp/user-profile.png',
						img: '/assets/temp/trending-nft1.png',
						variant: 'TrendingFusionNFT',
					},
					{
						profileImg: '/assets/temp/user-profile.png',
						img: '/assets/temp/trending-nft2.png',
						variant: 'TrendingFusionNFT',
					},
					{
						profileImg: '/assets/temp/user-profile.png',
						img: '/assets/temp/trending-nft3.png',
						variant: 'TrendingFusionNFT',
					},
					{
						profileImg: '/assets/temp/user-profile.png',
						img: '/assets/temp/trending-nft4.png',
						variant: 'TrendingFusionNFT',
					},
					{
						profileImg: '/assets/temp/user-profile.png',
						img: '/assets/temp/trending-nft1.png',
						variant: 'TrendingFusionNFT',
					},
					{
						profileImg: '/assets/temp/user-profile.png',
						img: '/assets/temp/trending-nft2.png',
						variant: 'TrendingFusionNFT',
					},
					{
						profileImg: '/assets/temp/user-profile.png',
						img: '/assets/temp/trending-nft3.png',
						variant: 'TrendingFusionNFT',
					},
					{
						profileImg: '/assets/temp/user-profile.png',
						img: '/assets/temp/trending-nft4.png',
						variant: 'TrendingFusionNFT',
					},
				])
				setIsLoading(false)
			} catch (error) {
				console.error('Error fetching Fusion NFT data:', error)
				setIsLoading(false)
			}
		}

		fetchData()
	}, [])

	return (
		<section className='trendingFusionNFTSection containerPadding mt-10 lg:mt-20'>
			<div className='flex flex-col gap-y-4 lg:flex-row items-center lg:justify-between lg:pl-2'>
				<div className='text-center lg:text-left'>
					<h1 className='text-2xl lg:text-5xl font-semibold'>
						Trending Fusion NFT's
					</h1>
					<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
						Most Appreciated NFT's of the Day
					</span>
				</div>
				<div className='flex items-center gap-x-4'>
					<GroupFilterButtons
						active={activeFilter}
						onChange={(value) => setActiveFilter(value)}
						values={filterBtnValues}
					/>
					<Link href='/explore?tab=fusion' className='hidden lg:inline-block'>
						<Button variant='outline' className='flex mx-auto !px-6'>
							Explore More
						</Button>
					</Link>
				</div>
			</div>

			<div className='mt-7 lg:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 3xl:grid-cols-6 gap-5 lg:gap-7'>
				{isLoading || !nftData
					? // Show skeleton cards while loading
						Array.from({ length: 8 }).map((_, index) => (
							<SkeletonNFTCard
								key={`skeleton-fusion-nft-${index}`}
								variant='TrendingFusionNFT'
							/>
						))
					: // Show actual NFT cards when data is loaded
						nftData.map((nft, index) => (
							<NFTCard
								key={`fusion-nft-${
									// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
									index
								}`}
								profileImg={nft.profileImg}
								img={nft.img}
								name='John Doe'
								variant={nft.variant}
							/>
						))}
			</div>
			<Link href='/explore?tab=fusion' className='lg:hidden'>
				<Button variant='outline' className='flex mx-auto !px-6 mt-7'>
					Explore More
				</Button>
			</Link>
		</section>
	)
}
