import CreateAndSellNFTBanner from './CreateAndSellNFTBanner'
// import ExploreByCategorySection from './ExploreByCategorySection'
import HeroSection from './HeroSection'
import LiveAuctionSection from './LiveAuctionSection'
// import StepsForFusionSection from './StepsForFusionSection'
import StepsForNFtSection from './StepsForNftSection'
import TrendingCollectionSection from './TrendingCollectionSection'
import TrendingCreatorsSection from './TrendingCreatorsSection'
// import TrendingFusionNFTSection from './TrendingFusionNFtSection'
import TrendingNFTSection from './TrendingNFTSection'

export default function Page() {
	return (
		<div className='pb-10'>
			<HeroSection />
			<LiveAuctionSection />
			<TrendingNFTSection />
			<TrendingCollectionSection />
			{/* <StepsForFusionSection /> */}
			{/* <TrendingFusionNFTSection /> */}
			<StepsForNFtSection />
			{/* <ExploreByCategorySection /> */}
			<TrendingCreatorsSection />
			<CreateAndSellNFTBanner />
		</div>
	)
}
