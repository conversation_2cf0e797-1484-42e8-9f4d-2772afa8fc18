import { Link } from '@/components/atoms/Link'
import { Button } from '@/components/ui/button'
import NFTCategory from '../../components/cards/NFTCategory'

export default function ExploreByCategory() {
	return (
		<section className='exploreByCategory containerPadding mt-10 lg:mt-20'>
			<div className='text-center'>
				<h1 className='text-2xl lg:text-5xl font-semibold'>
					Explore by Category
				</h1>
				<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
					NFT categories that appeal to a wide range of collectors and creators
				</span>
			</div>

			<div className='mt-7 lg:mt-14 grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6 gap-5 lg:gap-7'>
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Art' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Music' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Games' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Video' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Fashion' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Art' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Music' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Games' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Video' />
				<NFTCategory img='/assets/temp/trending-nft2.png' caption='Fashion' />
			</div>

			<Link href='/explore?tab=fusion'>
				<Button variant='outline' className='flex mx-auto !px-6 mt-7'>
					Explore More Categories
				</Button>
			</Link>
		</section>
	)
}
