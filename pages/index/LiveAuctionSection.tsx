import { Link } from '@/components/atoms/Link'
import NFTCard from '@/components/cards/NFTCard'
import SkeletonNFTCard from '@/components/SkeletonNFTCard'
import { Button } from '@/components/ui/button'
import { type NFTWithListing, useLiveAuction } from '@/hooks/useHome'

export default function LiveAuctionSection() {
	// Use the live auction hook
	const { data: nftData, isLoading } = useLiveAuction()

	if ((!nftData || nftData?.length === 0) && !isLoading) {
		return null
	}

	// If data is available, render the section
	return (
		<section className='liveAuctionSection containerPadding relative z-10 -mt-4'>
			<div className='flex items-center justify-between pl-2'>
				<div>
					<h1 className='text-2xl lg:text-5xl font-semibold'>Live Auction</h1>
					<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
						Most valued finearts for sale day
					</span>
				</div>
				<Link href='/explore?tab=nft'>
					<Button variant='outline' className='hidden md:flex !px-6'>
						Explore More
					</Button>
				</Link>
			</div>

			<div className='mt-7 lg:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-5 lg:gap-7'>
				{isLoading || !nftData || nftData.length === 0 ? (
					// Show skeleton cards while loading
					<>
						<SkeletonNFTCard variant='LiveAuction' />
						<SkeletonNFTCard variant='LiveAuction' />
						<SkeletonNFTCard variant='LiveAuction' />
						<SkeletonNFTCard variant='LiveAuction' />
					</>
				) : (
					// Show actual NFT cards when data is loaded
					nftData.map((nft, index) => {
						return (
							<NFTCard
								key={nft.publicKey}
								img={nft.imageUrl}
								name={nft.name}
								profileImg={
									nft.owner?.imageUrl || '/assets/temp/user-profile.png'
								}
								biddingPrice={nft.listing?.price || 0}
								variant='LiveAuction'
								listingEndsAt={new Date(nft.listing?.endTime || 0).getTime()}
								publicKey={nft.publicKey}
								ownerName={nft?.owner?.username}
								ownerPublicKey={nft.owner.publicKey}
								isListed={nft.listing?.status === 'ACTIVE'}
								isAuction={nft.listing?.listingType === 'AUCTION'}
								collectionName={nft?.collection?.name ?? ''}
							/>
						)
					})
				)}
			</div>
			<Link href='/explore?tab=nft'>
				<Button variant='outline' className='flex mx-auto md:hidden !px-6 mt-7'>
					Explore More
				</Button>
			</Link>
		</section>
	)
}
