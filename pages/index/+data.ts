import type { PageContextServer } from 'vike/types'
import {
	type CollectionWithImages,
	onGetTrendingCollections,
} from './Page.telefunc'

export type HomeData = {
	trendingCollections: CollectionWithImages[]
}

export default async function data(
	_pageContext: PageContextServer,
): Promise<HomeData | null> {
	try {
		// Fetch initial data for the homepage
		// We'll use a parallel fetch pattern for better performance
		const [trendingCollections] = await Promise.all([
			onGetTrendingCollections({ timeFilter: '30D', limit: 6 }),
		])

		return {
			trendingCollections,
		}
	} catch (error) {
		console.error('Error fetching data for home page:', error)
		// Return empty arrays instead of null to prevent errors in UI components
		return {
			trendingCollections: [],
		}
	}
}
