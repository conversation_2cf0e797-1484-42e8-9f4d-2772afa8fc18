import { Image } from '@unpic/react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import {
	buttonVariants,
	headerVariants,
	heroImageVariants,
	linkVariants,
} from '@/lib/animation/animationVariants'
import { useScrollDetection } from '@/lib/hooks/useScrollDetection'
import { Link } from '../../components/atoms/Link'

const MotionButton = motion(Button)

export default function HeroSection() {
	const { isScrolled, ref } = useScrollDetection(0.6)

	return (
		<motion.section
			ref={ref}
			className='heroSection relative flex items-center h-screen w-full mb-8'
			initial='hidden'
			animate={!isScrolled ? 'visible' : 'hidden'}
		>
			{/* Desktop Background Image */}
			<Image
				height={1000}
				width={1536}
				alt='hero-bg'
				layout='fixed'
				background='auto'
				className='h-screen absolute hidden lg:block w-full z-[-1] select-none'
				loading='eager'
				sizes='(max-width: 768px) 0vw, 100vw'
				fetchPriority='high'
				src={'/assets/img/hero-bg-desktop.png'}
			/>

			{/* Mobile Background Image */}
			<Image
				height={1000}
				width={1536}
				alt='hero-bg'
				layout='fixed'
				background='auto'
				className='h-screen absolute lg:hidden w-full z-[-1] select-none'
				loading='eager'
				sizes='(max-width: 768px) 0vw, 100vw'
				fetchPriority='high'
				src={'/assets/img/hero-bg-mobile2.png'}
			/>

			{/* Shadows */}
			<div className='heroShadowTop absolute top-0 left-0 right-0 min-h-[315px] lg:h-[400px]' />
			<div className='heroShadowBottom absolute -bottom-20 lg:bottom-[-1px] left-0 right-0 min-h-[500px] lg:h-[550px] z-10' />

			{/* Hero Image - Desktop */}
			<motion.div
				className='h-screen w-auto hidden md:block select-none absolute bottom-0 right-0 top-0'
				variants={heroImageVariants}
				initial='initial'
				animate='animate'
			>
				<Image
					height={937}
					width={827}
					alt='hero-girl-desktop'
					layout='fixed'
					background='auto'
					className='h-screen w-auto'
					loading='eager'
					fetchPriority='high'
					src={'/assets/img/hero-girl-desktop.png'}
				/>
			</motion.div>

			{/* Hero Image - Mobile */}
			<motion.div
				className='h-screen w-auto md:hidden select-none absolute bottom-0 right-0 top-0'
				variants={heroImageVariants}
				initial='initial'
				animate='animate'
			>
				<Image
					height={800}
					width={301}
					alt='hero-girl-mobile'
					layout='fixed'
					background='auto'
					className='h-screen w-auto'
					loading='eager'
					fetchPriority='high'
					src={'/assets/img/hero-girl-mobile2.png'}
				/>
			</motion.div>

			{/* Content */}
			<div className='w-full h-full containerPadding md:h-auto z-10'>
				<div className='max-w-1/2 pt-[20svh] md:pt-0 lg:max-w-[450px] xl:max-w-[600px] 2xl:max-w-[700px]'>
					{/* Title */}
					<motion.h1
						className='text-[40px] md:text-[48px] lg:text-[70px] xl:text-[95px] 2xl:text-[110px] font-squada font-normal leading-none'
						style={{ letterSpacing: '-0.03em' }}
						variants={headerVariants}
						initial='hidden'
						animate='visible'
					>
						Discover & Trade Exclusive NFTs on Shogunex Fusion
					</motion.h1>

					{/* Subtitle */}
					<motion.p
						className='absolute bottom-[18svh] lg:static text-sm lg:text-xl 2xl:text-2xl font-medium lg:mt-4'
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.3, duration: 0.5 }}
					>
						Join the decentralized revolution — powerful, secure, and
						creator-friendly.
					</motion.p>

					{/* Buttons */}
					<div className='absolute bottom-[10svh] lg:static lg:mt-4 flex items-center gap-x-4 lg:gap-x-5'>
						{[
							{ href: '/studio', label: 'Create', ghost: true },
							{ href: '/explore', label: 'Explore More', ghost: false },
						].map((btn, i) => (
							<motion.div
								key={btn.href}
								variants={linkVariants}
								initial='hidden'
								animate='visible'
								custom={i}
								whileHover='hover'
							>
								<Link href={btn.href}>
									<MotionButton
										variant={btn.ghost ? 'ghost' : undefined}
										className={`h-[30px] lg:h-[45px] ${
											btn.ghost
												? 'lg:w-[120px] border border-white hover:bg-white hover:text-black'
												: 'w-[130px] lg:w-[200px]'
										}`}
										whileHover='hover'
										whileTap='tap'
										variants={buttonVariants}
									>
										{btn.label}
									</MotionButton>
								</Link>
							</motion.div>
						))}
					</div>
				</div>
			</div>
		</motion.section>
	)
}
