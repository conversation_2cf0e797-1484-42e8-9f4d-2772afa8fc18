import { useState } from 'react'
import GroupFilterButtons from '@/components/atoms/GroupFilterButtons'
import { Link } from '@/components/atoms/Link'
import NFTCard from '@/components/cards/NFTCard'
import SkeletonNFTCard from '@/components/SkeletonNFTCard'
import { Button } from '@/components/ui/button'
import { useTrendingNFTList } from '@/hooks/useHome'

const filterBtnValues: readonly string[] = [
	'Today',
	'This Week',
	'This Month',
	'This Year',
]

// Map filter button values to day values
const filterToDays: Record<string, 1 | 7 | 30 | 365> = {
	Today: 1,
	'This Week': 7,
	'This Month': 30,
	'This Year': 365,
}

export default function TrendingNFTSection() {
	const [activeFilter, setActiveFilter] = useState<string>(filterBtnValues[0])

	// Get day value based on active filter
	const dayValue = filterToDays[activeFilter] || 1

	// Use the trending NFT hook with the selected day filter
	const { data: nfts = [], isLoading } = useTrendingNFTList(dayValue)

	// Handle filter change
	const handleFilterChange = (value: string) => {
		setActiveFilter(value)
	}

	if ((!nfts || nfts?.length === 0) && !isLoading) {
		return null
	}

	return (
		<section className='trendingNFTSection containerPadding mt-10 lg:mt-20'>
			<div className='flex flex-col gap-y-4 lg:flex-row items-center lg:justify-between lg:pl-2'>
				<div className='text-center lg:text-left'>
					<h1 className='text-2xl lg:text-5xl font-semibold'>Trending NFT's</h1>
					<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
						Most Appreciated NFT's of the Day
					</span>
				</div>
				<div className='flex items-center gap-x-4'>
					<GroupFilterButtons
						active={activeFilter}
						onChange={handleFilterChange}
						values={filterBtnValues}
					/>
					<Link href='/explore?tab=nft' className='hidden lg:inline-block'>
						<Button variant='outline' className='flex mx-auto !px-6'>
							Explore More
						</Button>
					</Link>
				</div>
			</div>

			<div className='mt-7 lg:mt-14 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 3xl:grid-cols-6 gap-5 lg:gap-7'>
				{isLoading || nfts.length === 0
					? // Show skeleton cards while loading or if no data
						Array.from({ length: 8 }).map((_, index) => (
							<SkeletonNFTCard
								// biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton loader
								key={`skeleton-nft-${index}`}
								variant='TrendingNFT'
							/>
						))
					: // Show actual NFT cards when data is loaded
						nfts.map((nft) => (
							<NFTCard
								key={nft.publicKey + nft.name}
								publicKey={nft.publicKey}
								name={nft.name}
								ownerName={nft?.owner?.username}
								profileImg={
									nft.owner.imageUrl || '/assets/temp/user-profile.png'
								}
								img={nft.imageUrl || '/assets/temp/trending-nft1.png'}
								variant='TrendingNFT'
								biddingPrice={nft.lastBiddingPrice || 0}
								ownerPublicKey={nft.owner.publicKey}
								isListed={nft.listing?.status === 'ACTIVE'}
								isAuction={nft.listing?.listingType === 'AUCTION'}
								listingEndsAt={
									nft.listing?.endTime
										? new Date(nft.listing.endTime).getTime()
										: null
								}
								collectionName={nft?.collection?.name ?? ''}
							/>
						))}
			</div>
			<Link href='/explore?tab=nft' className='lg:hidden'>
				<Button variant='outline' className='flex mx-auto !px-6 mt-7'>
					Explore More
				</Button>
			</Link>
		</section>
	)
}
