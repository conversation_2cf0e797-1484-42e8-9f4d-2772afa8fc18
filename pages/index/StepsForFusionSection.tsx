import { <PERSON><PERSON><PERSON>, CopyPlus, Home, Wallet } from 'lucide-react'
import type React from 'react'

export default function StepsForFusionSection() {
	return (
		<div className='py-10 lg:py-[80px] mt-[100px] containerPadding bg-container'>
			<div className='text-center'>
				<h1 className='text-2xl lg:text-5xl font-semibold'>
					Steps to create and sell your Fusion NFT
				</h1>
				<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
					Fusion NFT Steps
				</span>
			</div>

			<div className='grid grid-cols-2 lg:grid-cols-4 gap-x-7 gap-y-10 lg:gap-x-10 mt-7 lg:mt-[100px]'>
				<StepUI
					icon={
						<Wallet className='w-[18px] md:w-[50px] h-auto text-[#FF8F1F] transition-transform duration-300 group-hover:scale-110' />
					}
					title='Setup Your Wallet'
					caption='Connect your crypto wallet to start creating Fusion NFTs seamlessly'
				/>
				<StepUI
					icon={
						<BookCopy className='w-[18px] md:w-[50px] h-auto text-[#FF8F1F] transition-transform duration-300 group-hover:scale-110' />
					}
					title='Choose Your NFTs to Fuse'
					caption='Select multiple NFTs from your collection to merge into a unique Fusion NFT'
				/>
				<StepUI
					icon={
						<CopyPlus className='w-[18px] md:w-[40px] h-auto text-[#FF8F1F] transition-transform duration-300 group-hover:scale-110' />
					}
					title='Create Your Fusion NFTs'
					caption='Customize metadata, set attributes, and finalize your one-of-a-kind Fusion NFT'
				/>
				<StepUI
					icon={
						<Home className='w-[18px] md:w-[48px] h-auto text-[#FF8F1F] transition-transform duration-300 group-hover:scale-110' />
					}
					title='List Them for Sale'
					caption='Set a price or auction your Fusion NFT and showcase it to buyers worldwide'
				/>
			</div>
		</div>
	)
}

function StepUI({
	title,
	caption,
	icon,
}: {
	title: string
	caption: string
	icon: React.ReactNode
}) {
	return (
		<div className='flex-center flex-col gap-y-5 justify-between group transition-all duration-300 hover:shadow-[0_4px_20px_rgba(0,0,0,0.1)] hover:-translate-y-1'>
			<div className='flex-center rounded-full w-[48px] h-[48px] md:w-[122px] md:h-[122px] bg-black transition-all duration-300 group-hover:bg-opacity-90'>
				{icon}
			</div>
			<div className='mt-auto text-center'>
				<h4 className='text-xs md:text-xl xl:text-xl font-semibold transition-colors duration-300 group-hover:text-[#FF8F1F]'>
					{title}
				</h4>
				<p className='text-[10px] md:text-sm xl:text-lg font-light mt-3 transition-colors duration-300 group-hover:text-[#7E7E7E]'>
					{caption}
				</p>
			</div>
		</div>
	)
}
