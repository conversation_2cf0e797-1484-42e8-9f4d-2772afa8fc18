import { Swiper, SwiperSlide } from 'swiper/react'
import { Button } from '../../components/ui/button'
import 'swiper/css'
import { Link } from '@/components/atoms/Link'
import { useTrendingCreatorList } from '@/hooks/useHome'
import TrendingCreatorCard from '../../components/cards/TrendingCreatorCard'

export default function TrendingCreatorsSection() {
	// Use the trending creators hook
	const { data: creators = [], isLoading } = useTrendingCreatorList()

	if ((!creators || creators?.length === 0) && !isLoading) {
		return null
	}

	return (
		<section className='trendingCreators containerPadding mt-10 lg:mt-20'>
			<div className='flex items-center justify-between pl-2'>
				<div>
					<h1 className='text-2xl lg:text-5xl font-semibold'>
						Trending Creators
					</h1>
					<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
						Most valued finearts for sale day
					</span>
				</div>
				<Link href='/explore?tab=creator'>
					<Button variant='outline' className='hidden md:flex !px-6'>
						Explore More
					</Button>
				</Link>
			</div>

			{isLoading || creators.length === 0 ? (
				// Show skeleton loaders while loading or if no data
				<>
					<div className='hidden lg:grid mt-7 lg:mt-14 sm:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7'>
						{Array.from({ length: 4 }).map((_, index) => (
							<div
								// biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton loader
								key={`skeleton-creator-${index}`}
								className='animate-pulse'
							>
								<div className='bg-gray-200 rounded-[20px] h-[210px] mb-8' />
								<div className='flex flex-col items-center -mt-16'>
									<div className='bg-gray-300 rounded-full w-[118px] h-[118px]' />
									<div className='bg-gray-200 h-5 w-32 mt-3 rounded' />
									<div className='bg-gray-200 h-4 w-24 mt-2 rounded' />
								</div>
							</div>
						))}
					</div>
					<div className='w-full lg:hidden mt-7'>
						<Swiper
							slidesPerView={1.12}
							spaceBetween={16}
							navigation={true}
							className='trendingCreatorsSwiper'
						>
							{Array.from({ length: 4 }).map((_, index) => (
								// biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton loader
								<SwiperSlide key={`skeleton-creator-slide-${index}`}>
									<div className='animate-pulse'>
										<div className='bg-gray-200 rounded-[20px] h-[188px] mb-8' />
										<div className='flex flex-col items-center -mt-16'>
											<div className='bg-gray-300 rounded-full w-[96px] h-[96px]' />
											<div className='bg-gray-200 h-4 w-28 mt-3 rounded' />
											<div className='bg-gray-200 h-3 w-20 mt-2 rounded' />
										</div>
									</div>
								</SwiperSlide>
							))}
						</Swiper>
					</div>
				</>
			) : (
				// Show actual creators when data is loaded
				<>
					<div className='hidden lg:grid mt-7 lg:mt-14 sm:grid-cols-2 lg:grid-cols-4 gap-5 lg:gap-7'>
						{creators.map((creator) => (
							<TrendingCreatorCard
								key={creator.id}
								img={creator.nfts[0]?.imageUrl}
								creator={{
									name: creator.username,
									caption: `@${creator.username.toLowerCase().replace(/\s+/g, '')}`,
									profileImg: creator.imageUrl,
								}}
								username={creator.username}
							/>
						))}
					</div>
					<div className='w-full lg:hidden mt-7'>
						<Swiper
							slidesPerView={1.12}
							spaceBetween={16}
							navigation={true}
							className='trendingCreatorsSwiper'
						>
							{creators.map((creator) => (
								<SwiperSlide key={creator.id}>
									<TrendingCreatorCard
										img={creator.nfts[0]?.imageUrl}
										creator={{
											name: creator.username,
											caption: `@${creator.username.toLowerCase().replace(/\s+/g, '')}`,
											profileImg: creator.imageUrl,
										}}
										username={creator.username}
									/>
								</SwiperSlide>
							))}
						</Swiper>
					</div>
				</>
			)}

			{/* <Link href='/explore?tab=creator' className=''>
				<Button variant='outline' className='flex mx-auto md:hidden !px-6 mt-7'>
					Explore More
				</Button>
			</Link> */}
		</section>
	)
}
