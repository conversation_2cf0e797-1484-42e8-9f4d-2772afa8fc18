import type { TransactionBuilder } from '@metaplex-foundation/umi'
import { publicKey, transactionBuilder } from '@metaplex-foundation/umi'
import { base58 } from '@metaplex-foundation/umi/serializers'
import { useWallet } from '@solana/wallet-adapter-react'
import { Check, CircleHelp } from 'lucide-react'
import { useContext, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { navigate } from 'vike/client/router'
import customToast from '@/components/CustomToast'
import { Button } from '@/components/ui/button'
import {
	Form,
	FormControl,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { createAuctionListing, createFixedPriceListing } from '@/lib/generated'
import { AuctionType } from '@/lib/generated/types'
import { UmiContext } from '@/lib/umi'

export default function ListForSaleForm({
	assetPublicKey,
	ownerPublicKey,
	royalty,
	collectionPublicKey,
}: {
	assetPublicKey: string
	ownerPublicKey: string
	collectionPublicKey?: string
	royalty: number // in % basis points (100 = 100%)
}) {
	const wallet = useWallet()
	const umi = useContext(UmiContext)
	const [price, setPrice] = useState('')
	const [duration, setDuration] = useState('604800') // 7 days in seconds (default)
	const [loading, setLoading] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [transactionHash, setTransactionHash] = useState('')
	const [success, setSuccess] = useState(false)
	const [listingType, setListingType] = useState<'fixedPrice' | 'highestBid'>(
		'fixedPrice',
	)
	const [startDelaySeconds] = useState(0) // Start immediately by default
	const [extensionPeriod] = useState(300) // 5 minutes in seconds by default
	const [reservePrice, setReservePrice] = useState('')
	const [solToUsd, setSolToUsd] = useState(0) // SOL to USD conversion rate
	// Check if the current wallet public key matches the NFT owner's public key
	const isOwner = wallet.publicKey
		? wallet.publicKey.toBase58() === ownerPublicKey
		: false

	// Fetch SOL to USD conversion rate
	useEffect(() => {
		const fetchSolPrice = async () => {
			try {
				const response = await fetch(
					'https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd',
				)
				const data = await response.json()
				setSolToUsd(data.solana.usd)
			} catch (err) {
				console.error('Failed to fetch SOL price:', err)
				setSolToUsd(0) // Default to 0 if fetch fails
			}
		}

		fetchSolPrice()
		// Refresh price every 5 minutes
		const interval = setInterval(fetchSolPrice, 5 * 60 * 1000)

		return () => clearInterval(interval)
	}, [])

	const handleCreateListing = async () => {
		if (!umi || !wallet.publicKey || !wallet.signTransaction || !price) {
			setError('Wallet not connected or price not set.')
			return
		}

		if (!isOwner) {
			setError('Only the owner can list this NFT for sale.')
			return
		}

		setLoading(true)
		setError(null)

		try {
			// Validate price input
			const priceFloat = Number.parseFloat(price)
			if (Number.isNaN(priceFloat) || priceFloat <= 0) {
				throw new Error('Price must be a valid positive number.')
			}

			const priceLamports = BigInt(priceFloat * 1_000_000_000)

			// Validate duration
			const durationValue = Number.parseInt(duration)
			if (Number.isNaN(durationValue) || durationValue <= 0) {
				throw new Error('Duration must be a valid positive number.')
			}

			// Verify wallet matches owner
			if (wallet.publicKey?.toBase58() !== ownerPublicKey) {
				console.warn(
					'Warning: Wallet public key does not match owner public key!',
				)
			}

			// Build the transaction based on listing type
			let builder: TransactionBuilder

			if (listingType === 'fixedPrice') {
				builder = transactionBuilder().add(
					createFixedPriceListing(umi, {
						seller: umi.identity,
						asset: publicKey(assetPublicKey),
						price: priceLamports,
						duration: BigInt(Number.parseInt(duration)),
						collection: collectionPublicKey
							? publicKey(collectionPublicKey)
							: undefined,
					}),
				)
			} else {
				// Auction listing
				// Note: Reserve price is collected but not currently used in the auction listing
				// Will be implemented in a future update

				builder = transactionBuilder().add(
					createAuctionListing(umi, {
						seller: umi.identity,
						asset: publicKey(assetPublicKey),
						startPrice: priceLamports,
						startDelaySeconds: BigInt(startDelaySeconds),
						duration: BigInt(Number.parseInt(duration)),
						auctionType: AuctionType.English,
						extensionPeriod: BigInt(extensionPeriod),
						collection: collectionPublicKey
							? publicKey(collectionPublicKey)
							: undefined,
					}),
				)
			}

			try {
				const { signature } = await builder.sendAndConfirm(umi, {
					send: {
						skipPreflight: true,
					},
				})

				const serializedSignature = base58.deserialize(signature)[0]
				setTransactionHash(serializedSignature)
				setPrice('')
				setSuccess(true)
				// Update success message based on listing type
				const listingTypeText =
					listingType === 'fixedPrice' ? 'Fixed Price' : 'Auction'
				customToast.success(
					`NFT Listed Successfully for ${listingTypeText} at ${price} SOL`,
				)

				// Force reload the NFT details page using the proper Vike navigation approach
				// We're using window.location.href to force a full page refresh which will reload all data
				window.location.href = `/nft-details/${assetPublicKey.toString()}`
			} catch (err: unknown) {
				console.error('Failed to create listing:', err)
				// Extract more detailed error information
				let errorMessage = 'Unknown error occurred'

				if (err && typeof err === 'object' && 'InstructionError' in err) {
					const instructionError = (
						err as { InstructionError: [number, unknown] }
					).InstructionError
					const [index, errorInfo] = instructionError
					errorMessage = `Instruction error at index ${index}: ${JSON.stringify(errorInfo)}`
					console.error('Instruction error details:', { index, errorInfo })
				} else if (err instanceof Error) {
					errorMessage = err.message
				}

				setError(`Listing failed: ${errorMessage}`)
			}
		} catch (err: unknown) {
			console.error('Failed to create listing:', err)
			setError(
				`Listing failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
			)
		} finally {
			setLoading(false)
		}
	}

	const form = useForm()
	const [isResPriceIncl, setResPriceIncl] = useState(true)

	// Reset success state when form values change
	useEffect(() => {
		if (success) setSuccess(false)
	}, [success])

	// Fetch SOL to USD conversion rate
	useEffect(() => {
		const fetchSolPrice = async () => {
			try {
				const response = await fetch(
					'https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd',
				)
				const data = await response.json()
				setSolToUsd(data.solana.usd)
			} catch (err) {
				console.error('Failed to fetch SOL price:', err)
				setSolToUsd(0) // Default to 0 if fetch fails
			}
		}

		fetchSolPrice()
		// Refresh price every 5 minutes
		const interval = setInterval(fetchSolPrice, 5 * 60 * 1000)

		return () => clearInterval(interval)
	}, [])

	return (
		<Form {...form}>
			<div className='md:text-xl'>
				<span className='text-[#8A8A8A] ml-1'>Choose a type of sale</span>
				<div className='bg-container rounded-[10px] mt-1'>
					<RadioGroup
						defaultValue='fixedPrice'
						disabled={loading || !wallet.publicKey}
						value={listingType}
						onValueChange={(value) =>
							setListingType(value as 'fixedPrice' | 'highestBid')
						}
						className='gap-0'
					>
						<Label htmlFor='fixed-price' className='cursor-pointer'>
							<div className='w-full flex items-center justify-between px-5 py-3.5 md:px-10 md:py-8'>
								<div className='flex flex-col'>
									<span className='text-xs md:text-[22px] font-semibold text-white'>
										Fixed Price
									</span>
									<span className='text-[#7E7E7E] text-[10px] md:text-base font-light mt-2'>
										The item is listed at the price you set.
									</span>
								</div>
								<RadioGroupItem value='fixedPrice' id='fixed-price' />
							</div>
						</Label>

						<span className='block h-[1px] w-full bg-[#2D2D2D]' />

						<Label htmlFor='highest-bid' className='cursor-pointer'>
							<div className='flex w-full items-center justify-between px-5 py-3.5 md:px-10 md:py-8'>
								<div className='flex flex-col'>
									<span className='text-xs md:text-[22px] font-semibold text-white'>
										Sell to highest bidder
									</span>
									<span className='text-[#7E7E7E] text-[10px] md:text-base font-light mt-2'>
										The item will be auctioned to the highest bidder.
									</span>
								</div>
								<RadioGroupItem value='highestBid' id='highest-bid' />
							</div>
						</Label>
					</RadioGroup>
				</div>

				<div className='flex items-center gap-x-2 mt-[57px]'>
					<span className='text-xs md:text-xl font-semibold'>Set a Price</span>{' '}
					<CircleHelp className='w-5 h-auto text-white/30 cursor-pointer' />
				</div>

				<FormItem className='mt-10'>
					<FormLabel>Price</FormLabel>
					<FormControl>
						<div className='relative flex items-center h-10 md:h-[60px] bg-input border border-border rounded-md focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]'>
							<Input
								disabled={loading || !wallet.publicKey}
								value={price}
								onChange={(e) => setPrice(e.target.value)}
								type='number'
								step='0.000000001'
								min='0'
								placeholder='0.00'
								className='w-full h-full text-xs md:text-xl placeholder:!text-xs lg:placeholder:!text-xl bg-transparent border-none focus-visible:ring-0'
							/>
							<div className='flex text-xs md:text-xl items-center justify-end pl-1 h-full'>
								<span className='text-[#393939] pr-4 text-xs md:text-xl'>
									$
									{price && solToUsd
										? (Number(price) * solToUsd).toFixed(2)
										: '0.00'}
								</span>
								<div className='px-3 lg:px-5 flex-center text-xs md:text-xl xl:px-8 border-l border-[#2D2D2D] h-full'>
									SOL
								</div>
							</div>
						</div>
					</FormControl>
					<FormMessage />
				</FormItem>

				<FormLabel className='mt-[57px]'>Duration</FormLabel>
				<Select
					value={duration}
					onValueChange={(value) => setDuration(value)}
					disabled={loading || !wallet.publicKey}
				>
					<SelectTrigger className='w-full mt-2 text-xs lg:text-xl !h-10 md:!h-[60px]'>
						<SelectValue placeholder='Select a duration' />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value='3600'>1 Hour</SelectItem>
						<SelectItem value='86400'>1 Day</SelectItem>
						<SelectItem value='604800'>7 Days</SelectItem>
						<SelectItem value='1296000'>15 Days</SelectItem>
						<SelectItem value='2592000'>30 Days</SelectItem>
					</SelectContent>
				</Select>

				{listingType !== 'fixedPrice' && (
					<div className='flex items-center justify-between mt-[57px]'>
						<div className='flex items-center gap-x-2'>
							<span className='text-[10px] md:text-xl font-semibold text-[#8A8A8A]'>
								Include reserve price
							</span>{' '}
							<CircleHelp className='w-5 h-auto text-white/30 cursor-pointer' />
						</div>

						<Switch
							disabled={loading || !wallet.publicKey}
							defaultChecked
							checked={isResPriceIncl}
							onCheckedChange={() => setResPriceIncl(!isResPriceIncl)}
						/>
					</div>
				)}
				{isResPriceIncl && (
					<FormItem className='mt-8'>
						<FormControl>
							<div className='relative flex items-center h-10 md:h-[60px] bg-input border border-border rounded-md focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]'>
								<Input
									disabled={loading || !wallet.publicKey}
									value={reservePrice}
									onChange={(e) => setReservePrice(e.target.value)}
									type='number'
									step='0.000000001'
									min='0'
									placeholder='0.00'
									className='w-full h-full text-xs md:text-xl placeholder:!text-xs lg:placeholder:!text-xl bg-transparent border-none focus-visible:ring-0'
								/>
								<div className='flex text-xs md:text-xl items-center justify-end pl-1 h-full'>
									<span className='text-[#393939] pr-4 text-xs md:text-xl'>
										$
										{reservePrice && solToUsd
											? (Number(reservePrice) * solToUsd).toFixed(2)
											: '0.00'}
									</span>
									<div className='px-3 lg:px-5 flex-center text-xs md:text-xl xl:px-8 border-l border-[#2D2D2D] h-full'>
										SOL
									</div>
								</div>
							</div>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}

				<div className='w-full border-t-[2px] gradientBorder mt-14 pb-10 md:mt-[75px] md:pb-[75px]' />

				<div className='flex text-xs md:text-2xl font-semibold flex-col divide-y divide-[#2D2D2D]'>
					<div className='flex justify-between pb-5 md:pb-10'>
						<span>Listing Price</span>
						<span>{price ? `${price} SOL` : '0 SOL'}</span>
					</div>
					<div className='flex justify-between pt-5 md:pt-8 pb-5 md:pb-10'>
						<span>Shogun Fee</span>
						<span>0.5%</span>
					</div>
					<div className='flex justify-between pt-5 md:pt-8 pb-5 md:pb-10'>
						<span>Creator Earning</span>
						<span>{royalty}%</span>
					</div>
					<div className='flex items-start justify-between pt-5 md:pt-8 pb-5 md:pb-10'>
						<span className='text-sm md:text-3xl'>
							Total potential earnings
						</span>
						<div className='flex flex-col items-end'>
							<span className='text-sm md:text-3xl'>
								{price
									? `${(Number(price) * (1 - royalty / 100 - 0.005)).toFixed(6)} SOL`
									: '0 SOL'}
							</span>
							<span className='text-[#8A8A8A] text-xs md:text-xl mt-1 md:mt-3'>
								$
								{price && solToUsd
									? (
											Number(price) *
											(1 - royalty / 100 - 0.005) *
											solToUsd
										).toFixed(2)
									: '0.00'}{' '}
								USD
							</span>
						</div>
					</div>
				</div>

				{error && (
					<div className='mt-4 p-3 bg-red-900/30 border border-red-500 rounded-md text-red-200'>
						{error}
					</div>
				)}

				{success && (
					<div className='mt-4 p-3 bg-green-900/30 border border-green-500 rounded-md text-green-200 flex items-center'>
						<Check className='w-5 h-5 mr-2' />
						NFT listed successfully! Transaction hash: {transactionHash}
					</div>
				)}

				<div className='w-full flex justify-center mt-4'>
					{isOwner ? (
						<Button
							className='w-full text-sm md:text-xl max-w-[363px] h-10 md:h-[55px]'
							onClick={handleCreateListing}
							disabled={
								loading || !price || Number(price) <= 0 || !wallet.publicKey
							}
						>
							{loading ? 'Processing...' : 'List for Sale '}
						</Button>
					) : (
						<Button
							className='w-full text-sm md:text-xl max-w-[363px] h-10 md:h-[55px]'
							disabled
						>
							Only the owner can list this NFT
						</Button>
					)}
				</div>
			</div>
		</Form>
	)
}
