import type { PageContextServer } from 'vike/types'
import type { NFT } from '@/types/enhanced-assets'
import { onGetNFT } from '../nft-details/Page.telefunc'

export type ListForSaleData = {
	nft: NFT | null
}

export default async function data(
	pageContext: PageContextServer,
): Promise<ListForSaleData | null> {
	try {
		// Get NFT public key from URL parameters
		const publicKey = pageContext.routeParams.id

		if (!publicKey) {
			console.error('No NFT public key provided in route parameters')
			return {
				nft: null,
			}
		}

		// Fetch NFT data
		const nft = await onGetNFT(publicKey)

		return {
			nft,
		}
	} catch (error) {
		console.error('Error fetching data for list-for-sale page:', error)
		return {
			nft: null,
		}
	}
}
