import { publicKey } from '@metaplex-foundation/umi'
import type { PageContextServer } from 'vike/types'
import {
	fetchEnhancedAssetsByCollection,
	fetchEnhancedCollection,
} from '@/lib/nft/asset-utils'
import type { CollectionData } from '@/types/enhanced-assets'

export type { CollectionData }

export default async function data(
	pageContext: PageContextServer,
): Promise<CollectionData> {
	// Get collection ID from page context route parameters
	const collectionId = pageContext.routeParams.id

	if (!collectionId) {
		console.error('No collection ID provided in route parameters')
		throw new Error('No collection ID provided in route parameters')
	}

	const collectionKey = publicKey(collectionId)

	let retries = 10
	while (retries > 0) {
		try {
			// Fetch enhanced collection with metadata
			const collection = await fetchEnhancedCollection(collectionId)

			// Fetch all assets in the collection and enhance them with metadata
			const assets = await fetchEnhancedAssetsByCollection(collectionKey)

			return {
				assets,
				collection,
			}
		} catch (error) {
			console.error(
				`Error fetching collection data (attempt ${11 - retries}/10):`,
				error,
			)
			retries--
			if (retries === 0) {
				console.error('All retries failed. Throwing error.')
				throw error
			}
			// Wait for 5 seconds before retrying
			await new Promise((resolve) => setTimeout(resolve, 5000))
		}
	}
	// This line should theoretically be unreachable if the loop logic is correct
	// and always either returns data or throws an error from within the loop.
	// However, to satisfy TypeScript's need for a return path and to handle
	// any unforeseen edge cases where the loop might exit unexpectedly:
	console.error('Exited retry loop unexpectedly. Throwing error.')
	throw new Error('Failed to fetch collection data after multiple retries.')
}
