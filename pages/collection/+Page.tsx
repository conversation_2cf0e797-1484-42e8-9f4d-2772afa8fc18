import { Image } from '@unpic/react'
import { Badge<PERSON>heck, Filter, SlidersHorizontal } from 'lucide-react'
import { useEffect, useId, useState } from 'react'
import { useData } from 'vike-react/useData'
import { usePageContext } from 'vike-react/usePageContext'
import GroupedSocialButtons from '@/components/atoms/GroupedSocialButtons'
import LikeFeature from '@/components/atoms/LikeFeature'
import ShareFeature from '@/components/atoms/ShareFeature'
import Tag from '@/components/atoms/Tag'
import CollectionSkeleton from '@/components/CollectionSkeleton'
import NFTCard from '@/components/cards/NFTCard'
import Filters from '@/components/Filters'
import { ActivityLineChart } from '@/components/LineChart'
import MyActivities from '@/components/MyActivities'
import MyBreadcrumb from '@/components/MyBreadcrumb'
import { PaginationComp } from '@/components/MyPagination'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select'
import {
	type ActivityParams,
	useCollectionActivity,
} from '@/hooks/useCollectionActivity'
import { useCollectionLikeStatus } from '@/hooks/useFollowUser'
import { useNFTListing } from '@/hooks/useNFTlisting'
import { usePriceChart } from '@/hooks/usePriceChart'
import { useCountViews } from '@/hooks/useViews'
import { useCollectionDetails } from '@/lib/hooks/useCollectionDetails'
import { useBannerUrl } from '@/lib/providers/BannerProvider'
import { cn, getShareUrl } from '@/lib/utils'
import TagIcon from '@/public/assets/svg/tag-icon.svg?react'
import type { CollectionData } from './+data'

// Define common activity types for the filter dropdown
const activityTypeOptions = [
	{ value: 'ALL', label: 'All Types' },
	{ value: 'NFT_MINTED', label: 'Minted' },
	{ value: 'NFT_LISTED', label: 'Listed for Sale' },
	{ value: 'NFT_BID_PLACED', label: 'Bid Placed' },
	{ value: 'NFT_OFFER_CREATED', label: 'Offer Created' },
	{ value: 'NFT_OFFER_ACCEPTED', label: 'Offer Accepted' },
	{ value: 'NFT_TRANSFERRED', label: 'Transferred' },
]

export default function Page() {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false)
	const [itemsPage, setItemsPage] = useState<number>(1) // Renamed from 'page' to avoid conflict
	const [itemsLimit] = useState<number>(12) // Renamed from 'limit'
	const { collection } = useData<CollectionData>()
	const { data: likeData } = useCollectionLikeStatus(collection?.publicKey)
	const [selectedTraits, setSelectedTraits] = useState<{
		[category: string]: string[]
	}>({})

	const pageContext = usePageContext()
	const collectionId = pageContext?.routeParams?.id || ''

	// Track views for this collection
	useCountViews('collection', collectionId)

	const { data: collectionData, isLoading } = useCollectionDetails(collectionId)

	console.log(collectionData)

	// NFT Items Tab State
	const { data: nftResponse, isLoading: nftsIsLoading } = useNFTListing({
		collectionId: collectionId,
		page: itemsPage,
		limit: itemsLimit,
		traits: JSON.stringify(selectedTraits),
	})

	// Activity Tab State
	const [activityFilters, setActivityFilters] = useState<ActivityParams>({
		activityType: 'ALL',
		sortOrder: 'desc',
		page: 1,
		limit: 10, // Default limit for activities
	})

	const {
		data: activityData,
		isLoading: activityIsLoading,
		error: activityError,
	} = useCollectionActivity(collectionId, activityFilters)

	const {
		data: priceChartData,
		isLoading: priceChartIsLoading,
		error: priceChartError,
	} = usePriceChart(collectionId)

	const handleActivityFilterChange = (
		filterName: keyof ActivityParams,
		value: string | number,
	) => {
		setActivityFilters((prev) => ({
			...prev,
			[filterName]: value,
			page: filterName !== 'page' ? 1 : Number(value), // Reset to page 1 on filter change, unless it's page itself changing
		}))
	}

	const handleTraitFilterChange = (category: string, trait: string) => {
		setSelectedTraits((prev) => {
			const categoryTraits = prev[category] || []
			const isSelected = categoryTraits.includes(trait)
			const updatedTraits = isSelected
				? categoryTraits.filter((t) => t !== trait)
				: [...categoryTraits, trait]

			// Make a shallow copy of prev before modifying
			const updated = { ...prev }

			if (updatedTraits.length === 0) {
				//remove category if all traits removed
				delete updated[category]
			} else {
				updated[category] = updatedTraits
			}
			return updated
		})
	}

	const { setBannerUrl } = useBannerUrl()
	const key = useId()

	useEffect(() => {
		if (collectionData?.bannerUrl) {
			setBannerUrl(collectionData.bannerUrl)
		} else {
			setBannerUrl(null)
		}
	}, [collectionData, setBannerUrl])

	const [currentTab, setCurrentTab] = useState<'Items' | 'Activity'>('Items')

	// Define breadcrumb after collection is loaded
	const breadcrumb = [
		{ id: 1, title: 'Home', url: '/' },
		{
			id: 2,
			title: collection?.metadata?.name
				? collection.metadata.name.trim().slice(0, 25)
				: 'Collection',
		},
	]

	// If loading, show skeleton
	if (isLoading) {
		return <CollectionSkeleton />
	}

	// If no collection data is found after loading
	if (!collection && !isLoading) {
		return (
			<div className='containerPadding pt-4'>
				<div className='flex flex-col items-center justify-center h-[50vh]'>
					<h2 className='text-xl font-medium'>Collection not found</h2>
					<p className='mt-2 text-sm text-gray-400'>
						The collection you're looking for might have been removed or doesn't
						exist.
					</p>
					<Button className='mt-4' onClick={() => window.history.back()}>
						Go Back
					</Button>
				</div>
			</div>
		)
	}

	return (
		<div className='containerPadding pt-4'>
			<MyBreadcrumb items={breadcrumb} />

			{/* --------------Collection banner------------ */}
			<div className='pt-[4rem] lg:pt-[5rem]'>
				<div className='relative userProfileDetails'>
					<div className='flex flex-col w-full gap-4'>
						<div className='flex flex-row w-full gap-4 items-start'>
							<div className='relative rounded-full w-fit shrink-0 after:content-[""] after:absolute after:-inset-1 sm:after:-inset-1.5 lg:after:-inset-2 after:p-0.5 sm:after:p-1 after:bg-gradient-to-r after:from-[#FF8F1F] after:to-[#DD0003] after:-z-[1] after:rounded-full'>
								<Image
									src={
										collection?.metadata.image ||
										'/assets/img/default-profile.png'
									}
									className='h-[90px] w-[90px] sm:h-[100px] sm:w-[100px] lg:h-[180px] lg:w-[180px] rounded-full object-cover'
									layout='fixed'
									width={180}
									height={180}
									alt='user profile'
								/>
							</div>

							<div className='flex flex-col flex-grow min-w-0 mt-8 md:mt-0 md:ml-4'>
								<div className='flex items-center'>
									<span className='text-base lg:text-xl xl:text-2xl font-bold truncate capitalize'>
										{collection.metadata.name.trim().slice(0, 25)}
									</span>
									<BadgeCheck className='hidden w-5 h-5 text-black fill-primary ml-2 shrink-0' />
								</div>

								{/* Username, Social Links, Stats, Like & Share Buttons */}
								<div className='flex flex-row flex-wrap items-center justify-between gap-2 mt-1'>
									<div className='flex md:items-center flex-col md:flex-row gap-x-2 min-w-0 flex-shrink'>
										<span className='text-xs lg:text-xl xl:text-2xl truncate'>
											@{collection.metadata.name.trim().slice(0, 25)}
										</span>
										<span className='hidden sm:block w-0.5 h-5 bg-white/30' />
										<GroupedSocialButtons
											showRoundBlur={false}
											className='xl:gap-x-5 mt-2 md:mt-0'
											links={{
												twitter: collectionData?.xUrl as string,
												website: collectionData?.websiteUrl as string,
												solana: collection.publicKey,
											}}
										/>
									</div>

									{/* Right Side: Stats, Like & Share Buttons */}
									<div className='flex items-center gap-x-5 justify-end mt-3 md:mt-0'>
										{/* Stats */}
										{(collectionData?.nftCount ||
											collectionData?.ownerCount) && (
											<div className='flex divide-x-2 divide-white/50 gap-x-5 mr-4 text-xs lg:text-lg font-semibold'>
												{collectionData?.nftCount && (
													<span className='pr-5 text-nowrap'>
														{collectionData?.nftCount} NFT
														{collectionData?.nftCount === 1 ? '' : 's'}
													</span>
												)}
												{collectionData?.ownerCount && (
													<span className='text-nowrap'>
														{collectionData?.ownerCount} Owner
														{collectionData?.ownerCount === 1 ? '' : 's'}
													</span>
												)}
											</div>
										)}

										{/* Like & Share Buttons */}
										<div className='flex gap-x-3 items-center absolute top-0 right-0 md:static'>
											<LikeFeature
												type='collection'
												itemId={collection.publicKey}
												isLiked={likeData?.isLiked}
											/>
											<ShareFeature
												twitter={{
													text: `Check out this collection: ${collection.metadata.name} on Shogun NFT Marketplace`,
													url: getShareUrl('collection', collection.publicKey),
													hashtags: 'shogun, nft, marketplace, solana',
												}}
											/>
										</div>
									</div>
								</div>

								{/* Description */}
								<p className='hidden md:block mt-4 md:text-sm lg:text-base xl:text-lg line-clamp-3'>
									{collection.metadata.description}
								</p>

								<div className='hidden md:flex items-center gap-3 flex-wrap mt-3'>
									<TagIcon className='w-5 h-auto text-white' />
									{collectionData?.tags.map((tag) => (
										<Tag key={tag} value={tag} />
									))}
								</div>
							</div>
						</div>
					</div>
					<div className='md:hidden mt-5'>
						<p className='text-[10px] text-center leading-loose'>
							{collection.metadata.description}
						</p>

						<div className='flex items-center gap-3 flex-wrap mt-3'>
							<TagIcon className='w-4 h-auto text-white' />
							{collectionData?.tags.map((tag) => (
								<Tag key={tag} value={tag} />
							))}
						</div>
					</div>
				</div>
			</div>
			{/* --------------Collection banner end-------- */}

			<div className='mt-7 w-full text-2xl font-semibold flex gap-x-5 lg:gap-x-10 items-center text-[#8A8A8A] py-4 border-b-[1.5px] gradientBorder'>
				<span
					tabIndex={0}
					role='tab'
					onClick={() => setCurrentTab('Items')}
					className={cn(
						'cursor-pointer text-nowrap',
						currentTab === 'Items' && 'text-white',
					)}
				>
					Items
				</span>
				<span
					onClick={() => {
						setCurrentTab('Activity')
						setIsSidebarOpen(false)
					}}
					role='tab'
					tabIndex={-1}
					className={cn(
						'cursor-pointer text-nowrap',
						currentTab === 'Activity' && 'text-white',
					)}
				>
					Activity
				</span>
			</div>

			<div className='CollectionWrapper lg:flex mt-7'>
				<Filters
					filters={{
						traits: collectionData?.nftTraitFilters,
						onClickTrait: handleTraitFilterChange,
						selectedTraits,
					}}
					isSidebarOpen={isSidebarOpen}
					setIsSidebarOpen={setIsSidebarOpen}
				/>

				<div className='CollectionMainArea w-full @container'>
					{currentTab === 'Items' &&
						(nftResponse?.nfts && nftResponse.nfts.length > 0 ? (
							<>
								<div className='filtersBarTop flex items-center gap-x-2'>
									<Button
										variant={'ghost'}
										className={cn(
											'border border-muted-foreground has-[>svg]:px-3 lg:has-[>svg]:px-5',
											isSidebarOpen && 'bg-primary',
										)}
										onClick={() => setIsSidebarOpen(!isSidebarOpen)}
									>
										<SlidersHorizontal className='w-4 h-auto' />
										Filters
									</Button>
									{(collectionData?.nftCount || 0) > 0 ? (
										<span className='lg:ml-4 text-sm font-medium'>
											{nftResponse?.nfts.length || 0} Result
											{nftResponse?.nfts.length >= 1 ? '' : 's'}
										</span>
									) : (
										<span />
									)}
								</div>

								<div className='grid mt-7 grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
									{nftResponse.nfts.map((nft) => (
										<NFTCard
											name={nft?.name || 'Untitled NFT'}
											ownerName={nft?.owner?.username || 'Unknown'}
											profileImg={nft?.owner?.imageUrl || ''}
											img={nft?.imageUrl || ''}
											ownerPublicKey={nft?.owner?.publicKey || ''}
											key={nft?.publicKey || `nft-${Math.random()}`}
											variant='TrendingNFT'
											biddingPrice={
												nft?.listing?.price ?? nft?.lastBiddingPrice ?? 0
											}
											publicKey={nft?.publicKey || ''}
											isListed={nft?.listing?.status === 'ACTIVE'}
											isAuction={nft?.listing?.listingType === 'AUCTION'}
											listingEndsAt={
												nft?.listing?.endTime
													? new Date(nft.listing.endTime).getTime()
													: null
											}
											collectionName={nft?.collection?.name || ''}
										/>
									))}
								</div>

								<PaginationComp
									className='mt-7'
									currentPage={itemsPage}
									setCurrentPage={(newPage) => {
										setItemsPage(newPage)
									}}
									totalPages={
										nftResponse?.limit && nftResponse?.total
											? Math.ceil(nftResponse.total / nftResponse.limit)
											: 1
									}
								/>
							</>
						) : !nftsIsLoading ? (
							<div className='flex flex-col items-center justify-center py-8'>
								<p className='text-base font-medium'>
									No NFTs found in this collection
								</p>
								<p className='text-sm text-gray-400 mt-2'>
									There are currently no items available in this collection.
								</p>
							</div>
						) : (
							<div className='grid grid-cols-1 @sm:grid-cols-2 @min-[50rem]:grid-cols-3 @6xl:grid-cols-4 gap-5 lg:gap-7'>
								{Array(4)
									.fill(0)
									.map(() => (
										<div
											key={`skeleton-${key}-${Math.random().toString(36).substring(2, 9)}`}
											className='rounded-lg bg-gray-800 animate-pulse h-[300px]'
										/>
									))}
							</div>
						))}

					{currentTab === 'Activity' && (
						<div>
							{priceChartIsLoading && (
								<p className='text-center py-4'>Loading Price Chart...</p>
							)}
							{priceChartData && (
								<ActivityLineChart data={priceChartData || []} />
							)}
							{priceChartError && (
								<p className='text-center py-4 text-red-500'>
									Error loading Price Chart: {priceChartError.message}
								</p>
							)}
							{!priceChartData && !priceChartIsLoading && !priceChartError && (
								<p className='text-center py-4'>
									No Price Chart found for this collection.
								</p>
							)}

							{activityIsLoading && (
								<p className='text-center py-4'>Loading activities...</p>
							)}
							{activityData && (
								<>
									{/* ------------Filters for Activity Details------------- */}
									<div className='flex gap-x-4 items-center lg:mt-10 my-6'>
										<div>
											<Label className='text-sm text-muted-foreground'>
												Activity Type:
											</Label>
											<Select
												value={activityFilters.activityType}
												defaultValue={activityTypeOptions[0].value}
												onValueChange={(value) =>
													handleActivityFilterChange('activityType', value)
												}
											>
												<SelectTrigger className='mt-1 cursor-pointer'>
													<SelectValue placeholder='Activity Type' />
												</SelectTrigger>
												<SelectContent>
													{activityTypeOptions.map((option) => (
														<SelectItem key={option.value} value={option.value}>
															{option.label}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</div>
										<div>
											<Label className='text-sm text-muted-foreground'>
												Sorting:
											</Label>
											<Select
												defaultValue='asc'
												value={activityFilters.sortOrder}
												onValueChange={(value) =>
													handleActivityFilterChange('sortOrder', value)
												}
											>
												<SelectTrigger className='mt-1 cursor-pointer'>
													<SelectValue placeholder='Order' />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value='asc'>Ascending</SelectItem>
													<SelectItem value='desc'>Descending</SelectItem>
												</SelectContent>
											</Select>
										</div>
									</div>

									<MyActivities
										collectionUrl={collection.metadata.image}
										activities={activityData.activities || []}
									/>

									{activityData.pagination &&
										activityData.pagination.totalActivities > 0 && (
											<PaginationComp
												className='mt-7'
												currentPage={activityData.pagination.currentPage}
												setCurrentPage={(newPage) =>
													handleActivityFilterChange('page', newPage)
												}
												totalPages={activityData.pagination.totalPages}
											/>
										)}
								</>
							)}

							{activityError && (
								<p className='text-center py-4 text-red-500'>
									Error loading activities: {activityError.message}
								</p>
							)}
							{!activityData && !activityIsLoading && !activityError && (
								<p className='text-center py-4'>
									No activities found for this collection.
								</p>
							)}
						</div>
					)}
				</div>
			</div>
		</div>
	)
}
