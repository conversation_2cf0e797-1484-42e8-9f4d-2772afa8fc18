'use client'
import {
	Facebook,
	Globe,
	ImagePlus,
	Instagram,
	Loader2,
	Wallet,
} from 'lucide-react'

import MyBreadcrumb from '@/components/MyBreadcrumb'
import { Button } from '@/components/ui/button'
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { truncateAddress } from '@/lib/utils'
import { useProfileForm } from './useProfileForm'

const breadcrumbs = [
	{ id: 1, title: 'Home', url: '/' },
	{ id: 2, title: 'Edit Profile' },
]
export function EditProfileForm() {
	const profileForm = useProfileForm()
	const form = profileForm.form
	return (
		<div className='containerPadding pb-20'>
			<MyBreadcrumb items={breadcrumbs} />
			<div className='pt-[3.5rem] lg:pt-[7rem]'>
				<div className='relative flex flex-col items-center gap-y-3'>
					<Input
						type='file'
						ref={profileForm.bannerInputRef}
						onChange={profileForm.bannerFileChange}
						accept='image/*'
						className='hidden'
						id='banner-upload'
					/>

					<button
						className='absolute right-0 -top-4 lg:top-0 flex-center cursor-pointer w-10 h-10 rounded-full backdrop-blur-md'
						style={{ background: 'rgba(44, 44, 44, 0.5)' }}
						disabled={profileForm.bannerUploading}
						onClick={profileForm.handleBannerClick}
					>
						{profileForm.bannerUploading ? (
							<Loader2 className='w-4 h-4 animate-spin' />
						) : (
							<ImagePlus className='w-4 h-4' />
						)}
					</button>

					<div className='relative h-28 w-28 rounded-full overflow-hidden bg-card transition-all duration-300 hover:scale-105'>
						<div className='absolute inset-0 rounded-full bg-gradient-to-br from-[#DD0003] to-[#FF8F1F] p-[4px]'>
							<div className='h-full w-full rounded-full overflow-hidden bg-card'>
								<img
									src={
										profileForm.profilePreviewUrl ||
										`https://ui-avatars.com/api/?background=000000&color=ffffff&name=${profileForm.form.watch(
											'userName',
										)}`
									}
									alt='Profile'
									className='h-full w-full object-cover'
								/>
							</div>
						</div>
					</div>

					<Input
						type='file'
						ref={profileForm.profileInputRef}
						onChange={profileForm.profileFileChange}
						accept='image/*'
						className='hidden'
						id='profile-upload'
					/>

					<Button
						onClick={profileForm.handleProfileClick}
						className='text-[11px] font-normal rounded-full z-10 border border-border bg-[#373737] text-white hover:bg-[#373737]/80'
						disabled={profileForm.profileUploading}
						variant='outline'
						size='sm'
					>
						{profileForm.profileUploading
							? 'Uploading...'
							: 'Upload Profile Photo'}
					</Button>
				</div>
			</div>

			<div className='flex flex-col items-center gap-y-3 pt-4'>
				{/* User Name */}
				<div className='text-base text-muted-foreground'>
					{profileForm.form.watch('userName')}
				</div>
				{/* Wallet Address */}
				<div className='text-xs flex items-center gap-x-2'>
					{/* Show wallet address like 3nVt...FbT8Z */}
					<Wallet className='w-4 h-4' />
					{truncateAddress(profileForm?.publicKey ?? '', 4, 5)}
				</div>
			</div>

			<div className='space-y-8 mt-7 mx-auto max-w-4xl'>
				<div>
					<Form {...form}>
						<form onSubmit={profileForm.onsSubmit} className='space-y-6'>
							<h2 className='text-lg font-medium text-foreground'>
								Basic Information
							</h2>
							<div className='border-line-gradient w-full h-1 mt-3 mb-8' />
							<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								<FormField
									control={form.control}
									name='imageUrl'
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input {...field} type='hidden' />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name='bannerUrl'
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input {...field} type='hidden' />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name='userName'
									render={({ field }) => (
										<FormItem>
											<FormLabel>User Name</FormLabel>
											<FormControl>
												<Input disabled={profileForm.isUpdating} {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='email'
									render={({ field }) => (
										<FormItem>
											<FormLabel>E-Mail</FormLabel>
											<FormControl>
												<Input
													disabled={profileForm.isUpdating}
													{...field}
													type='email'
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name='bio'
								render={({ field }) => (
									<FormItem>
										<FormLabel>Bio</FormLabel>
										<FormControl>
											<Textarea
												disabled={profileForm.isUpdating}
												{...field}
												className='min-h-[100px]'
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<h2 className='text-lg font-medium text-foreground mt-12'>
								Social Media
							</h2>
							<div className='border-line-gradient w-full h-1 mt-3 mb-8' />
							<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								<FormField
									control={form.control}
									name='twitterId'
									render={({ field }) => (
										<FormItem>
											<FormLabel>Twitter</FormLabel>
											<FormControl>
												<div className='relative'>
													<Input {...field} disabled={profileForm.isUpdating} />
													<div className='absolute inset-y-0 right-3 flex items-center'>
														<svg
															xmlns='http://www.w3.org/2000/svg'
															width='1em'
															height='1em'
															viewBox='0 0 24 24'
															aria-label='Twitter/X logo'
															role='img'
														>
															<title>Twitter/X logo</title>
															<path
																fill='currentColor'
																d='m17.687 3.063l-4.996 5.711l-4.32-5.711H2.112l7.477 9.776l-7.086 8.099h3.034l5.469-6.25l4.78 6.25h6.102l-7.794-10.304l6.625-7.571zm-1.064 16.06L5.654 4.782h1.803l10.846 14.34z'
															/>
														</svg>

														{/* <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg> */}
													</div>
												</div>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='facebookId'
									render={({ field }) => (
										<FormItem>
											<FormLabel>Facebook</FormLabel>
											<FormControl>
												<div className='relative'>
													<Input disabled={profileForm.isUpdating} {...field} />
													<div className='absolute inset-y-0 right-3 flex items-center'>
														<Facebook className='w-4 h-4' />
														{/* <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg> */}
													</div>
												</div>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='instagramId'
									render={({ field }) => (
										<FormItem>
											<FormLabel>Instagram</FormLabel>
											<FormControl>
												<div className='relative'>
													<Input disabled={profileForm.isUpdating} {...field} />
													<div className='absolute inset-y-0 right-3 flex items-center'>
														<Instagram className='w-4 h-4' />
														{/* <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg> */}
													</div>
												</div>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='websiteId'
									render={({ field }) => (
										<FormItem>
											<FormLabel>Website</FormLabel>
											<FormControl>
												<div className='relative'>
													<Input {...field} disabled={profileForm.isUpdating} />
													<div className='absolute inset-y-0 right-3 flex items-center'>
														<Globe className='w-4 h-4' />
													</div>
												</div>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className='flex justify-center pt-4'>
								<Button
									variant='outline'
									type='submit'
									className='min-w-[180px]'
									disabled={profileForm.isUpdating}
								>
									{profileForm.isUpdating ? (
										<>
											<Loader2 className='mr-2 h-4 w-4 animate-spin' />
											Updating...
										</>
									) : (
										'Update Profile'
									)}
								</Button>
							</div>
						</form>
					</Form>
				</div>
			</div>
		</div>
	)
}
