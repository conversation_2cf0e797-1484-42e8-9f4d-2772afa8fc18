/** biome-ignore-all lint/correctness/useExhaustiveDependencies: <explanation> */
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { navigate } from 'vike/client/router'
import { useData } from 'vike-react/useData'
import { z } from 'zod'
import customToast from '@/components/CustomToast'
import { useCloudFileUpload } from '@/lib/hooks/useCloudFileUpload'
import { useBannerUrl } from '@/lib/providers/BannerProvider'
import type { PublicUser } from '@/pages/(protected)/edit-profile/+data.client'
import { useUpdateProfile } from '@/hooks/useEditProfile'

const profileFormSchema = z.object({
	userName: z.string().min(2, {
		message: 'Username must be at least 2 characters.',
	}),
	email: z.string().email({
		message: 'Please enter a valid email address.',
	}),
	bio: z.string().optional(),
	twitterId: z.string().optional(),
	instagramId: z.string().optional(),
	websiteId: z.string().optional(),
	telegramId: z.string().optional(),
	facebookId: z.string().optional(),
	imageUrl: z.string().optional(),
	bannerUrl: z.string().optional(),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

export function useProfileForm() {
	const profileUpload = useCloudFileUpload()
	const bannerUpload = useCloudFileUpload()
	const [isUpdating, setIsUpdating] = useState(false)
	const { setBannerUrl } = useBannerUrl()
	const queryClient = useQueryClient()

	const user = useData<PublicUser>()

	useEffect(() => {
		if (user?.bannerUrl) {
			setBannerUrl(user?.bannerUrl)
		}
	}, [user])

	const form = useForm<ProfileFormValues>({
		resolver: zodResolver(profileFormSchema),
		defaultValues: {
			userName: user?.userName ?? '',
			email: user?.email ?? '',
			bio: user?.bio ?? '',
			twitterId: user?.twitterId ?? '',
			websiteId: user?.websiteId ?? '',
			instagramId: user?.instagramId ?? '',
			telegramId: user?.telegramId ?? '',
			imageUrl: user?.imageUrl ?? '',
			bannerUrl: user?.bannerUrl ?? '',
			facebookId: user?.facebookId ?? '',
		},
	})

	const { dirtyFields } = form.formState

	const {
		mutate: updateProfile,
		isPending,
		isError,
		error,
	} = useUpdateProfile({
		onSuccess: (data) => {
			if (data.success) {
				//console.log('data', data)
				//toast.success('Profile updated successfully!');
				// Additional actions after successful update
				customToast.success('Profile updated successfully')
				queryClient.invalidateQueries({ queryKey: ['me_user'] })
				navigate(`/profile/${user?.publicKey}`)
				//for.reset(data)
			} else {
				//toast.error(data.message);
			}
			setIsUpdating(false)
		},
		onError: (error) => {
			setIsUpdating(false)
			customToast.error('Failed to update profile')
		},
	})

	const onSubmit = async (data: ProfileFormValues) => {
		const dirtyFieldKeys = Object.keys(dirtyFields)
		if (dirtyFieldKeys.length === 0) {
			setIsUpdating(false)
			return
		}
		setIsUpdating(true)
		const updateParams: Partial<ProfileFormValues> = {}

		try {
			for (const k in dirtyFieldKeys) {
				const key = dirtyFieldKeys[k]
				if (key in data) {
					updateParams[key as keyof ProfileFormValues] =
						data[key as keyof ProfileFormValues]
				}
			}
			updateProfile(updateParams)

			//form.reset(data.data)
			//
			//if (res.success) {
			//	form.reset(data)
			//	customToast.success('Profile updated successfully')
			//	queryClient.invalidateQueries({ queryKey: ['me_user'] })
			//	navigate(`/profile/${user?.publicKey}`)
			//}
		} catch (error) {
			customToast.error('Failed to update profile')
		} finally {
			//setIsUpdating(false)
		}
	}

	// Update imageUrl and bannerUrl when upload preview changes
	const updateImageUrlFormValues = () => {
		if (profileUpload.previewUrl) {
			form.setValue('imageUrl', profileUpload.previewUrl, { shouldDirty: true })
		}
		if (bannerUpload.previewUrl) {
			setBannerUrl(bannerUpload.previewUrl)
			form.setValue('bannerUrl', bannerUpload.previewUrl, { shouldDirty: true })
		}
	}

	// Call update function when preview URLs change
	useEffect(() => {
		updateImageUrlFormValues()
	}, [profileUpload.previewUrl, bannerUpload.previewUrl])

	return {
		form,
		handleBannerClick: bannerUpload.handleThumbnailClick,
		handleProfileClick: profileUpload.handleThumbnailClick,
		bannerUploading: bannerUpload.isUploading,
		profileUploading: profileUpload.isUploading,
		bannerFileChange: bannerUpload.handleFileChange,
		profileFileChange: profileUpload.handleFileChange,
		profileInputRef: profileUpload.fileInputRef,
		bannerInputRef: bannerUpload.fileInputRef,
		bannerPreviewUrl: bannerUpload.previewUrl || user?.bannerUrl,
		profilePreviewUrl: profileUpload.previewUrl || user?.imageUrl,
		publicKey: user?.publicKey ?? '',
		onsSubmit: form.handleSubmit(onSubmit),
		isUpdating,
	}
}
