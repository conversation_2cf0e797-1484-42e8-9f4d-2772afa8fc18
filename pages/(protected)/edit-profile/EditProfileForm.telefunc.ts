import { removeUndefined } from '@/lib/utils'
import { getUser } from '@/server/helpers/getUser'
import { prisma } from '@/server/lib/prismaClient'

export async function onEditProfile({
	userName,
	imageUrl,
	instagramId,
	twitterId,
	websiteId,
	telegramId,
	bio,
	email,
	bannerUrl,
	facebookId,
}: {
	userName?: string
	imageUrl?: string
	instagramId?: string
	twitterId?: string
	websiteId?: string
	telegramId?: string
	bio?: string
	email?: string
	bannerUrl?: string
	facebookId?: string
}) {
	const user = getUser()

	try {
		const updateParams = removeUndefined({
			...{
				userName: userName,
				username: userName?.toLowerCase(),
				imageUrl,
				instagramId,
				twitterId,
				websiteId,
				telegramId,
				bio,
				email,
				bannerUrl,
				facebookId,
			},
		})

		await prisma.user.update({
			where: { id: user.userId },
			data: updateParams,
		})

		return { success: true, message: 'User update successful' }
	} catch (e) {
		return { success: false, message: 'User update failed' }
	}
}
