import { AlertTriangle } from 'lucide-react'
import { navigate } from 'vike/client/router'
import { Button } from '@/components/ui/button'

export default function CollectionFailurePage() {
	const handleTryAgain = () => {
		navigate('/studio/collection/deploy')
	}

	const handleViewHelp = () => {
		window.open('/help/collection-deployment', '_blank')
	}

	const handleContactSupport = () => {
		window.open('mailto:<EMAIL>', '_blank')
	}

	return (
		<div className='container m-auto max-w-5xl pt-20 pb-8 flex flex-col items-center justify-center h-screen'>
			<div className='w-full max-w-md flex flex-col items-center text-center'>
				<div className='mb-6 rounded-xl overflow-hidden w-40 h-40 bg-red-100 flex items-center justify-center'>
					<AlertTriangle
						size={80}
						className='text-red-500'
						aria-label='Error alert icon'
					/>
				</div>

				<h1 className='text-4xl font-bold mb-4'>Deployment Failed</h1>

				<p className='text-muted-foreground mb-8'>
					We encountered an issue while deploying your collection to the Solana
					blockchain. This could be due to network congestion, transaction
					failures, or other blockchain-related issues.
				</p>

				<div className='flex flex-col w-full gap-3 mb-6'>
					<Button onClick={handleTryAgain}>Try Again</Button>

					<Button variant='outline' onClick={handleViewHelp}>
						View Help Center
					</Button>
				</div>

				<Button
					variant='ghost'
					className='text-muted-foreground'
					onClick={handleContactSupport}
				>
					Contact Support
				</Button>
			</div>
		</div>
	)
}
