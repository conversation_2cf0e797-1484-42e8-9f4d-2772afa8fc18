import { createCollection, ruleSet } from '@metaplex-foundation/mpl-core'
import { generateSigner, publicKey } from '@metaplex-foundation/umi'
import { base58 } from '@metaplex-foundation/umi/serializers'
import { useWallet } from '@solana/wallet-adapter-react'
import { Image } from '@unpic/react'
import { Check } from 'lucide-react'
import { ofetch } from 'ofetch'
import { useContext, useState } from 'react'
import { navigate } from 'vike/client/router'
import customToast from '@/components/CustomToast'
import { FileUpload, FormContainer, StudioHeader } from '@/components/studio'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { TagSelector } from '@/components/ui/tag-selector'
import { Textarea } from '@/components/ui/textarea'
import { useDeployCollection } from '@/hooks/useDeployCollection'
import { tags } from '@/lib/constants/tags'
import { uploadFile } from '@/lib/fileUpload'
import { tryCatch } from '@/lib/try-catch'
import { UmiContext } from '@/lib/umi'
import type { UploadData } from '@/types/UploadData'

export default function CreateCollectionPage() {
	const [logoImage, setLogoImage] = useState<File>()
	const [name, setName] = useState('')
	const [description, setDescription] = useState('')
	const [royaltyPercentage, setRoyaltyPercentage] = useState('5')
	const [xUrl, setXUrl] = useState('')
	const [websiteUrl, setWebsiteUrl] = useState('')
	const [selectedTags, setSelectedTags] = useState<string[]>([])
	const [allTags, setAllTags] = useState<string[]>(tags) // Include predefined tags and any custom ones
	const [transactionHash, setTransactionHash] = useState('')
	const [isLoading, setIsLoading] = useState(false)
	const [isUploadingBanner, setIsUploadingBanner] = useState(false)
	const [uploadedBannerUrl, setUploadedBannerUrl] = useState<string>('')
	const umi = useContext(UmiContext)
	const wallet = useWallet()
	const { mutateAsync: deployCollection } = useDeployCollection()

	const handleLogoChange = (file: File) => {
		if (file) {
			setLogoImage(file)
		}
	}
	const handleBannerChange = async (file: File) => {
		if (!file) return
		try {
			setIsUploadingBanner(true)
			const fileUploadResponse = await uploadFile(file)
			if (!fileUploadResponse.success) {
				setIsUploadingBanner(false)
				return
			}
			setUploadedBannerUrl(fileUploadResponse.url)
		} catch (e) {
			console.error('Error uploading file:', e)
		} finally {
			setIsUploadingBanner(false)
		}
	}

	const handleTagSelect = (tag: string) => {
		// Check if we're at the maximum and trying to add a new tag
		const maxTags = 5
		if (selectedTags.length >= maxTags && !selectedTags.includes(tag)) {
			customToast.error(`You can select a maximum of ${maxTags} tags`)
			return
		}

		// If this is a new custom tag, add it to allTags
		if (!allTags.includes(tag)) {
			setAllTags((prev) => [...prev, tag])
		}

		// Toggle tag selection
		setSelectedTags(
			(prev) =>
				prev.includes(tag)
					? prev.filter((t) => t !== tag) // Remove tag if already selected
					: [...prev, tag], // Add tag if not selected
		)
	}

	const handleCreateCollection = async () => {
		if (!logoImage) {
			customToast.error('Please upload a logo image')
			return
		}

		if (!name) {
			customToast.error('Please provide a collection name')
			return
		}

		if (!description || description.length < 200) {
			customToast.error(
				'Please provide minimum 200 characters description for your collection',
			)
			return
		}

		if (selectedTags.length === 0) {
			customToast.error('Please select at least one tag for your collection')
			return
		}

		if (!wallet.publicKey || !wallet.wallet) {
			customToast.error('Please connect your wallet first')
			return
		}

		setIsLoading(true)

		try {
			// Create a FormData object similar to NFTMinting.tsx approach
			const formData = new FormData()

			// Add all form fields to the formData
			formData.append('name', name)
			formData.append('description', description)
			formData.append('royaltyPercentage', royaltyPercentage)
			formData.append('tags', JSON.stringify(selectedTags))

			if (logoImage) {
				formData.append('logo', logoImage)
			}
			// Send the request to get metadata URL
			const backendResponse = await ofetch<UploadData>(
				`${import.meta.env.PUBLIC_ENV__NFT_UPLOAD_URL}/upload/collection`,
				{
					method: 'POST',
					body: formData,
				},
			)

			if (!backendResponse || !backendResponse.success) {
				throw new Error('Failed to process collection data on backend')
			}
			if (!umi || !wallet.publicKey || !wallet.signTransaction) {
				throw new Error(
					'Wallet not connected or fee collector address not set.',
				)
			}
			// 2. Create a signer for the asset
			const collectionSigner = generateSigner(umi)

			const { signature } = await createCollection(umi, {
				collection: collectionSigner,
				name,
				uri: backendResponse.data.metadataUri,
				plugins: [
					{
						type: 'Royalties',
						basisPoints: Number.parseInt(royaltyPercentage) * 100,
						creators: [
							{
								address: publicKey(wallet.publicKey),
								percentage: 100,
							},
						],
						ruleSet: ruleSet('None'),
					},
				],
			}).sendAndConfirm(umi)

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)
			// Now call the REST API endpoint to deploy the collection
			const { data: deployResponse, error: deployError } = await tryCatch(
				deployCollection({
					name,
					description,
					royaltyBasisPoints: Number.parseInt(royaltyPercentage) * 100, // Convert percentage to basis points
					logoUrl: backendResponse.data.fileUrl,
					bannerUrl: uploadedBannerUrl,
					metadataUri: backendResponse.data.metadataUri, // Pass the metadata URI from backend
					collectionAddress: collectionSigner.publicKey.toString(),
					transactionHash: serializedSignature,
					xUrl,
					websiteUrl,
					tags: selectedTags,
				}),
			)

			if (deployError) {
				customToast.error('Failed to deploy collection')
				console.error('Collection deployment error:', deployError)
				throw new Error(deployError.message || 'Failed to deploy collection')
			}

			if (deployResponse?.success) {
				customToast.success('Collection created successfully!')
				navigate('/studio/collection/success', {
					pageContext: {
						mint: {
							collection: {
								address: collectionSigner.publicKey.toString(),
								transactionHash: serializedSignature,
							},
						},
					},
				})
			} else {
				throw new Error(
					deployResponse?.message || 'Failed to create collection',
				)
			}
		} catch (error) {
			customToast.error('Failed to create collection')
			console.error('Collection creation error:', error)
			navigate('/studio/collection/failure', {
				pageContext: {
					error: {
						message:
							error instanceof Error ? error.message : 'Unknown error occurred',
						transactionHash: transactionHash || '',
					},
				},
			})
		} finally {
			setIsLoading(false)
		}
	}

	return (
		<FormContainer>
			<StudioHeader
				title='Create Collection'
				description="You'll need to deploy an MPL Core contract on the blockchain to create a collection for your NFT."
			/>

			<div className='space-y-8'>
				{/* Logo Image */}
				<FileUpload
					id='logo-image'
					label='Logo Image'
					onChange={handleLogoChange}
					height='h-56'
					objectFit='contain'
					recommendedSize='400 x 400px'
					maxSizeMB={5}
					disabled={isLoading}
				/>

				{/* Banner Image */}
				<FileUpload
					id='collection-banner-image'
					label='Banner Image'
					uploadFileToCloud={handleBannerChange}
					height='h-56'
					objectFit='contain'
					recommendedSize='400 x 400px'
					maxSizeMB={5}
					disabled={isUploadingBanner}
				/>

				<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
					{/* Name */}
					<div className='space-y-2'>
						<Label htmlFor='name'>Collection Name</Label>
						<Input
							id='name'
							placeholder='My Awesome Collection'
							value={name}
							onChange={(e) => setName(e.target.value)}
							required
							disabled={isLoading}
						/>
						<p className='text-xs text-muted-foreground'>
							The name of your collection
						</p>
					</div>
				</div>

				{/* Description */}
				<div className='space-y-2'>
					<Label htmlFor='description'>Description</Label>
					<Textarea
						id='description'
						placeholder='Describe your collection...'
						value={description}
						onChange={(e) => setDescription(e.target.value)}
						rows={3}
						disabled={isLoading}
					/>
					<p className='text-xs text-muted-foreground'>
						The description of your collection
					</p>
				</div>

				{/* Tags Section */}
				<div className='space-y-4'>
					<div className='flex flex-col space-y-2'>
						{' '}
						<Label htmlFor='tags'>Collection Tags</Label>
						<TagSelector
							tags={allTags}
							selectedTags={selectedTags}
							onTagSelect={handleTagSelect}
							disabled={isLoading}
							aria-label='Select tags for your collection'
							className='mt-2'
							maxTagsSelectable={5}
						/>
						<p className='text-xs text-muted-foreground'>
							Select up to 5 tags for your collection. You can choose from
							existing tags or create your own custom tags.
						</p>
					</div>
				</div>

				{/* Social Links Section - NEW */}
				<div className='space-y-4 py-4'>
					<h3 className='text-lg font-medium'>Social Links</h3>
					{/* X URL Input */}
					<div className='space-y-2'>
						<Label htmlFor='x-url'>X URL (Optional)</Label>
						<Input
							id='x-url'
							type='url'
							placeholder='https://x.com/yourprofile'
							value={xUrl}
							onChange={(e) => setXUrl(e.target.value)}
							disabled={isLoading}
						/>
						<p className='text-xs text-muted-foreground'>
							Link to your X (formerly Twitter) profile.
						</p>
					</div>

					{/* Website URL Input */}
					<div className='space-y-2'>
						<Label htmlFor='website-url'>Website URL (Optional)</Label>
						<Input
							id='website-url'
							type='url'
							placeholder='https://yourwebsite.com'
							value={websiteUrl}
							onChange={(e) => setWebsiteUrl(e.target.value)}
							disabled={isLoading}
						/>
						<p className='text-xs text-muted-foreground'>
							Link to your personal or project website.
						</p>
					</div>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
					{/* Chain */}
					<div className='space-y-2'>
						<Label htmlFor='creator-address'>Chain</Label>
						<div className='flex justify-between items-center bg-input border border-border rounded-lg px-4 py-2'>
							<div className='flex justify-start items-center font-medium text-base'>
								<Image
									src='/assets/svg/solana.svg'
									alt='solana'
									layout='fullWidth'
									className='size-8 mr-4'
								/>
								Solana
							</div>
							<div className='flex justify-end items-center gap-2 py-1 px-3 bg-white/20 rounded-full font-medium text-xs'>
								<Check className='h-3 w-3' />
								Selected
							</div>
						</div>
						<p className='text-xs text-muted-foreground'>
							The blockchain on which the collection will be deployed
						</p>
					</div>
					{/* Royalty Percentage */}
					<div className='space-y-2'>
						<Label htmlFor='royalty'>Default Royalty Percentage</Label>
						<div className='flex items-center'>
							<Input
								id='royalty'
								type='number'
								min='0'
								max='50'
								value={royaltyPercentage}
								onChange={(e) => setRoyaltyPercentage(e.target.value)}
								className='max-w-[100px]'
								disabled={isLoading}
							/>
							<span className='ml-2 text-muted-foreground'>%</span>
						</div>
						<p className='text-xs text-muted-foreground'>
							Default royalty that applies to NFTs in this collection
						</p>
					</div>
				</div>

				<div className='flex justify-center pt-4'>
					<Button
						onClick={handleCreateCollection}
						disabled={isLoading || !logoImage || !name}
						className='px-8 py-6 w-full max-w-xs'
					>
						{isLoading ? 'Creating Collection...' : 'Create Collection'}
					</Button>
				</div>
			</div>
		</FormContainer>
	)
}
