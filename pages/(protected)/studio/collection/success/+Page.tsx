import { CircleCheck, ExternalLink } from 'lucide-react'
import { navigate } from 'vike/client/router'
import { usePageContext } from 'vike-react/usePageContext'
import { Button } from '@/components/ui/button'
import { handleViewOnSolscan } from '@/lib/utils'

export default function CollectionSuccessPage() {
	const { mint } = usePageContext()
	const handleViewCollection = () => {
		navigate(`/collection/${mint.collection.address}`)
	}

	const handleCreateNFT = () => {
		navigate('/studio/nft/mint', {
			pageContext: {
				collection: {
					address: mint.collection.address,
				},
			},
		})
	}

	return (
		<div className='container m-auto max-w-5xl pt-20 pb-8 flex flex-col items-center justify-center h-screen'>
			<div className='w-full max-w-md flex flex-col items-center text-center'>
				<div className='mb-6 rounded-xl overflow-hidden w-40 h-40 bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center'>
					<CircleCheck
						size={80}
						className='text-white'
						aria-label='Error alert icon'
					/>
				</div>

				<h1 className='text-4xl font-bold mb-4'>Collection Deployed!</h1>

				<p className='text-muted-foreground mb-6'>
					Your collection has been successfully deployed on the Solana
					blockchain
				</p>

				<div className='flex flex-col sm:flex-row gap-4 w-full mb-6'>
					<Button
						variant='outline'
						className='flex-1'
						onClick={handleViewCollection}
					>
						View Collection
					</Button>

					<Button className='flex-1' onClick={handleCreateNFT}>
						Create an NFT
					</Button>
				</div>

				<Button
					variant='ghost'
					className='text-muted-foreground flex items-center gap-2'
					onClick={() => handleViewOnSolscan(mint.collection.transactionHash)}
				>
					View on Solscan <ExternalLink size={16} />
				</Button>
			</div>
		</div>
	)
}
