import { Circle<PERSON>he<PERSON>, ExternalLink, Layers } from 'lucide-react'
import { navigate } from 'vike/client/router'
import { usePageContext } from 'vike-react/usePageContext'
import ShareButton from '@/components/buttons/ShareButton'
import { Button } from '@/components/ui/button'
import { handleViewOnSolscan } from '@/lib/utils'
import { useEffect } from 'react'

export default function NFTSuccessPage() {
	const { mint, collection } = usePageContext() || {}
	const { bulkUpload } = mint || {}

	// Redirect to mint page if no mint data is available
	useEffect(() => {
		if (!mint?.nft?.address) {
			navigate('/studio/nft/mint')
		}
	}, [mint])

	// If no mint data, show loading or redirect
	if (!mint?.nft?.address) {
		return null
	}

	const handleViewNFT = () => {
		// Navigate to NFT detail page
		navigate(`/nft-details/${mint?.nft?.address}`)
	}

	const handleCreateAnotherNFT = () => {
		navigate('/studio/nft/mint', {
			pageContext: {
				collection: collection?.address
					? {
							address: collection.address,
						}
					: undefined,
			},
		})
	}

	const handleViewCollection = () => {
		// Navigate to collection page if collection address exists
		if (collection?.address) {
			navigate(`/collection/${collection.address}`)
		}
	}

	// URL for the NFT detail page
	const nftDetailUrl =
		typeof window !== 'undefined'
			? `${window.location.origin}/nft-details/${mint?.nft?.address}`
			: `/nft-details/${mint?.nft?.address}`

	return (
		<div className='container m-auto max-w-5xl pt-20 pb-8 flex flex-col items-center justify-center h-screen'>
			<div className='w-full max-w-md flex flex-col items-center text-center'>
				<div className='mb-6 rounded-xl overflow-hidden w-40 h-40 bg-gradient-to-br from-emerald-500 to-blue-600 flex items-center justify-center'>
					{/* Display NFT image here */}
					<CircleCheck
						size={80}
						className='text-white'
						aria-label='Error alert icon'
					/>
				</div>

				<h1 className='text-4xl font-bold mb-4'>
					{bulkUpload
						? 'NFTs Successfully Created!'
						: 'NFT Successfully Created!'}
				</h1>

				<p className='text-muted-foreground mb-6'>
					{bulkUpload
						? 'Your NFTs have been minted and are now available on the blockchain'
						: 'Your NFT has been minted and is now available on the blockchain'}
				</p>

				<div className='flex flex-col sm:flex-row gap-4 w-full mb-6'>
					{!bulkUpload && (
						<Button
							variant='outline'
							className='flex-1'
							onClick={handleViewNFT}
						>
							View NFT
						</Button>
					)}
					{bulkUpload && collection?.address && (
						<Button
							variant='outline'
							className='flex-1'
							onClick={handleViewCollection}
						>
							View Collection
						</Button>
					)}

					<Button className='flex-1' onClick={handleCreateAnotherNFT}>
						Create Another NFT
					</Button>
				</div>

				<div className='flex flex-col sm:flex-row gap-2 w-full'>
					{!bulkUpload && collection?.address ? (
						<Button
							variant='ghost'
							className='text-muted-foreground flex items-center justify-center gap-2 flex-1'
							onClick={handleViewCollection}
						>
							View Collection <Layers size={16} />
						</Button>
					) : null}

					<ShareButton
						variant='ghost'
						className='text-muted-foreground flex items-center justify-center gap-2 flex-1'
						title={
							bulkUpload
								? 'NFTs Successfully Created!'
								: 'NFT Successfully Created!'
						}
						url={
							bulkUpload && collection?.address
								? `/collection/${collection.address}`
								: nftDetailUrl
						}
						description={
							bulkUpload
								? 'Check out my new NFT collection on Shogunex Fusion!'
								: 'Check out my new NFT on Shogunex Fusion!'
						}
					/>
				</div>
			</div>
		</div>
	)
}
