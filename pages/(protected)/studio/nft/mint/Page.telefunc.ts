import { getUser } from '@/server/helpers/getUser'
import { prisma } from '@/server/lib/prismaClient'

// MIGRATED FUNCTIONS:
// - onMintNft → POST /api/nft/mint (see hooks/useMintNft.ts)
// - onGetOneTimeAccessTokenForMinting → POST /api/nft/mint/token (see hooks/useMintToken.ts)

export async function onGetUserCollections() {
	const user = getUser()
	const collections = await prisma.collection.findMany({
		select: {
			id: true,
			publicKey: true,
			name: true,
			logoUrl: true,
			royaltyBasisPoints: true,
		},
		where: {
			ownerId: user.userId,
		},
	})
	return collections
}
