import type { Prisma } from 'prisma-client/edge'
import { tryCatch } from '@/lib/try-catch'
import { prisma } from '@/server/lib/prismaClient'
import { onGetUserCollections } from './Page.telefunc'

export type Collection = {
	id: string
	publicKey: string
	name: string
	royaltyBasisPoints: number
	logoUrl: string
}

export type CollectionList = Prisma.CollectionGetPayload<{
	select: {
		id: true
		publicKey: true
		name: true
		logoUrl: true
		royaltyBasisPoints: true
	}
}>

export default async function data(): Promise<CollectionList[]> {
	const { data: collections, error: collectionError } = await tryCatch(
		onGetUserCollections(),
	)
	if (collectionError) {
		return []
	}
	return collections
}
