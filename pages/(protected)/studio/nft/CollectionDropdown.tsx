'use client'

import { Label } from '@radix-ui/react-label'
import {
	GalleryVerticalEnd,
	Grid2X2Plus,
	Plus,
	PlusCircle,
	X,
} from 'lucide-react'
import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'
import { Button } from '@/components/ui/button'
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select'
import type { Collection } from './mint/+data.client'

export default function CollectionDropdown({
	collections,
	existingCollectionAddress,
	onCollectionSelect,
	disabled = false,
}: {
	collections: Collection[]
	existingCollectionAddress: string | null
	onCollectionSelect?: (collectionAddress: string | null) => void
	disabled?: boolean
}) {
	const [selectedCollection, setSelectedCollection] = useState<string>('')
	const selectId = 'collection-select'

	// const handleCreateCollection = () => {
	// 	navigate('/studio/collection/deploy')
	// }

	const handleCollectionSelect = (value: string) => {
		setSelectedCollection(value)
		// Call the onCollectionSelect prop function if provided
		if (onCollectionSelect) {
			onCollectionSelect(value || null)
		}
	}

	// Initialize selected collection if existingCollectionAddress is provided
	useEffect(() => {
		if (existingCollectionAddress) {
			setSelectedCollection(existingCollectionAddress)
			// Call onCollectionSelect with the existing collection address
			if (onCollectionSelect) {
				onCollectionSelect(existingCollectionAddress)
			}
		}
	}, [existingCollectionAddress, onCollectionSelect])

	return (
		<div className='space-y-2'>
			<div className='flex items-center justify-between'>
				<Label htmlFor={selectId} className='text-sm font-medium'>
					Collection
				</Label>
				<button
					className='text-primary flex items-center gap-1 text-sm'
					onClick={() => navigate('/studio/collection/deploy')}
					disabled={disabled}
				>
					<PlusCircle className='size-4' />
					Create new collection
				</button>
			</div>

			<Select
				value={selectedCollection}
				onValueChange={handleCollectionSelect}
				disabled={disabled}
			>
				<SelectTrigger
					className='w-full text-foreground !h-16 px-5'
					disabled={disabled}
				>
					<div className='flex items-center gap-4 text-foreground'>
						<GalleryVerticalEnd className='size-4 text-foreground' />
						<SelectValue placeholder='Choose a collection' />
					</div>
				</SelectTrigger>
				<SelectContent>
					<SelectGroup>
						{/* Create New Option */}
						<button
							type='button'
							disabled={disabled}
							onClick={() => {
								navigate('/studio/collection/deploy')
							}}
							className='flex w-full items-center gap-2 py-3 cursor-pointer  hover:bg-white/5'
						>
							<div className='flex items-center gap-3'>
								<div className='flex items-center justify-center size-8 bg-white/10 rounded-md'>
									<Grid2X2Plus className='size-5' />
								</div>
								<span>Create New a collection</span>
							</div>
						</button>

						{/* Collection Options */}
						{collections?.map((collection) => (
							<SelectItem
								key={collection.publicKey}
								value={collection.publicKey}
								className='flex items-center gap-2 py-4 cursor-pointer hover:bg-gray-800'
								disabled={disabled}
							>
								<div className='flex items-center gap-3'>
									{collection.logoUrl && (
										<img
											src={collection.logoUrl}
											alt={collection.name}
											className='size-10 rounded-md object-cover'
										/>
									)}
									<div>
										<div className='font-medium text-white'>
											{collection.name}
										</div>
										{collection.symbol && (
											<div className='text-xs text-gray-400'>
												{collection.symbol}
											</div>
										)}
									</div>
								</div>
							</SelectItem>
						))}
					</SelectGroup>
				</SelectContent>
			</Select>

			{selectedCollection && selectedCollection !== 'create-new' && (
				<div className='flex items-center gap-2 p-3 rounded-lg border border-primary bg-input relative'>
					{collections.find((c) => c.publicKey === selectedCollection)
						?.logoUrl && (
						<img
							src={
								collections.find((c) => c.publicKey === selectedCollection)
									?.logoUrl
							}
							alt={
								collections.find((c) => c.publicKey === selectedCollection)
									?.name
							}
							className='size-10 rounded-md object-cover'
						/>
					)}
					<div>
						<p className='font-medium text-white'>
							{
								collections.find((c) => c.publicKey === selectedCollection)
									?.name
							}
						</p>
						<p className='text-xs text-gray-400'>
							{collections.find((c) => c.publicKey === selectedCollection)
								?.symbol || 'Selected collection'}
						</p>
					</div>

					{/* Close button */}
					<Button
						type='button'
						size='icon'
						variant='ghost'
						disabled={disabled}
						onClick={() => {
							setSelectedCollection('')
							if (onCollectionSelect) {
								onCollectionSelect(null)
							}
						}}
						className='absolute right-3 top-1/2 -translate-y-1/2 hover:text-destructive cursor-pointer'
					>
						<X className='size-4 m-auto' />
					</Button>
				</div>
			)}
		</div>
	)
}
