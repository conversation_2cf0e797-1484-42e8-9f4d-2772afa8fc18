'use client'

import { Image } from '@unpic/react'
import { ArrowLeft, GalleryVerticalEnd, ImageIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { navigate } from 'vike/client/router'
import { Link } from '@/components/atoms/Link'
import { ActionButton, FormContainer } from '@/components/studio'
import { cn } from '@/lib/utils'
import BrandLogo from '@/public/assets/Logo/logo-white.svg?react'

export default function CreateNFTPage() {
	const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
	const [isModalOpen, setIsModalOpen] = useState(false)

	// Check authentication status on mount
	useEffect(() => {
		const accessToken = localStorage.getItem('accessToken')
		setIsAuthenticated(!!accessToken)
		if (!accessToken) {
			setIsModalOpen(true) // Open modal if not authenticated
		}
	}, [])

	// Handle create option selection
	const chooseCreateOption = (option: 'nft' | 'collection') => {
		navigate(
			option === 'collection'
				? '/studio/collection/deploy'
				: '/studio/nft/mint',
		)
	}

	// If authentication status is still loading, show a loading state
	if (isAuthenticated === null) {
		return <div>Loading...</div>
	}

	return (
		<div className='relative'>
			{/* Main page content - always rendered, dimmed when modal is open */}
			<div
				className={cn(
					'w-full min-h-screen transition-all duration-300 grid grid-cols-1 md:grid-cols-2 overflow-hidden',
					isModalOpen && !isAuthenticated && 'opacity-80', // Clearer background
				)}
			>
				<FormContainer className='flex flex-col justify-start md:justify-center items-start p-0 pt-32 md:pt-0 md:h-screen relative'>
					{/* Back button with animation */}
					<Link
						href='/'
						className='inline-flex items-center gap-2 mb-6 md:mb-8 hover:font-bold transition-all duration-200 hover:translate-x-[-4px]'
					>
						<ArrowLeft className='h-5 w-5' />
						<span>Back</span>
					</Link>

					{/* Create header */}
					<div className='flex items-start gap-4 mb-6 w-full'>
						<div className='h-10 w-10 rounded-full bg-card flex items-center justify-center mt-2 flex-shrink-0'>
							<BrandLogo className='w-6 m-auto h-auto text-white' />
						</div>
						<div>
							<h1 className='text-2xl md:text-3xl font-semibold tracking-tight'>
								Create
							</h1>
							<p className='text-sm text-muted-foreground mt-1'>
								Fill in the details to mint your unique NFT to the blockchain
							</p>
						</div>
					</div>

					{/* Create options with hover and focus animations */}
					<div className='grid gap-4 mt-6 md:mt-8 w-full'>
						<ActionButton
							icon={<GalleryVerticalEnd className='h-5 w-5 md:h-6 md:w-6' />}
							title='Create a collection'
							description='Launch your collection of NFTs to mint.'
							onClick={() => chooseCreateOption('collection')}
							disabled={!isAuthenticated}
						/>
						<ActionButton
							icon={<ImageIcon className='h-5 w-5 md:h-6 md:w-6' />}
							title='Create NFT'
							description='Create and mint an NFT directly to your wallet'
							onClick={() => chooseCreateOption('nft')}
							disabled={!isAuthenticated}
						/>
					</div>
				</FormContainer>

				{/* Image section - hidden on mobile */}
				<div
					className={cn(
						'transition-opacity duration-500',
						'hidden md:block h-full',
					)}
				>
					<Image
						src='/assets/img/create-nft.png'
						className='h-screen w-full object-cover'
						width={800}
						height={1000}
						layout='constrained'
					/>
				</div>
			</div>
		</div>
	)
}
