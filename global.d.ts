declare global {
	namespace Vike {
		interface PageContext {
			mint: {
				collection: {
					address: string
					transactionHash: string
				}
				nft: {
					address: string
					transactionHash: string
				}
				bulkUpload: boolean
			}
			collection: {
				address: string
			}
			nft: {
				address: string
			}
			error: {
				error: unknown
				transactionHash?: string
			}
			// Refine type of pageContext.Page (it's `unknown` by default)
			Page: () => React.JSX.Element
		}
	}
}

// If you define Vike.PageContext in a .d.ts file then
// make sure there is at least one export/import statement.
// Tell TypeScript this file isn't an ambient module:
export {}
