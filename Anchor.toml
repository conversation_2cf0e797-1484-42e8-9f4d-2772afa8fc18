[toolchain]
anchor_version = "0.30.1"

[features]
resolution = true
skip-lint = false

[programs.localnet]
fusion_core = "AngSgV33DJcQCVjHkpLvjqz5vjtAbippaRNfcG8KRUKF"
fusion_marketplace = "Fg6PaFpoGXkYsidMpWTK6W2BeZ7FEfcYkg476zPFsLnS"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "Localnet"
wallet = "~/.config/solana/id.json"

[[test.genesis]]
address = "CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d"
program = "./tests/programs/mpl_core.so"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/shogunex-fusion.ts"
