//! TestBed for Solana program testing using LiteSVM
//!
//! This crate provides a flexible, extensible testing framework for Solana programs,
//! with a focus on NFT operations and the Shogun Fusion ecosystem.

use std::collections::HashMap;
use std::sync::{Arc, Mutex};

#[cfg(feature = "account-deserialize")]
use anchor_lang::AccountDeserialize;

pub use litesvm::{types::TransactionResult, LiteSVM};
use solana_program::{instruction::Instruction, message::Message};
use solana_program_runtime::invoke_context::BuiltinFunctionWithContext;
use solana_sdk::account::{Account, WritableAccount};
use solana_sdk::native_loader;
use solana_sdk::{
    clock::Clock,
    pubkey::Pubkey,
    rent::Rent,
    signature::{Keypair, Signature},
    signer::Signer,
    transaction::Transaction,
};
use std::borrow::BorrowMut;

mod errors;
pub use errors::*;

pub mod types;
pub use types::*;

// pub mod macros;
// pub use macros::*;

/// Core TestBed structure for Solana program testing
pub struct TestBed {
    /// LiteSVM instance for simulating the Solana runtime
    pub svm: Arc<Mutex<LiteSVM>>,
    /// Payer account for transactions
    pub payer: Keypair,
    /// Mapping of deployed programs by name
    programs: HashMap<String, Box<dyn TestProgram>>,
    /// Transaction logs and events
    logs: Vec<(Signature, Vec<String>)>,
}

impl TestBed {
    pub async fn new() -> Result<Self, TestBedError> {
        let svm = Arc::new(Mutex::new(LiteSVM::new()));
        let payer = Keypair::new();

        svm.lock()?.airdrop(&payer.pubkey(), 1_000_000_000)?;

        Ok(Self {
            svm,
            payer,
            programs: Default::default(),
            logs: Default::default(),
        })
    }

    /// Register a program handler that can process instructions
    pub async fn register_program_handler(
        &mut self,
        program_id: Pubkey,
    ) -> Result<(), TestBedError> {
        self.svm
            .lock()?
            .add_builtin(program_id, |_, _, _, _, _, _| ());

        Ok(())
    }

    pub async fn register_program<P: TestProgram + 'static>(
        &mut self,
        name: &str,
        program: P,
        path: &str,
    ) -> Result<(), TestBedError> {
        self.svm
            .lock()?
            .add_program_from_file(program.program_id(), path)
            .map_err(|e| {
                TestBedError::ProgramError(format!("Failed to add program {}: {:?}", path, e))
            })?;

        self.programs.insert(name.to_string(), Box::new(program));

        Ok(())
    }

    /// Execute a transaction
    pub async fn execute_transaction(
        &mut self,
        instructions: &[Instruction],
        signers: &[&Keypair],
    ) -> Result<Signature, TestBedError> {
        let message = Message::new(instructions, Some(&self.payer.pubkey()));
        let mut tx = Transaction::new_unsigned(message);
        let mut signers = signers;

        tx.partial_sign(signers, self.svm.lock()?.latest_blockhash());
        tx.partial_sign(&[&self.payer], self.svm.lock()?.latest_blockhash());

        // Process transaction with LiteSVM
        let result = self.svm.lock()?.send_transaction(tx).map_err(|e| {
            println!("{:?}", e);

            e
        })?;

        let signature = result.clone().signature;

        // Store logs for later inspection
        self.store_logs(signature, result.clone().logs);

        Ok(signature)
    }

    pub fn create_funded_user(&mut self, sol_amount: u64) -> Result<Keypair, TestBedError> {
        let user = Keypair::new();

        self.svm.lock()?.airdrop(&user.pubkey(), sol_amount)?;

        Ok(user)
    }

    /// Store transaction logs
    fn store_logs(&mut self, signature: Signature, logs: Vec<String>) {
        self.logs.push((signature, logs));
    }

    /// Get a registered program
    pub fn get_program<T: 'static>(&self, name: &str) -> Option<&T> {
        self.programs
            .get(name)
            .and_then(|program| program.as_any().downcast_ref::<T>())
    }

    /// Get transaction logs
    pub fn get_transaction_logs(&self, signature: &Signature) -> Option<&Vec<String>> {
        self.logs
            .iter()
            .find(|(sig, _)| sig == signature)
            .map(|(_, logs)| logs)
    }

    /// creating the account
    pub fn create_account_with_data(
        &mut self,
        pubkey: Pubkey,
        owner: &Pubkey,
        data: &[u8],
        executable: bool,
    ) -> Result<(), TestBedError> {
        let mut svm = self.svm.lock()?;

        let lamports = svm.minimum_balance_for_rent_exemption(data.len());

        let account = solana_sdk::account::Account {
            lamports,
            data: data.to_vec(),
            owner: *owner,
            executable,
            rent_epoch: 0,
        };

        svm.set_account(pubkey, account).unwrap();

        Ok(())
    }

    /// Get account data
    pub fn get_account(&self, pubkey: &Pubkey) -> Option<solana_sdk::account::Account> {
        self.svm.lock().ok()?.get_account(pubkey)
    }

    // Get account data
    #[cfg(feature = "account-deserialize")]
    pub async fn get_account_data<T: AccountDeserialize>(
        &self,
        address: &Pubkey,
    ) -> Result<T, TestBedError> {
        let mut account = self
            .svm
            .lock()?
            .get_account(address)
            .ok_or_else(|| TestBedError::AccountNotFound(*address))?;

        T::try_deserialize(&mut account.data_as_mut_slice().as_ref()).map_err(|e| {
            TestBedError::DeserializationError(format!("Failed to deserialize account data: {}", e))
        })
    }

    pub fn get_clock(&self) -> Result<Clock, TestBedError> {
        let mut svm = self.svm.lock()?;

        Ok(svm.get_sysvar::<Clock>())
    }

    pub fn get_rent(&self) -> Result<Rent, TestBedError> {
        let mut svm = self.svm.lock()?;

        Ok(svm.get_sysvar::<Rent>())
    }

    pub fn set_clock(&mut self, clock: Clock) -> Result<(), TestBedError> {
        let mut svm = self.svm.lock()?;

        svm.set_sysvar::<Clock>(&clock);

        Ok(())
    }

    /// Advance the clock by a specified number of seconds
    pub fn advance_clock_by_seconds(&mut self, seconds: i64) -> Result<(), TestBedError> {
        // Get current clock
        let current_clock = self.get_clock()?;

        // Set new clock with advanced time
        self.set_clock(Clock {
            slot: current_clock.slot + (seconds as u64) * 2,
            epoch: current_clock.epoch,
            unix_timestamp: current_clock.unix_timestamp + seconds,
            leader_schedule_epoch: current_clock.leader_schedule_epoch,
            epoch_start_timestamp: current_clock.epoch_start_timestamp,
        });

        Ok(())
    }
}
