//! Test macros for Solana program testing
//! 
//! This module provides macros for simplifying test setup and execution.

/// Main test macro for setting up a TestBed and running a test
/// 
/// # Example
/// 
/// ```
/// test!("Test name", |testbed| {
///     // Test code here
/// });
/// ```
#[macro_export]
macro_rules! test {
    ($name:expr, |$testbed:ident| $body:block) => {
        #[tokio::test]
        async fn $name() -> anyhow::Result<()> {
            // Create TestBed
            let mut $testbed = $crate::TestBed::new().await;
            
            // Run test body
            $body
        }
    };
}

/// Macro for creating an NFT
/// 
/// # Example
/// 
/// ```
/// let nft = create_nft!(testbed,
///     owner: user.pubkey(),
///     name: "My NFT",
///     symbol: "NFT",
///     uri: "https://example.com/nft.json",
///     attributes: [
///         ("color", "red"),
///         ("power", "10")
///     ]
/// )?;
/// ```
#[macro_export]
macro_rules! create_nft {
    ($testbed:expr, owner: $owner:expr, name: $name:expr, symbol: $symbol:expr, uri: $uri:expr $(, attributes: [$( ($attr_name:expr, $attr_value:expr) ),*])?) => {{
        // This is a placeholder - in a real implementation, this would create an NFT
        // using MPL Core and return an NftInfo struct
        
        // For now, we'll just create a dummy NftInfo
        let mint = solana_sdk::signer::keypair::Keypair::new();
        let metadata = solana_program::pubkey::Pubkey::find_program_address(
            &[
                b"metadata",
                mpl_core::ID.as_ref(),
                mint.pubkey().as_ref(),
            ],
            &mpl_core::ID,
        ).0;
        
        let token_account = solana_program::pubkey::Pubkey::find_program_address(
            &[
                $owner.as_ref(),
                spl_token::ID.as_ref(),
                mint.pubkey().as_ref(),
            ],
            &spl_token::ID,
        ).0;
        
        // In a real implementation, we would create these accounts and initialize them
        
        Ok($crate::NftInfo {
            mint: mint.pubkey(),
            metadata,
            master_edition: None,
            token_account,
            owner: $owner,
        })
    }};
}

/// Macro for asserting that an event was emitted
/// 
/// # Example
/// 
/// ```
/// assert_event_emitted!(testbed, signature, "FusionCreated", |event: FusionCreatedEvent| {
///     assert_eq!(event.fusion_account, fusion_account);
///     assert_eq!(event.owner, user.pubkey());
/// });
/// ```
#[macro_export]
macro_rules! assert_event_emitted {
    ($testbed:expr, $signature:expr, $event_name:expr, |$event:ident: $type:ty| $body:block) => {{
        let events = $testbed.parse_events::<$type>(&$signature, $event_name);
        assert!(!events.is_empty(), "No {} events found", $event_name);
        
        for event_result in events {
            let $event = event_result?;
            $body
        }
        
        Ok(())
    }};
}

/// Macro for asserting metadata attributes
/// 
/// # Example
/// 
/// ```
/// assert_metadata!(metadata, |metadata| {
///     assert!(metadata.name.contains("Base NFT + Feature NFT"));
///     assert_eq!(metadata.symbol, "FUSED");
///     assert!(metadata.uri.contains("fusion"));
/// });
/// ```
#[macro_export]
macro_rules! assert_metadata {
    ($metadata:expr, |$meta:ident| $body:block) => {{
        let $meta = $metadata;
        $body
        Ok(())
    }};
}

/// Macro for asserting a specific metadata attribute
/// 
/// # Example
/// 
/// ```
/// assert_attribute!(metadata, "color", "red");
/// ```
#[macro_export]
macro_rules! assert_attribute {
    ($metadata:expr, $name:expr, $value:expr) => {{
        let attributes = &$metadata.attributes;
        let attribute = attributes.iter()
            .find(|attr| attr.trait_type == $name)
            .ok_or_else(|| anyhow::anyhow!("Attribute not found: {}", $name))?;
        
        assert_eq!(attribute.value, $value, "Attribute {} has wrong value", $name);
        
        Ok(())
    }};
}
