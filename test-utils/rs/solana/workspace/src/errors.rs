use litesvm::types::FailedTransactionMetadata;
use solana_program::pubkey::Pubkey;
use std::{
    error::Error,
    sync::{MutexGuard, PoisonError},
};

/// Error types for TestBed operations
#[derive(thiserror::Error, Debug)]
pub enum TestBedError {
    #[error("Program not found: {0}")]
    ProgramNotFound(String),

    #[error("Program error: {0}")]
    ProgramError(String),

    #[error("Account not found: {0}")]
    AccountNotFound(Pubkey),

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("Deserialization error: {0}")]
    DeserializationError(String),

    #[error("Transaction error: {0}")]
    TransactionError(solana_sdk::transaction::TransactionError),

    #[error("Test error: {0}")]
    TestError(String),

    #[error("Assertion failed: {0}")]
    AssertionFailed(String),
}

impl From<FailedTransactionMetadata> for TestBedError {
    fn from(error: FailedTransactionMetadata) -> Self {
        TestBedError::TransactionError(error.err)
    }
}

impl<T> From<PoisonError<MutexGuard<'_, T>>> for TestBedError {
    fn from(e: PoisonError<MutexGuard<'_, T>>) -> Self {
        TestBedError::TestError(e.to_string())
    }
}

impl From<std::io::Error> for TestBedError {
    fn from(error: std::io::Error) -> Self {
        TestBedError::TestError(format!("IO Error: {}", error))
    }
}

// impl From<anchor_lang::error::Error> for TestBedError {
//     fn from(error: anchor_lang::error::Error) -> Self {
//         TestBedError::ProgramError(format!("Anchor error: {}", error))
//     }
// }
