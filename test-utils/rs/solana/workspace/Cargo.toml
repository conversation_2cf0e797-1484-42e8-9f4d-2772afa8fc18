[package]
name = "workspace"
version = "0.1.0"
edition = "2021"
description = "Workspace utilities for Solana program testing"
license = "MIT"

[dependencies]
litesvm = "0.2.1"
solana-program = { version = "1.18.26" }
solana-sdk = { version = "1.18.26" }
thiserror = "1.0.57"
async-trait = "0.1.68"
solana-program-runtime = { version = "1.18.26" }
anchor-lang = { version = "0.30.1", optional = true  }

[features]
default = []
test-utils = []
account-deserialize = ["anchor-lang"]
