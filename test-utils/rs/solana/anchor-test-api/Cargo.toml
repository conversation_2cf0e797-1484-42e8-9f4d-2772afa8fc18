[package]
name = "anchor-test-api"
version = "0.1.0"
edition = "2021"
description = "Test API generator for Anchor programs"
license = "MIT"

[lib]
proc-macro = true

[dependencies]
proc-macro2 = "1.0.78"
quote = "1.0.35"
syn = { version = "2.0.52", features = ["full", "extra-traits"] }
workspace = { path = "../workspace", features = ["test-utils"] }
heck = "0.4.1" 

[dev-dependencies]
async-trait = "0.1.68"
solana-sdk = { version = "1.18.26" }
solana-program = { version = "1.18.26" }
anchor-lang = "0.30.1"

[features]
default = []
test-utils = []
