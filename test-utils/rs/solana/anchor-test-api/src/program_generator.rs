use proc_macro2::TokenStream;
use quote::format_ident;
use quote::quote;
use quote::ToTokens;
use syn::{
    parse_quote,
    visit::{self, Visit},
    File, Item, ItemMod, ItemStruct,
};

use crate::{instruction_generator, testbed_extension_generator, utils};

/// Generates the test program struct for an Anchor program.
pub fn generate_program_struct(program_mod: &ItemMod) -> TokenStream {
    let program_name = &program_mod.ident;
    let program_struct_name = format_ident!(
        "{}Program",
        utils::to_pascal_case(&program_name.to_string())
    );

    quote! {
        #[derive(Debug, <PERSON><PERSON>)]
        pub struct #program_struct_name {
            pub program_id: ::solana_sdk::pubkey::Pubkey,
        }

        impl #program_struct_name {
            pub fn new() -> Self {
                Self {
                    program_id: ::solana_sdk::pubkey::Pubkey::new_unique(),
                }
            }

            pub fn with_program_id(program_id: ::solana_sdk::pubkey::Pubkey) -> Self {
                Self {
                    program_id,
                }
            }
        }

        #[::async_trait::async_trait]
        impl ::workspace::TestProgram for #program_struct_name {
            fn program_id(&self) -> ::solana_sdk::pubkey::Pubkey {
                self.program_id
            }

            fn as_any(&self) -> &dyn ::std::any::Any {
                self
            }
        }
    }
}

/// Generates the test program module for an Anchor program.
pub fn generate_test_program(program_mod: &ItemMod) -> TokenStream {
    let program_struct = generate_program_struct(program_mod);
    let testbed_extension = testbed_extension_generator::generate_testbed_extension(program_mod);
    let instruction_methods = instruction_generator::generate_instruction_methods(program_mod);

    let program_name = &program_mod.ident;
    let program_struct_name = quote::format_ident!(
        "{}Program",
        crate::utils::to_pascal_case(&program_name.to_string())
    );

    // Generate the test program module
    quote! {
        #program_mod

        // Add the test program module
        #[cfg(feature = "test-utils")]
        pub mod testing {
            use super::*;
            use ::solana_sdk::{
                pubkey::Pubkey,
                instruction::Instruction,
                signature::{Keypair, Signature, Signer},
            };
            use ::std::any::Any;
            use ::async_trait::async_trait;
            use ::workspace::{TestBed, TestBedError, TestProgram};

            #program_struct

            #testbed_extension

            impl #program_struct_name {
                #instruction_methods
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use syn::parse_quote;

    #[test]
    fn test_generate_program_struct() {
        // Test with a simple program name
        let program_mod: ItemMod = parse_quote! {
            pub mod test_program {
                // module contents
            }
        };

        let result = generate_program_struct(&program_mod);
        let result_str = result.to_string();

        // Check struct declaration
        assert!(result_str.contains("# [derive (Debug , Clone)]"));
        assert!(result_str.contains("pub struct TestProgramProgram"));
        assert!(result_str.contains("pub program_id : :: solana_sdk :: pubkey :: Pubkey"));

        // Check constructor methods
        assert!(result_str.contains("pub fn new () -> Self"));
        assert!(
            result_str.contains("program_id : :: solana_sdk :: pubkey :: Pubkey :: new_unique ()")
        );
        assert!(result_str.contains(
            "pub fn with_program_id (program_id : :: solana_sdk :: pubkey :: Pubkey) -> Self"
        ));

        // Check TestProgram trait implementation
        assert!(result_str.contains("impl :: workspace :: TestProgram for TestProgramProgram"));
        assert!(result_str.contains("fn program_id (& self) -> :: solana_sdk :: pubkey :: Pubkey"));
        assert!(result_str.contains("fn as_any (& self) -> & dyn :: std :: any :: Any"));
    }

    #[test]
    fn test_generate_test_program() {
        // Create a simple program module
        let program_mod: ItemMod = parse_quote! {
            pub mod fusion_core {
                #[test_api]
                pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
                    Ok(())
                }
            }
        };

        let result = generate_test_program(&program_mod);
        let result_str = result.to_string();
        // Check the overall structure
        assert!(result_str.contains("pub mod fusion_core"));
        assert!(result_str.contains("# [cfg (feature = \"test-utils\")]"));
        assert!(result_str.contains("pub mod testing"));

        // Check imports
        assert!(result_str.contains("use super :: *"));
        assert!(result_str.contains("use :: solana_sdk"));
        assert!(result_str.contains("use :: async_trait :: async_trait"));
        assert!(result_str.contains("use :: workspace :: { TestBed , TestBedError , TestProgram }"));

        // Check program struct is included
        assert!(result_str.contains("pub struct FusionCoreProgram"));

        // Check testbed extension is included
        assert!(result_str.contains("pub trait FusionCoreProgramTestBed"));

        // Check instruction methods are included
        assert!(result_str.contains("impl FusionCoreProgram"));

        // Check AnchorAccounts trait is defined
        assert!(result_str.contains("to_account_metas"));
    }
}
