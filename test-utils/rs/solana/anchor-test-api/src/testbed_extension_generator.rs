use crate::utils;
use heck::{<PERSON><PERSON><PERSON>ab<PERSON><PERSON>, ToSnakeCase};
use proc_macro2::{Ident, Span, TokenStream};
use quote::{format_ident, quote};
use syn::{ItemFn, ItemMod};

/// Generates the TestBed extension trait for an Anchor program.
pub fn generate_testbed_extension(program_mod: &ItemMod) -> TokenStream {
    let program_name = &program_mod.ident;
    let program_name_str = program_name.to_string();
    let package_name = program_name_str.to_kebab_case();
    let program_struct_name = format_ident!(
        "{}Program",
        utils::to_pascal_case(&program_name.to_string())
    );
    let trait_name = format_ident!(
        "{}ProgramTestBed",
        utils::to_pascal_case(&program_name.to_string())
    );
    let register_method_name = format_ident!("register_{}", program_name);
    let get_method_name = format_ident!("{}", program_name);

    quote! {
        #[::async_trait::async_trait]
        pub trait #trait_name {
            async fn #register_method_name(&mut self) -> std::result::Result<(), TestBedError>;
            fn #get_method_name(&self) -> #program_struct_name;
        }

        #[::async_trait::async_trait]
        impl #trait_name for ::workspace::TestBed {
            async fn #register_method_name(&mut self) -> std::result::Result<(), TestBedError> {
                let program = #program_struct_name::with_program_id(crate::ID);

                // Find the workspace root (where Anchor.toml is located)
                let workspace_root = find_workspace_root().unwrap_or_else(|| {
                    // If we can't find it, assume we're already there
                    std::env::current_dir().unwrap_or_default()
                });

                // Build absolute path to the program binary
                let program_path = workspace_root
                    .join("target/deploy")
                    .join(format!("{}.so", #program_name_str))
                    .to_string_lossy()
                    .to_string();

                // Register the program with the TestBed
                self.register_program(#program_name_str, program, &program_path).await.unwrap();

                Ok(())
            }

            fn #get_method_name(&self) -> #program_struct_name {
                // Get the program from the TestBed
                self.get_program::<#program_struct_name>(#program_name_str)
                    .expect("Program not registered").clone()
            }
        }

        // Helper to find the workspace root (where Anchor.toml is located)
        fn find_workspace_root() -> Option<std::path::PathBuf> {
            let mut current = std::env::current_dir().ok()?;

            // Walk up the directory tree until we find Anchor.toml
            loop {
                if current.join("Anchor.toml").exists() {
                    return Some(current);
                }

                if !current.pop() {
                    // We've reached the root of the filesystem
                    return None;
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use syn::parse_quote;

    #[test]
    fn test_generate_testbed_extension_basic() {
        // Test with a simple program name
        let program_mod: ItemMod = parse_quote! {
            pub mod basic_program {
                // module contents
            }
        };

        let result = generate_testbed_extension(&program_mod);
        let result_str = result.to_string();

        // Check trait name
        assert!(result_str.contains("pub trait BasicProgramProgramTestBed"));

        // Check method names
        assert!(result_str.contains("async fn register_basic_program"));
        assert!(result_str.contains("fn basic_program"));

        // Check implementation
        assert!(result_str.contains("impl BasicProgramProgramTestBed for :: workspace :: TestBed"));
        assert!(result_str
            .contains("let program = BasicProgramProgram :: with_program_id (crate :: ID)"));
        assert!(result_str.contains("self . register_program"));
        assert!(result_str.contains("self . get_program :: < BasicProgramProgram >"));
        assert!(result_str.contains("expect (\"Program not registered\")"));
    }

    #[test]
    fn test_generate_testbed_extension_pascal_case() {
        // Test with a PascalCase program name
        let program_mod: ItemMod = parse_quote! {
            pub mod PascalProgram {
                // module contents
            }
        };

        let result = generate_testbed_extension(&program_mod);
        let result_str = result.to_string();

        // Check trait name (should still be PascalCase)
        assert!(result_str.contains("pub trait PascalProgramProgramTestBed"));

        // Check method names (register_ should be lowercase)
        assert!(result_str.contains("async fn register_PascalProgram"));
        assert!(result_str.contains("fn PascalProgram"));
    }

    #[test]
    fn test_generate_testbed_extension_snake_case() {
        // Test with a snake_case program name
        let program_mod: ItemMod = parse_quote! {
            pub mod snake_case_program {
                // module contents
            }
        };

        let result = generate_testbed_extension(&program_mod);
        let result_str = result.to_string();

        // Check trait name (should be converted to PascalCase)
        assert!(result_str.contains("pub trait SnakeCaseProgramProgramTestBed"));

        // Check method names
        assert!(result_str.contains("async fn register_snake_case_program"));
        assert!(result_str.contains("fn snake_case_program"));
    }
}
