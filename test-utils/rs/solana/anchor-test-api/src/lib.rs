extern crate proc_macro;

use proc_macro::TokenStream;
use syn::{parse_macro_input, ItemMod, ItemFn};

mod instruction_generator;
mod program_generator;
mod testbed_extension_generator;
mod utils;

/// Generates a test program representation for an Anchor program.
///
/// This macro is applied to an Anchor program module and generates:
/// 1. A program struct that implements the `TestProgram` trait.
/// 2. A trait that extends the `TestBed` with methods for the program.
/// 3. Methods on the program struct for each instruction.
///
/// # Example
///
/// ```
/// #[program]
/// #[test_program]
/// pub mod fusion_core {
///     use super::*;
///     
///     pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
///         // Instruction implementation
///         Ok(())
///     }
/// }
/// ```
#[proc_macro_attribute]
pub fn test_program(_attr: TokenStream, item: TokenStream) -> TokenStream {
    let input = parse_macro_input!(item as ItemMod);
    
    // Generate the test program
    let expanded = program_generator::generate_test_program(&input);
    
    TokenStream::from(expanded)
}

/// Marks an Anchor instruction handler for inclusion in the generated test API.
///
/// This macro is applied to instruction handlers and doesn't modify the function
/// itself, but marks it for the `#[test_program]` macro to generate test API methods.
///
/// # Example
///
/// ```
/// #[test_api]
/// pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
///     // Instruction implementation
///     Ok(())
/// }
/// ```
#[proc_macro_attribute]
pub fn test_api(_attr: TokenStream, item: TokenStream) -> TokenStream {
    // Just return the original function unmodified
    // The actual test API is generated by the test_program macro
    item
}
