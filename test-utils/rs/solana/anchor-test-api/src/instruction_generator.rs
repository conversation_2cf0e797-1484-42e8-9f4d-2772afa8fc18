use crate::utils;
use proc_macro2::TokenStream;
use quote::format_ident;
use quote::quote;
use syn::{ItemFn, ItemMod};

/// Generates the instruction methods for an Anchor program.
pub fn generate_instruction_methods(program_mod: &ItemMod) -> TokenStream {
    let mut methods = TokenStream::new();

    // Extract the instruction handlers from the program module
    if let Some((_, content)) = &program_mod.content {
        for item in content {
            if let syn::Item::Fn(item_fn) = item {
                methods.extend(generate_instruction_method(&item_fn));
            }
        }
    }

    methods
}

/// Extracts the base context type name from a Context<T> type.
fn extract_context_type_name(item_fn: &ItemFn) -> Option<syn::Ident> {
    for arg in &item_fn.sig.inputs {
        if let syn::FnArg::Typed(pat_type) = arg {
            if let syn::Pat::Ident(pat_ident) = &*pat_type.pat {
                if pat_ident.ident == "ctx" {
                    if let syn::Type::Path(type_path) = &*pat_type.ty {
                        if let Some(segment) = type_path.path.segments.last() {
                            if segment.ident == "Context" {
                                if let syn::PathArguments::AngleBracketed(args) = &segment.arguments
                                {
                                    // Find the first Type argument (ignoring lifetimes)
                                    for arg in &args.args {
                                        if let syn::GenericArgument::Type(ty) = arg {
                                            // Extract the base type, handling possible generics
                                            return extract_base_type_name(ty);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    None
}

fn extract_base_type_name(ty: &syn::Type) -> Option<syn::Ident> {
    match ty {
        syn::Type::Path(path) => {
            // Get the first segment (the base type name)
            let first_segment = path.path.segments.first()?;

            // If this is a simple type without generics, return it
            if matches!(first_segment.arguments, syn::PathArguments::None) {
                return Some(first_segment.ident.clone());
            }

            // If it has generics, we still just want the base name
            Some(first_segment.ident.clone())
        }
        // Handle reference types like &T
        syn::Type::Reference(type_ref) => extract_base_type_name(&type_ref.elem),
        // Handle other type variants if needed
        _ => None,
    }
}

fn has_remaining_accounts(item_fn: &ItemFn) -> bool {
    use syn::visit::Visit;

    struct RemainingAccountsVisitor {
        found: bool,
    }

    impl<'ast> Visit<'ast> for RemainingAccountsVisitor {
        fn visit_expr(&mut self, expr: &'ast syn::Expr) {
            if let syn::Expr::Field(field) = expr {
                if let syn::Expr::Path(expr_path) = &*field.base {
                    if expr_path
                        .path
                        .segments
                        .last()
                        .map(|s| s.ident == "ctx")
                        .unwrap_or(false)
                        && field.member == format_ident!("remaining_accounts").into()
                    {
                        self.found = true;
                    }
                }
            }

            syn::visit::visit_expr(self, expr);
        }
    }
    let mut visitor = RemainingAccountsVisitor { found: false };
    visitor.visit_item_fn(item_fn);
    visitor.found
}

/// Generates an instruction method for an Anchor instruction handler.
fn generate_instruction_method(item_fn: &ItemFn) -> TokenStream {
    let fn_name = &item_fn.sig.ident;
    let fn_args = &item_fn.sig.inputs;

    let context_type_name = extract_context_type_name(item_fn).expect("Context type not found");

    // Extract any additional arguments
    let mut additional_args = Vec::new();
    let mut has_remaining_accounts = has_remaining_accounts(item_fn);

    for arg in fn_args {
        if let syn::FnArg::Typed(pat_type) = arg {
            if let syn::Pat::Ident(pat_ident) = &*pat_type.pat {
                if pat_ident.ident != "ctx" {
                    additional_args.push((pat_ident.ident.clone(), pat_type.ty.clone()));
                }
            }
        }
    }

    // Generate the method signature
    let additional_arg_params = additional_args.iter().map(|(name, ty)| {
        quote! { #name: #ty }
    });

    // Generate the method body
    let additional_arg_names = additional_args.iter().map(|(name, _)| {
        quote! { #name }
    });

    if has_remaining_accounts {
        quote! {
            pub async fn #fn_name(
                &self,
                testbed: &mut ::workspace::TestBed,
                accounts: crate::accounts::#context_type_name,
                signers: &[& ::solana_sdk::signature::Keypair],
                remaining_accounts: &[::solana_sdk::instruction::AccountMeta],
                #(#additional_arg_params),*
            ) -> std::result::Result<::solana_sdk::signature::Signature, TestBedError> {
                // Create the discriminator (8 bytes)
                let discriminator = ::anchor_lang::solana_program::hash::hash(
                    format!("global:{}", stringify!(#fn_name)).as_bytes()
                ).to_bytes()[..8].to_vec();

                // Create the account metas
                let mut account_metas = accounts.to_account_metas(None);
                account_metas.extend_from_slice(remaining_accounts);

                // Create the instruction data
                let mut data = discriminator;

                // Add additional arguments to the instruction data
                #(
                    ::anchor_lang::AnchorSerialize::serialize(&#additional_arg_names, &mut data)?;
                )*

                // Create the instruction
                let ix = ::solana_sdk::instruction::Instruction {
                    program_id: self.program_id(),
                    accounts: account_metas,
                    data,
                };

                // Execute the instruction
                testbed.execute_transaction(&[ix], signers).await
            }
        }
    } else {
        quote! {
            pub async fn #fn_name(
                &self,
                testbed: &mut ::workspace::TestBed,
                accounts: crate::accounts::#context_type_name,
                signers: &[& ::solana_sdk::signature::Keypair],
                #(#additional_arg_params),*
            ) -> std::result::Result<::solana_sdk::signature::Signature, TestBedError> {
                // Create the discriminator (8 bytes)
                let discriminator = ::anchor_lang::solana_program::hash::hash(
                    format!("global:{}", stringify!(#fn_name)).as_bytes()
                ).to_bytes()[..8].to_vec();

                // Create the account metas
                let account_metas = accounts.to_account_metas(None);

                // Create the instruction data
                let mut data = discriminator;

                // Add additional arguments to the instruction data
                #(
                    ::anchor_lang::AnchorSerialize::serialize(&#additional_arg_names, &mut data)?;
                )*

                // Create the instruction
                let ix = ::solana_sdk::instruction::Instruction {
                    program_id: self.program_id(),
                    accounts: account_metas,
                    data,
                };

                // Execute the instruction
                testbed.execute_transaction(&[ix], signers).await
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use syn::parse_quote;

    #[test]
    fn test_extract_context_type_name() {
        let item_fn: ItemFn = parse_quote! {
            pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
                Ok(())
            }
        };

        let result = extract_context_type_name(&item_fn);
        assert_eq!(result.unwrap().to_string(), "Initialize");
    }

    #[test]
    fn test_extract_context_type_name_with_lifetime() {
        let item_fn: ItemFn = parse_quote! {
            pub fn initialize(ctx: Context<Initialize<'info>>) -> Result<()> {
                Ok(())
            }
        };

        let result = extract_context_type_name(&item_fn);
        assert_eq!(result.unwrap().to_string(), "Initialize");
    }

    #[test]
    fn test_generate_instruction_method() {
        let item_fn: ItemFn = parse_quote! {
            pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
                Ok(())
            }
        };

        let result = generate_instruction_method(&item_fn);

        let result_str = result.to_string();

        assert!(result_str.contains("pub async fn initialize"));
        assert!(result_str.contains("accounts : crate :: accounts :: Initialize"));
        assert!(result_str.contains("let discriminator ="));
        assert!(result_str.contains("let account_metas ="));
        assert!(result_str.contains("testbed . execute_transaction"));
    }

    #[test]
    fn test_generate_instruction_method_with_args() {
        let item_fn: ItemFn = parse_quote! {
            pub fn fuse_nfts(ctx: Context<FuseNfts>, strength: u8, fusion_type: u8) -> Result<()> {
                Ok(())
            }
        };

        let result = generate_instruction_method(&item_fn);
        let result_str = result.to_string();

        assert!(result_str.contains("pub async fn fuse_nfts"));
        assert!(result_str.contains("accounts : crate :: accounts :: FuseNfts"));
        assert!(result_str.contains("strength : u8"));
        assert!(result_str.contains("fusion_type : u8"));
        assert!(result_str.contains(":: anchor_lang :: AnchorSerialize :: serialize (& strength "));
        assert!(
            result_str.contains(":: anchor_lang :: AnchorSerialize :: serialize (& fusion_type ")
        );
    }

    #[test]
    #[should_panic(expected = "Context type not found")]
    fn test_generate_instruction_method_missing_context() {
        let item_fn: ItemFn = parse_quote! {
            pub fn invalid_function(value: u64) -> Result<()> {
                Ok(())
            }
        };

        generate_instruction_method(&item_fn);
    }

    #[test]
    fn test_generate_instruction_method_with_remaining_accounts() {
        let item_fn: ItemFn = parse_quote! { pub fn accept_offer( ctx: Context<AcceptOffer>) -> Result<()> {
            ctx.accounts.handler(ctx.remaining_accounts);
            Ok(())
        } };

        let result = generate_instruction_method(&item_fn);
        let result_str = result.to_string();

        assert!(result_str
            .contains("remaining_accounts : & [:: solana_sdk :: instruction :: AccountMeta]"));
        assert!(result_str.contains("account_metas . extend_from_slice (remaining_accounts)"));
    }
}
