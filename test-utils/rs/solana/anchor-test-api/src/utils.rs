use syn::{ItemFn, Attribute};

/// Converts a snake_case string to PascalCase.
pub fn to_pascal_case(s: &str) -> String {
    let mut result = String::new();
    let mut capitalize_next = true;
    
    for c in s.chars() {
        if c == '_' {
            capitalize_next = true;
        } else if capitalize_next {
            result.push(c.to_ascii_uppercase());
            capitalize_next = false;
        } else {
            result.push(c);
        }
    }
    
    result
}

/// Checks if an item has the test_api attribute.
pub fn has_test_api_attribute(item: &ItemFn) -> bool {
    item.attrs.iter().any(|attr| is_test_api_attribute(attr))
}

/// Checks if an attribute is the test_api attribute.
fn is_test_api_attribute(attr: &Attribute) -> bool {
    attr.path().is_ident("test_api")
}
