import { prisma } from '../server/lib/prismaClient'

/**
 * <PERSON><PERSON>t to refresh time-based metrics in the database
 *
 * This script can be scheduled to run daily using an external scheduler:
 * - Heroku Scheduler
 * - AWS Lambda with EventBridge
 * - Google Cloud Scheduler
 * - Cron job on your server
 *
 * It calls the refresh_time_based_metrics() function in the database
 * which updates:
 * - NFT activity counts for 7-day and 30-day periods
 * - Collection recent sales counts
 */
async function refreshMetrics() {
	try {
		console.log('Starting metrics refresh...')

		// Execute the refresh function
		await prisma.$executeRaw`SELECT refresh_time_based_metrics()`

		console.log('Successfully refreshed time-based metrics')
	} catch (error) {
		console.error('Error refreshing metrics:', error)
		process.exit(1)
	} finally {
		await prisma.$disconnect()
	}
}

// Run the function if this script is executed directly
if (require.main === module) {
	refreshMetrics()
}

// Export the function for use in other scripts
export { refreshMetrics }
