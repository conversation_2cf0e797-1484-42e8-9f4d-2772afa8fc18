import type { Variants } from 'framer-motion'

export const headerVariants: Variants = {
	hidden: { y: -50, opacity: 0 },
	visible: {
		y: 0,
		opacity: 1,
		transition: { duration: 0.5, ease: 'easeOut' },
	},
}

export const linkVariants: Variants = {
	hidden: { y: 15, opacity: 0 },
	visible: (i: number) => ({
		y: 0,
		opacity: 1,
		transition: { delay: i * 0.1, duration: 0.3, ease: 'easeOut' },
	}),
	hover: {
		y: -2,
		color: '#f43f5e',
		scale: 1.05,
		transition: { duration: 0.2, ease: 'easeOut' },
	},
}

export const buttonVariants: Variants = {
	hover: {
		scale: 1.03,
		transition: { duration: 0.2, ease: 'easeOut' },
	},
	tap: {
		scale: 0.97,
		transition: { duration: 0.1, ease: 'easeOut' },
	},
}

export const heroImageVariants: Variants = {
	initial: { y: 30, opacity: 0 },
	animate: {
		y: 0,
		opacity: 1,
		transition: { duration: 0.6, ease: 'easeOut' },
	},
}
