import type { Variants } from 'framer-motion'

export const headerVariants: Variants = {
	hidden: { y: -50, opacity: 0 },
	visible: {
		y: 0,
		opacity: 1,
		transition: { duration: 0.5, ease: 'easeOut' },
	},
}

export const linkVariants: Variants = {
	hidden: { y: 15, opacity: 0 },
	visible: (i: number) => ({
		y: 0,
		opacity: 1,
		transition: { delay: i * 0.1, duration: 0.3, ease: 'easeOut' },
	}),
	hover: {
		y: -2, // Keep position change for subtle hover
		transition: { duration: 0.2, ease: 'easeOut' },
	},
}

export const mobileMenuVariants: Variants = {
	hidden: { x: 20, opacity: 0 },
	visible: {
		x: 0,
		opacity: 1,
		transition: { duration: 0.4, ease: 'easeOut' },
	},
}

export const buttonVariants: Variants = {
	hover: {
		opacity: 0.9, // Use opacity for hover instead of scale or shadow
		transition: { duration: 0.2, ease: 'easeOut' },
	},
	tap: {
		opacity: 0.8, // Use opacity for tap instead of scale
		transition: { duration: 0.1, ease: 'easeOut' },
	},
}

export const logoVariants: Variants = {
	initial: { y: -10, opacity: 0 }, // Changed scale to y for consistency with fade
	animate: {
		y: 0,
		opacity: 1,
		transition: { duration: 0.4, ease: 'easeOut' },
	},
	hover: {
		opacity: 0.9, // Use opacity for hover instead of scale or filter
		transition: { duration: 0.2, ease: 'easeOut' },
	},
}

export const heroImageVariants: Variants = {
	initial: { y: 30, opacity: 0 },
	animate: {
		y: 0,
		opacity: 1,
		transition: { duration: 0.6, ease: 'easeOut' },
	},
}
