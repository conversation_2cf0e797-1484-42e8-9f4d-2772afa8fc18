import { mplCore } from '@metaplex-foundation/mpl-core'
import { mplTokenMetadata } from '@metaplex-foundation/mpl-token-metadata'
import {
	SPL_ASSOCIATED_TOKEN_PROGRAM_ID,
	SPL_TOKEN_PROGRAM_ID,
} from '@metaplex-foundation/mpl-toolbox'
import type { Umi } from '@metaplex-foundation/umi'
import type { Cluster } from '@metaplex-foundation/umi'
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults'
import { walletAdapterIdentity } from '@metaplex-foundation/umi-signer-wallet-adapters'
import type { WalletAdapter } from '@solana/wallet-adapter-base'
import { createContext } from 'react'
import { createFusionMarketplaceProgram as fusionMarketplaceProgram } from '@/lib/generated'

// Create a React context for Umi
export const UmiContext = createContext<Umi | null>(null)

// Function to create a configured Umi instance with wallet for frontend use
export function createConfiguredUmi(
	endpoint: string,
	walletAdapter: WalletAdapter,
) {
	const umi = createUmi(endpoint, { commitment: 'finalized' })
	umi.use(walletAdapterIdentity(walletAdapter))
	umi.use(mplCore()) // Add MPL Core program
	umi.use(mplTokenMetadata())
	// umi.programs.add(fusionMarketplaceProgram()) // Add our Fusion Marketplace program

	// Add splAssociatedToken program
	const splAssociatedTokenProgram = {
		name: 'splAssociatedToken',
		publicKey: SPL_ASSOCIATED_TOKEN_PROGRAM_ID,
		getErrorFromCode: (_code: number) => null,
		getErrorFromName: (_name: string) => null,
		isOnCluster: (cluster: Cluster) => {
			return (
				['mainnet', 'devnet', 'testnet', 'localnet'] as Cluster[]
			).includes(cluster)
		},
	}
	umi.programs.add(splAssociatedTokenProgram)

	// Add splToken program
	const splTokenProgram = {
		name: 'splToken',
		publicKey: SPL_TOKEN_PROGRAM_ID,
		getErrorFromCode: (_code: number) => null,
		getErrorFromName: (_name: string) => null,
		isOnCluster: (cluster: Cluster) => {
			return ['mainnet', 'devnet', 'testnet', 'localnet'].includes(cluster)
		},
	}
	umi.programs.add(splTokenProgram)

	return umi
}

// Function to create a configured Umi instance without wallet for backend use
export function createBackendUmi(endpoint: string): Umi {
	const umi = createUmi(endpoint, { commitment: 'finalized' })
		.use(mplCore()) // Add MPL Core program
		.use({
			install(umi) {
				umi.programs.add(fusionMarketplaceProgram())
			},
		})

	umi.use(mplTokenMetadata())

	// Add splAssociatedToken program
	const splAssociatedTokenProgram = {
		name: 'splAssociatedToken',
		publicKey: SPL_ASSOCIATED_TOKEN_PROGRAM_ID,
		getErrorFromCode: (_code: number) => null,
		getErrorFromName: (_name: string) => null,
		isOnCluster: (cluster: Cluster) => {
			return ['mainnet', 'devnet', 'testnet', 'localnet'].includes(cluster)
		},
	}
	umi.programs.add(splAssociatedTokenProgram)

	// Add splToken program
	const splTokenProgram = {
		name: 'splToken',
		publicKey: SPL_TOKEN_PROGRAM_ID,
		getErrorFromCode: (_code: number) => null,
		getErrorFromName: (_name: string) => null,
		isOnCluster: (cluster: Cluster) => {
			return ['mainnet', 'devnet', 'testnet', 'localnet'].includes(cluster)
		},
	}
	umi.programs.add(splTokenProgram)

	return umi
}
