/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Account,
  Context,
  Option,
  OptionOrNullable,
  Pda,
  PublicKey,
  RpcAccount,
  RpcGetAccountOptions,
  RpcGetAccountsOptions,
  assertAccountExists,
  deserializeAccount,
  gpaBuilder,
  publicKey as toPublicKey,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  i64,
  mapSerializer,
  option,
  publicKey as publicKeySerializer,
  struct,
  u32,
  u64,
  u8,
} from '@metaplex-foundation/umi/serializers';
import {
  AuctionType,
  AuctionTypeArgs,
  getAuctionTypeSerializer,
} from '../types';

export type Auction = Account<AuctionAccountData>;

export type AuctionAccountData = {
  discriminator: Uint8Array;
  /** The parent Listing account of this auction. */
  listing: PublicKey;
  /** The type of auction, determines bidding rules. */
  auctionType: AuctionType;
  /** The amount of the current highest bid in lamports. */
  highestBid: bigint;
  /** The public key of the current highest bidder. */
  highestBidder: Option<PublicKey>;
  /** The total number of bids placed on this auction. */
  bidsCount: number;
  /** The timestamp when bidding is scheduled to start. */
  startTime: bigint;
  /**
   * The timestamp when the auction is scheduled to end.
   * This can be extended by the `extension_period` if bids occur near the end.
   */
  endTime: bigint;
  /**
   * The duration (in seconds) by which the auction `end_time` is extended
   * if a new highest bid is placed within this period before the current `end_time`.
   */
  extensionPeriod: bigint;
  /** Escrow bump */
  escrowBump: number;
  /** PDA bump seed. */
  bump: number;
};

export type AuctionAccountDataArgs = {
  /** The parent Listing account of this auction. */
  listing: PublicKey;
  /** The type of auction, determines bidding rules. */
  auctionType: AuctionTypeArgs;
  /** The amount of the current highest bid in lamports. */
  highestBid: number | bigint;
  /** The public key of the current highest bidder. */
  highestBidder: OptionOrNullable<PublicKey>;
  /** The total number of bids placed on this auction. */
  bidsCount: number;
  /** The timestamp when bidding is scheduled to start. */
  startTime: number | bigint;
  /**
   * The timestamp when the auction is scheduled to end.
   * This can be extended by the `extension_period` if bids occur near the end.
   */
  endTime: number | bigint;
  /**
   * The duration (in seconds) by which the auction `end_time` is extended
   * if a new highest bid is placed within this period before the current `end_time`.
   */
  extensionPeriod: number | bigint;
  /** Escrow bump */
  escrowBump: number;
  /** PDA bump seed. */
  bump: number;
};

export function getAuctionAccountDataSerializer(): Serializer<
  AuctionAccountDataArgs,
  AuctionAccountData
> {
  return mapSerializer<AuctionAccountDataArgs, any, AuctionAccountData>(
    struct<AuctionAccountData>(
      [
        ['discriminator', bytes({ size: 8 })],
        ['listing', publicKeySerializer()],
        ['auctionType', getAuctionTypeSerializer()],
        ['highestBid', u64()],
        ['highestBidder', option(publicKeySerializer())],
        ['bidsCount', u32()],
        ['startTime', i64()],
        ['endTime', i64()],
        ['extensionPeriod', u64()],
        ['escrowBump', u8()],
        ['bump', u8()],
      ],
      { description: 'AuctionAccountData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([218, 94, 247, 242, 126, 233, 131, 81]),
    })
  ) as Serializer<AuctionAccountDataArgs, AuctionAccountData>;
}

export function deserializeAuction(rawAccount: RpcAccount): Auction {
  return deserializeAccount(rawAccount, getAuctionAccountDataSerializer());
}

export async function fetchAuction(
  context: Pick<Context, 'rpc'>,
  publicKey: PublicKey | Pda,
  options?: RpcGetAccountOptions
): Promise<Auction> {
  const maybeAccount = await context.rpc.getAccount(
    toPublicKey(publicKey, false),
    options
  );
  assertAccountExists(maybeAccount, 'Auction');
  return deserializeAuction(maybeAccount);
}

export async function safeFetchAuction(
  context: Pick<Context, 'rpc'>,
  publicKey: PublicKey | Pda,
  options?: RpcGetAccountOptions
): Promise<Auction | null> {
  const maybeAccount = await context.rpc.getAccount(
    toPublicKey(publicKey, false),
    options
  );
  return maybeAccount.exists ? deserializeAuction(maybeAccount) : null;
}

export async function fetchAllAuction(
  context: Pick<Context, 'rpc'>,
  publicKeys: Array<PublicKey | Pda>,
  options?: RpcGetAccountsOptions
): Promise<Auction[]> {
  const maybeAccounts = await context.rpc.getAccounts(
    publicKeys.map((key) => toPublicKey(key, false)),
    options
  );
  return maybeAccounts.map((maybeAccount) => {
    assertAccountExists(maybeAccount, 'Auction');
    return deserializeAuction(maybeAccount);
  });
}

export async function safeFetchAllAuction(
  context: Pick<Context, 'rpc'>,
  publicKeys: Array<PublicKey | Pda>,
  options?: RpcGetAccountsOptions
): Promise<Auction[]> {
  const maybeAccounts = await context.rpc.getAccounts(
    publicKeys.map((key) => toPublicKey(key, false)),
    options
  );
  return maybeAccounts
    .filter((maybeAccount) => maybeAccount.exists)
    .map((maybeAccount) => deserializeAuction(maybeAccount as RpcAccount));
}

export function getAuctionGpaBuilder(
  context: Pick<Context, 'rpc' | 'programs'>
) {
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );
  return gpaBuilder(context, programId)
    .registerFields<{
      discriminator: Uint8Array;
      listing: PublicKey;
      auctionType: AuctionTypeArgs;
      highestBid: number | bigint;
      highestBidder: OptionOrNullable<PublicKey>;
      bidsCount: number;
      startTime: number | bigint;
      endTime: number | bigint;
      extensionPeriod: number | bigint;
      escrowBump: number;
      bump: number;
    }>({
      discriminator: [0, bytes({ size: 8 })],
      listing: [8, publicKeySerializer()],
      auctionType: [40, getAuctionTypeSerializer()],
      highestBid: [41, u64()],
      highestBidder: [49, option(publicKeySerializer())],
      bidsCount: [null, u32()],
      startTime: [null, i64()],
      endTime: [null, i64()],
      extensionPeriod: [null, u64()],
      escrowBump: [null, u8()],
      bump: [null, u8()],
    })
    .deserializeUsing<Auction>((account) => deserializeAuction(account))
    .whereField(
      'discriminator',
      new Uint8Array([218, 94, 247, 242, 126, 233, 131, 81])
    );
}
