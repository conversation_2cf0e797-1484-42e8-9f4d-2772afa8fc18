/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Account,
  Context,
  Option,
  OptionOrNullable,
  Pda,
  PublicKey,
  RpcAccount,
  RpcGetAccountOptions,
  RpcGetAccountsOptions,
  assertAccountExists,
  deserializeAccount,
  gpaBuilder,
  publicKey as toPublicKey,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bool,
  bytes,
  i64,
  mapSerializer,
  option,
  publicKey as publicKeySerializer,
  struct,
  u64,
  u8,
} from '@metaplex-foundation/umi/serializers';

export type Offer = Account<OfferAccountData>;

export type OfferAccountData = {
  discriminator: Uint8Array;
  /** Version for future upgrades */
  version: number;
  /** Buyer making the offer */
  buyer: PublicKey;
  /** Specific NFT asset */
  asset: PublicKey;
  /** Price offered in lamports */
  price: bigint;
  /** Optional SPL token for payment (None = SOL) */
  currency: Option<PublicKey>;
  /** When the offer expires */
  expiresAt: bigint;
  /** When the offer was created */
  createdAt: bigint;
  /** Whether the offer has been filled */
  isFilled: boolean;
  /** Whether the offer has been cancelled */
  isCancelled: boolean;
  /** PDA bump */
  escrowBump: number;
  /** PDA bump */
  bump: number;
};

export type OfferAccountDataArgs = {
  /** Version for future upgrades */
  version: number;
  /** Buyer making the offer */
  buyer: PublicKey;
  /** Specific NFT asset */
  asset: PublicKey;
  /** Price offered in lamports */
  price: number | bigint;
  /** Optional SPL token for payment (None = SOL) */
  currency: OptionOrNullable<PublicKey>;
  /** When the offer expires */
  expiresAt: number | bigint;
  /** When the offer was created */
  createdAt: number | bigint;
  /** Whether the offer has been filled */
  isFilled: boolean;
  /** Whether the offer has been cancelled */
  isCancelled: boolean;
  /** PDA bump */
  escrowBump: number;
  /** PDA bump */
  bump: number;
};

export function getOfferAccountDataSerializer(): Serializer<
  OfferAccountDataArgs,
  OfferAccountData
> {
  return mapSerializer<OfferAccountDataArgs, any, OfferAccountData>(
    struct<OfferAccountData>(
      [
        ['discriminator', bytes({ size: 8 })],
        ['version', u8()],
        ['buyer', publicKeySerializer()],
        ['asset', publicKeySerializer()],
        ['price', u64()],
        ['currency', option(publicKeySerializer())],
        ['expiresAt', i64()],
        ['createdAt', i64()],
        ['isFilled', bool()],
        ['isCancelled', bool()],
        ['escrowBump', u8()],
        ['bump', u8()],
      ],
      { description: 'OfferAccountData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([215, 88, 60, 71, 170, 162, 73, 229]),
    })
  ) as Serializer<OfferAccountDataArgs, OfferAccountData>;
}

export function deserializeOffer(rawAccount: RpcAccount): Offer {
  return deserializeAccount(rawAccount, getOfferAccountDataSerializer());
}

export async function fetchOffer(
  context: Pick<Context, 'rpc'>,
  publicKey: PublicKey | Pda,
  options?: RpcGetAccountOptions
): Promise<Offer> {
  const maybeAccount = await context.rpc.getAccount(
    toPublicKey(publicKey, false),
    options
  );
  assertAccountExists(maybeAccount, 'Offer');
  return deserializeOffer(maybeAccount);
}

export async function safeFetchOffer(
  context: Pick<Context, 'rpc'>,
  publicKey: PublicKey | Pda,
  options?: RpcGetAccountOptions
): Promise<Offer | null> {
  const maybeAccount = await context.rpc.getAccount(
    toPublicKey(publicKey, false),
    options
  );
  return maybeAccount.exists ? deserializeOffer(maybeAccount) : null;
}

export async function fetchAllOffer(
  context: Pick<Context, 'rpc'>,
  publicKeys: Array<PublicKey | Pda>,
  options?: RpcGetAccountsOptions
): Promise<Offer[]> {
  const maybeAccounts = await context.rpc.getAccounts(
    publicKeys.map((key) => toPublicKey(key, false)),
    options
  );
  return maybeAccounts.map((maybeAccount) => {
    assertAccountExists(maybeAccount, 'Offer');
    return deserializeOffer(maybeAccount);
  });
}

export async function safeFetchAllOffer(
  context: Pick<Context, 'rpc'>,
  publicKeys: Array<PublicKey | Pda>,
  options?: RpcGetAccountsOptions
): Promise<Offer[]> {
  const maybeAccounts = await context.rpc.getAccounts(
    publicKeys.map((key) => toPublicKey(key, false)),
    options
  );
  return maybeAccounts
    .filter((maybeAccount) => maybeAccount.exists)
    .map((maybeAccount) => deserializeOffer(maybeAccount as RpcAccount));
}

export function getOfferGpaBuilder(context: Pick<Context, 'rpc' | 'programs'>) {
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );
  return gpaBuilder(context, programId)
    .registerFields<{
      discriminator: Uint8Array;
      version: number;
      buyer: PublicKey;
      asset: PublicKey;
      price: number | bigint;
      currency: OptionOrNullable<PublicKey>;
      expiresAt: number | bigint;
      createdAt: number | bigint;
      isFilled: boolean;
      isCancelled: boolean;
      escrowBump: number;
      bump: number;
    }>({
      discriminator: [0, bytes({ size: 8 })],
      version: [8, u8()],
      buyer: [9, publicKeySerializer()],
      asset: [41, publicKeySerializer()],
      price: [73, u64()],
      currency: [81, option(publicKeySerializer())],
      expiresAt: [null, i64()],
      createdAt: [null, i64()],
      isFilled: [null, bool()],
      isCancelled: [null, bool()],
      escrowBump: [null, u8()],
      bump: [null, u8()],
    })
    .deserializeUsing<Offer>((account) => deserializeOffer(account))
    .whereField(
      'discriminator',
      new Uint8Array([215, 88, 60, 71, 170, 162, 73, 229])
    );
}
