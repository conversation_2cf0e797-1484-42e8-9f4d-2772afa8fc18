/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Account,
  Context,
  Pda,
  PublicKey,
  RpcAccount,
  RpcGetAccountOptions,
  RpcGetAccountsOptions,
  assertAccountExists,
  deserializeAccount,
  gpaBuilder,
  publicKey as toPublicKey,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  mapSerializer,
  publicKey as publicKeySerializer,
  struct,
  u16,
  u64,
  u8,
} from '@metaplex-foundation/umi/serializers';

export type MarketplaceConfig = Account<MarketplaceConfigAccountData>;

export type MarketplaceConfigAccountData = {
  discriminator: Uint8Array;
  /** Version for future upgrades */
  version: number;
  /** admin that can update the configuration */
  admin: PublicKey;
  /** Account that receives marketplace fees */
  feeCollector: PublicKey;
  /** Protocol fee in basis points (e.g., 250 = 2.5%) */
  protocolFeeBp: number;
  /** Minimum bid increment in basis points (e.g., 500 = 5%) */
  minBidIncrementBp: number;
  /** Minimum listing duration in seconds */
  minListingDuration: bigint;
  /** Maximum listing duration in seconds */
  maxListingDuration: bigint;
  /** PDA bump */
  bump: number;
  /** PDA bump for marketplace authority */
  marketplaceAuthorityBump: number;
};

export type MarketplaceConfigAccountDataArgs = {
  /** Version for future upgrades */
  version: number;
  /** admin that can update the configuration */
  admin: PublicKey;
  /** Account that receives marketplace fees */
  feeCollector: PublicKey;
  /** Protocol fee in basis points (e.g., 250 = 2.5%) */
  protocolFeeBp: number;
  /** Minimum bid increment in basis points (e.g., 500 = 5%) */
  minBidIncrementBp: number;
  /** Minimum listing duration in seconds */
  minListingDuration: number | bigint;
  /** Maximum listing duration in seconds */
  maxListingDuration: number | bigint;
  /** PDA bump */
  bump: number;
  /** PDA bump for marketplace authority */
  marketplaceAuthorityBump: number;
};

export function getMarketplaceConfigAccountDataSerializer(): Serializer<
  MarketplaceConfigAccountDataArgs,
  MarketplaceConfigAccountData
> {
  return mapSerializer<
    MarketplaceConfigAccountDataArgs,
    any,
    MarketplaceConfigAccountData
  >(
    struct<MarketplaceConfigAccountData>(
      [
        ['discriminator', bytes({ size: 8 })],
        ['version', u8()],
        ['admin', publicKeySerializer()],
        ['feeCollector', publicKeySerializer()],
        ['protocolFeeBp', u16()],
        ['minBidIncrementBp', u16()],
        ['minListingDuration', u64()],
        ['maxListingDuration', u64()],
        ['bump', u8()],
        ['marketplaceAuthorityBump', u8()],
      ],
      { description: 'MarketplaceConfigAccountData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([169, 22, 247, 131, 182, 200, 81, 124]),
    })
  ) as Serializer<
    MarketplaceConfigAccountDataArgs,
    MarketplaceConfigAccountData
  >;
}

export function deserializeMarketplaceConfig(
  rawAccount: RpcAccount
): MarketplaceConfig {
  return deserializeAccount(
    rawAccount,
    getMarketplaceConfigAccountDataSerializer()
  );
}

export async function fetchMarketplaceConfig(
  context: Pick<Context, 'rpc'>,
  publicKey: PublicKey | Pda,
  options?: RpcGetAccountOptions
): Promise<MarketplaceConfig> {
  const maybeAccount = await context.rpc.getAccount(
    toPublicKey(publicKey, false),
    options
  );
  assertAccountExists(maybeAccount, 'MarketplaceConfig');
  return deserializeMarketplaceConfig(maybeAccount);
}

export async function safeFetchMarketplaceConfig(
  context: Pick<Context, 'rpc'>,
  publicKey: PublicKey | Pda,
  options?: RpcGetAccountOptions
): Promise<MarketplaceConfig | null> {
  const maybeAccount = await context.rpc.getAccount(
    toPublicKey(publicKey, false),
    options
  );
  return maybeAccount.exists
    ? deserializeMarketplaceConfig(maybeAccount)
    : null;
}

export async function fetchAllMarketplaceConfig(
  context: Pick<Context, 'rpc'>,
  publicKeys: Array<PublicKey | Pda>,
  options?: RpcGetAccountsOptions
): Promise<MarketplaceConfig[]> {
  const maybeAccounts = await context.rpc.getAccounts(
    publicKeys.map((key) => toPublicKey(key, false)),
    options
  );
  return maybeAccounts.map((maybeAccount) => {
    assertAccountExists(maybeAccount, 'MarketplaceConfig');
    return deserializeMarketplaceConfig(maybeAccount);
  });
}

export async function safeFetchAllMarketplaceConfig(
  context: Pick<Context, 'rpc'>,
  publicKeys: Array<PublicKey | Pda>,
  options?: RpcGetAccountsOptions
): Promise<MarketplaceConfig[]> {
  const maybeAccounts = await context.rpc.getAccounts(
    publicKeys.map((key) => toPublicKey(key, false)),
    options
  );
  return maybeAccounts
    .filter((maybeAccount) => maybeAccount.exists)
    .map((maybeAccount) =>
      deserializeMarketplaceConfig(maybeAccount as RpcAccount)
    );
}

export function getMarketplaceConfigGpaBuilder(
  context: Pick<Context, 'rpc' | 'programs'>
) {
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );
  return gpaBuilder(context, programId)
    .registerFields<{
      discriminator: Uint8Array;
      version: number;
      admin: PublicKey;
      feeCollector: PublicKey;
      protocolFeeBp: number;
      minBidIncrementBp: number;
      minListingDuration: number | bigint;
      maxListingDuration: number | bigint;
      bump: number;
      marketplaceAuthorityBump: number;
    }>({
      discriminator: [0, bytes({ size: 8 })],
      version: [8, u8()],
      admin: [9, publicKeySerializer()],
      feeCollector: [41, publicKeySerializer()],
      protocolFeeBp: [73, u16()],
      minBidIncrementBp: [75, u16()],
      minListingDuration: [77, u64()],
      maxListingDuration: [85, u64()],
      bump: [93, u8()],
      marketplaceAuthorityBump: [94, u8()],
    })
    .deserializeUsing<MarketplaceConfig>((account) =>
      deserializeMarketplaceConfig(account)
    )
    .whereField(
      'discriminator',
      new Uint8Array([169, 22, 247, 131, 182, 200, 81, 124])
    );
}

export function getMarketplaceConfigSize(): number {
  return 95;
}
