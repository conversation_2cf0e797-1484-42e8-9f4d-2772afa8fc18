/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Account,
  Context,
  Option,
  OptionOrNullable,
  Pda,
  PublicKey,
  RpcAccount,
  RpcGetAccountOptions,
  RpcGetAccountsOptions,
  assertAccountExists,
  deserializeAccount,
  gpaBuilder,
  publicKey as toPublicKey,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bool,
  bytes,
  i64,
  mapSerializer,
  option,
  publicKey as publicKeySerializer,
  struct,
  u64,
  u8,
} from '@metaplex-foundation/umi/serializers';
import {
  ListingType,
  ListingTypeArgs,
  getListingTypeSerializer,
} from '../types';

export type Listing = Account<ListingAccountData>;

export type ListingAccountData = {
  discriminator: Uint8Array;
  /** Version for future upgrades */
  version: number;
  /** Type of listing and link to Auction if applicable */
  listingType: ListingType;
  /** Seller of the NFT */
  seller: PublicKey;
  /** NFT asset being sold */
  asset: PublicKey;
  /** Price in lamports */
  price: bigint;
  /** Optional SPL token for payment (None = SOL) */
  currency: Option<PublicKey>;
  /** Whether the listing is active */
  isActive: boolean;
  /** Timestamp when the listing was created */
  createdAt: bigint;
  /** Timestamp when the listing was last updated (e.g., cancelled) */
  updatedAt: bigint;
  /** When the listing ends (for auctions) */
  endsAt: Option<bigint>;
  /** PDA bump */
  bump: number;
};

export type ListingAccountDataArgs = {
  /** Version for future upgrades */
  version: number;
  /** Type of listing and link to Auction if applicable */
  listingType: ListingTypeArgs;
  /** Seller of the NFT */
  seller: PublicKey;
  /** NFT asset being sold */
  asset: PublicKey;
  /** Price in lamports */
  price: number | bigint;
  /** Optional SPL token for payment (None = SOL) */
  currency: OptionOrNullable<PublicKey>;
  /** Whether the listing is active */
  isActive: boolean;
  /** Timestamp when the listing was created */
  createdAt: number | bigint;
  /** Timestamp when the listing was last updated (e.g., cancelled) */
  updatedAt: number | bigint;
  /** When the listing ends (for auctions) */
  endsAt: OptionOrNullable<number | bigint>;
  /** PDA bump */
  bump: number;
};

export function getListingAccountDataSerializer(): Serializer<
  ListingAccountDataArgs,
  ListingAccountData
> {
  return mapSerializer<ListingAccountDataArgs, any, ListingAccountData>(
    struct<ListingAccountData>(
      [
        ['discriminator', bytes({ size: 8 })],
        ['version', u8()],
        ['listingType', getListingTypeSerializer()],
        ['seller', publicKeySerializer()],
        ['asset', publicKeySerializer()],
        ['price', u64()],
        ['currency', option(publicKeySerializer())],
        ['isActive', bool()],
        ['createdAt', i64()],
        ['updatedAt', i64()],
        ['endsAt', option(i64())],
        ['bump', u8()],
      ],
      { description: 'ListingAccountData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([218, 32, 50, 73, 43, 134, 26, 58]),
    })
  ) as Serializer<ListingAccountDataArgs, ListingAccountData>;
}

export function deserializeListing(rawAccount: RpcAccount): Listing {
  return deserializeAccount(rawAccount, getListingAccountDataSerializer());
}

export async function fetchListing(
  context: Pick<Context, 'rpc'>,
  publicKey: PublicKey | Pda,
  options?: RpcGetAccountOptions
): Promise<Listing> {
  const maybeAccount = await context.rpc.getAccount(
    toPublicKey(publicKey, false),
    options
  );
  assertAccountExists(maybeAccount, 'Listing');
  return deserializeListing(maybeAccount);
}

export async function safeFetchListing(
  context: Pick<Context, 'rpc'>,
  publicKey: PublicKey | Pda,
  options?: RpcGetAccountOptions
): Promise<Listing | null> {
  const maybeAccount = await context.rpc.getAccount(
    toPublicKey(publicKey, false),
    options
  );
  return maybeAccount.exists ? deserializeListing(maybeAccount) : null;
}

export async function fetchAllListing(
  context: Pick<Context, 'rpc'>,
  publicKeys: Array<PublicKey | Pda>,
  options?: RpcGetAccountsOptions
): Promise<Listing[]> {
  const maybeAccounts = await context.rpc.getAccounts(
    publicKeys.map((key) => toPublicKey(key, false)),
    options
  );
  return maybeAccounts.map((maybeAccount) => {
    assertAccountExists(maybeAccount, 'Listing');
    return deserializeListing(maybeAccount);
  });
}

export async function safeFetchAllListing(
  context: Pick<Context, 'rpc'>,
  publicKeys: Array<PublicKey | Pda>,
  options?: RpcGetAccountsOptions
): Promise<Listing[]> {
  const maybeAccounts = await context.rpc.getAccounts(
    publicKeys.map((key) => toPublicKey(key, false)),
    options
  );
  return maybeAccounts
    .filter((maybeAccount) => maybeAccount.exists)
    .map((maybeAccount) => deserializeListing(maybeAccount as RpcAccount));
}

export function getListingGpaBuilder(
  context: Pick<Context, 'rpc' | 'programs'>
) {
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );
  return gpaBuilder(context, programId)
    .registerFields<{
      discriminator: Uint8Array;
      version: number;
      listingType: ListingTypeArgs;
      seller: PublicKey;
      asset: PublicKey;
      price: number | bigint;
      currency: OptionOrNullable<PublicKey>;
      isActive: boolean;
      createdAt: number | bigint;
      updatedAt: number | bigint;
      endsAt: OptionOrNullable<number | bigint>;
      bump: number;
    }>({
      discriminator: [0, bytes({ size: 8 })],
      version: [8, u8()],
      listingType: [9, getListingTypeSerializer()],
      seller: [null, publicKeySerializer()],
      asset: [null, publicKeySerializer()],
      price: [null, u64()],
      currency: [null, option(publicKeySerializer())],
      isActive: [null, bool()],
      createdAt: [null, i64()],
      updatedAt: [null, i64()],
      endsAt: [null, option(i64())],
      bump: [null, u8()],
    })
    .deserializeUsing<Listing>((account) => deserializeListing(account))
    .whereField(
      'discriminator',
      new Uint8Array([218, 32, 50, 73, 43, 134, 26, 58])
    );
}
