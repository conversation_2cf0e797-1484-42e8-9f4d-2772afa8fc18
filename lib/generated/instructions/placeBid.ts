/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Context,
  Pda,
  PublicKey,
  Signer,
  TransactionBuilder,
  transactionBuilder,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  mapSerializer,
  publicKey as publicKeySerializer,
  struct,
  u64,
} from '@metaplex-foundation/umi/serializers';
import {
  ResolvedAccount,
  ResolvedAccountsWithIndices,
  expectPublicKey,
  getAccountMetasAndSigners,
} from '../shared';

// Accounts.
export type PlaceBidInstructionAccounts = {
  /** Bidder placing the bid */
  bidder: Signer;
  asset: PublicKey | Pda;
  /** Auction Listing being bid on */
  listing: PublicKey | Pda;
  /** Auction-specific state to update */
  auction?: PublicKey | Pda;
  /** Needs to be mutable to receive funds. Required if auction.highest_bidder is Some. */
  previousBidder?: PublicKey | Pda;
  escrow?: PublicKey | Pda;
  /** Marketplace configuration (needed for min bid increment) */
  marketplaceConfig?: PublicKey | Pda;
  /** System program */
  systemProgram?: PublicKey | Pda;
  /** Clock for timestamps */
  clock?: PublicKey | Pda;
};

// Data.
export type PlaceBidInstructionData = {
  discriminator: Uint8Array;
  bidAmount: bigint;
};

export type PlaceBidInstructionDataArgs = { bidAmount: number | bigint };

export function getPlaceBidInstructionDataSerializer(): Serializer<
  PlaceBidInstructionDataArgs,
  PlaceBidInstructionData
> {
  return mapSerializer<
    PlaceBidInstructionDataArgs,
    any,
    PlaceBidInstructionData
  >(
    struct<PlaceBidInstructionData>(
      [
        ['discriminator', bytes({ size: 8 })],
        ['bidAmount', u64()],
      ],
      { description: 'PlaceBidInstructionData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([238, 77, 148, 91, 200, 151, 92, 146]),
    })
  ) as Serializer<PlaceBidInstructionDataArgs, PlaceBidInstructionData>;
}

// Args.
export type PlaceBidInstructionArgs = PlaceBidInstructionDataArgs;

// Instruction.
export function placeBid(
  context: Pick<Context, 'eddsa' | 'programs'>,
  input: PlaceBidInstructionAccounts & PlaceBidInstructionArgs
): TransactionBuilder {
  // Program ID.
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );

  // Accounts.
  const resolvedAccounts = {
    bidder: {
      index: 0,
      isWritable: true as boolean,
      value: input.bidder ?? null,
    },
    asset: {
      index: 1,
      isWritable: true as boolean,
      value: input.asset ?? null,
    },
    listing: {
      index: 2,
      isWritable: true as boolean,
      value: input.listing ?? null,
    },
    auction: {
      index: 3,
      isWritable: true as boolean,
      value: input.auction ?? null,
    },
    previousBidder: {
      index: 4,
      isWritable: true as boolean,
      value: input.previousBidder ?? null,
    },
    escrow: {
      index: 5,
      isWritable: true as boolean,
      value: input.escrow ?? null,
    },
    marketplaceConfig: {
      index: 6,
      isWritable: false as boolean,
      value: input.marketplaceConfig ?? null,
    },
    systemProgram: {
      index: 7,
      isWritable: false as boolean,
      value: input.systemProgram ?? null,
    },
    clock: {
      index: 8,
      isWritable: false as boolean,
      value: input.clock ?? null,
    },
  } satisfies ResolvedAccountsWithIndices;

  // Arguments.
  const resolvedArgs: PlaceBidInstructionArgs = { ...input };

  // Default values.
  if (!resolvedAccounts.auction.value) {
    resolvedAccounts.auction.value = context.eddsa.findPda(programId, [
      bytes().serialize(new Uint8Array([97, 117, 99, 116, 105, 111, 110])),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.listing.value)
      ),
    ]);
  }
  if (!resolvedAccounts.escrow.value) {
    resolvedAccounts.escrow.value = context.eddsa.findPda(programId, [
      bytes().serialize(
        new Uint8Array([
          101, 115, 99, 114, 111, 119, 45, 112, 97, 121, 109, 101, 110, 116,
        ])
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.listing.value)
      ),
    ]);
  }
  if (!resolvedAccounts.marketplaceConfig.value) {
    resolvedAccounts.marketplaceConfig.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 99, 111,
            110, 102, 105, 103,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.systemProgram.value) {
    resolvedAccounts.systemProgram.value = context.programs.getPublicKey(
      'systemProgram',
      '11111111111111111111111111111111'
    );
    resolvedAccounts.systemProgram.isWritable = false;
  }
  if (!resolvedAccounts.clock.value) {
    resolvedAccounts.clock.value = context.programs.getPublicKey(
      'clock',
      'SysvarC1ock11111111111111111111111111111111'
    );
    resolvedAccounts.clock.isWritable = false;
  }

  // Accounts in order.
  const orderedAccounts: ResolvedAccount[] = Object.values(
    resolvedAccounts
  ).sort((a, b) => a.index - b.index);

  // Keys and Signers.
  const [keys, signers] = getAccountMetasAndSigners(
    orderedAccounts,
    'programId',
    programId
  );

  // Data.
  const data = getPlaceBidInstructionDataSerializer().serialize(
    resolvedArgs as PlaceBidInstructionDataArgs
  );

  // Bytes Created On Chain.
  const bytesCreatedOnChain = 0;

  return transactionBuilder([
    { instruction: { keys, programId, data }, signers, bytesCreatedOnChain },
  ]);
}
