/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Context,
  Pda,
  PublicKey,
  Signer,
  TransactionBuilder,
  transactionBuilder,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  mapSerializer,
  publicKey as publicKeySerializer,
  struct,
} from '@metaplex-foundation/umi/serializers';
import {
  ResolvedAccount,
  ResolvedAccountsWithIndices,
  expectPublicKey,
  getAccountMetasAndSigners,
} from '../shared';

// Accounts.
export type CancelAuctionListingInstructionAccounts = {
  /** Seller who created the listing */
  seller: Signer;
  asset: PublicKey | Pda;
  collection?: PublicKey | Pda;
  /** Listing to cancel */
  listing?: PublicKey | Pda;
  /** Auction state account */
  auction?: PublicKey | Pda;
  /**
   * Escrow account holding the highest bid (if auction)
   * Closing/refund logic handled in instruction handler.
   */

  escrow?: PublicKey | Pda;
  /** Passed in by the client if auction exists and highest_bidder is Some. */
  highestBidder?: PublicKey | Pda;
  /** Marketplace configuration */
  marketplaceConfig?: PublicKey | Pda;
  marketplaceAuthority?: PublicKey | Pda;
  /** MPL Core program */
  mplCoreProgram?: PublicKey | Pda;
  /** System program */
  systemProgram?: PublicKey | Pda;
  /** Clock for timestamps */
  clock?: PublicKey | Pda;
};

// Data.
export type CancelAuctionListingInstructionData = { discriminator: Uint8Array };

export type CancelAuctionListingInstructionDataArgs = {};

export function getCancelAuctionListingInstructionDataSerializer(): Serializer<
  CancelAuctionListingInstructionDataArgs,
  CancelAuctionListingInstructionData
> {
  return mapSerializer<
    CancelAuctionListingInstructionDataArgs,
    any,
    CancelAuctionListingInstructionData
  >(
    struct<CancelAuctionListingInstructionData>(
      [['discriminator', bytes({ size: 8 })]],
      { description: 'CancelAuctionListingInstructionData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([53, 243, 11, 87, 55, 184, 226, 0]),
    })
  ) as Serializer<
    CancelAuctionListingInstructionDataArgs,
    CancelAuctionListingInstructionData
  >;
}

// Instruction.
export function cancelAuctionListing(
  context: Pick<Context, 'eddsa' | 'programs'>,
  input: CancelAuctionListingInstructionAccounts
): TransactionBuilder {
  // Program ID.
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );

  // Accounts.
  const resolvedAccounts = {
    seller: {
      index: 0,
      isWritable: true as boolean,
      value: input.seller ?? null,
    },
    asset: {
      index: 1,
      isWritable: true as boolean,
      value: input.asset ?? null,
    },
    collection: {
      index: 2,
      isWritable: true as boolean,
      value: input.collection ?? null,
    },
    listing: {
      index: 3,
      isWritable: true as boolean,
      value: input.listing ?? null,
    },
    auction: {
      index: 4,
      isWritable: true as boolean,
      value: input.auction ?? null,
    },
    escrow: {
      index: 5,
      isWritable: true as boolean,
      value: input.escrow ?? null,
    },
    highestBidder: {
      index: 6,
      isWritable: true as boolean,
      value: input.highestBidder ?? null,
    },
    marketplaceConfig: {
      index: 7,
      isWritable: false as boolean,
      value: input.marketplaceConfig ?? null,
    },
    marketplaceAuthority: {
      index: 8,
      isWritable: false as boolean,
      value: input.marketplaceAuthority ?? null,
    },
    mplCoreProgram: {
      index: 9,
      isWritable: false as boolean,
      value: input.mplCoreProgram ?? null,
    },
    systemProgram: {
      index: 10,
      isWritable: false as boolean,
      value: input.systemProgram ?? null,
    },
    clock: {
      index: 11,
      isWritable: false as boolean,
      value: input.clock ?? null,
    },
  } satisfies ResolvedAccountsWithIndices;

  // Default values.
  if (!resolvedAccounts.listing.value) {
    resolvedAccounts.listing.value = context.eddsa.findPda(programId, [
      bytes().serialize(new Uint8Array([108, 105, 115, 116, 105, 110, 103])),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.asset.value)
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.seller.value)
      ),
    ]);
  }
  if (!resolvedAccounts.auction.value) {
    resolvedAccounts.auction.value = context.eddsa.findPda(programId, [
      bytes().serialize(new Uint8Array([97, 117, 99, 116, 105, 111, 110])),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.listing.value)
      ),
    ]);
  }
  if (!resolvedAccounts.escrow.value) {
    resolvedAccounts.escrow.value = context.eddsa.findPda(programId, [
      bytes().serialize(
        new Uint8Array([
          101, 115, 99, 114, 111, 119, 45, 112, 97, 121, 109, 101, 110, 116,
        ])
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.listing.value)
      ),
    ]);
  }
  if (!resolvedAccounts.marketplaceConfig.value) {
    resolvedAccounts.marketplaceConfig.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 99, 111,
            110, 102, 105, 103,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.marketplaceAuthority.value) {
    resolvedAccounts.marketplaceAuthority.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 97, 117,
            116, 104, 111, 114, 105, 116, 121,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.mplCoreProgram.value) {
    resolvedAccounts.mplCoreProgram.value = context.programs.getPublicKey(
      'mplCoreProgram',
      'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
    );
    resolvedAccounts.mplCoreProgram.isWritable = false;
  }
  if (!resolvedAccounts.systemProgram.value) {
    resolvedAccounts.systemProgram.value = context.programs.getPublicKey(
      'systemProgram',
      '11111111111111111111111111111111'
    );
    resolvedAccounts.systemProgram.isWritable = false;
  }
  if (!resolvedAccounts.clock.value) {
    resolvedAccounts.clock.value = context.programs.getPublicKey(
      'clock',
      'SysvarC1ock11111111111111111111111111111111'
    );
    resolvedAccounts.clock.isWritable = false;
  }

  // Accounts in order.
  const orderedAccounts: ResolvedAccount[] = Object.values(
    resolvedAccounts
  ).sort((a, b) => a.index - b.index);

  // Keys and Signers.
  const [keys, signers] = getAccountMetasAndSigners(
    orderedAccounts,
    'programId',
    programId
  );

  // Data.
  const data = getCancelAuctionListingInstructionDataSerializer().serialize({});

  // Bytes Created On Chain.
  const bytesCreatedOnChain = 0;

  return transactionBuilder([
    { instruction: { keys, programId, data }, signers, bytesCreatedOnChain },
  ]);
}
