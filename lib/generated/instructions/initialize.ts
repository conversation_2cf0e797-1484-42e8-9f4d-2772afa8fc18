/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Context,
  Pda,
  <PERSON>Key,
  Signer,
  TransactionBuilder,
  transactionBuilder,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  mapSerializer,
  struct,
  u16,
  u64,
} from '@metaplex-foundation/umi/serializers';
import {
  ResolvedAccount,
  ResolvedAccountsWithIndices,
  getAccountMetasAndSigners,
} from '../shared';

// Accounts.
export type InitializeInstructionAccounts = {
  /** Payer for the transaction */
  payer?: Signer;
  /** Admin that can update the marketplace configuration */
  admin: Signer;
  /** Account that will receive marketplace fees */
  feeCollector: PublicKey | Pda;
  /** Marketplace configuration account */
  marketplaceConfig?: PublicKey | Pda;
  /** Marketplace authority account */
  marketplaceAuthority?: PublicKey | Pda;
  /** System program */
  systemProgram?: PublicKey | Pda;
};

// Data.
export type InitializeInstructionData = {
  discriminator: Uint8Array;
  /** Protocol fee in basis points (e.g., 250 = 2.5%) */
  protocolFeeBp: number;
  /** Minimum bid increment in basis points (e.g., 500 = 5%) */
  minBidIncrementBp: number;
  /** Minimum listing duration in seconds */
  minListingDuration: bigint;
  /** Maximum listing duration in seconds */
  maxListingDuration: bigint;
};

export type InitializeInstructionDataArgs = {
  /** Protocol fee in basis points (e.g., 250 = 2.5%) */
  protocolFeeBp: number;
  /** Minimum bid increment in basis points (e.g., 500 = 5%) */
  minBidIncrementBp: number;
  /** Minimum listing duration in seconds */
  minListingDuration: number | bigint;
  /** Maximum listing duration in seconds */
  maxListingDuration: number | bigint;
};

export function getInitializeInstructionDataSerializer(): Serializer<
  InitializeInstructionDataArgs,
  InitializeInstructionData
> {
  return mapSerializer<
    InitializeInstructionDataArgs,
    any,
    InitializeInstructionData
  >(
    struct<InitializeInstructionData>(
      [
        ['discriminator', bytes({ size: 8 })],
        ['protocolFeeBp', u16()],
        ['minBidIncrementBp', u16()],
        ['minListingDuration', u64()],
        ['maxListingDuration', u64()],
      ],
      { description: 'InitializeInstructionData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([175, 175, 109, 31, 13, 152, 155, 237]),
    })
  ) as Serializer<InitializeInstructionDataArgs, InitializeInstructionData>;
}

// Args.
export type InitializeInstructionArgs = InitializeInstructionDataArgs;

// Instruction.
export function initialize(
  context: Pick<Context, 'eddsa' | 'payer' | 'programs'>,
  input: InitializeInstructionAccounts & InitializeInstructionArgs
): TransactionBuilder {
  // Program ID.
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );

  // Accounts.
  const resolvedAccounts = {
    payer: {
      index: 0,
      isWritable: true as boolean,
      value: input.payer ?? null,
    },
    admin: {
      index: 1,
      isWritable: true as boolean,
      value: input.admin ?? null,
    },
    feeCollector: {
      index: 2,
      isWritable: false as boolean,
      value: input.feeCollector ?? null,
    },
    marketplaceConfig: {
      index: 3,
      isWritable: true as boolean,
      value: input.marketplaceConfig ?? null,
    },
    marketplaceAuthority: {
      index: 4,
      isWritable: false as boolean,
      value: input.marketplaceAuthority ?? null,
    },
    systemProgram: {
      index: 5,
      isWritable: false as boolean,
      value: input.systemProgram ?? null,
    },
  } satisfies ResolvedAccountsWithIndices;

  // Arguments.
  const resolvedArgs: InitializeInstructionArgs = { ...input };

  // Default values.
  if (!resolvedAccounts.payer.value) {
    resolvedAccounts.payer.value = context.payer;
  }
  if (!resolvedAccounts.marketplaceConfig.value) {
    resolvedAccounts.marketplaceConfig.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 99, 111,
            110, 102, 105, 103,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.marketplaceAuthority.value) {
    resolvedAccounts.marketplaceAuthority.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 97, 117,
            116, 104, 111, 114, 105, 116, 121,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.systemProgram.value) {
    resolvedAccounts.systemProgram.value = context.programs.getPublicKey(
      'systemProgram',
      '11111111111111111111111111111111'
    );
    resolvedAccounts.systemProgram.isWritable = false;
  }

  // Accounts in order.
  const orderedAccounts: ResolvedAccount[] = Object.values(
    resolvedAccounts
  ).sort((a, b) => a.index - b.index);

  // Keys and Signers.
  const [keys, signers] = getAccountMetasAndSigners(
    orderedAccounts,
    'programId',
    programId
  );

  // Data.
  const data = getInitializeInstructionDataSerializer().serialize(
    resolvedArgs as InitializeInstructionDataArgs
  );

  // Bytes Created On Chain.
  const bytesCreatedOnChain = 0;

  return transactionBuilder([
    { instruction: { keys, programId, data }, signers, bytesCreatedOnChain },
  ]);
}
