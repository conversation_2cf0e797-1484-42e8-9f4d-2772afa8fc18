/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Context,
  Pda,
  PublicKey,
  Signer,
  TransactionBuilder,
  transactionBuilder,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  mapSerializer,
  publicKey as publicKeySerializer,
  struct,
} from '@metaplex-foundation/umi/serializers';
import {
  ResolvedAccount,
  ResolvedAccountsWithIndices,
  expectPublicKey,
  getAccountMetasAndSigners,
} from '../shared';

// Accounts.
export type AcceptOfferInstructionAccounts = {
  /** The seller accepting the offer */
  seller: Signer;
  /** The buyer who made the offer */
  buyer: PublicKey | Pda;
  /** The NFT asset being sold */
  asset: PublicKey | Pda;
  collection?: PublicKey | Pda;
  /** The offer being accepted */
  offer?: PublicKey | Pda;
  /** Escrow account holding the payment */
  escrow?: PublicKey | Pda;
  /** Marketplace configuration */
  marketplaceConfig?: PublicKey | Pda;
  /** Fee collector account */
  feeCollector: PublicKey | Pda;
  /** Marketplace authority PDA */
  marketplaceAuthority?: PublicKey | Pda;
  mplCoreProgram?: PublicKey | Pda;
  /** System program for transfers */
  systemProgram?: PublicKey | Pda;
  /** Clock for timestamp validation */
  clock?: PublicKey | Pda;
};

// Data.
export type AcceptOfferInstructionData = { discriminator: Uint8Array };

export type AcceptOfferInstructionDataArgs = {};

export function getAcceptOfferInstructionDataSerializer(): Serializer<
  AcceptOfferInstructionDataArgs,
  AcceptOfferInstructionData
> {
  return mapSerializer<
    AcceptOfferInstructionDataArgs,
    any,
    AcceptOfferInstructionData
  >(
    struct<AcceptOfferInstructionData>(
      [['discriminator', bytes({ size: 8 })]],
      { description: 'AcceptOfferInstructionData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([227, 82, 234, 131, 1, 18, 48, 2]),
    })
  ) as Serializer<AcceptOfferInstructionDataArgs, AcceptOfferInstructionData>;
}

// Instruction.
export function acceptOffer(
  context: Pick<Context, 'eddsa' | 'programs'>,
  input: AcceptOfferInstructionAccounts
): TransactionBuilder {
  // Program ID.
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );

  // Accounts.
  const resolvedAccounts = {
    seller: {
      index: 0,
      isWritable: true as boolean,
      value: input.seller ?? null,
    },
    buyer: {
      index: 1,
      isWritable: true as boolean,
      value: input.buyer ?? null,
    },
    asset: {
      index: 2,
      isWritable: true as boolean,
      value: input.asset ?? null,
    },
    collection: {
      index: 3,
      isWritable: false as boolean,
      value: input.collection ?? null,
    },
    offer: {
      index: 4,
      isWritable: true as boolean,
      value: input.offer ?? null,
    },
    escrow: {
      index: 5,
      isWritable: true as boolean,
      value: input.escrow ?? null,
    },
    marketplaceConfig: {
      index: 6,
      isWritable: false as boolean,
      value: input.marketplaceConfig ?? null,
    },
    feeCollector: {
      index: 7,
      isWritable: true as boolean,
      value: input.feeCollector ?? null,
    },
    marketplaceAuthority: {
      index: 8,
      isWritable: false as boolean,
      value: input.marketplaceAuthority ?? null,
    },
    mplCoreProgram: {
      index: 9,
      isWritable: false as boolean,
      value: input.mplCoreProgram ?? null,
    },
    systemProgram: {
      index: 10,
      isWritable: false as boolean,
      value: input.systemProgram ?? null,
    },
    clock: {
      index: 11,
      isWritable: false as boolean,
      value: input.clock ?? null,
    },
  } satisfies ResolvedAccountsWithIndices;

  // Default values.
  if (!resolvedAccounts.offer.value) {
    resolvedAccounts.offer.value = context.eddsa.findPda(programId, [
      bytes().serialize(new Uint8Array([111, 102, 102, 101, 114])),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.buyer.value)
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.asset.value)
      ),
    ]);
  }
  if (!resolvedAccounts.escrow.value) {
    resolvedAccounts.escrow.value = context.eddsa.findPda(programId, [
      bytes().serialize(
        new Uint8Array([
          101, 115, 99, 114, 111, 119, 45, 112, 97, 121, 109, 101, 110, 116,
        ])
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.offer.value)
      ),
    ]);
  }
  if (!resolvedAccounts.marketplaceConfig.value) {
    resolvedAccounts.marketplaceConfig.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 99, 111,
            110, 102, 105, 103,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.marketplaceAuthority.value) {
    resolvedAccounts.marketplaceAuthority.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 97, 117,
            116, 104, 111, 114, 105, 116, 121,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.mplCoreProgram.value) {
    resolvedAccounts.mplCoreProgram.value = context.programs.getPublicKey(
      'mplCoreProgram',
      'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
    );
    resolvedAccounts.mplCoreProgram.isWritable = false;
  }
  if (!resolvedAccounts.systemProgram.value) {
    resolvedAccounts.systemProgram.value = context.programs.getPublicKey(
      'systemProgram',
      '11111111111111111111111111111111'
    );
    resolvedAccounts.systemProgram.isWritable = false;
  }
  if (!resolvedAccounts.clock.value) {
    resolvedAccounts.clock.value = context.programs.getPublicKey(
      'clock',
      'SysvarC1ock11111111111111111111111111111111'
    );
    resolvedAccounts.clock.isWritable = false;
  }

  // Accounts in order.
  const orderedAccounts: ResolvedAccount[] = Object.values(
    resolvedAccounts
  ).sort((a, b) => a.index - b.index);

  // Keys and Signers.
  const [keys, signers] = getAccountMetasAndSigners(
    orderedAccounts,
    'programId',
    programId
  );

  // Data.
  const data = getAcceptOfferInstructionDataSerializer().serialize({});

  // Bytes Created On Chain.
  const bytesCreatedOnChain = 0;

  return transactionBuilder([
    { instruction: { keys, programId, data }, signers, bytesCreatedOnChain },
  ]);
}
