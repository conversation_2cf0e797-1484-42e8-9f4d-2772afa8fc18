/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Context,
  Pda,
  PublicKey,
  Signer,
  TransactionBuilder,
  transactionBuilder,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  i64,
  mapSerializer,
  publicKey as publicKeySerializer,
  struct,
  u64,
} from '@metaplex-foundation/umi/serializers';
import {
  ResolvedAccount,
  ResolvedAccountsWithIndices,
  expectPublicKey,
  getAccountMetasAndSigners,
} from '../shared';

// Accounts.
export type CreateOfferInstructionAccounts = {
  /** The buyer creating the offer */
  buyer: Signer;
  /** The NFT asset being offered on */
  asset: PublicKey | Pda;
  /** The offer account to be created */
  offer?: PublicKey | Pda;
  /** Escrow account to hold the payment */
  escrow?: PublicKey | Pda;
  /** Marketplace config for validation rules */
  marketplaceConfig?: PublicKey | Pda;
  /** System program for CPI */
  systemProgram?: PublicKey | Pda;
  /** Clock for timestamp validation */
  clock?: PublicKey | Pda;
};

// Data.
export type CreateOfferInstructionData = {
  discriminator: Uint8Array;
  price: bigint;
  expiresIn: bigint;
};

export type CreateOfferInstructionDataArgs = {
  price: number | bigint;
  expiresIn: number | bigint;
};

export function getCreateOfferInstructionDataSerializer(): Serializer<
  CreateOfferInstructionDataArgs,
  CreateOfferInstructionData
> {
  return mapSerializer<
    CreateOfferInstructionDataArgs,
    any,
    CreateOfferInstructionData
  >(
    struct<CreateOfferInstructionData>(
      [
        ['discriminator', bytes({ size: 8 })],
        ['price', u64()],
        ['expiresIn', i64()],
      ],
      { description: 'CreateOfferInstructionData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([237, 233, 192, 168, 248, 7, 249, 241]),
    })
  ) as Serializer<CreateOfferInstructionDataArgs, CreateOfferInstructionData>;
}

// Args.
export type CreateOfferInstructionArgs = CreateOfferInstructionDataArgs;

// Instruction.
export function createOffer(
  context: Pick<Context, 'eddsa' | 'programs'>,
  input: CreateOfferInstructionAccounts & CreateOfferInstructionArgs
): TransactionBuilder {
  // Program ID.
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );

  // Accounts.
  const resolvedAccounts = {
    buyer: {
      index: 0,
      isWritable: true as boolean,
      value: input.buyer ?? null,
    },
    asset: {
      index: 1,
      isWritable: false as boolean,
      value: input.asset ?? null,
    },
    offer: {
      index: 2,
      isWritable: true as boolean,
      value: input.offer ?? null,
    },
    escrow: {
      index: 3,
      isWritable: true as boolean,
      value: input.escrow ?? null,
    },
    marketplaceConfig: {
      index: 4,
      isWritable: false as boolean,
      value: input.marketplaceConfig ?? null,
    },
    systemProgram: {
      index: 5,
      isWritable: false as boolean,
      value: input.systemProgram ?? null,
    },
    clock: {
      index: 6,
      isWritable: false as boolean,
      value: input.clock ?? null,
    },
  } satisfies ResolvedAccountsWithIndices;

  // Arguments.
  const resolvedArgs: CreateOfferInstructionArgs = { ...input };

  // Default values.
  if (!resolvedAccounts.offer.value) {
    resolvedAccounts.offer.value = context.eddsa.findPda(programId, [
      bytes().serialize(new Uint8Array([111, 102, 102, 101, 114])),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.buyer.value)
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.asset.value)
      ),
    ]);
  }
  if (!resolvedAccounts.escrow.value) {
    resolvedAccounts.escrow.value = context.eddsa.findPda(programId, [
      bytes().serialize(
        new Uint8Array([
          101, 115, 99, 114, 111, 119, 45, 112, 97, 121, 109, 101, 110, 116,
        ])
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.offer.value)
      ),
    ]);
  }
  if (!resolvedAccounts.marketplaceConfig.value) {
    resolvedAccounts.marketplaceConfig.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 99, 111,
            110, 102, 105, 103,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.systemProgram.value) {
    resolvedAccounts.systemProgram.value = context.programs.getPublicKey(
      'systemProgram',
      '11111111111111111111111111111111'
    );
    resolvedAccounts.systemProgram.isWritable = false;
  }
  if (!resolvedAccounts.clock.value) {
    resolvedAccounts.clock.value = context.programs.getPublicKey(
      'clock',
      'SysvarC1ock11111111111111111111111111111111'
    );
    resolvedAccounts.clock.isWritable = false;
  }

  // Accounts in order.
  const orderedAccounts: ResolvedAccount[] = Object.values(
    resolvedAccounts
  ).sort((a, b) => a.index - b.index);

  // Keys and Signers.
  const [keys, signers] = getAccountMetasAndSigners(
    orderedAccounts,
    'programId',
    programId
  );

  // Data.
  const data = getCreateOfferInstructionDataSerializer().serialize(
    resolvedArgs as CreateOfferInstructionDataArgs
  );

  // Bytes Created On Chain.
  const bytesCreatedOnChain = 0;

  return transactionBuilder([
    { instruction: { keys, programId, data }, signers, bytesCreatedOnChain },
  ]);
}
