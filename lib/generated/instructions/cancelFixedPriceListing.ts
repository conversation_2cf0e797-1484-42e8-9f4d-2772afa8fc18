/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Context,
  Pda,
  PublicKey,
  Signer,
  TransactionBuilder,
  transactionBuilder,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  mapSerializer,
  publicKey as publicKeySerializer,
  struct,
} from '@metaplex-foundation/umi/serializers';
import {
  ResolvedAccount,
  ResolvedAccountsWithIndices,
  expectPublicKey,
  getAccountMetasAndSigners,
} from '../shared';

// Accounts.
export type CancelFixedPriceListingInstructionAccounts = {
  /** Seller who created the listing */
  seller: Signer;
  asset: PublicKey | Pda;
  collection?: PublicKey | Pda;
  /** Listing to cancel */
  listing?: PublicKey | Pda;
  /** Marketplace configuration */
  marketplaceConfig?: PublicKey | Pda;
  marketplaceAuthority?: PublicKey | Pda;
  /** MPL Core program */
  mplCoreProgram?: PublicKey | Pda;
  /** System program */
  systemProgram?: PublicKey | Pda;
  /** Clock for timestamps */
  clock?: PublicKey | Pda;
};

// Data.
export type CancelFixedPriceListingInstructionData = {
  discriminator: Uint8Array;
};

export type CancelFixedPriceListingInstructionDataArgs = {};

export function getCancelFixedPriceListingInstructionDataSerializer(): Serializer<
  CancelFixedPriceListingInstructionDataArgs,
  CancelFixedPriceListingInstructionData
> {
  return mapSerializer<
    CancelFixedPriceListingInstructionDataArgs,
    any,
    CancelFixedPriceListingInstructionData
  >(
    struct<CancelFixedPriceListingInstructionData>(
      [['discriminator', bytes({ size: 8 })]],
      { description: 'CancelFixedPriceListingInstructionData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([4, 251, 13, 95, 189, 172, 90, 160]),
    })
  ) as Serializer<
    CancelFixedPriceListingInstructionDataArgs,
    CancelFixedPriceListingInstructionData
  >;
}

// Instruction.
export function cancelFixedPriceListing(
  context: Pick<Context, 'eddsa' | 'programs'>,
  input: CancelFixedPriceListingInstructionAccounts
): TransactionBuilder {
  // Program ID.
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );

  // Accounts.
  const resolvedAccounts = {
    seller: {
      index: 0,
      isWritable: true as boolean,
      value: input.seller ?? null,
    },
    asset: {
      index: 1,
      isWritable: true as boolean,
      value: input.asset ?? null,
    },
    collection: {
      index: 2,
      isWritable: true as boolean,
      value: input.collection ?? null,
    },
    listing: {
      index: 3,
      isWritable: true as boolean,
      value: input.listing ?? null,
    },
    marketplaceConfig: {
      index: 4,
      isWritable: false as boolean,
      value: input.marketplaceConfig ?? null,
    },
    marketplaceAuthority: {
      index: 5,
      isWritable: false as boolean,
      value: input.marketplaceAuthority ?? null,
    },
    mplCoreProgram: {
      index: 6,
      isWritable: false as boolean,
      value: input.mplCoreProgram ?? null,
    },
    systemProgram: {
      index: 7,
      isWritable: false as boolean,
      value: input.systemProgram ?? null,
    },
    clock: {
      index: 8,
      isWritable: false as boolean,
      value: input.clock ?? null,
    },
  } satisfies ResolvedAccountsWithIndices;

  // Default values.
  if (!resolvedAccounts.listing.value) {
    resolvedAccounts.listing.value = context.eddsa.findPda(programId, [
      bytes().serialize(new Uint8Array([108, 105, 115, 116, 105, 110, 103])),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.asset.value)
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.seller.value)
      ),
    ]);
  }
  if (!resolvedAccounts.marketplaceConfig.value) {
    resolvedAccounts.marketplaceConfig.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 99, 111,
            110, 102, 105, 103,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.marketplaceAuthority.value) {
    resolvedAccounts.marketplaceAuthority.value = context.eddsa.findPda(
      programId,
      [
        bytes().serialize(
          new Uint8Array([
            109, 97, 114, 107, 101, 116, 112, 108, 97, 99, 101, 45, 97, 117,
            116, 104, 111, 114, 105, 116, 121,
          ])
        ),
      ]
    );
  }
  if (!resolvedAccounts.mplCoreProgram.value) {
    resolvedAccounts.mplCoreProgram.value = context.programs.getPublicKey(
      'mplCoreProgram',
      'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
    );
    resolvedAccounts.mplCoreProgram.isWritable = false;
  }
  if (!resolvedAccounts.systemProgram.value) {
    resolvedAccounts.systemProgram.value = context.programs.getPublicKey(
      'systemProgram',
      '11111111111111111111111111111111'
    );
    resolvedAccounts.systemProgram.isWritable = false;
  }
  if (!resolvedAccounts.clock.value) {
    resolvedAccounts.clock.value = context.programs.getPublicKey(
      'clock',
      'SysvarC1ock11111111111111111111111111111111'
    );
    resolvedAccounts.clock.isWritable = false;
  }

  // Accounts in order.
  const orderedAccounts: ResolvedAccount[] = Object.values(
    resolvedAccounts
  ).sort((a, b) => a.index - b.index);

  // Keys and Signers.
  const [keys, signers] = getAccountMetasAndSigners(
    orderedAccounts,
    'programId',
    programId
  );

  // Data.
  const data = getCancelFixedPriceListingInstructionDataSerializer().serialize(
    {}
  );

  // Bytes Created On Chain.
  const bytesCreatedOnChain = 0;

  return transactionBuilder([
    { instruction: { keys, programId, data }, signers, bytesCreatedOnChain },
  ]);
}
