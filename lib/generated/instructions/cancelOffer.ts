/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  Context,
  Pda,
  PublicKey,
  Signer,
  TransactionBuilder,
  transactionBuilder,
} from '@metaplex-foundation/umi';
import {
  Serializer,
  bytes,
  mapSerializer,
  publicKey as publicKeySerializer,
  struct,
} from '@metaplex-foundation/umi/serializers';
import {
  ResolvedAccount,
  ResolvedAccountsWithIndices,
  expectPublicKey,
  getAccountMetasAndSigners,
} from '../shared';

// Accounts.
export type CancelOfferInstructionAccounts = {
  /** The buyer who created the offer */
  buyer: Signer;
  /** The NFT asset being sold */
  asset: PublicKey | Pda;
  /** The offer being cancelled */
  offer?: PublicKey | Pda;
  /** Escrow account holding the payment */
  escrow?: PublicKey | Pda;
  /** System program for transfers */
  systemProgram?: PublicKey | Pda;
  /** Clock for timestamp updates */
  clock?: PublicKey | Pda;
};

// Data.
export type CancelOfferInstructionData = { discriminator: Uint8Array };

export type CancelOfferInstructionDataArgs = {};

export function getCancelOfferInstructionDataSerializer(): Serializer<
  CancelOfferInstructionDataArgs,
  CancelOfferInstructionData
> {
  return mapSerializer<
    CancelOfferInstructionDataArgs,
    any,
    CancelOfferInstructionData
  >(
    struct<CancelOfferInstructionData>(
      [['discriminator', bytes({ size: 8 })]],
      { description: 'CancelOfferInstructionData' }
    ),
    (value) => ({
      ...value,
      discriminator: new Uint8Array([92, 203, 223, 40, 92, 89, 53, 119]),
    })
  ) as Serializer<CancelOfferInstructionDataArgs, CancelOfferInstructionData>;
}

// Instruction.
export function cancelOffer(
  context: Pick<Context, 'eddsa' | 'programs'>,
  input: CancelOfferInstructionAccounts
): TransactionBuilder {
  // Program ID.
  const programId = context.programs.getPublicKey(
    'fusionMarketplace',
    'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
  );

  // Accounts.
  const resolvedAccounts = {
    buyer: {
      index: 0,
      isWritable: true as boolean,
      value: input.buyer ?? null,
    },
    asset: {
      index: 1,
      isWritable: true as boolean,
      value: input.asset ?? null,
    },
    offer: {
      index: 2,
      isWritable: true as boolean,
      value: input.offer ?? null,
    },
    escrow: {
      index: 3,
      isWritable: true as boolean,
      value: input.escrow ?? null,
    },
    systemProgram: {
      index: 4,
      isWritable: false as boolean,
      value: input.systemProgram ?? null,
    },
    clock: {
      index: 5,
      isWritable: false as boolean,
      value: input.clock ?? null,
    },
  } satisfies ResolvedAccountsWithIndices;

  // Default values.
  if (!resolvedAccounts.offer.value) {
    resolvedAccounts.offer.value = context.eddsa.findPda(programId, [
      bytes().serialize(new Uint8Array([111, 102, 102, 101, 114])),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.buyer.value)
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.asset.value)
      ),
    ]);
  }
  if (!resolvedAccounts.escrow.value) {
    resolvedAccounts.escrow.value = context.eddsa.findPda(programId, [
      bytes().serialize(
        new Uint8Array([
          101, 115, 99, 114, 111, 119, 45, 112, 97, 121, 109, 101, 110, 116,
        ])
      ),
      publicKeySerializer().serialize(
        expectPublicKey(resolvedAccounts.offer.value)
      ),
    ]);
  }
  if (!resolvedAccounts.systemProgram.value) {
    resolvedAccounts.systemProgram.value = context.programs.getPublicKey(
      'systemProgram',
      '11111111111111111111111111111111'
    );
    resolvedAccounts.systemProgram.isWritable = false;
  }
  if (!resolvedAccounts.clock.value) {
    resolvedAccounts.clock.value = context.programs.getPublicKey(
      'clock',
      'SysvarC1ock11111111111111111111111111111111'
    );
    resolvedAccounts.clock.isWritable = false;
  }

  // Accounts in order.
  const orderedAccounts: ResolvedAccount[] = Object.values(
    resolvedAccounts
  ).sort((a, b) => a.index - b.index);

  // Keys and Signers.
  const [keys, signers] = getAccountMetasAndSigners(
    orderedAccounts,
    'programId',
    programId
  );

  // Data.
  const data = getCancelOfferInstructionDataSerializer().serialize({});

  // Bytes Created On Chain.
  const bytesCreatedOnChain = 0;

  return transactionBuilder([
    { instruction: { keys, programId, data }, signers, bytesCreatedOnChain },
  ]);
}
