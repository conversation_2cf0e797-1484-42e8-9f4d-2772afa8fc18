/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  Serializer,
  i64,
  publicKey as publicKeySerializer,
  struct,
  u64,
} from '@metaplex-foundation/umi/serializers';

/** Event emitted when an offer is created */
export type OfferCreatedEvent = {
  /** The offer account public key */
  offer: PublicKey;
  /** The buyer who made the offer */
  buyer: PublicKey;
  /** The asset the offer is for */
  asset: PublicKey;
  /** The price offered in lamports */
  price: bigint;
  /** Timestamp when the offer expires */
  expiresAt: bigint;
  /** Timestamp when the offer was created */
  createdAt: bigint;
};

export type OfferCreatedEventArgs = {
  /** The offer account public key */
  offer: PublicKey;
  /** The buyer who made the offer */
  buyer: PublicKey;
  /** The asset the offer is for */
  asset: PublicKey;
  /** The price offered in lamports */
  price: number | bigint;
  /** Timestamp when the offer expires */
  expiresAt: number | bigint;
  /** Timestamp when the offer was created */
  createdAt: number | bigint;
};

export function getOfferCreatedEventSerializer(): Serializer<
  OfferCreatedEventArgs,
  OfferCreatedEvent
> {
  return struct<OfferCreatedEvent>(
    [
      ['offer', publicKeySerializer()],
      ['buyer', publicKeySerializer()],
      ['asset', publicKeySerializer()],
      ['price', u64()],
      ['expiresAt', i64()],
      ['createdAt', i64()],
    ],
    { description: 'OfferCreatedEvent' }
  ) as Serializer<OfferCreatedEventArgs, OfferCreatedEvent>;
}
