/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  Serializer,
  i64,
  publicKey as publicKeySerializer,
  struct,
  u64,
} from '@metaplex-foundation/umi/serializers';
import { ListingType, ListingTypeArgs, getListingTypeSerializer } from '.';

/** Event emitted when a new listing is created */
export type ListingCreatedEvent = {
  /** Seller of the NFT */
  seller: PublicKey;
  /** NFT asset being sold */
  asset: PublicKey;
  /** Listing account */
  listing: PublicKey;
  /** Price in lamports */
  price: bigint;
  /**
   * TODO: Create as enum
   * Listing type (fixed price, auction)
   */
  listingType: ListingType;
  /** When the listing was created */
  createdAt: bigint;
  /** When the listing ends */
  endsAt: bigint;
};

export type ListingCreatedEventArgs = {
  /** Seller of the NFT */
  seller: PublicKey;
  /** NFT asset being sold */
  asset: PublicKey;
  /** Listing account */
  listing: PublicKey;
  /** Price in lamports */
  price: number | bigint;
  /**
   * TODO: Create as enum
   * Listing type (fixed price, auction)
   */
  listingType: ListingTypeArgs;
  /** When the listing was created */
  createdAt: number | bigint;
  /** When the listing ends */
  endsAt: number | bigint;
};

export function getListingCreatedEventSerializer(): Serializer<
  ListingCreatedEventArgs,
  ListingCreatedEvent
> {
  return struct<ListingCreatedEvent>(
    [
      ['seller', publicKeySerializer()],
      ['asset', publicKeySerializer()],
      ['listing', publicKeySerializer()],
      ['price', u64()],
      ['listingType', getListingTypeSerializer()],
      ['createdAt', i64()],
      ['endsAt', i64()],
    ],
    { description: 'ListingCreatedEvent' }
  ) as Serializer<ListingCreatedEventArgs, ListingCreatedEvent>;
}
