/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  Serializer,
  i64,
  publicKey as publicKeySerializer,
  struct,
  u64,
} from '@metaplex-foundation/umi/serializers';

/** Event emitted when a bid is placed on an auction */
export type BidPlacedEvent = {
  /** Listing account */
  listing: PublicKey;
  /** Auction account */
  auction: PublicKey;
  /** Bidder who placed the bid */
  bidder: PublicKey;
  /** Bid amount in lamports */
  bidAmount: bigint;
  /** When the bid was placed */
  timestamp: bigint;
};

export type BidPlacedEventArgs = {
  /** Listing account */
  listing: PublicKey;
  /** Auction account */
  auction: PublicKey;
  /** Bidder who placed the bid */
  bidder: PublicKey;
  /** Bid amount in lamports */
  bidAmount: number | bigint;
  /** When the bid was placed */
  timestamp: number | bigint;
};

export function getBidPlacedEventSerializer(): Serializer<
  BidPlacedEventArgs,
  BidPlacedEvent
> {
  return struct<BidPlacedEvent>(
    [
      ['listing', publicKeySerializer()],
      ['auction', publicKeySerializer()],
      ['bidder', publicKeySerializer()],
      ['bidAmount', u64()],
      ['timestamp', i64()],
    ],
    { description: 'BidPlacedEvent' }
  ) as Serializer<BidPlacedEventArgs, BidPlacedEvent>;
}
