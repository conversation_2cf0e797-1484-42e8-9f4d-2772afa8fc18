/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  Serializer,
  i64,
  publicKey as publicKeySerializer,
  struct,
} from '@metaplex-foundation/umi/serializers';

/** Event emitted when an offer is cancelled */
export type OfferCancelledEvent = {
  /** The offer account public key */
  offer: PublicKey;
  /** The buyer who cancelled the offer */
  buyer: PublicKey;
  /** The asset the offer was for */
  asset: PublicKey;
  /** Timestamp when the offer was cancelled */
  cancelledAt: bigint;
};

export type OfferCancelledEventArgs = {
  /** The offer account public key */
  offer: PublicKey;
  /** The buyer who cancelled the offer */
  buyer: PublicKey;
  /** The asset the offer was for */
  asset: PublicKey;
  /** Timestamp when the offer was cancelled */
  cancelledAt: number | bigint;
};

export function getOfferCancelledEventSerializer(): Serializer<
  OfferCancelledEventArgs,
  OfferCancelledEvent
> {
  return struct<OfferCancelledEvent>(
    [
      ['offer', publicKeySerializer()],
      ['buyer', publicKeySerializer()],
      ['asset', publicKeySerializer()],
      ['cancelledAt', i64()],
    ],
    { description: 'OfferCancelledEvent' }
  ) as Serializer<OfferCancelledEventArgs, OfferCancelledEvent>;
}
