/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  Serializer,
  i64,
  publicKey as publicKeySerializer,
  struct,
  u64,
} from '@metaplex-foundation/umi/serializers';

/** Event emitted when an offer is accepted */
export type OfferAcceptedEvent = {
  /** The offer account public key */
  offer: PublicKey;
  /** The buyer who made the offer */
  buyer: PublicKey;
  /** The seller who accepted the offer */
  seller: PublicKey;
  /** The asset being sold */
  asset: PublicKey;
  /** The price in lamports */
  price: bigint;
  /** Protocol fee collected */
  protocolFee: bigint;
  /** Royalty fee distributed */
  royaltyPayment: bigint;
  /** Timestamp when the offer was accepted */
  acceptedAt: bigint;
};

export type OfferAcceptedEventArgs = {
  /** The offer account public key */
  offer: PublicKey;
  /** The buyer who made the offer */
  buyer: <PERSON>Key;
  /** The seller who accepted the offer */
  seller: PublicKey;
  /** The asset being sold */
  asset: PublicKey;
  /** The price in lamports */
  price: number | bigint;
  /** Protocol fee collected */
  protocolFee: number | bigint;
  /** Royalty fee distributed */
  royaltyPayment: number | bigint;
  /** Timestamp when the offer was accepted */
  acceptedAt: number | bigint;
};

export function getOfferAcceptedEventSerializer(): Serializer<
  OfferAcceptedEventArgs,
  OfferAcceptedEvent
> {
  return struct<OfferAcceptedEvent>(
    [
      ['offer', publicKeySerializer()],
      ['buyer', publicKeySerializer()],
      ['seller', publicKeySerializer()],
      ['asset', publicKeySerializer()],
      ['price', u64()],
      ['protocolFee', u64()],
      ['royaltyPayment', u64()],
      ['acceptedAt', i64()],
    ],
    { description: 'OfferAcceptedEvent' }
  ) as Serializer<OfferAcceptedEventArgs, OfferAcceptedEvent>;
}
