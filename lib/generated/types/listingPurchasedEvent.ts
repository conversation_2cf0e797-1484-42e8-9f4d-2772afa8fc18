/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  Serializer,
  i64,
  publicKey as publicKeySerializer,
  struct,
  u64,
} from '@metaplex-foundation/umi/serializers';

/** Event emitted when a listing is purchased */
export type ListingPurchasedEvent = {
  /** Seller of the NFT */
  seller: PublicKey;
  /** Buyer of the NFT */
  buyer: PublicKey;
  /** NFT asset being sold */
  asset: PublicKey;
  /** Listing account */
  listing: PublicKey;
  /** Price paid in lamports */
  price: bigint;
  /** Protocol fee amount */
  protocolFee: bigint;
  /** Royalty fee distributed */
  royaltyPayment: bigint;
  /** When the purchase occurred */
  purchasedAt: bigint;
};

export type ListingPurchasedEventArgs = {
  /** Seller of the NFT */
  seller: PublicKey;
  /** Buyer of the NFT */
  buyer: PublicKey;
  /** NFT asset being sold */
  asset: PublicKey;
  /** Listing account */
  listing: PublicKey;
  /** Price paid in lamports */
  price: number | bigint;
  /** Protocol fee amount */
  protocolFee: number | bigint;
  /** Royalty fee distributed */
  royaltyPayment: number | bigint;
  /** When the purchase occurred */
  purchasedAt: number | bigint;
};

export function getListingPurchasedEventSerializer(): Serializer<
  ListingPurchasedEventArgs,
  ListingPurchasedEvent
> {
  return struct<ListingPurchasedEvent>(
    [
      ['seller', publicKeySerializer()],
      ['buyer', publicKeySerializer()],
      ['asset', publicKeySerializer()],
      ['listing', publicKeySerializer()],
      ['price', u64()],
      ['protocolFee', u64()],
      ['royaltyPayment', u64()],
      ['purchasedAt', i64()],
    ],
    { description: 'ListingPurchasedEvent' }
  ) as Serializer<ListingPurchasedEventArgs, ListingPurchasedEvent>;
}
