/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { Serializer, scalarEnum } from '@metaplex-foundation/umi/serializers';

/** Auction types */
export enum AuctionType {
  English,
}

export type AuctionTypeArgs = AuctionType;

export function getAuctionTypeSerializer(): Serializer<
  AuctionTypeArgs,
  AuctionType
> {
  return scalarEnum<AuctionType>(AuctionType, {
    description: 'AuctionType',
  }) as Serializer<AuctionTypeArgs, AuctionType>;
}
