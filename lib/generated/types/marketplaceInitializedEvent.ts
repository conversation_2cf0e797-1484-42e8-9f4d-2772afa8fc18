/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  Serializer,
  publicKey as publicKeySerializer,
  struct,
  u16,
} from '@metaplex-foundation/umi/serializers';

/** Event emitted when the marketplace is initialized */
export type MarketplaceInitializedEvent = {
  /** Admin that can update the marketplace configuration */
  admin: PublicKey;
  /** Account that receives marketplace fees */
  feeCollector: PublicKey;
  /** Protocol fee in basis points */
  protocolFeeBp: number;
};

export type MarketplaceInitializedEventArgs = MarketplaceInitializedEvent;

export function getMarketplaceInitializedEventSerializer(): Serializer<
  MarketplaceInitializedEventArgs,
  MarketplaceInitializedEvent
> {
  return struct<MarketplaceInitializedEvent>(
    [
      ['admin', publicKeySerializer()],
      ['feeCollector', publicKeySerializer()],
      ['protocolFeeBp', u16()],
    ],
    { description: 'MarketplaceInitializedEvent' }
  ) as Serializer<MarketplaceInitializedEventArgs, MarketplaceInitializedEvent>;
}
