/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  Serializer,
  i64,
  publicKey as publicKeySerializer,
  struct,
} from '@metaplex-foundation/umi/serializers';

/** Event emitted when a listing is cancelled */
export type ListingCancelledEvent = {
  /** Seller of the NFT */
  seller: PublicKey;
  /** NFT asset being sold */
  asset: PublicKey;
  /** Listing account */
  listing: PublicKey;
  /** When the listing was cancelled */
  cancelledAt: bigint;
};

export type ListingCancelledEventArgs = {
  /** Seller of the NFT */
  seller: PublicKey;
  /** NFT asset being sold */
  asset: PublicKey;
  /** Listing account */
  listing: PublicKey;
  /** When the listing was cancelled */
  cancelledAt: number | bigint;
};

export function getListingCancelledEventSerializer(): Serializer<
  ListingCancelledEventArgs,
  ListingCancelledEvent
> {
  return struct<ListingCancelledEvent>(
    [
      ['seller', publicKeySerializer()],
      ['asset', publicKeySerializer()],
      ['listing', publicKeySerializer()],
      ['cancelledAt', i64()],
    ],
    { description: 'ListingCancelledEvent' }
  ) as Serializer<ListingCancelledEventArgs, ListingCancelledEvent>;
}
