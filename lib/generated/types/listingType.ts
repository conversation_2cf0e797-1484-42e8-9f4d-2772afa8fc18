/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import { PublicKey } from '@metaplex-foundation/umi';
import {
  GetDataEnumKind,
  GetDataEnumKindContent,
  Serializer,
  dataEnum,
  publicKey as publicKeySerializer,
  struct,
  tuple,
  unit,
} from '@metaplex-foundation/umi/serializers';

/** Listing types */
export type ListingType =
  | { __kind: 'FixedPrice' }
  | { __kind: 'Auction'; fields: [PublicKey] };

export type ListingTypeArgs = ListingType;

export function getListingTypeSerializer(): Serializer<
  ListingTypeArgs,
  ListingType
> {
  return dataEnum<ListingType>(
    [
      ['FixedPrice', unit()],
      [
        'Auction',
        struct<GetDataEnumKindContent<ListingType, 'Auction'>>([
          ['fields', tuple([publicKeySerializer()])],
        ]),
      ],
    ],
    { description: 'ListingType' }
  ) as Serializer<ListingTypeArgs, ListingType>;
}

// Data Enum Helpers.
export function listingType(
  kind: 'FixedPrice'
): GetDataEnumKind<ListingTypeArgs, 'FixedPrice'>;
export function listingType(
  kind: 'Auction',
  data: GetDataEnumKindContent<ListingTypeArgs, 'Auction'>['fields']
): GetDataEnumKind<ListingTypeArgs, 'Auction'>;
export function listingType<K extends ListingTypeArgs['__kind']>(
  kind: K,
  data?: any
): Extract<ListingTypeArgs, { __kind: K }> {
  return Array.isArray(data)
    ? { __kind: kind, fields: data }
    : { __kind: kind, ...(data ?? {}) };
}
export function isListingType<K extends ListingType['__kind']>(
  kind: K,
  value: ListingType
): value is ListingType & { __kind: K } {
  return value.__kind === kind;
}
