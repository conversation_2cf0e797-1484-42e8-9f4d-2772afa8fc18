/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  ClusterFilter,
  Context,
  Program,
  PublicKey,
} from '@metaplex-foundation/umi';
import {
  getFusionMarketplaceErrorFromCode,
  getFusionMarketplaceErrorFromName,
} from '../errors';

export const FUSION_MARKETPLACE_PROGRAM_ID =
  'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8' as PublicKey<'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'>;

export function createFusionMarketplaceProgram(): Program {
  return {
    name: 'fusionMarketplace',
    publicKey: FUSION_MARKETPLACE_PROGRAM_ID,
    getErrorFromCode(code: number, cause?: Error) {
      return getFusionMarketplaceErrorFromCode(code, this, cause);
    },
    getErrorFromName(name: string, cause?: Error) {
      return getFusionMarketplaceErrorFromName(name, this, cause);
    },
    isOnCluster() {
      return true;
    },
  };
}

export function getFusionMarketplaceProgram<T extends Program = Program>(
  context: Pick<Context, 'programs'>,
  clusterFilter?: ClusterFilter
): T {
  return context.programs.get<T>('fusionMarketplace', clusterFilter);
}

export function getFusionMarketplaceProgramId(
  context: Pick<Context, 'programs'>,
  clusterFilter?: ClusterFilter
): PublicKey {
  return context.programs.getPublicKey(
    'fusionMarketplace',
    FUSION_MARKETPLACE_PROGRAM_ID,
    clusterFilter
  );
}
