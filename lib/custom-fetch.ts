// Removed Telefunc import - now using REST API directly

export function setupCustomFetch() {
	let isRefreshing = false
	let refreshPromise = null
	type RequestQueue = {
		url: RequestInfo | URL
		options: RequestInit | undefined
		resolve: ((value: Response | PromiseLike<Response>) => void) | null
		reject: ((reason?: unknown) => void) | null
	}
	const requestQueue: RequestQueue[] = []

	// Override fetch to handle 401s
	const originalFetch = window.fetch
	window.fetch = async (...args): Promise<Response> => {
		let isAPIRequest = false
		let isTelefuncRequest = false
		let url: URL

		// Normalize the URL to handle both string and URL objects
		if (args[0] instanceof URL) {
			url = args[0]
		} else if (typeof args[0] === 'string') {
			// Check if it's a telefunc request before parsing
			if (args[0].startsWith('/_telefunc')) {
				isTelefuncRequest = true
			}

			// For absolute URLs
			if (args[0].startsWith('http')) {
				url = new URL(args[0])
			} else {
				// For relative URLs
				url = new URL(args[0], window.location.origin)
			}

			// Update args[0] with the URL object
			args[0] = url
		} else {
			// If it's not a URL or string, pass through unchanged
			return originalFetch(...args)
		}

		// Check if it's a same-origin request
		if (url.origin === window.location.origin) {
			// If not already identified as telefunc, check if it's an API request
			if (!isTelefuncRequest && url.pathname.startsWith('/api')) {
				isAPIRequest = true
			}

			// Add token to authorization header for API and Telefunc requests
			if (isAPIRequest || isTelefuncRequest) {
				const accessToken = localStorage.getItem('accessToken')
				if (accessToken) {
					args[1] = {
						...(args[1] ?? {}),
						headers: {
							...(args[1]?.headers ?? {}),
							Authorization: `Bearer ${accessToken}`,
						},
					}
				}
			}
		}

		const response = await originalFetch(...args)

		// For regular API requests, handle status codes directly
		if (isAPIRequest) {
			if (response.status === 403) {
				logout()
				return response
			}

			if (response.status === 401) {
				return handleUnauthorized(args, response)
			}
		}

		// For Telefunc requests, we need to check the response body
		if (isTelefuncRequest) {
			// Clone the response so we can read it twice
			const clonedResponse = response.clone()
			try {
				const responseData = (await clonedResponse.json()) as {
					abort: boolean
					ret: {
						status: number
					}
				}

				// Telefunc errors are in responseData.error
				if (responseData.abort) {
					// Check for authentication errors in the message
					if (responseData.ret.status === 401) {
						return handleUnauthorized(args, response)
					}

					// Check for permission errors
					if (responseData.ret.status === 403) {
						logout()
						return response
					}
				}
			} catch (e) {
				// If we can't parse the JSON, just continue
			}
		}

		return response
	}

	async function handleUnauthorized(
		args: Parameters<typeof window.fetch>,
		response: Response,
	): Promise<Response> {
		// Clone the request for later retry
		const request: RequestQueue = {
			url: args[0] as RequestInfo | URL,
			options: args[1] as RequestInit | undefined,
			resolve: null,
			reject: null,
		}

		// Create a new promise that will be resolved when the request is retried
		const retryPromise = new Promise<Response>((resolve, reject) => {
			request.resolve = resolve
			request.reject = reject
		})

		// Start token refresh if not already in progress
		if (!isRefreshing) {
			isRefreshing = true

			try {
				refreshPromise = refreshTokenFunction()
				await refreshPromise

				// Process all queued requests with the new token
				processQueue()
			} catch (error) {
				// If refresh fails, reject all queued requests with the error
				if (error instanceof Error) {
					rejectQueue(error)
				} else {
					rejectQueue(new Error(String(error)))
				}
				throw error
			} finally {
				isRefreshing = false
				refreshPromise = null
			}
		} else {
			// If already refreshing, wait for it to complete
			requestQueue.push(request)
			return retryPromise
		}

		// Retry the current request with new token
		const newToken = localStorage.getItem('accessToken')
		const newOptions = {
			...args[1],
			headers: {
				...(args[1]?.headers ?? {}),
				Authorization: `Bearer ${newToken}`,
			},
		}

		return originalFetch(args[0], newOptions)
	}
	// Function to process the queued requests
	function processQueue() {
		for (const request of requestQueue) {
			// Retry the request with the new token
			const newToken = localStorage.getItem('accessToken')
			const newOptions = request.options
				? {
						...request.options,
						headers: {
							...(request.options.headers ?? {}),
							Authorization: `Bearer ${newToken}`,
						},
					}
				: undefined

			originalFetch(request.url, newOptions)
				.then((response) => {
					if (request.resolve) {
						request.resolve(response)
					}
				})
				.catch((err) => {
					if (request.reject) {
						request.reject(err)
					}
				})
		}

		// Clear the queue
		requestQueue.length = 0
	}

	// Function to reject all queued requests
	function rejectQueue(error: Error) {
		for (const request of requestQueue) {
			if (request.reject) {
				request.reject(error)
			}
		}

		// Clear the queue
		requestQueue.length = 0
	}

	// Function to refresh the token using REST API
	async function refreshTokenFunction() {
		const refreshToken = localStorage.getItem('refreshToken')

		if (!refreshToken) {
			// No refresh token, can't refresh
			logout()
			throw new Error('No refresh token available')
		}

		try {
			const res = await originalFetch('/api/auth/refresh', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ refreshToken }),
			})

			if (!res.ok) {
				const errorData = await res.json()
				throw new Error(errorData.message || 'Token refresh failed')
			}

			const result = await res.json()

			if (!result.success) {
				throw new Error(result.message || 'Token refresh failed')
			}

			// Save the new tokens
			localStorage.setItem('accessToken', result.accessToken)
			localStorage.setItem('refreshToken', result.refreshToken)

			return result.accessToken
		} catch (error) {
			logout()
			throw error
		}
	}

	function logout() {
		//localStorage.clear()
		localStorage.removeItem('accessToken')
		localStorage.removeItem('refreshToken')
	}
}
