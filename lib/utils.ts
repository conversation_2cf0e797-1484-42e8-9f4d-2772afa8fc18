import type { SignatureBytes } from '@solana/kit'
import { verifySignature } from '@solana/kit'
import { PublicKey } from '@solana/web3.js'
import { type ClassValue, clsx } from 'clsx'
import { toast } from 'sonner'
import { twMerge } from 'tailwind-merge'
import customToast from '@/components/CustomToast'

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs))
}

export function uint8ArrayToBase64(uint8Array: Uint8Array) {
	return btoa(String.fromCharCode(...uint8Array))
}
export function base64ToUint8Array(base64String: string) {
	const binaryString = atob(base64String)
	const uint8Array = new Uint8Array(binaryString.length)
	for (let i = 0; i < binaryString.length; i++) {
		uint8Array[i] = binaryString.charCodeAt(i)
	}
	return uint8Array
}

export const copyText = async ({ data }: { data: string }) => {
	try {
		await navigator.clipboard.writeText(data)
		customToast.success('Successfully copied !')
	} catch (e) {
		console.error(e)
		customToast.error('Ups, Failed to copy!')
	}
}

export const encoder = new TextEncoder()

export async function convertPublicKeyToCryptoKey(
	publicKeyStr: string,
): Promise<CryptoKey | null> {
	try {
		const publicKey = new PublicKey(publicKeyStr)
		const publicKeyPair = await crypto.subtle.importKey(
			'raw',
			publicKey.toBytes(),
			{
				name: 'Ed25519',
				namedCurve: 'Ed25519',
			},
			true, // `extractable`: Whether the key can be extracted
			['verify'], // `keyUsages`: Public key can only be used for verification
		)

		return publicKeyPair
	} catch (e) {
		return null
	}
}

export function removeUndefined<T extends object>(obj: T): Partial<T> {
	return Object.fromEntries(
		Object.entries(obj).filter(([_, value]) => value !== undefined),
	) as Partial<T>
}

export async function verifySolanaSignature({
	publicKey,
	signature,
	message,
}: {
	publicKey: string
	signature: Uint8Array<ArrayBufferLike>
	message: string
}): Promise<boolean> {
	try {
		if (!validateMessage(message)) {
			return false
		}

		if (!validateMessage(message)) {
			return false
		}

		const enccodedPublicKey = await convertPublicKeyToCryptoKey(publicKey)
		if (!enccodedPublicKey) {
			return false
		}

		const signatureBytes = toSignatureBytes(signature)

		if (!signatureBytes) {
			return false
		}

		const verified = await verifySignature(
			enccodedPublicKey,
			signatureBytes,
			new TextEncoder().encode(message),
		)

		return verified
	} catch (e) {
		return false
	}
}

export function getCurrentTimeStamp(): number {
	return Math.floor(Date.now() / 1000)
}

export function validateMessage(message: string) {
	try {
		const timestamp = Number.parseInt(message.replace('signin', ''), 10)
		const differenceInSeconds = getCurrentTimeStamp() - timestamp

		return differenceInSeconds < 300
	} catch (e) {
		return false
	}
}

export const randomUsername = () => {
	const cryptoTerms = [
		'Crypto',
		'Bitcoin',
		'Ethereum',
		'Blockchain',
		'DeFi',
		'NFT',
		'Token',
		'Altcoin',
		'Satoshi',
		'Hash',
		'HODL',
		'Moon',
		'Bull',
		'Bear',
		'Shiba',
		'Doge',
		'Vitalik',
		'Bullish',
		'Bearish',
		'Decentralized',
		'Digital',
		'Virtual',
		'Encrypted',
		'Anonymous',
		'Volatile',
		'Innovative',
		'Disruptive',
		'Smart',
		'Distributed',
	]
	const roles = [
		'Ninja',
		'Disciple',
		'Wizard',
		'Emperor',
		'Baroness',
		'Alchemist',
		'Dragon',
		'Titan',
		'Miner',
		'Hero',
		'Knight',
		'Dynamo',
		'Sage',
		'Nomad',
		'Rider',
		'Pirate',
		'Viking',
		'Samurai',
		'Warrior',
		'Soldier',
		'Whale',
		'Guru',
		'Master',
		'God',
		'King',
		'Angel',
		'Pharaoh',
		'Demon',
		'Ghost',
	]

	const randomFirstName =
		cryptoTerms[Math.floor(Math.random() * cryptoTerms.length)]
	const randomLastName = roles[Math.floor(Math.random() * roles.length)]
	const randomDigits = Math.floor(Math.random() * 10)
	const randomDigits2 = generateRandomString(4)

	return `${randomFirstName}${randomLastName}${randomDigits}${randomDigits2}`
}

export const generateRandomString = (length: number) => {
	const characters =
		'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
	let result = ''
	for (let i = 0; i < length; i++) {
		result += characters.charAt(Math.floor(Math.random() * characters.length))
	}
	return result
}

export function toSignatureBytes(
	input: Uint8Array | ArrayBuffer | ArrayBufferView,
): SignatureBytes {
	if (input instanceof Uint8Array) {
		return input as SignatureBytes
	}

	if (input instanceof ArrayBuffer) {
		return new Uint8Array(input) as SignatureBytes
	}

	if (input instanceof DataView || 'buffer' in input) {
		return new Uint8Array(input.buffer) as SignatureBytes
	}

	throw new Error(
		'Invalid input type. Must be Uint8Array, ArrayBuffer, or ArrayBufferView.',
	)
}

export const clog = (...args: unknown[]) => {
	if (import.meta.env.PUBLIC_ENV__NODE_ENV === 'development') {
		const timestamp = new Date().toISOString().split('T')[1].slice(0, 12)
		console.log(`[DEV LOG ${timestamp}]:`, ...args)
	}
}

export const cerror = (...args: unknown[]) => {
	if (import.meta.env.PUBLIC_ENV__NODE_ENV === 'development') {
		const timestamp = new Date().toISOString().split('T')[1].slice(0, 12)
		console.error(`[DEV ERROR ${timestamp}]:`, ...args)
	}
}

//export const toggleOverlay = (isOpen: boolean) => {
//	const mainElement = document.querySelector('main')
//	if (mainElement) {
//		// to  disable pointer events
//		mainElement.classList.toggle('pointer-events-none', isOpen)
//
//		//custom background color with opacity
//		mainElement.classList.toggle('bg-[rgba(100,100,100,0.9)]', isOpen)
//
//		//  transition effect for smooth changes
//		mainElement.classList.toggle('transition-all', isOpen)
//	}
//}

export const toggleOverlay = (isOpen: boolean) => {
	const overlayElement = document.getElementById('overlay')

	if (overlayElement) {
		// Show or hide the overlay
		overlayElement.classList.toggle('hidden', !isOpen)

		// Add or remove pointer events
		overlayElement.classList.toggle('pointer-events-auto', isOpen)
		overlayElement.classList.toggle('pointer-events-none', !isOpen)

		// Smooth transition effect
		overlayElement.classList.toggle('transition-all', isOpen)
	}
}
// Function to truncate wallet addresses
export const truncateAddress = (
	address: string,
	startLength = 4,
	endLength = 5,
) => {
	if (!address) return ''

	// Check if address is already truncated or too short
	if (address.length <= startLength + endLength + 3) return address

	const start = address.substring(0, startLength)
	const end = address.substring(address.length - endLength)

	return `${start}...${end}`
}

export const handleViewOnSolscan = (
	txHash: string,
	type: 'tx' | 'account' = 'tx',
) => {
	const isProduction =
		process.env.PUBLIC_ENV__NODE_ENV === 'production' ? '' : '?cluster=devnet'

	window.open(`https://solscan.io/${type}/${txHash}${isProduction}`, '_blank')
}

export const roundDown = (value: number, decimals = 4) => {
	return Math.floor(value * 10 ** decimals) / 10 ** decimals
}

/**
 * Parses CSV string data into a JSON array
 * @param csvString The CSV string to parse
 * @param delimiter The delimiter character (default: ',')
 * @returns An array of objects where keys are column headers and values are row values
 */
export const parseCSVToJSON = (
	csvString: string,
	delimiter = ',',
): Record<string, string>[] => {
	// Split the CSV string into lines
	const lines = csvString.split(/\r?\n/).filter((line) => line.trim() !== '')

	if (lines.length === 0) {
		return []
	}

	// Extract headers from the first line
	const headers = lines[0].split(delimiter).map((header) => header.trim())

	// Process data rows
	const result: Record<string, string>[] = []

	for (let i = 1; i < lines.length; i++) {
		const line = lines[i]
		const values = line.split(delimiter)

		// Skip empty lines
		if (values.length === 1 && values[0].trim() === '') {
			continue
		}

		// Create an object for this row
		const obj: Record<string, string> = {}

		// Map each value to its corresponding header
		headers.forEach((header, index) => {
			obj[header] = values[index] ? values[index].trim() : ''
		})

		result.push(obj)
	}

	return cleanValues(result) as Record<string, string>[]
}

/**
 * Type definition for any data that can be cleaned
 * Can be a string, number, boolean, null, undefined, object, or array of any of these
 */
type CleanableData =
	| string
	| number
	| boolean
	| null
	| undefined
	| { [key: string]: CleanableData }
	| CleanableData[]

/**
 * Function to clean values by removing unnecessary quotes
 * @param data - The data to clean (object, array or primitive)
 * @returns Cleaned data with properly formatted string values
 */
function cleanValues(data: CleanableData): CleanableData {
	// Handle arrays
	if (Array.isArray(data)) {
		return data.map((item) => cleanValues(item))
	}

	// Handle null or undefined
	if (data === null || data === undefined) {
		return data
	}

	// Handle objects
	if (typeof data === 'object') {
		const cleanedObj: { [key: string]: CleanableData } = {}

		// Process each property in the object
		for (const [key, value] of Object.entries(data)) {
			// If the value is a string
			if (typeof value === 'string') {
				// Remove any extra quotes and fix unbalanced quotes
				let cleanedValue = value

				// Remove quotes at the beginning and end if they exist
				if (cleanedValue.startsWith('"') && cleanedValue.endsWith('"')) {
					cleanedValue = cleanedValue.substring(1, cleanedValue.length - 1)
				}
				// Handle case where only beginning quote exists
				else if (cleanedValue.startsWith('"')) {
					cleanedValue = cleanedValue.substring(1)
				}
				// Handle case where only ending quote exists
				else if (cleanedValue.endsWith('"')) {
					cleanedValue = cleanedValue.substring(0, cleanedValue.length - 1)
				}

				cleanedObj[key] = cleanedValue
			} else if (typeof value === 'object' && value !== null) {
				// Recursively clean nested objects or arrays
				cleanedObj[key] = cleanValues(value)
			} else {
				// If not a string or object, keep as is
				cleanedObj[key] = value
			}
		}

		return cleanedObj
	}

	// Return primitive values as-is
	return data
}

export const getShareUrl = (
	type: 'nft' | 'collection' | 'profile',
	publicKey: string,
) => {
	const currentUrl = 'https://shogun.xyz'
	return `${currentUrl}/${type}/${publicKey}`
}

export const calculateFloorDiff = (nftPrice: number, offerPrice: number) => {
	// Return empty string if either price is invalid
	if (!nftPrice || !offerPrice || nftPrice <= 0) {
		return ''
	}

	// Calculate the percentage difference
	const difference = ((offerPrice - nftPrice) / nftPrice) * 100

	// Round to nearest integer for cleaner display
	const roundedDiff = Math.round(Math.abs(difference))

	// Format the string based on whether offer is above or below floor price
	if (difference > 0) {
		return `${roundedDiff}% above`
	}
	if (difference < 0) {
		return `${roundedDiff}% below`
	}
	return 'at floor'
}
