import type { OffersMadeResponse } from '@/types/user.types'

export const dummyOffersMade: OffersMadeResponse = {
	offers: [
		{
			id: '550e8400-e29b-41d4-a716-446655440000',
			publicKey: 'DummyPublicKey123456789ABCDEF',
			price: 2.5,
			expiresAt: '2025-05-03T15:05:08.000Z',
			nft: {
				id: '550e8400-e29b-41d4-a716-446655440001',
				name: 'Cosmic Explorer #42',
				imageUrl: 'https://example.com/nft/cosmic-explorer-42.png',
				publicKey: 'NFTPublicKey123456789ABCDEF1',
			},
			seller: {
				id: '550e8400-e29b-41d4-a716-446655440003',
				userName: 'nft_creator',
				avatar: 'https://example.com/avatars/nft_creator.png',
				publicKey: 'SellerPublicKey123456789ABCDEF',
			},
			floorDiff: '3% below',
		},
		{
			id: '550e8400-e29b-41d4-a716-446655440004',
			publicKey: 'DummyPublicKey987654321FEDCBA',
			price: 1.75,
			expiresAt: '2025-05-03T15:05:08.000Z',
			nft: {
				id: '550e8400-e29b-41d4-a716-446655440005',
				name: 'Digital Dreamscape #17',
				imageUrl: 'https://example.com/nft/digital-dreamscape-17.png',
				publicKey: 'NFTPublicKey987654321FEDCBA1',
			},
			seller: {
				id: '550e8400-e29b-41d4-a716-446655440007',
				userName: 'digital_artist',
				avatar: 'https://example.com/avatars/digital_artist.png',
				publicKey: 'SellerPublicKey987654321FEDCBA',
			},
			floorDiff: '3% below',
		},
		{
			id: '550e8400-e29b-41d4-a716-446655440008',
			publicKey: 'DummyPublicKeyABCDEF123456789',
			price: 5.0,
			expiresAt: '2025-05-03T15:05:08.000Z',
			nft: {
				id: '550e8400-e29b-41d4-a716-446655440009',
				name: 'Ethereal Essence #88',
				imageUrl: 'https://example.com/nft/ethereal-essence-88.png',
				publicKey: 'NFTPublicKeyABCDEF1234567891',
			},
			seller: {
				id: '550e8400-e29b-41d4-a716-446655440011',
				userName: 'crypto_gallery',
				avatar: 'https://example.com/avatars/crypto_gallery.png',
				publicKey: 'SellerPublicKeyABCDEF123456789',
			},

			floorDiff: '3% above',
		},
	],
	total: 27, // Total number of offers made by the user
	page: 1, // Current page
	limit: 10, // Items per page
}

export const dummyOffersReceived = {
	offers: [
		{
			id: '550e8400-e29b-41d4-a716-446655440020',
			publicKey: 'BuyerPublicKey123456789ABCDEF',
			price: 2.3,
			expiresAt: '2025-05-10T15:05:08.000Z',
			floorDiff: '5% below',
			nft: {
				id: '550e8400-e29b-41d4-a716-446655440021',
				name: 'Quantum Realm #12',
				imageUrl: 'https://example.com/nft/quantum-realm-12.png',
				publicKey: 'NFTPublicKey123456789ABCDEF2',
			},
			buyer: {
				id: '550e8400-e29b-41d4-a716-446655440022',
				userName: 'crypto_collector',
				avatar: 'https://example.com/avatars/crypto_collector.png',
				publicKey: 'BuyerPublicKey123456789ABCDEF',
			},
		},
		{
			id: '550e8400-e29b-41d4-a716-446655440023',
			publicKey: 'BuyerPublicKey987654321FEDCBA',
			price: 3.2,
			expiresAt: '2025-05-15T15:05:08.000Z',
			floorDiff: '2% above',
			nft: {
				id: '550e8400-e29b-41d4-a716-446655440024',
				name: 'Celestial Guardian #54',
				imageUrl: 'https://example.com/nft/celestial-guardian-54.png',
				publicKey: 'NFTPublicKey987654321FEDCBA2',
			},
			buyer: {
				id: '550e8400-e29b-41d4-a716-446655440025',
				userName: 'nft_enthusiast',
				avatar: 'https://example.com/avatars/nft_enthusiast.png',
				publicKey: 'BuyerPublicKey987654321FEDCBA',
			},
		},
		{
			id: '550e8400-e29b-41d4-a716-446655440026',
			publicKey: 'BuyerPublicKeyABCDEF123456789',
			price: 1.8,
			expiresAt: '2025-05-20T15:05:08.000Z',
			floorDiff: '10% below',
			nft: {
				id: '550e8400-e29b-41d4-a716-446655440027',
				name: 'Digital Genesis #33',
				imageUrl: 'https://example.com/nft/digital-genesis-33.png',
				publicKey: 'NFTPublicKeyABCDEF1234567892',
			},
			buyer: {
				id: '550e8400-e29b-41d4-a716-446655440028',
				userName: 'art_investor',
				avatar: 'https://example.com/avatars/art_investor.png',
				publicKey: 'BuyerPublicKeyABCDEF123456789',
			},
		},
		{
			id: '550e8400-e29b-41d4-a716-446655440029',
			publicKey: 'BuyerPublicKey123ABCDEF456789',
			price: 4.5,
			expiresAt: '2025-05-25T15:05:08.000Z',
			floorDiff: '8% above',
			nft: {
				id: '550e8400-e29b-41d4-a716-446655440030',
				name: 'Cyber Nomad #77',
				imageUrl: 'https://example.com/nft/cyber-nomad-77.png',
				publicKey: 'NFTPublicKey123ABCDEF4567893',
			},
			buyer: {
				id: '550e8400-e29b-41d4-a716-446655440031',
				userName: 'digital_whale',
				avatar: 'https://example.com/avatars/digital_whale.png',
				publicKey: 'BuyerPublicKey123ABCDEF456789',
			},
		},
	],
	total: 32, // Total number of offers received by the user
	page: 1, // Current page
	limit: 10, // Items per page
}

/**
 * Helper function to get a subset of dummy offers made with pagination
 * @param page Page number (1-indexed)
 * @param limit Items per page
 * @param search Optional search term to filter offers by name
 */
export function getPaginatedDummyOffersMade(
	page = 1,
	limit = 10,
	search?: string,
): OffersMadeResponse {
	let filteredOffers = dummyOffersMade.offers

	// Apply search filter if provided
	if (search) {
		const searchLower = search.toLowerCase()
		filteredOffers = filteredOffers.filter(
			(offer) =>
				offer.nft?.name.toLowerCase().includes(searchLower) ||
				offer.seller?.userName.toLowerCase().includes(searchLower),
		)
	}

	// Calculate pagination
	const startIndex = (page - 1) * limit
	const endIndex = startIndex + limit
	const paginatedOffers = filteredOffers.slice(startIndex, endIndex)

	return {
		offers: paginatedOffers,
		total: filteredOffers.length,
		page,
		limit,
	}
}

/**
 * Helper function to get a subset of dummy offers received with pagination
 * @param page Page number (1-indexed)
 * @param limit Items per page
 * @param search Optional search term to filter offers by name
 */
export function getPaginatedDummyOffersReceived(
	page = 1,
	limit = 10,
	search?: string,
) {
	let filteredOffers = dummyOffersReceived.offers

	// Apply search filter if provided
	if (search) {
		const searchLower = search.toLowerCase()
		filteredOffers = filteredOffers.filter(
			(offer) =>
				offer.nft?.name.toLowerCase().includes(searchLower) ||
				offer.buyer?.userName.toLowerCase().includes(searchLower),
		)
	}

	// Calculate pagination
	const startIndex = (page - 1) * limit
	const endIndex = startIndex + limit
	const paginatedOffers = filteredOffers.slice(startIndex, endIndex)

	return {
		offers: paginatedOffers,
		total: filteredOffers.length,
		page,
		limit,
	}
}
