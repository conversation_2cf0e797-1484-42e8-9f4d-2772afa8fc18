/**
 * Program IDL in camelCase format in order to be used in JS/TS.
 *
 * Note that this is only a type helper and is not the actual IDL. The original
 * IDL can be found at `target/idl/fusion_marketplace.json`.
 */
export type FusionMarketplace = {
	address: 'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8'
	metadata: {
		name: 'fusionMarketplace'
		version: '0.1.0'
		spec: '0.1.0'
		description: 'Created with Anchor'
	}
	instructions: [
		{
			name: 'acceptOffer'
			docs: ['Accept an offer on a specific NFT']
			discriminator: [227, 82, 234, 131, 1, 18, 48, 2]
			accounts: [
				{
					name: 'seller'
					docs: ['The seller accepting the offer']
					writable: true
					signer: true
				},
				{
					name: 'buyer'
					docs: ['The buyer who made the offer']
					writable: true
				},
				{
					name: 'asset'
					docs: ['The NFT asset being sold']
					writable: true
				},
				{
					name: 'collection'
					optional: true
				},
				{
					name: 'offer'
					docs: ['The offer being accepted']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [111, 102, 102, 101, 114]
							},
							{
								kind: 'account'
								path: 'buyer'
							},
							{
								kind: 'account'
								path: 'asset'
							},
						]
					}
				},
				{
					name: 'escrow'
					docs: ['Escrow account holding the payment']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									101,
									115,
									99,
									114,
									111,
									119,
									45,
									112,
									97,
									121,
									109,
									101,
									110,
									116,
								]
							},
							{
								kind: 'account'
								path: 'offer'
							},
						]
					}
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'feeCollector'
					docs: ['Fee collector account']
					writable: true
				},
				{
					name: 'marketplaceAuthority'
					docs: ['Marketplace authority PDA']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									97,
									117,
									116,
									104,
									111,
									114,
									105,
									116,
									121,
								]
							},
						]
					}
				},
				{
					name: 'mplCoreProgram'
					address: 'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
				},
				{
					name: 'systemProgram'
					docs: ['System program for transfers']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamp validation']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: []
		},
		{
			name: 'cancelAuctionListing'
			docs: ['Cancel auction listing']
			discriminator: [53, 243, 11, 87, 55, 184, 226, 0]
			accounts: [
				{
					name: 'seller'
					docs: ['Seller who created the listing']
					writable: true
					signer: true
				},
				{
					name: 'asset'
					writable: true
				},
				{
					name: 'collection'
					writable: true
					optional: true
				},
				{
					name: 'listing'
					docs: ['Listing to cancel']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [108, 105, 115, 116, 105, 110, 103]
							},
							{
								kind: 'account'
								path: 'asset'
							},
							{
								kind: 'account'
								path: 'seller'
							},
						]
					}
				},
				{
					name: 'auction'
					docs: ['Auction state account']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [97, 117, 99, 116, 105, 111, 110]
							},
							{
								kind: 'account'
								path: 'listing'
							},
						]
					}
				},
				{
					name: 'escrow'
					docs: [
						'Escrow account holding the highest bid (if auction)',
						'Closing/refund logic handled in instruction handler.',
					]
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									101,
									115,
									99,
									114,
									111,
									119,
									45,
									112,
									97,
									121,
									109,
									101,
									110,
									116,
								]
							},
							{
								kind: 'account'
								path: 'listing'
							},
						]
					}
				},
				{
					name: 'highestBidder'
					docs: [
						'Passed in by the client if auction exists and highest_bidder is Some.',
					]
					writable: true
					optional: true
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'marketplaceAuthority'
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									97,
									117,
									116,
									104,
									111,
									114,
									105,
									116,
									121,
								]
							},
						]
					}
				},
				{
					name: 'mplCoreProgram'
					docs: ['MPL Core program']
					address: 'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
				},
				{
					name: 'systemProgram'
					docs: ['System program']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamps']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: []
		},
		{
			name: 'cancelFixedPriceListing'
			docs: ['Cancel fixed price listing']
			discriminator: [4, 251, 13, 95, 189, 172, 90, 160]
			accounts: [
				{
					name: 'seller'
					docs: ['Seller who created the listing']
					writable: true
					signer: true
				},
				{
					name: 'asset'
					writable: true
				},
				{
					name: 'collection'
					writable: true
					optional: true
				},
				{
					name: 'listing'
					docs: ['Listing to cancel']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [108, 105, 115, 116, 105, 110, 103]
							},
							{
								kind: 'account'
								path: 'asset'
							},
							{
								kind: 'account'
								path: 'seller'
							},
						]
					}
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'marketplaceAuthority'
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									97,
									117,
									116,
									104,
									111,
									114,
									105,
									116,
									121,
								]
							},
						]
					}
				},
				{
					name: 'mplCoreProgram'
					docs: ['MPL Core program']
					address: 'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
				},
				{
					name: 'systemProgram'
					docs: ['System program']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamps']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: []
		},
		{
			name: 'cancelOffer'
			docs: ['Cancel an existing offer']
			discriminator: [92, 203, 223, 40, 92, 89, 53, 119]
			accounts: [
				{
					name: 'buyer'
					docs: ['The buyer who created the offer']
					writable: true
					signer: true
				},
				{
					name: 'asset'
					docs: ['The NFT asset being sold']
					writable: true
				},
				{
					name: 'offer'
					docs: ['The offer being cancelled']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [111, 102, 102, 101, 114]
							},
							{
								kind: 'account'
								path: 'buyer'
							},
							{
								kind: 'account'
								path: 'asset'
							},
						]
					}
				},
				{
					name: 'escrow'
					docs: ['Escrow account holding the payment']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									101,
									115,
									99,
									114,
									111,
									119,
									45,
									112,
									97,
									121,
									109,
									101,
									110,
									116,
								]
							},
							{
								kind: 'account'
								path: 'offer'
							},
						]
					}
				},
				{
					name: 'systemProgram'
					docs: ['System program for transfers']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamp updates']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: []
		},
		{
			name: 'claimAuction'
			docs: ['Claim auction of a completed auction listing']
			discriminator: [28, 183, 186, 104, 188, 1, 75, 191]
			accounts: [
				{
					name: 'asset'
					writable: true
				},
				{
					name: 'collection'
					writable: true
					optional: true
				},
				{
					name: 'winner'
					docs: ['The highest bidder who is claiming the NFT']
					writable: true
					signer: true
				},
				{
					name: 'listing'
					docs: ['Listing being claimed']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [108, 105, 115, 116, 105, 110, 103]
							},
							{
								kind: 'account'
								path: 'asset'
							},
							{
								kind: 'account'
								path: 'listing.seller'
								account: 'listing'
							},
						]
					}
				},
				{
					name: 'auction'
					docs: ['Auction state to close']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [97, 117, 99, 116, 105, 111, 110]
							},
							{
								kind: 'account'
								path: 'listing'
							},
						]
					}
				},
				{
					name: 'escrow'
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									101,
									115,
									99,
									114,
									111,
									119,
									45,
									112,
									97,
									121,
									109,
									101,
									110,
									116,
								]
							},
							{
								kind: 'account'
								path: 'listing'
							},
						]
					}
				},
				{
					name: 'seller'
					writable: true
				},
				{
					name: 'marketplaceAuthority'
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									97,
									117,
									116,
									104,
									111,
									114,
									105,
									116,
									121,
								]
							},
						]
					}
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'feeCollector'
					docs: ['Fee collector account from MarketplaceConfig']
					writable: true
				},
				{
					name: 'mplCoreProgram'
					address: 'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
				},
				{
					name: 'systemProgram'
					docs: ['System program']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamps']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: []
		},
		{
			name: 'createAuctionListing'
			docs: ['Create an auction listing for an NFT']
			discriminator: [104, 106, 53, 140, 163, 107, 143, 158]
			accounts: [
				{
					name: 'seller'
					writable: true
					signer: true
				},
				{
					name: 'asset'
					docs: ['The NFT asset being auctioned']
					writable: true
				},
				{
					name: 'collection'
					writable: true
					optional: true
				},
				{
					name: 'listing'
					docs: ['Listing being auctioned']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [108, 105, 115, 116, 105, 110, 103]
							},
							{
								kind: 'account'
								path: 'asset'
							},
							{
								kind: 'account'
								path: 'seller'
							},
						]
					}
				},
				{
					name: 'auction'
					docs: ['Auction-specific state account']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [97, 117, 99, 116, 105, 111, 110]
							},
							{
								kind: 'account'
								path: 'listing'
							},
						]
					}
				},
				{
					name: 'escrow'
					docs: ['Escrow account to hold the highest bid (SOL)']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									101,
									115,
									99,
									114,
									111,
									119,
									45,
									112,
									97,
									121,
									109,
									101,
									110,
									116,
								]
							},
							{
								kind: 'account'
								path: 'listing'
							},
						]
					}
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'marketplaceAuthority'
					docs: ['Marketplace authority account']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									97,
									117,
									116,
									104,
									111,
									114,
									105,
									116,
									121,
								]
							},
						]
					}
				},
				{
					name: 'mplCoreProgram'
					docs: ['MPL Core program']
					address: 'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
				},
				{
					name: 'systemProgram'
					docs: ['System program']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'rent'
					docs: ['Rent sysvar']
					address: 'SysvarRent111111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamps']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: [
				{
					name: 'startPrice'
					type: 'u64'
				},
				{
					name: 'startDelaySeconds'
					type: 'u64'
				},
				{
					name: 'duration'
					type: 'u64'
				},
				{
					name: 'auctionType'
					type: {
						defined: {
							name: 'auctionType'
						}
					}
				},
				{
					name: 'extensionPeriod'
					type: 'u64'
				},
			]
		},
		{
			name: 'createFixedPriceListing'
			docs: ['Create a fixed price listing for an NFT']
			discriminator: [190, 255, 221, 190, 225, 27, 157, 202]
			accounts: [
				{
					name: 'seller'
					docs: ['Seller creating the listing']
					writable: true
					signer: true
				},
				{
					name: 'asset'
					docs: ['The NFT asset being listed']
					writable: true
				},
				{
					name: 'collection'
					writable: true
					optional: true
				},
				{
					name: 'listing'
					docs: ['Listing account to be created or updated']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [108, 105, 115, 116, 105, 110, 103]
							},
							{
								kind: 'account'
								path: 'asset'
							},
							{
								kind: 'account'
								path: 'seller'
							},
						]
					}
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'marketplaceAuthority'
					docs: ['Marketplace authority PDA']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									97,
									117,
									116,
									104,
									111,
									114,
									105,
									116,
									121,
								]
							},
						]
					}
				},
				{
					name: 'mplCoreProgram'
					docs: ['MPL Core program']
					address: 'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
				},
				{
					name: 'systemProgram'
					docs: ['System program']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'rent'
					docs: ['Rent sysvar']
					address: 'SysvarRent111111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamps']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: [
				{
					name: 'price'
					type: 'u64'
				},
				{
					name: 'duration'
					type: 'u64'
				},
			]
		},
		{
			name: 'createOffer'
			docs: ['Create an offer on a specific NFT']
			discriminator: [237, 233, 192, 168, 248, 7, 249, 241]
			accounts: [
				{
					name: 'buyer'
					docs: ['The buyer creating the offer']
					writable: true
					signer: true
				},
				{
					name: 'asset'
					docs: ['The NFT asset being offered on']
				},
				{
					name: 'offer'
					docs: ['The offer account to be created']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [111, 102, 102, 101, 114]
							},
							{
								kind: 'account'
								path: 'buyer'
							},
							{
								kind: 'account'
								path: 'asset'
							},
						]
					}
				},
				{
					name: 'escrow'
					docs: ['Escrow account to hold the payment']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									101,
									115,
									99,
									114,
									111,
									119,
									45,
									112,
									97,
									121,
									109,
									101,
									110,
									116,
								]
							},
							{
								kind: 'account'
								path: 'offer'
							},
						]
					}
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace config for validation rules']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'systemProgram'
					docs: ['System program for CPI']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamp validation']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: [
				{
					name: 'price'
					type: 'u64'
				},
				{
					name: 'expiresIn'
					type: 'i64'
				},
			]
		},
		{
			name: 'initialize'
			docs: ['Initialize the marketplace with configuration']
			discriminator: [175, 175, 109, 31, 13, 152, 155, 237]
			accounts: [
				{
					name: 'payer'
					docs: ['Payer for the transaction']
					writable: true
					signer: true
				},
				{
					name: 'admin'
					docs: ['Admin that can update the marketplace configuration']
					writable: true
					signer: true
				},
				{
					name: 'feeCollector'
					docs: ['Account that will receive marketplace fees']
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration account']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'marketplaceAuthority'
					docs: ['Marketplace authority account']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									97,
									117,
									116,
									104,
									111,
									114,
									105,
									116,
									121,
								]
							},
						]
					}
				},
				{
					name: 'systemProgram'
					docs: ['System program']
					address: '11111111111111111111111111111111'
				},
			]
			args: [
				{
					name: 'config'
					type: {
						defined: {
							name: 'marketplaceConfigArgs'
						}
					}
				},
			]
		},
		{
			name: 'placeBid'
			docs: ['Place bidding on an auction listing']
			discriminator: [238, 77, 148, 91, 200, 151, 92, 146]
			accounts: [
				{
					name: 'bidder'
					docs: ['Bidder placing the bid']
					writable: true
					signer: true
				},
				{
					name: 'asset'
					writable: true
				},
				{
					name: 'listing'
					docs: ['Auction Listing being bid on']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [108, 105, 115, 116, 105, 110, 103]
							},
							{
								kind: 'account'
								path: 'asset'
							},
							{
								kind: 'account'
								path: 'listing.seller'
								account: 'listing'
							},
						]
					}
				},
				{
					name: 'auction'
					docs: ['Auction-specific state to update']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [97, 117, 99, 116, 105, 111, 110]
							},
							{
								kind: 'account'
								path: 'listing'
							},
						]
					}
				},
				{
					name: 'previousBidder'
					docs: [
						'Needs to be mutable to receive funds. Required if auction.highest_bidder is Some.',
					]
					writable: true
					optional: true
				},
				{
					name: 'escrow'
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									101,
									115,
									99,
									114,
									111,
									119,
									45,
									112,
									97,
									121,
									109,
									101,
									110,
									116,
								]
							},
							{
								kind: 'account'
								path: 'listing'
							},
						]
					}
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration (needed for min bid increment)']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'systemProgram'
					docs: ['System program']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamps']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: [
				{
					name: 'bidAmount'
					type: 'u64'
				},
			]
		},
		{
			name: 'purchaseListing'
			docs: ['Purchase a fixed price listing']
			discriminator: [246, 29, 226, 161, 105, 118, 198, 150]
			accounts: [
				{
					name: 'buyer'
					docs: ['Buyer purchasing the NFT']
					writable: true
					signer: true
				},
				{
					name: 'seller'
					docs: ['Seller who created the listing']
					writable: true
				},
				{
					name: 'asset'
					docs: ['The NFT asset being purchased']
					writable: true
				},
				{
					name: 'collection'
					writable: true
					optional: true
				},
				{
					name: 'listing'
					docs: ['Listing being purchased']
					writable: true
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [108, 105, 115, 116, 105, 110, 103]
							},
							{
								kind: 'account'
								path: 'asset'
							},
							{
								kind: 'account'
								path: 'seller'
							},
						]
					}
				},
				{
					name: 'marketplaceConfig'
					docs: ['Marketplace configuration']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									99,
									111,
									110,
									102,
									105,
									103,
								]
							},
						]
					}
				},
				{
					name: 'marketplaceAuthority'
					docs: ['Marketplace authority account']
					pda: {
						seeds: [
							{
								kind: 'const'
								value: [
									109,
									97,
									114,
									107,
									101,
									116,
									112,
									108,
									97,
									99,
									101,
									45,
									97,
									117,
									116,
									104,
									111,
									114,
									105,
									116,
									121,
								]
							},
						]
					}
				},
				{
					name: 'feeCollector'
					docs: ['Fee collector account']
					writable: true
				},
				{
					name: 'mplCoreProgram'
					docs: ['MPL Core program']
					address: 'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d'
				},
				{
					name: 'systemProgram'
					docs: ['System program']
					address: '11111111111111111111111111111111'
				},
				{
					name: 'clock'
					docs: ['Clock for timestamps']
					address: 'SysvarC1ock11111111111111111111111111111111'
				},
			]
			args: []
		},
	]
	accounts: [
		{
			name: 'auction'
			discriminator: [218, 94, 247, 242, 126, 233, 131, 81]
		},
		{
			name: 'baseAssetV1'
			discriminator: [0, 0, 0, 0, 0, 0, 0, 0]
		},
		{
			name: 'listing'
			discriminator: [218, 32, 50, 73, 43, 134, 26, 58]
		},
		{
			name: 'marketplaceConfig'
			discriminator: [169, 22, 247, 131, 182, 200, 81, 124]
		},
		{
			name: 'offer'
			discriminator: [215, 88, 60, 71, 170, 162, 73, 229]
		},
	]
	events: [
		{
			name: 'MarketplaceInitializedEvent'
			discriminator: [210, 12, 132, 72, 140, 185, 169, 157]
		},
		{
			name: 'ListingCreatedEvent'
			discriminator: [73, 147, 169, 146, 121, 205, 118, 3]
		},
		{
			name: 'ListingCancelledEvent'
			discriminator: [203, 204, 24, 205, 33, 24, 151, 195]
		},
		{
			name: 'ListingPurchasedEvent'
			discriminator: [157, 158, 113, 67, 106, 149, 68, 41]
		},
		{
			name: 'BidPlacedEvent'
			discriminator: [141, 248, 16, 119, 196, 169, 112, 121]
		},
		{
			name: 'OfferCreatedEvent'
			discriminator: [32, 143, 189, 56, 177, 0, 13, 106]
		},
		{
			name: 'OfferAcceptedEvent'
			discriminator: [31, 235, 72, 77, 41, 239, 44, 71]
		},
		{
			name: 'OfferCancelledEvent'
			discriminator: [28, 92, 211, 104, 195, 62, 50, 196]
		},
	]
	types: [
		{
			name: 'auction'
			docs: [
				'Stores the dynamic state of an active auction, linked to a Listing account.',
			]
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'listing'
						docs: ['The parent Listing account of this auction.']
						type: 'pubkey'
					},
					{
						name: 'auctionType'
						docs: ['The type of auction, determines bidding rules.']
						type: {
							defined: {
								name: 'auctionType'
							}
						}
					},
					{
						name: 'highestBid'
						docs: ['The amount of the current highest bid in lamports.']
						type: 'u64'
					},
					{
						name: 'highestBidder'
						docs: ['The public key of the current highest bidder.']
						type: {
							option: 'pubkey'
						}
					},
					{
						name: 'bidsCount'
						docs: ['The total number of bids placed on this auction.']
						type: 'u32'
					},
					{
						name: 'startTime'
						docs: ['The timestamp when bidding is scheduled to start.']
						type: 'i64'
					},
					{
						name: 'endTime'
						docs: [
							'The timestamp when the auction is scheduled to end.',
							'This can be extended by the `extension_period` if bids occur near the end.',
						]
						type: 'i64'
					},
					{
						name: 'extensionPeriod'
						docs: [
							'The duration (in seconds) by which the auction `end_time` is extended',
							'if a new highest bid is placed within this period before the current `end_time`.',
						]
						type: 'u64'
					},
					{
						name: 'escrowBump'
						docs: ['Escrow bump']
						type: 'u8'
					},
					{
						name: 'bump'
						docs: ['PDA bump seed.']
						type: 'u8'
					},
				]
			}
		},
		{
			name: 'auctionType'
			docs: ['Auction types']
			type: {
				kind: 'enum'
				variants: [
					{
						name: 'english'
					},
				]
			}
		},
		{
			name: 'baseAssetV1'
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'key'
						type: {
							defined: {
								name: 'key'
							}
						}
					},
					{
						name: 'owner'
						type: 'pubkey'
					},
					{
						name: 'updateAuthority'
						type: {
							defined: {
								name: 'updateAuthority'
							}
						}
					},
					{
						name: 'name'
						type: 'string'
					},
					{
						name: 'uri'
						type: 'string'
					},
					{
						name: 'seq'
						type: {
							option: 'u64'
						}
					},
				]
			}
		},
		{
			name: 'bidPlacedEvent'
			docs: ['Event emitted when a bid is placed on an auction']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'listing'
						docs: ['Listing account']
						type: 'pubkey'
					},
					{
						name: 'auction'
						docs: ['Auction account']
						type: 'pubkey'
					},
					{
						name: 'bidder'
						docs: ['Bidder who placed the bid']
						type: 'pubkey'
					},
					{
						name: 'bidAmount'
						docs: ['Bid amount in lamports']
						type: 'u64'
					},
					{
						name: 'timestamp'
						docs: ['When the bid was placed']
						type: 'i64'
					},
				]
			}
		},
		{
			name: 'key'
			type: {
				kind: 'enum'
				variants: [
					{
						name: 'uninitialized'
					},
					{
						name: 'assetV1'
					},
					{
						name: 'hashedAssetV1'
					},
					{
						name: 'pluginHeaderV1'
					},
					{
						name: 'pluginRegistryV1'
					},
					{
						name: 'collectionV1'
					},
				]
			}
		},
		{
			name: 'listing'
			docs: ['NFT listing account.']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'version'
						docs: ['Version for future upgrades']
						type: 'u8'
					},
					{
						name: 'listingType'
						docs: ['Type of listing and link to Auction if applicable']
						type: {
							defined: {
								name: 'listingType'
							}
						}
					},
					{
						name: 'seller'
						docs: ['Seller of the NFT']
						type: 'pubkey'
					},
					{
						name: 'asset'
						docs: ['NFT asset being sold']
						type: 'pubkey'
					},
					{
						name: 'price'
						docs: ['Price in lamports']
						type: 'u64'
					},
					{
						name: 'currency'
						docs: ['Optional SPL token for payment (None = SOL)']
						type: {
							option: 'pubkey'
						}
					},
					{
						name: 'isActive'
						docs: ['Whether the listing is active']
						type: 'bool'
					},
					{
						name: 'createdAt'
						docs: ['Timestamp when the listing was created']
						type: 'i64'
					},
					{
						name: 'updatedAt'
						docs: [
							'Timestamp when the listing was last updated (e.g., cancelled)',
						]
						type: 'i64'
					},
					{
						name: 'endsAt'
						docs: ['When the listing ends (for auctions)']
						type: {
							option: 'i64'
						}
					},
					{
						name: 'bump'
						docs: ['PDA bump']
						type: 'u8'
					},
				]
			}
		},
		{
			name: 'listingCancelledEvent'
			docs: ['Event emitted when a listing is cancelled']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'seller'
						docs: ['Seller of the NFT']
						type: 'pubkey'
					},
					{
						name: 'asset'
						docs: ['NFT asset being sold']
						type: 'pubkey'
					},
					{
						name: 'listing'
						docs: ['Listing account']
						type: 'pubkey'
					},
					{
						name: 'cancelledAt'
						docs: ['When the listing was cancelled']
						type: 'i64'
					},
				]
			}
		},
		{
			name: 'listingCreatedEvent'
			docs: ['Event emitted when a new listing is created']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'seller'
						docs: ['Seller of the NFT']
						type: 'pubkey'
					},
					{
						name: 'asset'
						docs: ['NFT asset being sold']
						type: 'pubkey'
					},
					{
						name: 'listing'
						docs: ['Listing account']
						type: 'pubkey'
					},
					{
						name: 'price'
						docs: ['Price in lamports']
						type: 'u64'
					},
					{
						name: 'listingType'
						docs: [
							'TODO: Create as enum',
							'Listing type (fixed price, auction)',
						]
						type: {
							defined: {
								name: 'listingType'
							}
						}
					},
					{
						name: 'createdAt'
						docs: ['When the listing was created']
						type: 'i64'
					},
					{
						name: 'endsAt'
						docs: ['When the listing ends']
						type: 'i64'
					},
				]
			}
		},
		{
			name: 'listingPurchasedEvent'
			docs: ['Event emitted when a listing is purchased']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'seller'
						docs: ['Seller of the NFT']
						type: 'pubkey'
					},
					{
						name: 'buyer'
						docs: ['Buyer of the NFT']
						type: 'pubkey'
					},
					{
						name: 'asset'
						docs: ['NFT asset being sold']
						type: 'pubkey'
					},
					{
						name: 'listing'
						docs: ['Listing account']
						type: 'pubkey'
					},
					{
						name: 'price'
						docs: ['Price paid in lamports']
						type: 'u64'
					},
					{
						name: 'protocolFee'
						docs: ['Protocol fee amount']
						type: 'u64'
					},
					{
						name: 'royaltyPayment'
						docs: ['Royalty fee distributed']
						type: 'u64'
					},
					{
						name: 'purchasedAt'
						docs: ['When the purchase occurred']
						type: 'i64'
					},
				]
			}
		},
		{
			name: 'listingType'
			docs: ['Listing types']
			type: {
				kind: 'enum'
				variants: [
					{
						name: 'fixedPrice'
					},
					{
						name: 'auction'
						fields: ['pubkey']
					},
				]
			}
		},
		{
			name: 'marketplaceConfig'
			docs: ['Marketplace configuration account']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'version'
						docs: ['Version for future upgrades']
						type: 'u8'
					},
					{
						name: 'admin'
						docs: ['admin that can update the configuration']
						type: 'pubkey'
					},
					{
						name: 'feeCollector'
						docs: ['Account that receives marketplace fees']
						type: 'pubkey'
					},
					{
						name: 'protocolFeeBp'
						docs: ['Protocol fee in basis points (e.g., 250 = 2.5%)']
						type: 'u16'
					},
					{
						name: 'minBidIncrementBp'
						docs: ['Minimum bid increment in basis points (e.g., 500 = 5%)']
						type: 'u16'
					},
					{
						name: 'minListingDuration'
						docs: ['Minimum listing duration in seconds']
						type: 'u64'
					},
					{
						name: 'maxListingDuration'
						docs: ['Maximum listing duration in seconds']
						type: 'u64'
					},
					{
						name: 'bump'
						docs: ['PDA bump']
						type: 'u8'
					},
					{
						name: 'marketplaceAuthorityBump'
						docs: ['PDA bump for marketplace authority']
						type: 'u8'
					},
				]
			}
		},
		{
			name: 'marketplaceConfigArgs'
			docs: ['Arguments for initializing or updating marketplace configuration']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'protocolFeeBp'
						docs: ['Protocol fee in basis points (e.g., 250 = 2.5%)']
						type: 'u16'
					},
					{
						name: 'minBidIncrementBp'
						docs: ['Minimum bid increment in basis points (e.g., 500 = 5%)']
						type: 'u16'
					},
					{
						name: 'minListingDuration'
						docs: ['Minimum listing duration in seconds']
						type: 'u64'
					},
					{
						name: 'maxListingDuration'
						docs: ['Maximum listing duration in seconds']
						type: 'u64'
					},
				]
			}
		},
		{
			name: 'marketplaceInitializedEvent'
			docs: ['Event emitted when the marketplace is initialized']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'admin'
						docs: ['Admin that can update the marketplace configuration']
						type: 'pubkey'
					},
					{
						name: 'feeCollector'
						docs: ['Account that receives marketplace fees']
						type: 'pubkey'
					},
					{
						name: 'protocolFeeBp'
						docs: ['Protocol fee in basis points']
						type: 'u16'
					},
				]
			}
		},
		{
			name: 'offer'
			docs: ['Offer account']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'version'
						docs: ['Version for future upgrades']
						type: 'u8'
					},
					{
						name: 'buyer'
						docs: ['Buyer making the offer']
						type: 'pubkey'
					},
					{
						name: 'asset'
						docs: ['Specific NFT asset']
						type: 'pubkey'
					},
					{
						name: 'price'
						docs: ['Price offered in lamports']
						type: 'u64'
					},
					{
						name: 'currency'
						docs: ['Optional SPL token for payment (None = SOL)']
						type: {
							option: 'pubkey'
						}
					},
					{
						name: 'expiresAt'
						docs: ['When the offer expires']
						type: 'i64'
					},
					{
						name: 'createdAt'
						docs: ['When the offer was created']
						type: 'i64'
					},
					{
						name: 'isFilled'
						docs: ['Whether the offer has been filled']
						type: 'bool'
					},
					{
						name: 'isCancelled'
						docs: ['Whether the offer has been cancelled']
						type: 'bool'
					},
					{
						name: 'escrowBump'
						docs: ['PDA bump']
						type: 'u8'
					},
					{
						name: 'bump'
						docs: ['PDA bump']
						type: 'u8'
					},
				]
			}
		},
		{
			name: 'offerAcceptedEvent'
			docs: ['Event emitted when an offer is accepted']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'offer'
						docs: ['The offer account public key']
						type: 'pubkey'
					},
					{
						name: 'buyer'
						docs: ['The buyer who made the offer']
						type: 'pubkey'
					},
					{
						name: 'seller'
						docs: ['The seller who accepted the offer']
						type: 'pubkey'
					},
					{
						name: 'asset'
						docs: ['The asset being sold']
						type: 'pubkey'
					},
					{
						name: 'price'
						docs: ['The price in lamports']
						type: 'u64'
					},
					{
						name: 'protocolFee'
						docs: ['Protocol fee collected']
						type: 'u64'
					},
					{
						name: 'royaltyPayment'
						docs: ['Royalty fee distributed']
						type: 'u64'
					},
					{
						name: 'acceptedAt'
						docs: ['Timestamp when the offer was accepted']
						type: 'i64'
					},
				]
			}
		},
		{
			name: 'offerCancelledEvent'
			docs: ['Event emitted when an offer is cancelled']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'offer'
						docs: ['The offer account public key']
						type: 'pubkey'
					},
					{
						name: 'buyer'
						docs: ['The buyer who cancelled the offer']
						type: 'pubkey'
					},
					{
						name: 'asset'
						docs: ['The asset the offer was for']
						type: 'pubkey'
					},
					{
						name: 'cancelledAt'
						docs: ['Timestamp when the offer was cancelled']
						type: 'i64'
					},
				]
			}
		},
		{
			name: 'offerCreatedEvent'
			docs: ['Event emitted when an offer is created']
			type: {
				kind: 'struct'
				fields: [
					{
						name: 'offer'
						docs: ['The offer account public key']
						type: 'pubkey'
					},
					{
						name: 'buyer'
						docs: ['The buyer who made the offer']
						type: 'pubkey'
					},
					{
						name: 'asset'
						docs: ['The asset the offer is for']
						type: 'pubkey'
					},
					{
						name: 'price'
						docs: ['The price offered in lamports']
						type: 'u64'
					},
					{
						name: 'expiresAt'
						docs: ['Timestamp when the offer expires']
						type: 'i64'
					},
					{
						name: 'createdAt'
						docs: ['Timestamp when the offer was created']
						type: 'i64'
					},
				]
			}
		},
		{
			name: 'updateAuthority'
			type: {
				kind: 'enum'
				variants: [
					{
						name: 'none'
					},
					{
						name: 'address'
						fields: ['pubkey']
					},
					{
						name: 'collection'
						fields: ['pubkey']
					},
				]
			}
		},
	]
}
