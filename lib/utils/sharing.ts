import { toast } from 'sonner'
import customToast from '@/components/CustomToast'

/**
 * Copies the provided text to clipboard
 * @param text Text to copy to clipboard
 * @param successMessage Optional success message to show
 * @returns Promise that resolves to true if successful, false otherwise
 */
export const copyToClipboard = async (
	text: string,
	successMessage = 'Copied to clipboard!',
): Promise<boolean> => {
	// Check if running in browser environment
	if (typeof navigator === 'undefined' || !navigator.clipboard) {
		console.error('Clipboard API not available')
		customToast.error('Clipboard not available in this environment')
		return false
	}

	try {
		await navigator.clipboard.writeText(text)
		customToast.success(successMessage)
		return true
	} catch (error) {
		console.error('Failed to copy to clipboard:', error)
		customToast.error('Failed to copy to clipboard')
		return false
	}
}

/**
 * Shares content using the Web Share API if available
 * Falls back to copying to clipboard if Web Share API is not available
 *
 * @param options Share options
 * @returns Promise that resolves to true if successful, false otherwise
 */
export const shareContent = async (options: {
	title: string
	text: string
	url: string
}): Promise<boolean> => {
	// Check if running in browser environment and Web Share API is available
	if (typeof navigator !== 'undefined' && 'share' in navigator) {
		try {
			await navigator.share(options)
			return true
		} catch (error) {
			// User cancelled or share failed
			if ((error as Error).name !== 'AbortError') {
				console.error('Error sharing content:', error)
				customToast.error('Failed to share content')
			}
			return false
		}
	} else {
		// Fallback to copying the URL
		return copyToClipboard(options.url, 'Link copied to clipboard!')
	}
}
