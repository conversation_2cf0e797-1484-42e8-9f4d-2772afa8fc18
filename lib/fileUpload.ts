import { onFileUploadPresignedUrl } from '@/server/app/files/files.telefunc'

export async function uploadFile(file: File) {
	try {
		// Get presigned URL from server
		const presignedUrlResult = await onFileUploadPresignedUrl({
			fileName: file.name,
			fileType: file.type,
		})

		if (!presignedUrlResult.success || !presignedUrlResult.url) {
			throw new Error(presignedUrlResult.message || 'Failed to get upload URL')
		}

		const presignedUrl = presignedUrlResult.url

		// Create custom fetch with abort controller
		const customUpload = async () => {
			const abortController = new AbortController()

			// Abort the request if it takes longer than 60 seconds
			setTimeout(() => {
				abortController.abort()
			}, 60000)

			// Use node-fetch compatible approach that works in browsers
			const response = await fetch(presignedUrl, {
				method: 'PUT',
				headers: {
					'Content-Type': file.type,
				},
				body: file,
				signal: abortController.signal,
			})

			if (!response.ok) {
				throw new Error(`HTTP Error: ${response.status}`)
			}

			return response
		}
		// Perform the actual upload
		await customUpload()
		// The final URL will depend on your S3/R2 configuration
		const finalUrl = presignedUrlResult.fileUrl as string

		return {
			success: true,
			message: 'File uploaded successfully',
			url: finalUrl,
		}
	} catch (error) {
		return {
			success: false,
			message: 'Error uploading file',
			url: '',
		}
	} finally {
	}
}
