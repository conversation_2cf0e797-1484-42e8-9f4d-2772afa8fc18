import {
	type AssetV1,
	fetchAssetsByCollection,
	fetchCollection,
} from '@metaplex-foundation/mpl-core'
import { type PublicKey, publicKey } from '@metaplex-foundation/umi'
import { getServerUmi } from '@/server/lib/umiClient'
import type {
	EnhancedAsset,
	EnhancedCollection,
} from '../../types/enhanced-assets'
import type { MetaData } from '../../types/MetaData'

/**
 * Fetches metadata for an asset from its URI
 * @param uri The URI pointing to the metadata
 * @returns The metadata or null if fetching failed
 */
export async function fetchMetadata(uri: string): Promise<MetaData | null> {
	try {
		const metaDataResponse = await fetch(uri, {
			headers: {
				Accept: 'application/json',
			},
		})

		if (!metaDataResponse.ok) {
			console.error(
				`Failed to fetch metadata from URI ${uri}: ${metaDataResponse.statusText}`,
			)
			return null
		}

		return (await metaDataResponse.json()) as MetaData
	} catch (error) {
		console.error(`Error fetching metadata from URI ${uri}:`, error)
		return null
	}
}

/**
 * Enhances an asset by adding its metadata
 * @param asset The asset to enhance
 * @returns The enhanced asset or null if fetching metadata failed
 */
export async function enhanceAssetWithMetadata(
	asset: AssetV1,
): Promise<EnhancedAsset | null> {
	if (!asset.uri) {
		return null
	}
	const metadata = await fetchMetadata(asset.uri)

	if (!metadata) {
		return null
	}

	return {
		...asset,
		metadata,
	}
}

/**
 * Fetches a collection by its ID and enhances it with metadata
 * @param collectionId The collection ID as a string
 * @returns The enhanced collection
 * @throws Error if collection or metadata cannot be fetched
 */
export async function fetchEnhancedCollection(
	collectionId: string,
): Promise<EnhancedCollection> {
	try {
		const umi = getServerUmi()
		const collectionKey = publicKey(collectionId)
		const collection = await fetchCollection(umi, collectionKey)

		const metadata = await fetchMetadata(collection.uri)

		if (!metadata) {
			throw new Error('Failed to fetch collection metadata')
		}

		return {
			...collection,
			metadata,
		}
	} catch (error) {
		console.error('Error fetching collection:', error)
		throw error
	}
}

/**
 * Fetches all assets in a collection and enhances them with metadata
 * @param collectionKey The public key of the collection
 * @returns Array of enhanced assets
 */
export async function fetchEnhancedAssetsByCollection(
	collectionKey: PublicKey,
): Promise<EnhancedAsset[]> {
	const umi = getServerUmi()
	
	// Fetch live data from blockchain
	const assetsByCollection = await fetchAssetsByCollection(umi, collectionKey, {
		skipDerivePlugins: true, // Skip plugin derivation for faster fetching
	})

	// Create promises for all asset enhancements
	const enhancementPromises = assetsByCollection.map(async (asset) => {
		try {
			const enhancedAsset = await enhanceAssetWithMetadata(asset)
			return {
				success: true,
				asset: enhancedAsset,
			}
		} catch (error) {
			console.error(`Error processing asset ${asset.key}:`, error)
			return {
				success: false,
				error,
			}
		}
	})

	// Execute all enhancement promises concurrently
	const results = await Promise.all(enhancementPromises)
	
	// Process results and gather enhanced assets
	const enhancedAssets: EnhancedAsset[] = []

	for (const result of results) {
		if (result.success && result.asset) {
			enhancedAssets.push(result.asset)
		}
	}

	return enhancedAssets
}

