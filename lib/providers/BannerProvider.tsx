import { createContext, type <PERSON>actNode, useContext, useState } from 'react'

interface BannerContextType {
	bannerUrl: string | null
	setBannerUrl: (url: string | null) => void
	resetBannerUrl: () => void
}

const DEFAULT_BANNER_URL = null

const BannerContext = createContext<BannerContextType | undefined>(undefined)

export const BannerProvider = ({ children }: { children: ReactNode }) => {
	const [bannerUrl, setBannerUrl] = useState<string | null>(DEFAULT_BANNER_URL)

	const resetBannerUrl = () => {
		setBannerUrl(null)
	}

	return (
		<BannerContext.Provider value={{ bannerUrl, setBannerUrl, resetBannerUrl }}>
			{children}
		</BannerContext.Provider>
	)
}

export const useBannerUrl = () => {
	const context = useContext(BannerContext)
	if (context === undefined) {
		throw new Error('useBannerUrl must be used within a BannerProvider')
	}
	return context
}
