import { useEffect, useState } from 'react'
import { useIntersectionObserver } from 'usehooks-ts'

export function useScrollDetection(threshold = 0.7) {
	const [isScrolled, setIsScrolled] = useState(false)
	const { isIntersecting, ref } = useIntersectionObserver({ threshold })

	useEffect(() => {
		if (isIntersecting) {
			setIsScrolled(false)
		} else {
			setIsScrolled(true)
		}
	}, [isIntersecting])

	return { isScrolled, ref }
}
