import { useEffect, useState } from 'react'

// Constants
const SOL_PRICE_STORAGE_KEY = 'solana_price_data'
const MAX_CACHE_AGE_MS = 30 * 60 * 1000 // 30 minutes

type SolanaPriceData = {
	price: number
	timestamp: number
}

/**
 * Get cached Solana price data from localStorage
 */
const getCachedSolPrice = (): SolanaPriceData | null => {
	if (typeof window === 'undefined') {
		return null
	}

	try {
		const storedData = localStorage.getItem(SOL_PRICE_STORAGE_KEY)
		if (!storedData) {
			return null
		}

		const parsed = JSON.parse(storedData) as SolanaPriceData
		return parsed
	} catch (err) {
		console.error('Failed to parse cached SOL price:', err)
		return null
	}
}

/**
 * Store Solana price data in localStorage
 */
const storeSolPrice = (price: number): void => {
	if (typeof window === 'undefined') {
		return
	}

	try {
		const data: SolanaPriceData = {
			price,
			timestamp: Date.now(),
		}
		localStorage.setItem(SOL_PRICE_STORAGE_KEY, JSON.stringify(data))
	} catch (err) {
		console.error('Failed to store SOL price in localStorage:', err)
	}
}

/**
 * Check if cached data is fresh (within 30 minutes)
 */
const isCacheFresh = (cachedData: SolanaPriceData | null): boolean => {
	if (!cachedData) return false
	const ageMs = Date.now() - cachedData.timestamp
	return ageMs < MAX_CACHE_AGE_MS
}

/**
 * Custom hook to fetch and track the current Solana price in USD
 * @param refreshInterval - Interval in milliseconds to refresh the price (default: 5 minutes)
 * @returns The current SOL to USD conversion rate
 */
export function useSolanaPrice(refreshInterval = 5 * 60 * 1000) {
	// Initialize state from localStorage if available and fresh - use function form to avoid re-calculation
	const [solToUsd, setSolToUsd] = useState<number>(() => {
		const cachedData = getCachedSolPrice()

		if (cachedData && isCacheFresh(cachedData)) {
			return cachedData.price
		}

		return 0
	})

	useEffect(() => {
		const fetchSolPrice = async () => {
			try {
				const response = await fetch(
					'https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd',
				)
				const data = await response.json()
				const price = data.solana.usd

				setSolToUsd(price)
				storeSolPrice(price)
			} catch (err) {
				console.error('Failed to fetch SOL price:', err)

				// If fetch fails but we have cached data, use it regardless of age
				const cachedData = getCachedSolPrice()
				if (cachedData && cachedData.price > 0) {
					setSolToUsd(cachedData.price)
				} else {
					setSolToUsd(0) // Default to 0 if fetch fails and no cache
				}
			}
		}

		// Check if we need to fetch initially
		const cachedData = getCachedSolPrice()
		const needsInitialFetch = !cachedData || !isCacheFresh(cachedData)
		
		if (needsInitialFetch) {
			fetchSolPrice()
		}

		// Set up interval for periodic refreshes
		const interval = setInterval(() => {
			const currentCachedData = getCachedSolPrice()
			
			if (!currentCachedData || !isCacheFresh(currentCachedData)) {
				fetchSolPrice()
			}
		}, refreshInterval)

		// Clean up interval on component unmount
		return () => {
			clearInterval(interval)
		}
	}, [refreshInterval])

	return solToUsd
}
