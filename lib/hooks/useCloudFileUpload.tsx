import { useCallback, useEffect, useRef, useState } from 'react'
import { uploadFile } from '../fileUpload'

interface UseCloudFileUploadProps {
	onUpload?: (url: string) => void
}

export function useCloudFileUpload({ onUpload }: UseCloudFileUploadProps = {}) {
	const previewRef = useRef<string | null>(null)
	const fileInputRef = useRef<HTMLInputElement>(null)
	const [previewUrl, setPreviewUrl] = useState<string | null>(null)
	const [fileName, setFileName] = useState<string | null>(null)
	const [file, setFile] = useState<File | null>(null)
	const [isUploading, setIsUploading] = useState(false)

	const handleThumbnailClick = useCallback(() => {
		fileInputRef.current?.click()
	}, [])

	const handleFileChange = useCallback(
		async (event: React.ChangeEvent<HTMLInputElement>) => {
			setIsUploading(true)
			const file = event.target.files?.[0]
			if (file) {
				try {
					const fileUploadResponse = await uploadFile(file)

					if (!fileUploadResponse.success) {
						setIsUploading(false)
						return
					}
					setPreviewUrl(fileUploadResponse.url)

					previewRef.current = fileUploadResponse.url
					onUpload?.(fileUploadResponse.url)
				} catch (e) {
					console.error('Error uploading file:', e)
				} finally {
					setIsUploading(false)
				}
			}
			setIsUploading(false)
		},
		[onUpload],
	)

	const handleRemove = useCallback(() => {
		if (previewUrl) {
			URL.revokeObjectURL(previewUrl)
		}
		setPreviewUrl(null)
		setFileName(null)
		setFile(null)
		previewRef.current = null
		if (fileInputRef.current) {
			fileInputRef.current.value = ''
		}
	}, [previewUrl])

	useEffect(() => {
		return () => {
			if (previewRef.current) {
				URL.revokeObjectURL(previewRef.current)
			}
		}
	}, [])

	return {
		previewUrl,
		fileName,
		file,
		fileInputRef,
		handleThumbnailClick,
		handleFileChange,
		handleRemove,
		isUploading,
	}
}
