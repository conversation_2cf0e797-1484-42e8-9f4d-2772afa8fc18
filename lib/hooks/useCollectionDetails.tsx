import { useQuery } from '@tanstack/react-query'
import type { CollectionDetailsResponse } from '@/types/collection.types'

/**
 * Hook to fetch collection details by ID
 * @param collectionId - The ID of the collection to fetch
 * @returns Query result with collection details
 */
export function useCollectionDetails(collectionId: string | undefined) {
	return useQuery({
		queryKey: ['collection', collectionId],
		queryFn: async (): Promise<CollectionDetailsResponse> => {
			if (!collectionId) {
				throw new Error('Collection ID is required')
			}
			const response = await fetch(`/api/collection/${collectionId}`)
			if (!response.ok) {
				const errorData = await response.json()
				throw new Error(
					errorData.message || 'Failed to fetch collection details',
				)
			}
			return response.json()
		},
		enabled: !!collectionId, // Only run the query if collectionId is provided
	})
}
