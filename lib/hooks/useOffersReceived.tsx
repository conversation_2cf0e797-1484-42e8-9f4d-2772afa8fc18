import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import {
	type OffersReceivedResponse,
	type PaginationInput,
	paginatedOffersReceivedResponseSchema,
	paginationSchema,
} from '@/types/user.types'

export function useOffersReceived(params: Partial<PaginationInput> = {}) {
	const [isInitialLoading, setIsInitialLoading] = useState(true)

	const validationResult = paginationSchema.safeParse({
		page: params.page ?? 1,
		limit: params.limit ?? 10,
		search: params.search,
		sort: params.sort,
	})

	const validatedParams = validationResult.success
		? validationResult.data
		: { page: 1, limit: 10 }

	if (!validationResult.success) {
		console.warn('Invalid pagination parameters:', validationResult.error)
	}

	const queryParams = new URLSearchParams()
	queryParams.append('page', validatedParams.page.toString())
	queryParams.append('limit', validatedParams.limit.toString())
	if (validatedParams.search) {
		queryParams.append('search', validatedParams.search)
	}
	if (validatedParams.sort) {
		queryParams.append('sort', validatedParams.sort)
	}

	const query = useQuery({
		queryKey: ['offersMade', validatedParams],
		queryFn: async (): Promise<OffersReceivedResponse> => {
			try {
				const response = await fetch(
					`/api/users/offers-received?${queryParams.toString()}`,
					{
						headers: {
							'Content-Type': 'application/json',
						},
					},
				)

				if (!response.ok) {
					const errorData = await response.json()
					throw new Error(errorData.message || 'Failed to fetch offers made')
				}

				const data = await response.json()

				try {
					const responseValidation =
						paginatedOffersReceivedResponseSchema.safeParse(data)
					if (!responseValidation.success) {
						console.error(
							'Response validation failed:',
							responseValidation.error,
						)
						throw new Error('Invalid response format from server')
					}
					return responseValidation.data
				} catch (validationError) {
					console.error('Response validation failed:', validationError)
					throw new Error('Invalid response format from server')
				}
			} finally {
				setIsInitialLoading(false)
			}
		},
		retry: 2,
		staleTime: 30000,
	})

	useEffect(() => {
		if (!query.isPending && isInitialLoading) {
			setIsInitialLoading(false)
		}
	}, [query.isPending, isInitialLoading])

	return {
		...query,
		isInitialLoading,
		isRefetching: query.isFetching && !query.isPending,
		data: query.data ?? {
			offers: [],
			total: 0,
			page: validatedParams.page,
			limit: validatedParams.limit,
		},
	}
}
