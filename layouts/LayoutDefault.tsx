import './tailwind.css'
import '@solana/wallet-adapter-react-ui/styles.css'
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base'
import {
	ConnectionProvider,
	WalletProvider,
} from '@solana/wallet-adapter-react'
import { clusterApiUrl } from '@solana/web3.js'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import type React from 'react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { Toaster } from 'sonner'
import { navigate } from 'vike/client/router'
import { usePageContext } from 'vike-react/usePageContext'
import Footer from '@/components/Footer'
import GlobalLoader from '@/components/GlobalLoader'
import Header from '@/components/Header'
import { LoginButton } from '@/components/login/LoginButton'
import { UmiProvider } from '@/components/UmiProvider'
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog'
import { BannerProvider } from '@/lib/providers/BannerProvider'

// Create a new QueryClient with default options to prevent excessive re-renders
const queryClient = new QueryClient({})

export default function LayoutDefault({
	children,
}: {
	children: React.ReactNode
}) {
	// The network can be set to 'devnet', 'testnet', or 'mainnet-beta'.
	const network = WalletAdapterNetwork.Devnet
	const [isModalOpen, setIsModalOpen] = useState(false)
	const pageContext = usePageContext()

	// Define protected pages that require authentication
	const protectedPages = ['/edit-profile', '/studio']

	// You can also provide a custom RPC endpoint.
	const endpoint = useMemo(() => clusterApiUrl(network), [network])

	const wallets = useMemo(
		() => [
			/**
			 * Wallets that implement either of these standards will be available automatically.
			 *
			 *   - Solana Mobile Stack Mobile Wallet Adapter Protocol
			 *     (https://github.com/solana-mobile/mobile-wallet-adapter)
			 *   - Solana Wallet Standard
			 *     (https://github.com/anza-xyz/wallet-standard)
			 *
			 * If you wish to support a wallet that supports neither of those standards,
			 * instantiate its legacy wallet adapter here. Common legacy adapters can be found
			 * in the npm package `@solana/wallet-adapter-wallets`.
			 */
		],
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[],
	)
	const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)

	// Check authentication status on mount
	useEffect(() => {
		const accessToken = localStorage.getItem('accessToken')
		setIsAuthenticated(!!accessToken)

		// Get the current path
		const pathname = pageContext.urlPathname

		// Only show the modal for protected pages if not authenticated
		if (
			!accessToken &&
			protectedPages.some((page) => pathname.startsWith(page))
		) {
			setIsModalOpen(true)
		}
	}, [pageContext.urlPathname])
	return (
		<QueryClientProvider client={queryClient}>
			<ConnectionProvider endpoint={endpoint}>
				<WalletProvider wallets={wallets} autoConnect>
					<UmiProvider>
						<BannerProvider>
							<Header />
							<main>{children}</main>
							<Toaster />
							{/* <div
							id='overlay'
							className='fixed inset-x-0 top-0 bottom-0 z-[998] hidden pointer-events-none transition-all bg-background/50 backdrop-blur-sm'
							/> */}

							{/* Modal for unauthenticated users */}
							<Dialog
								open={isModalOpen && !isAuthenticated}
								onOpenChange={(open) => {
									setIsModalOpen(open) // Always update the modal state
									if (!open && !isAuthenticated) {
										navigate('/') // Navigate to homepage only when closing and unauthenticated
									}
								}}
							>
								<DialogContent className='sm:max-w-md bg-card/90 backdrop-blur-md border border-muted/30 z-[999] p-10'>
									<DialogHeader>
										<DialogTitle className=' text-center'>
											Connect Your Wallet
										</DialogTitle>
										<p className='text-sm text-muted-foreground text-center mt-4'>
											Please connect your wallet and sign in to continue your
											NFT creation journey with Shogun.
										</p>
									</DialogHeader>
									<div className='mt-4 flex justify-center mx-auto'>
										<LoginButton disableOverlay={true} />
									</div>
								</DialogContent>
							</Dialog>
							<GlobalLoader />
							<Footer />
						</BannerProvider>
					</UmiProvider>
				</WalletProvider>
			</ConnectionProvider>
		</QueryClientProvider>
	)
}
