@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Squada+One&display=swap");
@import "tailwindcss";

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@layer base {
	:root {
		--background: hsl(0, 0%, 0%);
		--foreground: hsl(0, 0%, 100%);
		--container: hsla(0, 0%, 13%, 0.5);
		--card: hsl(0, 0%, 17%);
		--card-foreground: hsl(0, 0%, 100%);
		--popover: hsl(224 71.4% 4.1%);
		--popover-foreground: hsl(210 20% 98%);
		--primary: hsl(28, 98%, 49%);
		--primary-foreground: hsl(0, 0%, 100%);
		--secondary: hsl(357, 85%, 52%);
		--secondary-foreground: hsl(0, 0%, 100%);
		--muted: hsl(0, 0%, 16%);
		--muted-foreground: hsl(0 0% 54%);
		--accent: hsl(206, 7%, 79%);
		--accent-foreground: hsl(210, 2%, 20%);
		--destructive: hsl(0 62.8% 30.6%);
		--destructive-foreground: hsl(210 20% 98%);
		--border: hsl(0 0% 18%);
		--input: hsl(0 0% 10%);
		--placeholder: hsl(0 0% 22%);
		--ring: hsl(0 0% 13%);
		--chart-1: hsl(220 70% 50%);
		--chart-2: hsl(160 60% 45%);
		--chart-3: hsl(30 80% 55%);
		--chart-4: hsl(280 65% 60%);
		--chart-5: hsl(340 75% 55%);
	}
}

@theme {
	--breakpoint-2xl: 90rem;
	--breakpoint-3xl: 160rem;

	/* ----------Font families and colors---------- */
	--font-inter: "inter", sans-serif;
	--font-squada: "Squada One", sans-serif;

	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-container: var(--container);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-placeholder: var(--placeholder);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

/* Reset */
body {
	margin: 0;
	font-family: var(--font-inter), sans-serif;
	font-weight: 400;
	font-style: normal;
	background-color: var(--background);
	color: var(--foreground);
}
* {
	box-sizing: border-box;
}

/* Page Transition Animation */
#page-content {
	opacity: 1;
	transition: opacity 0.3s ease-in-out;
}
body.page-is-transitioning #page-content {
	opacity: 0;
}

@utility flex-center {
	@apply flex items-center justify-center;
}

.border-line-gradient {
	opacity: 1;
	height: 2px;
	background: linear-gradient(
		270deg,
		rgba(255, 255, 255, 0) 0%,
		#646464 49%,
		rgba(255, 255, 255, 0) 99%,
		rgba(255, 255, 255, 0) 99%
	);
}

@utility gradientBorder {
	border-image: linear-gradient(
		270deg,
		rgba(255, 255, 255, 0) -1%,
		#646464 49%,
		rgba(255, 255, 255, 0) 99%,
		rgba(255, 255, 255, 0) 99%
	);
	border-image-slice: 1;
}

.container {
	@apply w-full mx-auto px-6 md:px-8 lg:px-12 xl:px-20;
}

@utility containerPadding {
	@apply px-6 md:px-8 lg:px-12 xl:px-20 2xl:px-24;
}

@utility bgActiveBtn {
	background: radial-gradient(
		170% 170% at 50% 6%,
		#7a7a7a 0%,
		#424242 16%,
		rgba(0, 0, 0, 0) 100%
	);
}

#globalLoader.page-is-transitioning {
	display: flex !important;
	z-index: 100;
}

.heroShadowTop {
	background-image: linear-gradient(180deg, #000000 -20%, rgba(0, 0, 0, 0) 66%);
}

.heroShadowBottom {
	background-image: linear-gradient(0deg, #000000 27%, rgba(0, 0, 0, 0) 59%);
}

@media screen and (min-width: 1024px) {
	.heroShadowTop {
		background-image: linear-gradient(
			180deg,
			rgba(0, 0, 0, 0.74) -9%,
			rgba(0, 0, 0, 0) 79%
		);
	}

	.heroShadowBottom {
		background-image: linear-gradient(0deg, #000000 6%, rgba(0, 0, 0, 0) 64%);
	}
}

.outline-btn {
	@apply !rounded-full border-0 outline-0 backdrop-blur-sm px-3 py-2 z-10 relative cursor-pointer;
	background: radial-gradient(
		170% 170% at 50% 6%,
		#7a7a7a 0%,
		#424242 16%,
		rgba(0, 0, 0, 0) 100%
	);
}
.outline-btn::before {
	@apply absolute left-[2px] right-[2px] top-[2px] bottom-[2px] rounded-full bg-background;
	content: "";
	z-index: -1;
	transition: 200ms;
}
.outline-btn::after {
	@apply text-foreground text-sm;
	content: attr(data);
	background: radial-gradient(
		170% 170% at 50% 6%,
		#7a7a7a 0%,
		#424242 16%,
		rgba(0, 0, 0, 0) 100%
	);
	-webkit-background-clip: text;
	color: transparent;
	transition: 200ms;
}
.outline-btn:hover::before {
	opacity: 50%;
	top: 0px;
	right: 0px;
	bottom: 0px;
	left: 0px;
}
.outline-btn:hover::after {
	@apply text-foreground;
}

.toast-bg-success {
	background-image: linear-gradient(to right, #ed7a00, #563109ef, #000000);
}

.toast-bg-destructive {
	background-image: linear-gradient(to right, #6e0404, #350000, #000000);
}

.trendingFusionBg {
	background: linear-gradient(
		116deg,
		rgba(249, 81, 3, 0.8) 2%,
		rgba(249, 81, 3, 0.8) 2%,
		rgba(193, 39, 45, 0.8) 54%
	);
}

.gradientText {
	background: linear-gradient(90deg, #ffa85a -3%, #f97703 69%, #dd0003 110%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	color: transparent;
}

.activeFilterBtn {
	opacity: 1;
	background: radial-gradient(
		170% 170% at 50% 6%,
		#7a7a7a 0%,
		#424242 16%,
		rgba(0, 0, 0, 0) 100%
	);
	border: 0.5px solid #1d1d1d;
}

@utility blurredBtn {
	@apply flex-center cursor-pointer w-[30px] !h-[30px] lg:w-[50px] lg:!h-[50px] hover:border border-primary rounded-full bg-white/20 backdrop-blur-md;
}

@media only screen and (min-width: 1024px) {
	::-webkit-scrollbar {
		width: 10px;
		height: 10px;
	}
	/* Track */
	::-webkit-scrollbar-track {
		background: var(--color-container);
		border-radius: 10px;
	}

	/* Handle */
	::-webkit-scrollbar-thumb {
		background: #555;
		border-radius: 10px;
	}

	/* Handle on hover */
	::-webkit-scrollbar-thumb:hover {
		background: #888;
	}
}

input[type="number"] {
	/* hide arrows from number input */
	@apply [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none;
}

/* ------customizing sonner toaster width-----  */
[data-sonner-toaster] {
	@apply md:!w-[600px];
}
/* ------------------------------------------- */

.headerGradientForCollection {
	background: linear-gradient(
		180deg,
		#000000 -7%,
		rgba(0, 0, 0, 0) 55%,
		#000000 100%
	);
}
