// index.ts with debugging route
/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */

import { debugKVPaths } from './debug' // Import the debug utility
import { handleSsr } from './ssr'
import { handleStaticAssets } from './static-assets'

// TypeScript interface for FetchEvent
interface FetchEvent extends Event {
	request: Request
	respondWith(response: Response | Promise<Response>): void
	waitUntil(promise: Promise<any>): void
}

// Define environment with KV bindings
interface Env {
	__STATIC_CONTENT: KVNamespace
}

export default {
	// ES Module format worker
	async fetch(request: Request, env: Env, ctx: any): Promise<Response> {
		try {
			const url = new URL(request.url)

			// Debug route - only available in production to debug KV paths
			// ⚠️ Remove this in production after debugging ⚠️
			// if (url.pathname === '/__debug/kv') {
			//   return debugKVPaths(env);
			// }

			// Handle static assets first
			if (isAssetUrl(url.pathname)) {
				// Log the request path for debugging

				const assetEvent = {
					request,
					waitUntil: (promise: Promise<any>) => ctx.waitUntil(promise),
					env,
				}
				const assetResponse = await handleStaticAssets(assetEvent)
				if (assetResponse) return assetResponse
			}

			// Handle SSR
			const response = await handleSsr(request)
			if (response !== null) return response

			// Default not found
			return new Response('Not Found', { status: 404 })
		} catch (err) {
			return new Response('Internal Error', { status: 500 })
		}
	},
}

// For backwards compatibility with service worker format
addEventListener('fetch', (event: Event) => {
	const fetchEvent = event as FetchEvent
	fetchEvent.respondWith(
		// @ts-ignore - Access the default export's fetch method
		globalThis.default
			.fetch(
				fetchEvent.request,
				{
					__STATIC_CONTENT: (self as any).__STATIC_CONTENT,
				},
				{
					waitUntil: (promise: Promise<any>) => fetchEvent.waitUntil(promise),
				},
			)
			.catch((err: Error) => {
				console.error(err.stack)
				return new Response('Internal Error', { status: 500 })
			}),
	)
})

function isAssetUrl(pathname: string) {
	return (
		pathname.startsWith('/assets/') ||
		pathname.includes('.') || // Files with extensions
		pathname.startsWith('/static/')
	)
}
