// static-assets.ts
import { getAssetFromKV, type Options } from '@cloudflare/kv-asset-handler'

export { handleStaticAssets }

// Define environment with KV bindings
interface Env {
	__STATIC_CONTENT: KVNamespace
}

/**
 * The DEBUG flag will do two things that help during development:
 * 1. we will skip caching on the edge, which makes it easier to
 *    debug.
 * 2. we will return an error message on exception in your Response rather
 *    than the default 404.html page.
 */
const DEBUG = false

async function handleStaticAssets(event: {
	request: Request
	waitUntil: (promise: Promise<unknown>) => void
	env?: Env
}) {
	const options: Options = {
		ASSET_NAMESPACE: event.env?.__STATIC_CONTENT,
		cacheControl: {
			bypassCache: DEBUG,
		},
	}

	const namespace = event.env?.__STATIC_CONTENT
	if (!namespace) {
		return new Response('KV namespace not available', { status: 500 })
	}

	// Extract the request URL
	const url = new URL(event.request.url)
	const requestPath = url.pathname

	// Try to get the asset directly first
	try {
		// First attempt with standard options
		// @ts-ignore - Type differences between KV-asset-handler and our event
		const page = await getAssetFromKV(event, options)

		// Asset found! Return it with security headers
		const response = new Response(page.body, page)

		response.headers.set('X-XSS-Protection', '1; mode=block')
		response.headers.set('X-Content-Type-Options', 'nosniff')
		response.headers.set('X-Frame-Options', 'DENY')
		response.headers.set('Referrer-Policy', 'unsafe-url')
		response.headers.set('Feature-Policy', 'none')

		return response
	} catch (e) {
		// Normal getAssetFromKV failed, let's try content hash matching
	}

	// For content-hashed files, we need a custom approach
	try {
		// List all keys in the namespace
		const keys = await namespace.list()

		// Extract requested filename parts
		const requestedPathParts = requestPath.split('/')
		const requestedFilename = requestedPathParts[requestedPathParts.length - 1]

		// Find base filename without the final hash (if any)
		// Pattern: name.ABC123.svg -> name.ABC123
		const baseNameMatch = requestedFilename.match(/^(.+?)(\.[^.]+)?$/)
		const baseName = baseNameMatch ? baseNameMatch[1] : requestedFilename
		const fileExt = baseNameMatch?.[2] ? baseNameMatch[2] : ''

		// Look for a file that starts with the same name pattern but might have an extra hash
		// E.g., if looking for "logo.DLJJsk-H.svg", match "logo.DLJJsk-H.b34211d740.svg"
		const matchingKey = keys.keys.find((key) => {
			const keyName = key.name
			const keyParts = keyName.split('/')
			const keyFilename = keyParts[keyParts.length - 1]

			// Check if this key starts with our base filename and has the same extension
			return keyFilename.startsWith(baseName) && keyFilename.endsWith(fileExt)
		})

		if (matchingKey) {
			// We found a matching file with possibly different content hash

			// Get the asset using the matched key
			const asset = await namespace.get(matchingKey.name, 'arrayBuffer')

			if (asset) {
				// Determine content type based on file extension
				const contentType = getContentType(matchingKey.name)

				// Return the asset with appropriate headers
				const response = new Response(asset, {
					headers: {
						'Content-Type': contentType || 'application/octet-stream',
						'Cache-Control': 'public, max-age=31536000',
						'X-XSS-Protection': '1; mode=block',
						'X-Content-Type-Options': 'nosniff',
					},
				})

				return response
			}
		}

		// If we get here, we couldn't find a matching asset
		console.error(`Could not find matching asset for ${requestPath}`)

		// Try to serve 404 page
		if (!DEBUG) {
			try {
				// @ts-ignore - Type differences between KV-asset-handler and our event
				const notFoundResponse = await getAssetFromKV(event, {
					ASSET_NAMESPACE: namespace,
					mapRequestToAsset: (req: Request) =>
						new Request(`${new URL(req.url).origin}/error`, req),
				})

				return new Response(notFoundResponse.body, {
					...notFoundResponse,
					status: 404,
				})
			} catch (e) {
				/* fall through to returning error */
			}
		}

		return new Response(`Asset not found: ${requestPath}`, { status: 404 })
	} catch (error) {
		console.error('Error handling asset:', error)
		return new Response(
			`Error handling asset: ${error instanceof Error ? error.message : String(error)}`,
			{ status: 500 },
		)
	}
}

// Helper function to determine content type
function getContentType(path: string): string | null {
	const extension = path.split('.').pop()?.toLowerCase()

	const contentTypes = {
		html: 'text/html',
		css: 'text/css',
		js: 'application/javascript',
		json: 'application/json',
		png: 'image/png',
		jpg: 'image/jpeg',
		jpeg: 'image/jpeg',
		gif: 'image/gif',
		svg: 'image/svg+xml',
		ico: 'image/x-icon',
		txt: 'text/plain',
		pdf: 'application/pdf',
		woff: 'font/woff',
		woff2: 'font/woff2',
		ttf: 'font/ttf',
		eot: 'application/vnd.ms-fontobject',
		otf: 'font/otf',
	}

	return extension && extension in contentTypes ? contentTypes[extension] : null
}
