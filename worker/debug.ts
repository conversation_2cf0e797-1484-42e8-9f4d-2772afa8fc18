// Add this to your Worker to debug asset paths
// Only use temporarily to see what's in your KV store

export async function debugKVPaths(env) {
	if (!env.__STATIC_CONTENT) {
		return new Response('KV Namespace not available', { status: 500 })
	}

	try {
		// List all keys in the KV namespace
		const keys = await env.__STATIC_CONTENT.list()

		// Format response with HTML for better readability
		const html = `<!DOCTYPE html>
      <html>
        <head>
          <title>KV Namespace Content</title>
          <style>
            body { font-family: sans-serif; margin: 20px; }
            h1 { color: #333; }
            ul { list-style-type: none; padding: 0; }
            li { margin: 5px 0; padding: 5px; background: #f5f5f5; border-radius: 3px; }
          </style>
        </head>
        <body>
          <h1>Keys in KV Namespace</h1>
          <p>Total keys: ${keys.keys.length}</p>
          <ul>
            ${keys.keys.map((key) => `<li>${key.name}</li>`).join('')}
          </ul>
        </body>
      </html>`

		return new Response(html, {
			headers: { 'Content-Type': 'text/html' },
		})
	} catch (error) {
		return new Response(`Error listing KV keys: ${error.message}`, {
			status: 500,
		})
	}
}

// To use this, add a route in your main worker like:
// if (url.pathname === '/__debug/kv') {
//   return debugKVPaths(env);
// }
