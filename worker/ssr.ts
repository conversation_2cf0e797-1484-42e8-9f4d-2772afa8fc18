// ssr.ts
import { Hono } from 'hono'
import { compress } from 'hono/compress'
import app from '../hono-entry' // Import your Hono app

export { handleSsr }

// Create a Hono instance that will be used for SSR
const honoSSR = new Hono()

// Apply middlewares as needed
// honoSSR.use(compress());

// Mount your main app
honoSSR.route('/', app)

async function handleSsr(request: Request) {
	try {
		// Handle the request with Hono
		const response = await honoSSR.fetch(request)

		// If Hono returns null or undefined for some reason, return null
		// to let the asset handler try to serve a static file
		if (!response) {
			return null
		}

		return response
	} catch (error) {
		return new Response('SSR Error', { status: 500 })
	}
}
