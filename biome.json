{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.1/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/node_modules/**", "!**/dist/**", "!**/build/**", "!**/public/**", "!**/prisma/**", "!**/lib/generated-umi/**", "!**/lib/generated/**"]}, "formatter": {"enabled": true, "indentStyle": "tab", "attributePosition": "auto", "indentWidth": 2, "formatWithErrors": true, "lineWidth": 80}, "json": {"formatter": {"trailingCommas": "none"}}, "linter": {"enabled": true, "rules": {"suspicious": {"noExplicitAny": "error"}, "complexity": {"noBannedTypes": "error"}, "style": {"useLiteralEnumMembers": "error", "noCommaOperator": "error", "useNodejsImportProtocol": "error", "useAsConstAssertion": "error", "useNumericLiterals": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useConst": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "useExponentiationOperator": "error", "useTemplate": "error", "noParameterAssign": "error", "noNonNullAssertion": "error", "useDefaultParameterLast": "error", "noArguments": "error", "useImportType": "error", "useExportType": "error", "noUselessElse": "error", "useShorthandFunctionType": "error"}, "a11y": "off"}}, "javascript": {"formatter": {"quoteStyle": "single", "arrowParentheses": "always", "bracketSameLine": false, "bracketSpacing": true, "jsxQuoteStyle": "single", "quoteProperties": "asNeeded", "semicolons": "asNeeded"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}