import { useMeUser } from '@/hooks/useMeUser'

/**
 * Example component showing how to use the useMeUser hook
 */
export function MeUserExample() {
	const { data: user, isLoading, error } = useMeUser()

	if (isLoading) {
		return <div>Loading user data...</div>
	}

	if (error) {
		return <div>Error loading user data: {error.message}</div>
	}

	if (!user) {
		return <div>No user data available</div>
	}

	return (
		<div className='p-4 bg-white rounded-lg shadow'>
			<h2 className='text-2xl font-bold mb-4'>User Profile</h2>

			<div className='flex items-center mb-4'>
				{user.imageUrl ? (
					<img
						src={user.imageUrl}
						alt={user.userName || 'User'}
						className='w-16 h-16 rounded-full mr-4'
					/>
				) : (
					<div className='w-16 h-16 rounded-full bg-gray-200 mr-4 flex items-center justify-center'>
						<span className='text-gray-500 text-xl'>
							{user.userName?.charAt(0) || user.username?.charAt(0) || '?'}
						</span>
					</div>
				)}

				<div>
					<h3 className='text-xl font-semibold'>
						{user.userName || user.username || 'Anonymous User'}
					</h3>
					<p className='text-gray-500 text-sm'>{user.publicKey}</p>
				</div>
			</div>

			<div className='mb-4'>
				<h4 className='font-medium mb-2'>Bio</h4>
				<p className='text-gray-700'>{user.bio || 'No bio available'}</p>
			</div>

			<div className='mb-4'>
				<h4 className='font-medium mb-2'>Contact</h4>
				<p className='text-gray-700'>{user.email || 'No email available'}</p>
			</div>

			{/* Social links */}
			<div className='mt-4'>
				<h4 className='font-medium mb-2'>Social Links</h4>
				<div className='grid grid-cols-2 gap-2'>
					{user.twitterId && (
						<div className='flex items-center'>
							<span className='mr-2'>🐦</span>
							<span>{user.twitterId}</span>
						</div>
					)}
					{user.instagramId && (
						<div className='flex items-center'>
							<span className='mr-2'>📸</span>
							<span>{user.instagramId}</span>
						</div>
					)}
					{user.telegramId && (
						<div className='flex items-center'>
							<span className='mr-2'>✈️</span>
							<span>{user.telegramId}</span>
						</div>
					)}
					{user.websiteId && (
						<div className='flex items-center'>
							<span className='mr-2'>🌐</span>
							<span>{user.websiteId}</span>
						</div>
					)}
					{user.facebookId && (
						<div className='flex items-center'>
							<span className='mr-2'>👥</span>
							<span>{user.facebookId}</span>
						</div>
					)}
				</div>
			</div>
		</div>
	)
}
