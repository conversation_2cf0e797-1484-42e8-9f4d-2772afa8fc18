import { type Context, Hono } from 'hono'
import { telefunc } from 'telefunc'
import apiRoute from './server/app'
//import apiRoute from './server/api.routes'
import { authMiddleware } from './server/middleware/auth-middleware'
import { vikeHand<PERSON> } from './server/vike-handler'

const app = new Hono()

// Global error handler
app.onError((err, c) => {
	// Return a generic error response
	return c.json(
		{
			success: false,
			error: 'Internal server error',
			message: process.env.NODE_ENV === 'development' ? err.message : undefined,
		},
		500,
	)
})
export type User = {
	sub: string
	email?: string
}

app.use('*' as '*?', authMiddleware)

//app.route('/api', apiRoute)
// Request logging middleware
app.use('*', async (c, next) => {
	const start = Date.now()
	try {
		await next()
	} finally {
		const duration = Date.now() - start
	}
})

app.post('/_telefunc', authMiddleware, async (c: Context) => {
	try {
		const httpResponse = await telefunc({
			url: c.req.url,
			method: c.req.method,
			body: await c.req.text(),
			context: {
				user: c.get('user'),
				serverContext: c,
			},
		})

		const { body, statusCode, contentType } = httpResponse

		return new Response(body, {
			status: statusCode,
			headers: {
				'content-type': contentType,
			},
		})
	} catch (error) {
		// Return a generic error response
		return new Response(
			JSON.stringify({
				abort: true,
				ret: {
					status: 500,
					message: 'Internal server error processing telefunc request',
				},
			}),
			{
				status: 500,
				headers: {
					'content-type': 'application/json',
				},
			},
		)
	}
})
app.route('/api', apiRoute)
/**
 * Vike route
 *
 * @link {@see https://vike.dev}
 **/
app.all('*', async (c) => {
	try {
		// Convert Hono request to standard Request
		const request = new Request(c.req.url, {
			method: c.req.method,
			headers: c.req.raw.headers,
		})

		// Empty runtime context
		const runtime: { [key: string]: unknown } = {}

		// Handle the request with Vike
		return await vikeHandler(request, c, runtime)
	} catch (error) {
		// For API routes, return JSON error
		if (c.req.path.startsWith('/api/')) {
			return c.json(
				{
					success: false,
					error: 'Internal server error',
					message:
						process.env.NODE_ENV === 'development' ? String(error) : undefined,
				},
				500,
			)
		}

		// For page routes, redirect to error page
		return c.redirect('/error')
	}
})

export default app
