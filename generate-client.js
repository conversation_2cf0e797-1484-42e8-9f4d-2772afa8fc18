// Import required package
import path from 'node:path'
import { rootNodeFromAnchor } from '@codama/nodes-from-anchor'
import { renderVisitor } from '@codama/renderers-js-umi'
import { createFromRoot } from 'codama'
import anchorIdl from './lib/contracts/fusion_marketplace.json'

const codama = createFromRoot(rootNodeFromAnchor(anchorIdl))

const pathToGeneratedFolder = path.join(__dirname, 'lib', 'generated')
const options = {} // See below.
codama.accept(renderVisitor(pathToGeneratedFolder, options))
