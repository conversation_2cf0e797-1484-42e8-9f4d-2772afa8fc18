import type { Context, Next } from 'hono'
import { decode, verify } from 'hono/jwt'
import { ACCESS_TOKEN_SECRET } from '../config/auth.config'

export const authMiddleware = async (c: Context, next: Next) => {
	let user = null

	try {
		const authHeader = c.req.header()?.authorization
		if (authHeader?.startsWith('Bearer ')) {
			if (await verify(authHeader.split(' ')[1], ACCESS_TOKEN_SECRET)) {
				user = decode(authHeader.split(' ')[1]).payload
			}
		}
	} catch (e) {}

	c.set('user', user)
	await next()
}
