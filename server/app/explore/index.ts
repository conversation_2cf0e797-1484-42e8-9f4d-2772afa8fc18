import { getI8Encoder } from '@solana/kit'
import { <PERSON><PERSON> } from 'hono'
import { tryCatch } from '@/lib/try-catch'
import type { Env } from '../../types/hono-env.types'
import {
	type CollectionsResponse,
	getCollections,
	getCreators,
	getFusionNFTs,
	getNFTs,
	type NFTsResponse,
	type PaginationMeta,
	type ParamType,
	type TimeFilter,
} from './helper'
import { sortByData, sortTypeSchema, type SortType } from '@/types/nft.types'

// Define the valid tab types

const exploreRoute = new Hono<Env>()

/**
 * GET /api/explore
 *
 * Query parameters:
 * - tab: 'nft', 'fusion', or 'collection' (default: 'nft')
 * - time: '1H', '1D', '7D', or '30D' (TimeFilter)
 * - search: Search query string
 * - page: Page number (default: 1)
 * - limit: Number of items per page (default: 20)
 */
exploreRoute.get('/', async (c) => {
	try {
		// Extract query parameters
		const tab = (c.req.query('tab') as ParamType) || 'nft'
		const timeFilter = c.req.query('time') as TimeFilter
		const searchQuery = c.req.query('search')
		const page = c.req.query('page') ? Number(c.req.query('page')) : 1
		const limit = c.req.query('limit') ? Number(c.req.query('limit')) : 20

		// Validate and get sort parameter
		const rawSort = c.req.query('sort')
		const sortValidation = sortTypeSchema.safeParse(rawSort)
		const sort: SortType = sortValidation.success
			? sortValidation.data
			: (sortByData[0].value as SortType)

		// Fetch data based on the tab parameter
		if (tab === 'nft') {
			const { data: response, error } = await tryCatch(
				getNFTs({ timeFilter, searchQuery, page, limit, sort }),
			)

			if (error) {
				console.error('Error fetching NFTs:', error)
				return c.json(
					{
						success: false,
						error: 'Failed to fetch NFTs',
						message:
							process.env.NODE_ENV === 'development'
								? error.message
								: undefined,
					},
					500,
				)
			}

			return c.json({
				success: true,
				data: {
					nfts: response.items,
					collections: null,
					creators: null,
					pagination: response.pagination,
				},
			})
		}

		if (tab === 'fusion') {
			const { data: response, error } = await tryCatch(
				getFusionNFTs({ timeFilter, searchQuery, page, limit, sort }),
			)

			if (error) {
				console.error('Error fetching fusion NFTs:', error)
				return c.json(
					{
						success: false,
						error: 'Failed to fetch fusion NFTs',
						message:
							process.env.NODE_ENV === 'development'
								? error.message
								: undefined,
					},
					500,
				)
			}

			return c.json({
				success: true,
				data: {
					nfts: response.items,
					collections: null,
					creators: null,
					pagination: response.pagination,
				},
			})
		}

		if (tab === 'collection') {
			const { data: response, error } = await tryCatch(
				getCollections({ timeFilter, searchQuery, page, limit }),
			)

			if (error) {
				console.error('Error fetching collections:', error)
				return c.json(
					{
						success: false,
						error: 'Failed to fetch collections',
						message:
							process.env.NODE_ENV === 'development'
								? error.message
								: undefined,
					},
					500,
				)
			}

			return c.json({
				success: true,
				data: {
					collections: response.items,
					nfts: null,
					creators: null,
					pagination: response.pagination,
				},
			})
		}
		if (tab === 'creator') {
			const { data: response, error } = await tryCatch(
				getCreators({ timeFilter, searchQuery, page, limit }),
			)

			if (error) {
				console.error('Error fetching collections:', error)
				return c.json(
					{
						success: false,
						error: 'Failed to fetch collections',
						message:
							process.env.NODE_ENV === 'development'
								? error.message
								: undefined,
					},
					500,
				)
			}

			return c.json({
				success: true,
				data: {
					creators: response.items,
					nfts: null,
					collections: null,
					pagination: response.pagination,
				},
			})
		}

		// Default case if tab parameter is not recognized
		const { data: response, error } = await tryCatch(
			getNFTs({ timeFilter, searchQuery, page, limit, sort }),
		)

		if (error) {
			console.error('Error fetching NFTs (default):', error)
			return c.json(
				{
					success: false,
					error: 'Failed to fetch NFTs',
					message:
						process.env.NODE_ENV === 'development' ? error.message : undefined,
				},
				500,
			)
		}

		return c.json({
			success: true,
			data: {
				nfts: response.items,
				collections: null,
				creators: null,
				pagination: response.pagination,
			},
		})
	} catch (error) {
		console.error('Error in explore API:', error)
		return c.json(
			{
				success: false,
				error: 'Internal server error',
				message:
					process.env.NODE_ENV === 'development'
						? (error as Error).message
						: undefined,
			},
			500,
		)
	}
})

export { exploreRoute }
