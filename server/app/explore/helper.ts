import { Prisma } from 'prisma-client/edge'
import { prisma } from '@/server/lib/prismaClient'
import type { SortType } from '@/types/nft.types'
// import { listingType } from '@/lib/generated' // This import seems unused now

export type ParamType = 'nft' | 'fusion' | 'collection' | 'creator'
export type TimeFilter = '1H' | '1D' | '7D' | '30D' | 'ALL'

// Define the type for listing data
export type ListingData = {
	id: string
	price: number
	currency: string
	status: string
	listingType: string
	startTime: Date
	endTime: Date | null
	minBidIncrement: number | null
	reservePrice: number | null
	instantBuyPrice: number | null
	createdAt: Date
	updatedAt: Date
	isFixedPrice: boolean
	isAuction: boolean
	isActive: boolean
}
// Define the type for NFT object
export type NFTWithListing = {
	id: string
	name: string
	collectionName: string
	publicKey: string
	description: string
	logoUrl: string
	symbol: string
	biddingPrice: number
	owner:
		| {
				id: string
				username: string
				imageUrl: string
				publicKey: string
		  }
		| {
				publicKey: string
		  }
	listingData?: ListingData
}

// Define pagination metadata type
export type PaginationMeta = {
	currentPage: number
	totalPages: number
	totalItems: number
	itemsPerPage: number
}

// Define response types with pagination
export type NFTsResponse = {
	items: NFTWithListing[]
	pagination: PaginationMeta
}

// Define the type for Collection object
export type CollectionData = {
	id: string
	symbol: string
	name: string
	publicKey: string
	description: string
	logoUrl: string
	bannerUrl: string
	metadataUri: string
	creator: {
		id: string
		username: string
		imageUrl: string
		publicKey: string
	}
	lowestBiddingPrice: number
}

export type CollectionsResponse = {
	items: CollectionData[]
	pagination: PaginationMeta
}

export type CreatorData = {
	id: string
	userName: string
	imageUrl?: string
	publicKey: string
	activityCount: number
	nfts: Array<{
		id: string
		name: string
		imageUrl: string
		publicKey: string
	}>
}

export type CreatorsResponse = {
	items: CreatorData[]
	pagination: PaginationMeta
}

/**
 * Get NFTs with filtering and pagination
 */
export async function getNFTs({
	timeFilter,
	searchQuery,
	page = 1,
	limit = 20,
	sort,
}: {
	timeFilter?: TimeFilter
	searchQuery?: string
	page?: number
	limit?: number
	sort?: SortType
}): Promise<NFTsResponse> {
	try {
		// Calculate time range based on filter
		const timeRange = getTimeRangeFromFilter(timeFilter)

		// Build where clause for NFTs
		const where: Prisma.NFTWhereInput = {
			...(timeRange && { createdAt: { gte: timeRange } }),
			...(searchQuery && {
				OR: [
					{
						name: { contains: searchQuery, mode: Prisma.QueryMode.insensitive },
					},
					{
						description: {
							contains: searchQuery,
							mode: Prisma.QueryMode.insensitive,
						},
					},
				],
			}),
		}

		// Get total count for pagination
		const totalItems = await prisma.nFT.count({ where })

		// Calculate total pages
		const totalPages = Math.ceil(totalItems / limit)

		console.log('sort', sort)

		// Define orderBy clause based on sort parameter
		let orderBy: Prisma.NFTOrderByWithRelationInput = { createdAt: 'desc' } // Default sort
		if (sort === 'high_activity') {
			orderBy = { activityLogs: { _count: 'desc' } }
		} else if (sort === 'most_viewed') {
			orderBy = { totalViews: 'desc' }
		} else if (sort === 'recently_sold') {
			//TODO -later add proper filtering
			orderBy = { createdAt: 'desc' }
		} else if (sort === 'auction_first') {
			orderBy = { listings: { _count: 'desc' } }
		} else if (sort === 'price_high_to_low') {
			orderBy = { lastBiddingPrice: 'desc' }
		} else if (sort === 'price_low_to_high') {
			orderBy = { lastBiddingPrice: 'asc' }
		} else if (sort === 'most_liked') {
			orderBy = { totalLikes: 'desc' }
		} else if (sort === 'recently_created') {
			orderBy = { createdAt: 'desc' }
		}

		// Fetch NFTs with pagination and include owner and listings
		const nfts = await prisma.nFT.findMany({
			where,
			include: {
				owner: true,
				activityLogs: true,
				collection: {
					select: { name: true },
				},
				listings: {
					where: {
						status: 'ACTIVE',
					},
					orderBy: {
						createdAt: 'desc',
					},
					take: 1,
				},
			},
			skip: (page - 1) * limit,
			take: limit,
			orderBy, // Apply the orderBy clause
		})

		// Map to the expected NFT type with listing data
		const items = nfts.map((nft) => {
			const listing = nft.listings[0]

			const nftObject: NFTWithListing = {
				id: nft.id,
				name: nft.name,
				collectionName: nft.collection?.name ?? '',
				publicKey: nft.publicKey,
				description: nft.description || '',
				logoUrl: nft.imageUrl,
				symbol: nft.name.substring(0, 3).toUpperCase(),
				biddingPrice: nft.lastBiddingPrice || 0,
				owner: {
					id: nft.owner.id,
					username: nft.owner.username,
					imageUrl: nft.owner.imageUrl || '',
					publicKey: nft.owner.publicKey,
				},
			}

			// Add listing data if available
			if (listing) {
				nftObject.listingData = {
					id: listing.id,
					price: listing.price,
					currency: listing.currency,
					status: listing.status,
					listingType: listing.listingType,
					startTime: listing.startTime,
					endTime: listing.endTime,
					minBidIncrement: listing.minBidIncrement,
					reservePrice: listing.reservePrice,
					instantBuyPrice: listing.instantBuyPrice,
					createdAt: listing.createdAt,
					updatedAt: listing.updatedAt,
					isFixedPrice: listing.listingType === 'FIXED_PRICE',
					isAuction: listing.listingType === 'AUCTION',
					isActive: listing.status === 'ACTIVE',
				}
			}

			return nftObject
		})

		// Return items with pagination metadata
		return {
			items,
			pagination: {
				currentPage: page,
				totalPages,
				totalItems,
				itemsPerPage: limit,
			},
		}
	} catch (error) {
		console.error('Error fetching NFTs:', error)
		throw error
	}
}
//
//
///**
// * Get trending creators based on NFT activity
// * @param limit Maximum number of creators to return
// * @returns Array of trending creators with their NFTs
// */
//export async function getTrendingCreators(
//	limit = 4,
//): Promise<CreatorWithActivity[]> {
//	try {
//		// Get NFTs with the most activity
//		const trendingNFTs = await getTrendingNFTs('7D', 20)
//
//		// Extract creator IDs from trending NFTs
//		const creatorMap = new Map<
//			string,
//			{
//				creator: {
//					id: string
//					username: string
//					imageUrl?: string
//					publicKey: string
//				}
//				nfts: Array<{
//					id: string
//					name: string
//					imageUrl: string
//					publicKey: string
//				}>
//				activityCount: number
//			}
//		>()
//
//		// Get creator details for each NFT
//		for (const nft of trendingNFTs) {
//			// Skip if no creator
//			if (!nft.owner) continue
//
//			// Get creator details
//			const creator = await prisma.user.findUnique({
//				where: { id: nft.owner.id },
//				include: {
//					createdNFTs: {
//						take: 4,
//						orderBy: { createdAt: 'desc' },
//					},
//				},
//			})
//
//			if (!creator) continue
//
//			// Count activities for this creator's NFTs
//			const timeRange = getTimeRangeFromFilter('7D')
//			const activityCount = await prisma.activityLog.count({
//				where: {
//					nft: {
//						creatorId: creator.id,
//					},
//					...(timeRange && { createdAt: { gte: timeRange } }),
//				},
//			})
//
//			// Add or update creator in the map
//			if (creatorMap.has(creator.id)) {
//				const existing = creatorMap.get(creator.id)
//				if (existing) {
//					existing.activityCount += activityCount
//				}
//			} else {
//				creatorMap.set(creator.id, {
//					creator: {
//						id: creator.id,
//						username: creator.username,
//						imageUrl: creator.imageUrl || undefined,
//						publicKey: creator.publicKey,
//					},
//					nfts: creator.createdNFTs.map((nft) => ({
//						id: nft.id,
//						name: nft.name,
//						imageUrl: nft.imageUrl,
//						publicKey: nft.publicKey,
//					})),
//					activityCount,
//				})
//			}
//		}
//
//		// Convert map to array and sort by activity count
//		const creators = Array.from(creatorMap.values())
//			.sort((a, b) => b.activityCount - a.activityCount)
//			.slice(0, limit)
//
//		// Transform to the expected format
//		return creators.map((item) => ({
//			id: item.creator.id,
//			username: item.creator.username,
//			imageUrl: item.creator.imageUrl,
//			publicKey: item.creator.publicKey,
//			activityCount: item.activityCount,
//			nfts: item.nfts,
//		}))
//	} catch (error) {
//		console.error('Error fetching trending creators:', error)
//		return []
//	}
//}

/**
 * Get creators with filtering and pagination
 */
export async function getCreators({
	timeFilter,
	searchQuery,
	page = 1,
	limit = 20,
}: {
	timeFilter?: TimeFilter
	searchQuery?: string
	page?: number
	limit?: number
}): Promise<CreatorsResponse> {
	try {
		const timeRange = getTimeRangeFromFilter(timeFilter)

		const where: Prisma.UserWhereInput = {
			...(timeRange && { createdAt: { gte: timeRange } }),
			...(searchQuery && {
				OR: [
					{
						username: { contains: searchQuery.toLowerCase() },
					},
				],
			}),
			createdNFTs: {
				some: {},
			},
		}

		const totalItems = await prisma.user.count({ where })

		const totalPages = Math.ceil(totalItems / limit)

		const creators = await prisma.user.findMany({
			where,
			include: {
				createdNFTs: {
					orderBy: { lastBiddingPrice: 'asc' },
					take: 1,
				},
			},
			skip: (page - 1) * limit,
			take: limit,
			orderBy: { createdAt: 'desc' },
		})

		const items: CreatorData[] = await Promise.all(
			creators.map(async (creator) => {
				const timeRange = getTimeRangeFromFilter(timeFilter)
				const activityCount = await prisma.activityLog.count({
					where: {
						nft: {
							creatorId: creator.id,
						},
						...(timeRange && { createdAt: { gte: timeRange } }),
					},
				})
				return {
					id: creator.id,
					userName: creator.username, // match CreatorData type
					imageUrl: creator.imageUrl ?? '',
					publicKey: creator.publicKey,
					activityCount: activityCount,
					nfts: creator.createdNFTs.map((nft) => ({
						id: nft.id,
						name: nft.name,
						imageUrl: nft.imageUrl ?? '',
						publicKey: nft.publicKey,
					})),
				}
			}),
		)

		return {
			items,
			pagination: {
				currentPage: page,
				totalPages,
				totalItems,
				itemsPerPage: limit,
			},
		}
	} catch (error) {
		console.error('Error fetching creators:', error)
		throw error
	}
}

/**
 * Get collections with filtering and pagination
 */
export async function getCollections({
	timeFilter,
	searchQuery,
	page = 1,
	limit = 20,
	sort,
}: {
	timeFilter?: TimeFilter
	searchQuery?: string
	page?: number
	limit?: number
	sort?: SortType
}): Promise<CollectionsResponse> {
	try {
		// Calculate time range based on filter
		const timeRange = getTimeRangeFromFilter(timeFilter)

		let orderBy: Prisma.CollectionOrderByWithRelationInput = {
			createdAt: 'desc',
		} // Default sort
		if (sort === 'high_activity') {
			orderBy = { activityLogs: { _count: 'desc' } }
		} else if (sort === 'most_viewed') {
			orderBy = { totalViews: 'desc' }
		} else if (sort === 'recently_sold') {
			//TODO -later add proper filtering
			orderBy = { createdAt: 'desc' }
		} else if (sort === 'most_liked') {
			orderBy = { totalLikes: 'desc' }
		}

		// Build where clause for collections
		const where: Prisma.CollectionWhereInput = {
			...(timeRange && { createdAt: { gte: timeRange } }),
			...(searchQuery && {
				OR: [
					{
						name: { contains: searchQuery, mode: Prisma.QueryMode.insensitive },
					},
					{
						description: {
							contains: searchQuery,
							mode: Prisma.QueryMode.insensitive,
						},
					},
				],
			}),
		}

		// Get total count for pagination
		const totalItems = await prisma.collection.count({ where })

		// Calculate total pages
		const totalPages = Math.ceil(totalItems / limit)

		// Fetch collections with pagination and include owner
		const collections = await prisma.collection.findMany({
			where,
			include: {
				owner: true,
				nfts: {
					orderBy: { lastBiddingPrice: 'asc' },
					take: 1,
				},
			},
			skip: (page - 1) * limit,
			take: limit,
			orderBy: orderBy,
		})

		// Map to the expected Collection type
		const items = collections.map((collection) => {
			const lowestBiddingNFT = collection.nfts[0]

			return {
				id: collection.id,
				symbol: collection.symbol,
				name: collection.name,
				publicKey: collection.publicKey,
				description: collection.description,
				logoUrl: collection.logoUrl,
				bannerUrl: collection.bannerUrl,
				metadataUri: collection.metadataUri,
				creator: {
					id: collection.owner.id,
					username: collection.owner.username,
					imageUrl: collection.owner.imageUrl || '',
					publicKey: collection.owner.publicKey,
				},
				lowestBiddingPrice: lowestBiddingNFT?.lastBiddingPrice ?? 0,
			}
		})

		// Return items with pagination metadata
		return {
			items,
			pagination: {
				currentPage: page,
				totalPages,
				totalItems,
				itemsPerPage: limit,
			},
		}
	} catch (error) {
		console.error('Error fetching collections:', error)
		throw error
	}
}

/**
 * Get fusion NFTs with efficient database queries
 */
export async function getFusionNFTs({
	timeFilter,
	searchQuery,
	page = 1,
	limit = 20,
	sort,
}: {
	timeFilter?: TimeFilter
	searchQuery?: string
	page?: number
	limit?: number
	sort?: SortType
}): Promise<NFTsResponse> {
	try {
		// Calculate time range based on filter
		const timeRange = getTimeRangeFromFilter(timeFilter)

		// Build where clause for fusion NFTs (locked NFTs)
		const where: Prisma.NFTWhereInput = {
			isLocked: true,
			...(timeRange && { createdAt: { gte: timeRange } }),
			...(searchQuery && {
				OR: [
					{
						name: { contains: searchQuery, mode: Prisma.QueryMode.insensitive },
					},
					{
						description: {
							contains: searchQuery,
							mode: Prisma.QueryMode.insensitive,
						},
					},
				],
			}),
		}

		// Get total count for pagination
		const totalItems = await prisma.nFT.count({ where })

		// Calculate total pages
		const totalPages = Math.ceil(totalItems / limit)

		// Define orderBy clause based on sort parameter
		let orderBy: Prisma.NFTOrderByWithRelationInput = { createdAt: 'desc' } // Default sort
		if (sort === 'high_activity') {
			orderBy = { activityLogs: { _count: 'desc' } }
		}

		// Fetch fusion NFTs with pagination and include owner and listings
		const fusionNfts = await prisma.nFT.findMany({
			where,
			include: {
				owner: true,
				activityLogs: true,
				listings: {
					where: {
						status: 'ACTIVE',
					},
					orderBy: {
						createdAt: 'desc',
					},
					take: 1,
				},
			},
			skip: (page - 1) * limit,
			take: limit,
			orderBy, // Apply the orderBy clause
		})

		// Map to the expected NFT type with listing data
		const items = fusionNfts.map((nft) => {
			const listing = nft.listings[0]

			const nftObject: NFTWithListing = {
				id: nft.id,
				name: nft.name,
				collectionName: '',
				publicKey: nft.publicKey,
				description: nft.description || '',
				logoUrl: nft.imageUrl,
				symbol: nft.name.substring(0, 3).toUpperCase(),
				biddingPrice: nft.lastBiddingPrice || 0,
				owner: nft?.owner
					? {
							id: nft.owner?.id ?? '',
							username: nft.owner?.username ?? '',
							imageUrl: nft.owner?.imageUrl ?? '',
							publicKey: nft.owner?.publicKey ?? '',
						}
					: {
							publicKey: nft.ownerWalletAddress ?? '',
						},
			}

			// Add listing data if available
			if (listing) {
				nftObject.listingData = {
					id: listing.id,
					price: listing.price,
					currency: listing.currency,
					status: listing.status,
					listingType: listing.listingType,
					startTime: listing.startTime,
					endTime: listing.endTime,
					minBidIncrement: listing.minBidIncrement,
					reservePrice: listing.reservePrice,
					instantBuyPrice: listing.instantBuyPrice,
					createdAt: listing.createdAt,
					updatedAt: listing.updatedAt,
					isFixedPrice: listing.listingType === 'FIXED_PRICE',
					isAuction: listing.listingType === 'AUCTION',
					isActive: listing.status === 'ACTIVE',
				}
			}

			return nftObject
		})

		// Return items with pagination metadata
		return {
			items,
			pagination: {
				currentPage: page,
				totalPages,
				totalItems,
				itemsPerPage: limit,
			},
		}
	} catch (error) {
		console.error('Error fetching fusion NFTs:', error)
		throw error
	}
}

/**
 * Helper function to convert time filter to Date object
 */
function getTimeRangeFromFilter(timeFilter?: TimeFilter): Date | null {
	if (!timeFilter) return null

	const now = new Date()

	switch (timeFilter) {
		case '1H':
			return new Date(now.getTime() - 60 * 60 * 1000) // 1 hour ago
		case '1D':
			return new Date(now.getTime() - 24 * 60 * 60 * 1000) // 1 day ago
		case '7D':
			return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
		case '30D':
			return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
		default:
			return null
	}
}
