import { Buffer } from 'node:buffer'
import { fetchAsset } from '@metaplex-foundation/mpl-core'
import { publicKey } from '@metaplex-foundation/umi'
import { PublicKey } from '@solana/web3.js'
import { ListingStatus, type Prisma } from 'prisma-client/edge'
import { FUSION_MARKETPLACE_PROGRAM_ID } from '@/lib/generated'
import {
	enhanceAssetWithMetadata,
	fetchEnhancedCollection,
	fetchMetadata,
} from '@/lib/nft/asset-utils'
import { prisma } from '@/server/lib/prismaClient'
import { getServerUmi } from '@/server/lib/umiClient'
import type { NFT as BaseNFT } from '@/types/enhanced-assets'

// Extended NFT type with additional properties needed for the details page
export type NFT = BaseNFT & {
	collectionId?: string
	royaltyBasisPoints?: number
	updatedAt?: string
	metadataUrl?: string
	isLocked?: boolean
	attributes?: Array<{
		id: string
		traitType: string
		value: string
	}>
	collection?: {
		name: string
		description?: string
		logoUrl?: string
		bannerUrl?: string
		totalLikes: number
		totalViews: number
		owner?:
			| {
					id: string
					username: string
					imageUrl?: string
					publicKey: string
			  }
			| {
					publicKey: string
			  }
		publicKey: string
	}
	creater: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
	// Latest marketplace data
	latestListing?: {
		id: string
		publicKey: string
		auctionKey: string | null
		price: number
		currency: string
		status: 'ACTIVE' | 'SOLD' | 'CANCELLED' | 'EXPIRED'
		listingType: 'FIXED_PRICE' | 'AUCTION'
		startTime: string
		endTime?: string
		minBidIncrement?: number
		reservePrice?: number
		instantBuyPrice?: number
		createdAt: string
		updatedAt: string
		escrow: string
	}
	latestOffer?: {
		id: string
		publicKey: string
		price: number
		status: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED' | 'CANCELLED'
		expiresAt?: string
		createdAt: string
		buyer: {
			id: string
			username: string
			imageUrl?: string
			publicKey: string
		}
		escrow: string
	}
	latestBid?: {
		id: string
		amount: number
		createdAt: string
		bidder: {
			id: string
			username: string
			imageUrl?: string
			publicKey: string
		}
	}
	// User-specific flags
	isUserOwnThisNFT: boolean
	isUserHaveActiveOffer: boolean
	isUserHaveActiveListedNFTSale: boolean
}

// Constants for marketplace program
const MARKETPLACE_PROGRAM_ID = FUSION_MARKETPLACE_PROGRAM_ID
const LISTING_SEED = 'listing'

/**
 * Get NFT details by public key
 */
export async function getNFTByPublicKey(
	assetKey: string,
	userPublicKey?: string,
): Promise<NFT | null> {
	try {
		if (!assetKey) {
			console.error('Invalid publicKey provided to getNFTByPublicKey')
			return null
		}

		// Find NFT by publicKey with expanded includes
		let nft = await prisma.nFT.findFirst({
			where: {
				publicKey: assetKey,
			},
			include: {
				owner: true,
				creator: true,
				collection: {
					include: {
						owner: true,
					},
				},
			},
		})

		if (!nft) {
			console.log('NFT not found in database, fetching from blockchain...')
			const asset = await fetchAsset(getServerUmi(), publicKey(assetKey), {
				skipDerivePlugins: false,
			})

			if (asset) {
				const data = await enhanceAssetWithMetadata(asset)
				if (data?.updateAuthority && typeof data.updateAuthority === 'object') {
					const updateAuthorityAddress = data.updateAuthority.address as string
					// Try to find collection
					try {
						const collection = await prisma.collection.findFirst({
							where: {
								publicKey: updateAuthorityAddress,
							},
						})

						if (!collection) {
							const coreCollection = await fetchEnhancedCollection(
								updateAuthorityAddress,
							)
							// Collection not found, create it
							if (coreCollection?.metadata) {
								// Find owner by publicKey
								const updateAuthority =
									coreCollection.updateAuthority.toString()
								const owner = await prisma.user.findFirst({
									where: {
										publicKey: updateAuthority,
									},
								})

								if (owner) {
									await prisma.collection.create({
										data: {
											name: coreCollection.metadata.name,
											description: coreCollection.metadata.description,
											logoUrl: coreCollection.metadata.image,
											bannerUrl: coreCollection.metadata.image,
											metadataUri: coreCollection.uri,
											publicKey: updateAuthorityAddress,
											owner: {
												connect: { id: owner?.id },
											},
										},
									})
								}
							}
						}
					} catch (error) {
						console.error('Error creating collection:', error)
					}
				}

				if (!data) {
					console.error('Failed to enhance asset with metadata')
					return null
				}

				// Find owner by publicKey
				const ownerAddress = data.owner.toString()
				const owner = await prisma.user.findFirst({
					where: {
						publicKey: ownerAddress,
					},
				})

				if (!owner) {
					console.error('Owner not found for NFT:', ownerAddress)
					return null
				}

				// Prepare NFT data
				const nftData = {
					name: data.metadata.name,
					description: data.metadata.description,
					metadataUrl: data.uri,
					publicKey: assetKey,
					imageUrl: data.metadata.image,
					royaltyBasisPoints: data.metadata.seller_fee_basis_points,
					attributes: data.metadata.attributes,
					owner: {
						connect: { id: owner?.id },
					},
					creator: {
						connect: { id: owner?.id },
					},
				}

				// Find collection if it exists
				let collectionId = undefined
				if (data.updateAuthority && typeof data.updateAuthority === 'object') {
					const updateAuthorityAddress = data.updateAuthority.address as string
					const collection = await prisma.collection.findFirst({
						where: {
							publicKey: updateAuthorityAddress,
						},
					})

					if (collection) {
						collectionId = collection.id
					}
				}

				// Add collection connection if available
				const createData = collectionId
					? {
							...nftData,
							collection: {
								connect: { id: collectionId },
							},
						}
					: nftData

				// Create the NFT
				await prisma.nFT.create({
					data: createData,
				})

				// Fetch the newly created NFT with all the includes we need
				nft = await prisma.nFT.findFirst({
					where: {
						publicKey: assetKey,
					},
					include: {
						owner: true,
						creator: true,
						collection: {
							include: {
								owner: true,
							},
						},
					},
				})
			}
		}

		// If NFT is not found, return null
		if (!nft) {
			return null
		}

		// Generate listing address
		const [listing] = PublicKey.findProgramAddressSync(
			[
				Buffer.from(LISTING_SEED),
				new PublicKey(nft.publicKey).toBuffer(),
				new PublicKey(
					nft?.owner?.publicKey ? nft.owner.publicKey : '',
				).toBuffer(),
			],
			new PublicKey(MARKETPLACE_PROGRAM_ID),
		)

		// Fetch metadata from URL if available
		let attributes = undefined
		if (nft.metadataUrl) {
			try {
				const metadata = await fetchMetadata(nft.metadataUrl)
				if (metadata?.attributes) {
					// Process attributes from metadata
					attributes = metadata.attributes.map((attr) => ({
						id: `${attr.traitType || attr.trait_type}-${attr.value}`
							.replace(/\s+/g, '-')
							.toLowerCase(),
						traitType: attr.trait_type || attr.traitType,
						value: attr.value,
					}))
				}
			} catch (error) {
				console.error(
					`Error fetching metadata for NFT ${nft.publicKey}:`,
					error,
				)
			}
		}

		// Fallback to attributes stored in the database if metadata fetch failed
		if (!attributes && nft.attributes) {
			attributes = nft.attributes as Array<{
				id: string
				traitType: string
				value: string
			}>
		}

		// Fetch the latest active listing for this NFT
		const latestListing = await prisma.listing.findFirst({
			where: {
				nftId: nft.id,
				status: 'ACTIVE',
			},
			orderBy: {
				createdAt: 'desc',
			},
			include: {
				lastBid: {
					include: {
						bidder: true,
					},
				},
			},
		})

		// Fetch the latest offer for this NFT
		const latestOffer = await prisma.offer.findFirst({
			where: {
				nftId: nft.id,
				status: 'PENDING',
			},
			orderBy: {
				price: 'desc', // Get highest price offer first
			},
			include: {
				buyer: true,
			},
		})

		// Fetch the latest bid if this is an auction listing
		let latestBid = null
		if (latestListing && latestListing.listingType === 'AUCTION') {
			// If the listing already has a lastBid relation, use that
			if (latestListing.lastBid) {
				latestBid = latestListing.lastBid
			} else {
				// Otherwise fetch the latest bid for this listing
				latestBid = await prisma.bid.findFirst({
					where: {
						listingId: latestListing.id,
					},
					orderBy: {
						amount: 'desc', // Get highest bid first
					},
					include: {
						bidder: true,
					},
				})
			}
		}

		// Check if the user owns this NFT
		const isUserOwnThisNFT = userPublicKey === nft.owner?.publicKey

		// Check if the user has any active offers on this NFT
		let isUserHaveActiveOffer = false
		if (userPublicKey) {
			const userActiveOffers = await prisma.offer.findFirst({
				where: {
					nftId: nft.id,
					status: 'PENDING',
					buyer: {
						publicKey: userPublicKey,
					},
				},
			})
			isUserHaveActiveOffer = !!userActiveOffers
		}

		// Check if the user has an active listing for this NFT
		const isUserHaveActiveListedNFTSale =
			isUserOwnThisNFT && latestListing?.status === 'ACTIVE'

		// Map to the expected NFT type
		return {
			name: nft.name,
			publicKey: nft.publicKey,
			description: nft.description || '',
			logoUrl: nft.imageUrl,
			symbol: nft.name.substring(0, 3).toUpperCase(),
			biddingPrice: nft.lastBiddingPrice || 0,
			totalLikes: nft.totalLikes,
			totalViews: nft.totalViews,
			owner: {
				id: nft.owner.id,
				username: nft.owner.username,
				imageUrl: nft.owner.imageUrl || '',
				publicKey: nft.owner.publicKey,
			},
			creater: {
				id: nft.creator.id,
				username: nft.creator.username,
				imageUrl: nft.creator.imageUrl || '',
				publicKey: nft.creator.publicKey,
			},
			listing: listing.toString(),
			// Additional properties
			collectionId: nft.collectionId || undefined,
			royaltyBasisPoints: nft.royaltyBasisPoints || 0,
			updatedAt: nft.updatedAt?.toISOString(),
			metadataUrl: nft.metadataUrl || undefined,
			isLocked: nft.isLocked || false,
			attributes: attributes,
			// Include collection data if available
			collection: nft.collection
				? {
						name: nft.collection.name,
						description: nft.collection.description,
						logoUrl: nft.collection.logoUrl,
						bannerUrl: nft.collection.bannerUrl,
						publicKey: nft.collection.publicKey,
						totalLikes: nft.collection.totalLikes,
						totalViews: nft.collection.totalViews,
						owner: nft.collection.owner
							? {
									id: nft.collection.owner.id,
									username: nft.collection.owner.username,
									imageUrl: nft.collection.owner.imageUrl || '',
									publicKey: nft.collection.owner.publicKey,
								}
							: undefined,
					}
				: undefined,
			// Include latest marketplace data if available
			latestListing: latestListing
				? {
						id: latestListing.id,
						publicKey: latestListing.listKey,
						auctionKey: latestListing.auctionKey,
						price: latestListing.price,
						currency: latestListing.currency,
						status: latestListing.status,
						listingType: latestListing.listingType,
						startTime: latestListing.startTime.toISOString(),
						endTime: latestListing.endTime?.toISOString(),
						minBidIncrement: latestListing.minBidIncrement || undefined,
						reservePrice: latestListing.reservePrice || undefined,
						instantBuyPrice: latestListing.instantBuyPrice || undefined,
						createdAt: latestListing.createdAt.toISOString(),
						updatedAt: latestListing.updatedAt.toISOString(),
						escrow: latestListing.escrow,
					}
				: undefined,
			latestOffer: latestOffer
				? {
						id: latestOffer.id,
						publicKey: latestOffer.publicKey,
						price: latestOffer.price,
						status: latestOffer.status,
						expiresAt: latestOffer.expiresAt?.toISOString(),
						createdAt: latestOffer.createdAt.toISOString(),
						buyer: {
							id: latestOffer.buyer.id,
							username: latestOffer.buyer.username,
							imageUrl: latestOffer.buyer.imageUrl || '',
							publicKey: latestOffer.buyer.publicKey,
						},
						escrow: latestOffer.escrow,
					}
				: undefined,
			latestBid: latestBid
				? {
						id: latestBid.id,
						amount: latestBid.amount,
						createdAt: latestBid.createdAt.toISOString(),
						bidder: {
							id: latestBid.bidder.id,
							username: latestBid.bidder.username,
							imageUrl: latestBid.bidder.imageUrl || '',
							publicKey: latestBid.bidder.publicKey,
						},
					}
				: undefined,
			// User-specific flags
			isUserOwnThisNFT,
			isUserHaveActiveOffer,
			isUserHaveActiveListedNFTSale,
		}
	} catch (error) {
		console.error('Error fetching NFT:', error)
		return null
	}
}

/**
 * Activity type for NFT activity logs
 */
export type NFTActivity = {
	id: string
	type: string
	price?: {
		sol?: number
		usd?: number
	}
	from?: {
		id: string
		username: string
		publicKey: string
	}
	to?: {
		id: string
		username: string
		publicKey: string
	}
	date: string
	transactionHash: string
}

/**
 * Get NFT activity logs
 * @param publicKey The NFT's public key
 * @param limit Maximum number of activities to return
 * @param page Page number for pagination
 * @returns Object containing activities array and pagination info
 */
export async function getNFTActivities(
	publicKey: string,
	limit = 10,
	page = 1,
): Promise<{ activities: NFTActivity[]; total: number; pages: number }> {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to getNFTActivities')
			return { activities: [], total: 0, pages: 0 }
		}

		// First get the NFT ID from the public key
		const nft = await prisma.nFT.findFirst({
			where: { publicKey },
			select: { id: true },
		})

		if (!nft) {
			console.log(`NFT with publicKey ${publicKey} not found for activity logs`)
			return { activities: [], total: 0, pages: 0 }
		}

		// Calculate pagination
		const skip = (page - 1) * limit

		// Get total count for pagination
		const totalActivities = await prisma.activityLog.count({
			where: { nftId: nft.id, type: { not: 'ROYALTY_RECEIVED' } },
		})

		// Calculate total pages
		const totalPages = Math.ceil(totalActivities / limit)

		// Get activity logs for this NFT with pagination
		const activityLogs = await prisma.activityLog.findMany({
			where: { nftId: nft.id },
			include: {
				user: true,
			},
			orderBy: { createdAt: 'desc' },
			skip,
			take: limit,
		})

		// Transform activity logs to the expected format
		const formattedActivities = activityLogs.map((log) => {
			// Extract price from data if available
			const price =
				log.data && typeof log.data === 'object' && 'amount' in log.data
					? {
							sol:
								Number(
									(log.data as Record<string, unknown>).amount as string,
								) || 0,
						}
					: undefined

			// Extract from/to users if available in data
			const from = log.user
				? {
						id: log.user.id,
						username: log.user.username,
						publicKey: log.user.publicKey,
					}
				: undefined

			// For transfers, the 'to' user might be in the data
			const to =
				log.data && typeof log.data === 'object' && 'buyerId' in log.data
					? {
							id: (log.data as Record<string, unknown>).buyerId as string,
							username: 'User',
							publicKey: '',
						}
					: undefined

			return {
				id: log.id,
				type: log.type,
				price,
				from,
				to,
				date: log.createdAt.toISOString(),
				transactionHash: log.transactionHash,
			}
		})

		return {
			activities: formattedActivities,
			total: totalActivities,
			pages: totalPages,
		}
	} catch (error) {
		console.error('Error fetching NFT activities:', error)
		return { activities: [], total: 0, pages: 0 }
	}
}

/**
 * Get related NFTs from the same collection
 */
export async function getRelatedNFTs(
	publicKey: string,
	userPublicKey = '',
	limit = 10,
): Promise<NFT[]> {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to getRelatedNFTs')
			return []
		}

		// First get the NFT to find its collection
		const nft = await prisma.nFT.findFirst({
			where: { publicKey },
			select: { collectionId: true },
		})

		if (!nft) {
			console.log(`NFT with publicKey ${publicKey} not found`)
			return []
		}

		if (!nft.collectionId) {
			console.log(`NFT with publicKey ${publicKey} has no collection`)
			return []
		}

		// Get other NFTs from the same collection
		const relatedNfts = await prisma.nFT.findMany({
			where: {
				collectionId: nft.collectionId || undefined,
				NOT: { publicKey }, // Exclude the current NFT
			},
			select: {
				id: true,
				name: true,
				publicKey: true,
				description: true,
				imageUrl: true,
				metadataUrl: true,
				attributes: true,
				lastBiddingPrice: true,
				totalLikes: true,
				totalViews: true,
				royaltyBasisPoints: true,
				owner: {
					select: {
						id: true,
						username: true,
						imageUrl: true,
						publicKey: true,
					},
				},
				creator: {
					select: {
						id: true,
						username: true,
						imageUrl: true,
						publicKey: true,
					},
				},
				collection: {
					select: {
						name: true,
					},
				},
				listings: {
					where: {
						status: ListingStatus.ACTIVE,
					},
					select: {
						id: true,
						listKey: true,
						auctionKey: true,
						price: true,
						currency: true,
						status: true,
						listingType: true,
						startTime: true,
						endTime: true,
						minBidIncrement: true,
						reservePrice: true,
						instantBuyPrice: true,
						createdAt: true,
						updatedAt: true,
						escrow: true,
					},
				},
			},
			take: limit,
		})

		// Map to the expected NFT type
		const enhancedNfts = []

		// Process each related NFT and fetch metadata if available
		for (const relatedNft of relatedNfts) {
			const [listing] = PublicKey.findProgramAddressSync(
				[
					Buffer.from(LISTING_SEED),
					new PublicKey(relatedNft.publicKey).toBuffer(),
					new PublicKey(relatedNft.owner.publicKey).toBuffer(),
				],
				new PublicKey(MARKETPLACE_PROGRAM_ID),
			)

			// Fetch metadata from URL if available
			let attributes = undefined
			if (relatedNft.metadataUrl) {
				try {
					const metadata = await fetchMetadata(relatedNft.metadataUrl)
					if (metadata?.attributes) {
						// Process attributes from metadata
						attributes = metadata.attributes.map((attr) => ({
							id: `${attr.traitType}-${attr.value}`
								.replace(/\s+/g, '-')
								.toLowerCase(),
							traitType: attr.traitType,
							value: attr.value,
						}))
					}
				} catch (error) {
					console.error(
						`Error fetching metadata for NFT ${relatedNft.publicKey}:`,
						error,
					)
				}
			}

			// Fallback to attributes stored in the database if metadata fetch failed
			if (!attributes && relatedNft.attributes) {
				attributes = relatedNft.attributes as Array<{
					id: string
					traitType: string
					value: string
				}>
			}

			// Define an intermediate type for the raw listing data
			type RawListingData = {
				id: string
				listKey: string
				auctionKey: string | null
				price: number
				currency: string
				status: 'ACTIVE' | 'SOLD' | 'CANCELLED' | 'EXPIRED' // Use specific statuses
				listingType: 'FIXED_PRICE' | 'AUCTION' // Use specific types
				startTime: Date
				endTime: Date | null
				minBidIncrement: number | null
				reservePrice: number | null
				instantBuyPrice: number | null
				createdAt: Date
				updatedAt: Date
				escrow: string
			}

			const rawLatestListing = relatedNft?.listings?.[0] as
				| RawListingData
				| undefined // Get the raw listing and cast it
			const transformedLatestListing = rawLatestListing
				? {
						// Transform if exists
						id: rawLatestListing.id,
						publicKey: rawLatestListing.listKey, // Now TS should know listKey exists
						auctionKey: rawLatestListing.auctionKey,
						price: rawLatestListing.price,
						currency: rawLatestListing.currency,
						status: rawLatestListing.status,
						listingType: rawLatestListing.listingType,
						startTime: rawLatestListing.startTime.toISOString(),
						endTime: rawLatestListing.endTime?.toISOString(),
						minBidIncrement: rawLatestListing.minBidIncrement || undefined,
						reservePrice: rawLatestListing.reservePrice || undefined,
						instantBuyPrice: rawLatestListing.instantBuyPrice || undefined,
						createdAt: rawLatestListing.createdAt.toISOString(),
						updatedAt: rawLatestListing.updatedAt.toISOString(),
						escrow: rawLatestListing.escrow,
					}
				: undefined

			// Check if the user owns this NFT
			const isUserOwnThisNFT = userPublicKey === relatedNft.owner?.publicKey

			// Check if the user has any active offers on this NFT
			let isUserHaveActiveOffer = false
			if (userPublicKey) {
				const userActiveOffers = await prisma.offer.findFirst({
					where: {
						nftId: relatedNft.id,
						status: 'PENDING',
						buyer: {
							publicKey: userPublicKey,
						},
					},
				})
				isUserHaveActiveOffer = !!userActiveOffers
			}

			// Check if the user has any active listings for this NFT
			let isUserHaveActiveListedNFTSale = false
			if (isUserOwnThisNFT) {
				const userActiveListings = await prisma.listing.findFirst({
					where: {
						nftId: relatedNft.id,
						status: 'ACTIVE',
						sellerId: relatedNft.owner?.id,
					},
				})
				isUserHaveActiveListedNFTSale = !!userActiveListings
			}

			enhancedNfts.push({
				name: relatedNft.name,
				publicKey: relatedNft.publicKey,
				description: relatedNft.description || '',
				logoUrl: relatedNft.imageUrl,
				symbol: relatedNft.name.substring(0, 3).toUpperCase(),
				biddingPrice: relatedNft.lastBiddingPrice || 0,
				totalLikes: relatedNft.totalLikes,
				totalViews: relatedNft.totalViews,
				collectionName: relatedNft?.collection?.name ?? '',
				royaltyBasisPoints: relatedNft.royaltyBasisPoints,
				owner: {
					id: relatedNft.owner?.id,
					username: relatedNft.owner.username,
					imageUrl: relatedNft.owner.imageUrl || '',
					publicKey: relatedNft.owner.publicKey,
				},
				creater: {
					id: relatedNft.creator.id,
					username: relatedNft.creator.username,
					imageUrl: relatedNft.creator.imageUrl || '',
					publicKey: relatedNft.creator.publicKey,
				},
				listing: listing.toString(),
				latestListing: transformedLatestListing,
				attributes,
				// User-specific flags
				isUserOwnThisNFT,
				isUserHaveActiveOffer,
				isUserHaveActiveListedNFTSale,
			})
		}

		return enhancedNfts
	} catch (error) {
		console.error('Error fetching related NFTs:', error)
		return []
	}
}

/**
 * Offer type for NFT offers
 */
export type NFTOffer = {
	id: string
	publicKey: string
	price: number
	status: string
	expiresAt?: string
	createdAt: string
	updatedAt: string
	creator: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
	buyer: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
	escrow: string
}

/**
 * Get offers for an NFT
 * @param publicKey The NFT's public key
 * @param limit Maximum number of offers to return
 * @param page Page number for pagination
 * @returns Object containing offers array and pagination info
 */
export async function getNFTOffers(
	publicKey: string,
	limit = 10,
	page = 1,
): Promise<{ offers: NFTOffer[]; total: number; pages: number }> {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to getNFTOffers')
			return { offers: [], total: 0, pages: 0 }
		}

		// First get the NFT ID from the public key
		const nft = await prisma.nFT.findFirst({
			where: { publicKey },
			select: { id: true, creator: true },
		})

		if (!nft) {
			console.log(`NFT with publicKey ${publicKey} not found for offers`)
			return { offers: [], total: 0, pages: 0 }
		}

		// Calculate pagination
		const skip = (page - 1) * limit

		// Get total count for pagination
		const totalOffers = await prisma.offer.count({
			where: {
				nftId: nft.id,
				status: {
					in: ['PENDING', 'ACCEPTED'], // Only include active offers
				},
			},
		})

		// Calculate total pages
		const totalPages = Math.ceil(totalOffers / limit)

		// Get offers for this NFT with pagination
		const offers = await prisma.offer.findMany({
			where: {
				nftId: nft.id,
				status: {
					in: ['PENDING', 'ACCEPTED'], // Only include active offers
				},
			},
			include: {
				buyer: true,
			},
			orderBy: {
				price: 'desc', // Sort by highest price first
			},
			skip,
			take: limit,
		})

		// Transform offers to the expected format
		const formattedOffers = offers.map((offer) => ({
			id: offer.id,
			publicKey: offer.publicKey,
			price: offer.price,
			status: offer.status,
			expiresAt: offer.expiresAt?.toISOString(),
			createdAt: offer.createdAt.toISOString(),
			updatedAt: offer.updatedAt.toISOString(),
			creator: {
				id: nft.creator.id,
				username: nft.creator.username,
				imageUrl: nft.creator.imageUrl || '',
				publicKey: nft.creator.publicKey,
			},
			buyer: {
				id: offer.buyer.id,
				username: offer.buyer.username,
				imageUrl: offer.buyer.imageUrl || '',
				publicKey: offer.buyer.publicKey,
			},
			escrow: offer.escrow,
		}))

		return {
			offers: formattedOffers,
			total: totalOffers,
			pages: totalPages,
		}
	} catch (error) {
		console.error('Error fetching NFT offers:', error)
		return { offers: [], total: 0, pages: 0 }
	}
}

/**
 * Order type for NFT order history
 */
export type NFTOrder = {
	id: string
	publicKey: string
	price: number
	currency: string
	status: string
	transactionHash?: string
	createdAt: string
	updatedAt: string
	buyer: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
	seller: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
}

/**
 * Get order history for an NFT
 * @param publicKey The NFT's public key
 * @param limit Maximum number of orders to return
 * @returns Array of orders for the NFT
 */
export async function getNFTOrderHistory(
	publicKey: string,
	limit = 10,
): Promise<NFTOrder[]> {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to getNFTOrderHistory')
			return []
		}

		// First get the NFT ID from the public key
		const nft = await prisma.nFT.findFirst({
			where: { publicKey },
			select: { id: true },
		})

		if (!nft) {
			console.log(`NFT with publicKey ${publicKey} not found for order history`)
			return []
		}

		// Get orders for these listings
		const orders = await prisma.order.findMany({
			where: {
				publicKey: publicKey,
				status: 'COMPLETED', // Only include completed orders
			},
			include: {
				buyer: true,
				seller: true,
			},
			orderBy: { createdAt: 'desc' },
			take: limit,
		})

		// Transform orders to the expected format
		return orders.map((order) => ({
			id: order.id,
			publicKey: order.publicKey,
			price: order.price,
			currency: order.currency,
			status: order.status,
			transactionHash: order.transactionHash || undefined,
			createdAt: order.createdAt.toISOString(),
			updatedAt: order.updatedAt.toISOString(),
			buyer: {
				id: order.buyer.id,
				username: order.buyer.username,
				imageUrl: order.buyer.imageUrl || '',
				publicKey: order.buyer.publicKey,
			},
			seller: {
				id: order.seller.id,
				username: order.seller.username,
				imageUrl: order.seller.imageUrl || '',
				publicKey: order.seller.publicKey,
			},
		}))
	} catch (error) {
		console.error('Error fetching NFT order history:', error)
		return []
	}
}

export async function getPriceHistory(publicKey: string) {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to getPriceHistory')
			return []
		}

		// First get the NFT ID from the public key
		const nft = await prisma.nFT.findFirst({
			where: { publicKey },
			select: { id: true },
		})

		if (!nft) {
			console.log(`NFT with publicKey ${publicKey} not found for price history`)
			return []
		}

		// Get price history for this NFT
		const orderHistory = await prisma.order.findMany({
			where: {
				publicKey,
				status: 'COMPLETED', // Only include completed orders
			},
			orderBy: { createdAt: 'desc' },
			select: {
				price: true,
				createdAt: true,
			},
		})
		return orderHistory
	} catch (error) {
		console.error('Error fetching NFT price history:', error)
		return []
	}
}

/**
 * Listing history type for NFT listing history
 */
export type NFTListingHistory = {
	id: string
	publicKey: string
	price: number
	currency: string
	status: string
	listingType: string
	startTime: string
	endTime?: string
	minBidIncrement?: number
	reservePrice?: number
	instantBuyPrice?: number
	createdAt: string
	updatedAt: string
	seller: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
}

/**
 * Get listing history for an NFT
 * @param publicKey The NFT's public key
 * @param limit Maximum number of listings to return
 * @param page Page number for pagination
 * @returns Object containing listings array and pagination info
 */
export async function getNFTListingHistory(
	publicKey: string,
	limit = 10,
	page = 1,
): Promise<{ listings: NFTListingHistory[]; total: number; pages: number }> {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to getNFTListingHistory')
			return { listings: [], total: 0, pages: 0 }
		}

		// First get the NFT ID from the public key
		const nft = await prisma.nFT.findFirst({
			where: { publicKey },
			select: { id: true },
		})

		if (!nft) {
			console.log(
				`NFT with publicKey ${publicKey} not found for listing history`,
			)
			return { listings: [], total: 0, pages: 0 }
		}

		// Calculate pagination
		const skip = (page - 1) * limit

		// Get total count for pagination
		const totalListings = await prisma.listing.count({
			where: { nftId: nft.id },
		})

		// Calculate total pages
		const totalPages = Math.ceil(totalListings / limit)

		// Get listings for this NFT with pagination
		const listings = await prisma.listing.findMany({
			where: { nftId: nft.id },
			include: {
				seller: true,
			},
			orderBy: { createdAt: 'desc' },
			skip,
			take: limit,
		})

		// Transform listings to the expected format
		const formattedListings = listings.map((listing) => ({
			id: listing.id,
			publicKey: listing.listKey,
			price: listing.price,
			currency: listing.currency,
			status: listing.status,
			listingType: listing.listingType,
			startTime: listing.startTime.toISOString(),
			endTime: listing.endTime?.toISOString(),
			minBidIncrement: listing.minBidIncrement || undefined,
			reservePrice: listing.reservePrice || undefined,
			instantBuyPrice: listing.instantBuyPrice || undefined,
			createdAt: listing.createdAt.toISOString(),
			updatedAt: listing.updatedAt.toISOString(),
			seller: {
				id: listing.seller.id,
				username: listing.seller.username,
				imageUrl: listing.seller.imageUrl || '',
				publicKey: listing.seller.publicKey,
			},
		}))

		return {
			listings: formattedListings,
			total: totalListings,
			pages: totalPages,
		}
	} catch (error) {
		console.error('Error fetching NFT listing history:', error)
		return { listings: [], total: 0, pages: 0 }
	}
}
