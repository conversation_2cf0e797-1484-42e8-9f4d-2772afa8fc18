import { PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { v4 as uuidv4 } from 'uuid'
import { s3Client } from '@/server/lib/s3Client'

export async function onFileUploadPresignedUrl({
	fileName,
	fileType,
}: {
	fileName: string
	fileType: string
}): Promise<{
	success: boolean
	url?: string
	message?: string
	fileUrl?: string
}> {
	try {
		const bucketName = import.meta.env.R2_BUCKET_NAME || 'nft-marketplace'
		const uniqueFileName = `uploads/${uuidv4()}-${fileName}`
		const command = new PutObjectCommand({
			Bucket: bucketName,
			Key: uniqueFileName,
			ContentType: fileType,
			ACL: 'public-read',
		})
		const url = await getSignedUrl(s3Client, command, { expiresIn: 3600 })
		return {
			success: true,
			url,
			fileUrl: `https://${import.meta.env.R2_PUBLIC_DOMAIN}/${uniqueFileName}`,
		}
	} catch (error) {
		return {
			success: false,
			message: 'An unknown error occurred',
		}
	}
}
