/**
 * Represents a specific instruction within a transaction.
 */
/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
type InstructionDetail = {
	accounts: number[] // Indices of accounts involved in this instruction, referencing the main accountKeys array
	data: string // Instruction data, often base58 or base64 encoded
	programIdIndex: number // Index of the program ID in the main accountKeys array
}

/**
 * Represents inner instructions executed by a program called in the main transaction.
 */
type InnerInstructionDetail = {
	index: number // Index of the main instruction that triggered these inner instructions
	instructions: InstructionDetail[] // The list of inner instructions
}

/**
 * Represents accounts loaded via Address Lookup Tables (ALT).
 */
type LoadedAddresses = {
	readonly: string[] // Public keys of readonly accounts loaded
	writable: string[] // Public keys of writable accounts loaded
}

/**
 * Placeholder type for token balance information.
 * Replace with actual structure if known (e.g., from getParsedTokenAccounts).
 */
type TokenBalance = {
	// Example properties (adjust based on actual data structure):
	// accountIndex: number;
	// mint: string;
	// owner?: string;
	// uiTokenAmount?: {
	//   amount: string;
	//   decimals: number;
	//   uiAmount: number | null;
	//   uiAmountString: string;
	// };
	[key: string]: any // Allows for flexibility if structure varies
}

/**
 * Placeholder type for reward information.
 * Replace with actual structure if known.
 */
type Reward = {
	// Example properties (adjust based on actual data structure):
	// pubkey: string;
	// lamports: number;
	// postBalance: number;
	// rewardType: string | null; // e.g., "fee", "rent", "voting", "staking"
	// commission?: number | null; // Staking rewards only
	[key: string]: any // Allows for flexibility if structure varies
}

/**
 * Represents the metadata associated with a transaction's execution.
 */
type Meta = {
	err: any | null // Error object if the transaction failed, null otherwise
	fee: number // Transaction fee in lamports
	innerInstructions: InnerInstructionDetail[] // Inner instructions executed
	loadedAddresses: LoadedAddresses // Accounts loaded via ALTs
	logMessages: string[] // Log messages outputted by programs during execution
	postBalances: number[] // SOL balances of accounts after the transaction (order matches accountKeys)
	postTokenBalances: TokenBalance[] // Token balances of accounts after the transaction
	preBalances: number[] // SOL balances of accounts before the transaction (order matches accountKeys)
	preTokenBalances: TokenBalance[] // Token balances of accounts before the transaction
	rewards: Reward[] // Rewards paid to validators or delegators
}

/**
 * Header information for a transaction message.
 */
type MessageHeader = {
	numReadonlySignedAccounts: number // Number of readonly accounts that signed the transaction
	numReadonlyUnsignedAccounts: number // Number of readonly accounts that did not sign
	numRequiredSignatures: number // Number of signatures required for the transaction
}

/**
 * Represents a single instruction within the transaction message.
 * Note: Similar to InstructionDetail but used within the TransactionMessage context.
 */
type TransactionInstruction = {
	accounts: number[] // Indices of accounts involved, referencing accountKeys in TransactionMessage
	data: string // Instruction data (encoded)
	programIdIndex: number // Index of the program ID in accountKeys
}

/**
 * Represents an Address Lookup Table (ALT) used in the transaction.
 */
type AddressTableLookup = {
	accountKey: string // The public key of the ALT account
	writableIndexes: number[] // Buffer indexes of writable accounts loaded from the table
	readonlyIndexes: number[] // Buffer indexes of readonly accounts loaded from the table
}

/**
 * Represents the core message of a transaction, containing instructions and account keys.
 */
type TransactionMessage = {
	accountKeys: string[] // List of all account public keys involved in the transaction
	addressTableLookups: AddressTableLookup[] | null // ALTs used, null if none
	header: MessageHeader // Message header details
	instructions: TransactionInstruction[] // Main instructions of the transaction
	recentBlockhash: string // Blockhash used for the transaction
}

/**
 * Represents the complete transaction structure.
 */
type Transaction = {
	message: TransactionMessage // The transaction message
	signatures: string[] // List of signatures for the transaction
}

/**
 * Represents the overall structure of the transaction data retrieved.
 * This often corresponds to the result of Solana RPC calls like `getTransaction`.
 */
export type TransactionData = {
	blockTime: number // Unix timestamp of when the block was confirmed
	indexWithinBlock: number // Index of the transaction within the block
	meta: Meta // Transaction metadata
	slot: number // Slot number where the transaction was confirmed
	transaction: Transaction // The transaction details
	version: number | 'legacy' // Transaction version (e.g., 0 for versioned, 'legacy' for older)
}
