import { Hono } from 'hono'
import { fixedPriceListing } from './examples/fixed-price'
import { handleRequest } from './handle-events'

const event = new Hono()

// Health check endpoint
event.get('', async (c) => {
	return c.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// CORS preflight handling
event.options('', async (c) => {
	return c.text('ok')
})

// Main webhook endpoint for event processing
event.post('', async ({ json, req }) => {
	try {
		const body = await req.json()

		// Check if body is empty
		if (JSON.stringify(body) === JSON.stringify({})) {
			return json({ success: true })
		}

		// Process the event asynchronously to avoid blocking the response
		// We don't await this to ensure quick response to the webhook
		try {
			await handleRequest(body)
			return json({ success: true })
		} catch (error) {
			console.error('Error in async event processing', { error })
			return json(
				{ success: false, error: `Failed to process event: ${error}` },
				500,
			)
		}
	} catch (error) {
		return json(
			{
				success: false,
				error: 'Internal server error processing webhook',
			},
			500,
		)
	}
})

export default event
