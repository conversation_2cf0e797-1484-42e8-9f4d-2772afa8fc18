// Map of event names to handler names
export const eventToHandlerName: Record<string, string> = {
	// Listing related events
	ListingCreated: 'ListingCreatedEvent',
	ListingCancelled: 'ListingCancelledEvent',
	ListingPurchased: 'ListingPurchasedEvent',

	// Offer related events
	OfferCreated: 'OfferCreatedEvent',
	OfferAccepted: 'OfferAcceptedEvent',
	OfferCancelled: 'OfferCancelledEvent',

	// Bid related events
	BidPlaced: 'BidPlacedEvent',
}

// Create a reverse mapping from handler names to event names
export const handlerNameToEvent: Record<string, string> = {}
for (const [eventName, handlerName] of Object.entries(eventToHandlerName)) {
	handlerNameToEvent[handlerName] = eventName
}

// Create a proxy object that allows accessing handlers by name
// This allows code like eventHandlers.ListingCancelledEvent to work
export const eventHandlers = new Proxy({} as Record<string, string>, {
	get: (_, prop) => {
		if (typeof prop === 'string' && prop in handlerNameToEvent) {
			return handlerNameToEvent[prop]
		}
		return undefined
	},
	has: (_, prop) => {
		return typeof prop === 'string' && prop in handlerNameToEvent
	},
})
