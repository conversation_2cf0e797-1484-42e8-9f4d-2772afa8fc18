import { PublicKey } from '@solana/web3.js'

/**
 * Processes an event object by normalizing its data:
 * - Converts PublicKey instances to base58 strings
 * - Converts BN instances to BigInt values
 * - Converts enum objects to simple key-value pairs
 *
 * @param eventData The raw event data object
 * @returns A normalized event data object with consistent types
 */
export function processEventData(
	eventData: Record<string, unknown>,
): Record<string, string | bigint | object> {
	const processedData: Record<string, string | bigint | object> = {}

	for (const [key, value] of Object.entries(eventData)) {
		// Handle PublicKey instances
		if (value instanceof PublicKey) {
			processedData[key] = value.toBase58()
			continue
		}

		// Handle BN (Big Number) instances
		if (
			value &&
			typeof value === 'object' &&
			'toNumber' in value &&
			typeof value.toNumber === 'function'
		) {
			try {
				// For large numbers, use BigInt to avoid precision loss
				processedData[key] = BigInt(value.toString())
			} catch (error) {
				// Fallback to string representation if conversion fails
				processedData[key] = value.toString()
			}
			continue
		}

		// Handle enum-like objects (e.g., { FixedPrice: {} })
		if (
			value &&
			typeof value === 'object' &&
			!Array.isArray(value) &&
			Object.keys(value).length > 0
		) {
			const enumKeys = Object.keys(value)
			if (enumKeys.length === 1) {
				// Extract the enum variant name (e.g., "FixedPrice")
				const enumType = enumKeys[0]

				// Check if it's a complex enum with data
				const enumValue = value[enumType as keyof typeof value]
				if (
					enumValue &&
					typeof enumValue === 'object' &&
					Object.keys(enumValue).length > 0
				) {
					// For complex enums with data, preserve both the type and data
					processedData[key] = {
						type: enumType,
						data: processEventData(enumValue as Record<string, unknown>),
					}
				} else {
					// For simple enums, just use the type name
					processedData[key] = enumType
				}
				continue
			}
		}

		// Handle nested objects recursively
		if (
			value &&
			typeof value === 'object' &&
			!Array.isArray(value) &&
			!(value instanceof Date)
		) {
			processedData[key] = processEventData(value as Record<string, unknown>)
			continue
		}

		// Handle arrays recursively
		if (Array.isArray(value)) {
			processedData[key] = value.map((item) =>
				typeof item === 'object' && item !== null
					? processEventData(item as Record<string, unknown>)
					: item,
			)
			continue
		}

		// Pass through all other values unchanged
		processedData[key] = value as string | bigint | object
	}

	return processedData
}
