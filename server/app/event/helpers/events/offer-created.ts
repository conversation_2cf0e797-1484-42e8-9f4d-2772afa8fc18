import { <PERSON>Key } from '@solana/web3.js'
import { FUSION_MARKETPLACE_PROGRAM_ID } from '@/lib/generated'

const MARKETPLACE_PROGRAM_ID = FUSION_MARKETPLACE_PROGRAM_ID
/**
 * Event emitted when an offer is created
 * Based on the OfferCreatedEvent in fusion_marketplace.json
 */
export type OfferCreatedEvent = {
	/** The offer account public key */
	offer: PublicKey
	/** The buyer who made the offer */
	buyer: PublicKey
	/** The asset the offer is for */
	asset: PublicKey
	/** The price offered in lamports */
	price: bigint
	/** Timestamp when the offer expires */
	expires_at: bigint
	/** Timestamp when the offer was created */
	created_at: bigint
}

/**
 * Processed version of OfferCreatedEvent with normalized types
 */
export type ProcessedOfferCreatedEvent = {
	/** The offer account public key (base58 string) */
	offer: string
	/** The escrow account public key (base58 string) */
	escrow: string
	/** The buyer who made the offer (base58 string) */
	buyer: string
	/** The asset the offer is for (base58 string) */
	asset: string
	/** The price offered in lamports (BigInt) */
	price: bigint
	/** Timestamp when the offer expires (BigInt) */
	expiresAt: bigint
	/** Timestamp when the offer was created (BigInt) */
	createdAt: bigint
}

/**
 * Process an OfferCreatedEvent from the blockchain
 * @param eventData Raw event data from the blockchain
 * @returns Processed event data with normalized types
 */
export function processOfferCreatedEvent(
	eventData: Record<string, unknown>,
): ProcessedOfferCreatedEvent {
	// Convert price to BigInt
	const priceValue = eventData.price as bigint

	// Handle expires_at with fallback to expiresAt
	const expiresAtValue = (eventData.expires_at || eventData.expiresAt) as bigint

	// Handle created_at with fallback to createdAt
	const createdAtValue = (eventData.created_at || eventData.createdAt) as bigint

	const offer = eventData.offer as string
	const [escrow] = PublicKey.findProgramAddressSync(
		[Buffer.from('escrow-payment'), new PublicKey(offer).toBuffer()],
		new PublicKey(MARKETPLACE_PROGRAM_ID),
	)

	return {
		offer,
		escrow: escrow.toBase58(),
		buyer: eventData.buyer as string,
		asset: eventData.asset as string,
		price: priceValue,
		expiresAt: expiresAtValue,
		createdAt: createdAtValue,
	}
}
