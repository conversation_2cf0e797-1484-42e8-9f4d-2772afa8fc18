import type { PublicKey } from '@solana/web3.js'

/**
 * Event emitted when an offer is cancelled
 * Based on the OfferCancelledEvent in fusion_marketplace.json
 */
export type OfferCancelledEvent = {
	/** The offer account public key */
	offer: PublicKey
	/** The buyer who cancelled the offer */
	buyer: PublicKey
	/** The asset the offer was for */
	asset: PublicKey
	/** Timestamp when the offer was cancelled */
	cancelled_at: bigint
}

/**
 * Processed version of OfferCancelledEvent with normalized types
 */
export type ProcessedOfferCancelledEvent = {
	/** The offer account public key (base58 string) */
	offer: string
	/** The buyer who cancelled the offer (base58 string) */
	buyer: string
	/** The asset the offer was for (base58 string) */
	asset: string
	/** Timestamp when the offer was cancelled (BigInt) */
	cancelledAt: bigint
}

/**
 * Process an OfferCancelledEvent from the blockchain
 * @param eventData Raw event data from the blockchain
 * @returns Processed event data with normalized types
 */
export function processOfferCancelledEvent(
	eventData: Record<string, unknown>,
): ProcessedOfferCancelledEvent {
	// Handle cancelled_at with fallback to cancelledAt
	const cancelledAtValue = (eventData.cancelled_at ||
		eventData.cancelledAt) as bigint

	return {
		offer: eventData.offer as string,
		buyer: eventData.buyer as string,
		asset: eventData.asset as string,
		cancelledAt: cancelledAtValue,
	}
}
