import { PublicKey } from '@solana/web3.js'
import { FUSION_MARKETPLACE_PROGRAM_ID } from '@/lib/generated'

const MARKETPLACE_PROGRAM_ID = FUSION_MARKETPLACE_PROGRAM_ID
/**
 * Event emitted when a new listing is created
 * Based on the ListingCreatedEvent in fusion_marketplace.json
 */
export type ListingCreatedEvent = {
	/** Seller of the NFT */
	seller: PublicKey
	/** NFT asset being sold */
	asset: PublicKey
	/** Listing account */
	listing: PublicKey
	/** Price in lamports */
	price: bigint
	/** Listing type (fixed price, auction) */
	listing_type: object
	/** When the listing was created */
	created_at: bigint
	/** When the listing ends */
	ends_at: bigint
}

/**
 * Processed version of ListingCreatedEvent with normalized types
 */
export type ProcessedListingCreatedEvent = {
	/** Seller of the NFT (base58 string) */
	seller: string
	/** NFT asset being sold (base58 string) */
	asset: string
	/** Listing account (base58 string) */
	listing: string
	/** Escrow account (base58 string) */
	escrow: string
	/** Auction account (base58 string) */
	auction: string
	/** Price in lamports (BigInt) */
	price: bigint
	/** Listing type (string: "FixedPrice" or "Auction") */
	listingType: string
	/** When the listing was created (BigInt) */
	createdAt: bigint
	/** When the listing ends (BigInt) */
	endsAt: bigint
}

/**
 * Process a ListingCreatedEvent from the blockchain
 * @param eventData Raw event data from the blockchain
 * @returns Processed event data with normalized types
 */
export function processListingCreatedEvent(
	eventData: Record<string, unknown>,
): ProcessedListingCreatedEvent {
	// Extract listing type
	const listingTypeObj = eventData.listing_type as object
	const listingType = Object.keys(listingTypeObj)[0]
	const listing = eventData.listing as string
	const [escrow] = PublicKey.findProgramAddressSync(
		[Buffer.from('escrow-payment'), new PublicKey(listing).toBuffer()],
		new PublicKey(MARKETPLACE_PROGRAM_ID),
	)

	const [auction] = PublicKey.findProgramAddressSync(
		[Buffer.from('auction'), new PublicKey(listing).toBuffer()],
		new PublicKey(MARKETPLACE_PROGRAM_ID),
	)

	// Convert price to BigInt
	const priceValue = eventData.price as bigint

	// Handle created_at with fallback to createdAt
	const createdAtValue = eventData.created_at as bigint

	// Handle ends_at with fallback to endsAt
	const endsAtValue = eventData.ends_at as bigint

	return {
		seller: eventData.seller as string,
		asset: eventData.asset as string,
		listing,
		escrow: escrow.toBase58(),
		auction: auction.toBase58(),
		price: priceValue,
		listingType: listingType,
		createdAt: createdAtValue,
		endsAt: endsAtValue,
	}
}
