import type { PublicKey } from '@solana/web3.js'

/**
 * Common types used across event definitions
 * Based on the types defined in fusion_marketplace.json
 */

/**
 * Listing type enum
 * Represents the different types of listings available in the marketplace
 */
export type ListingType =
	| { FixedPrice: Record<string, never> }
	| { Auction: { fields: [PublicKey] } }

/**
 * Auction type enum
 * Represents the different types of auctions available in the marketplace
 */
export enum AuctionType {
	English = 0,
}
