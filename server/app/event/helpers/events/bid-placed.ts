import type { PublicKey } from '@solana/web3.js'

/**
 * Event emitted when a bid is placed on an auction
 * Based on the BidPlacedEvent in fusion_marketplace.json
 */
export type BidPlacedEvent = {
	/** Listing account */
	listing: string
	/** Auction account */
	auction: string
	/** Bidder who placed the bid */
	bidder: string
	/** Bid amount in lamports */
	bid_amount: bigint
	/** When the bid was placed */
	timestamp: bigint
}

/**
 * Processed version of BidPlacedEvent with normalized types
 */
export type ProcessedBidPlacedEvent = {
	/** Listing account (base58 string) */
	listing: string
	/** Auction account (base58 string) */
	auction: string
	/** Bidder who placed the bid (base58 string) */
	bidder: string
	/** Bid amount in lamports (BigInt) */
	bidAmount: bigint
	/** When the bid was placed (BigInt) */
	timestamp: bigint
}

/**
 * Process a BidPlacedEvent from the blockchain
 * @param eventData Raw event data from the blockchain
 * @returns Processed event data with normalized types
 */
export function processBidPlacedEvent(
	eventData: Record<string, unknown>,
): ProcessedBidPlacedEvent {
	// Handle bid_amount with fallback to bidAmount
	const bidAmountValue = eventData.bid_amount as bigint

	// Convert timestamp to BigInt
	const timestampValue = eventData.timestamp as bigint

	return {
		listing: eventData.listing as string,
		auction: eventData.auction as string,
		bidder: eventData.bidder as string,
		bidAmount: bidAmountValue,
		timestamp: timestampValue,
	}
}
