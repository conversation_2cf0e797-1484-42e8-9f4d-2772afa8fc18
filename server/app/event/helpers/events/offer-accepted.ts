import type { PublicKey } from '@solana/web3.js'

/**
 * Event emitted when an offer is accepted
 * Based on the OfferAcceptedEvent in fusion_marketplace.json
 */
export type OfferAcceptedEvent = {
	/** The offer account public key */
	offer: PublicKey
	/** The buyer who made the offer */
	buyer: PublicKey
	/** The seller who accepted the offer */
	seller: PublicKey
	/** The asset being sold */
	asset: PublicKey
	/** The price in lamports */
	price: bigint
	/** Protocol fee collected */
	protocol_fee: bigint
	/** Royalty fee distributed */
	royaltyPayment: bigint
	/** Timestamp when the offer was accepted */
	accepted_at: bigint
}

/**
 * Processed version of OfferAcceptedEvent with normalized types
 */
export type ProcessedOfferAcceptedEvent = {
	/** The offer account public key (base58 string) */
	offer: string
	/** The buyer who made the offer (base58 string) */
	buyer: string
	/** The seller who accepted the offer (base58 string) */
	seller: string
	/** The asset being sold (base58 string) */
	asset: string
	/** The price in lamports (BigInt) */
	price: bigint
	/** Protocol fee collected (BigInt) */
	protocolFee: bigint
	/** Royalty fee distributed */
	royaltyPayment: bigint
	/** Timestamp when the offer was accepted (BigInt) */
	acceptedAt: bigint
}

/**
 * Process an OfferAcceptedEvent from the blockchain
 * @param eventData Raw event data from the blockchain
 * @returns Processed event data with normalized types
 */
export function processOfferAcceptedEvent(
	eventData: Record<string, unknown>,
): ProcessedOfferAcceptedEvent {
	// Convert price to BigInt
	const priceValue = eventData.price as bigint

	// Handle protocol_fee with fallback to protocolFee
	const protocolFeeValue = (eventData.protocol_fee ||
		eventData.protocolFee) as bigint

	// Handle royaltyPayment with fallback to royaltyPayment
	const royaltyPaymentValue = (eventData.royalty_payment ||
		eventData.royaltyPayment) as bigint

	// Handle accepted_at with fallback to acceptedAt
	const acceptedAtValue = (eventData.accepted_at ||
		eventData.acceptedAt) as bigint

	return {
		offer: eventData.offer as string,
		buyer: eventData.buyer as string,
		seller: eventData.seller as string,
		asset: eventData.asset as string,
		price: priceValue,
		protocolFee: protocolFeeValue,
		royaltyPayment: royaltyPaymentValue,
		acceptedAt: acceptedAtValue,
	}
}
