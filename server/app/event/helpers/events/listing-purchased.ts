import type { PublicKey } from '@solana/web3.js'

/**
 * Event emitted when a listing is purchased
 * Based on the ListingPurchasedEvent in fusion_marketplace.json
 */
export type ListingPurchasedEvent = {
	/** Seller of the NFT */
	seller: PublicKey
	/** Buyer of the NFT */
	buyer: PublicKey
	/** NFT asset being sold */
	asset: PublicKey
	/** Listing account */
	listing: PublicKey
	/** Price paid in lamports */
	price: bigint
	/** Protocol fee amount */
	protocol_fee: bigint
	/** Royalty fee distributed */
	royaltyPayment: bigint
	/** When the purchase occurred */
	purchased_at: bigint
}

/**
 * Processed version of ListingPurchasedEvent with normalized types
 */
export type ProcessedListingPurchasedEvent = {
	/** Seller of the NFT (base58 string) */
	seller: string
	/** Buyer of the NFT (base58 string) */
	buyer: string
	/** NFT asset being sold (base58 string) */
	asset: string
	/** Listing account (base58 string) */
	listing: string
	/** Price paid in lamports (BigInt) */
	price: bigint
	/** Protocol fee amount (BigInt) */
	protocolFee: bigint
	/** Royalty fee distributed */
	royaltyPayment: bigint
	/** When the purchase occurred (BigInt) */
	purchasedAt: bigint
}

/**
 * Process a ListingPurchasedEvent from the blockchain
 * @param eventData Raw event data from the blockchain
 * @returns Processed event data with normalized types
 */
export function processListingPurchasedEvent(
	eventData: Record<string, unknown>,
): ProcessedListingPurchasedEvent {
	// Convert price to BigInt
	const priceValue = eventData.price as bigint

	// Handle protocol_fee with fallback to protocolFee
	const protocolFeeValue = (eventData.protocol_fee ||
		eventData.protocolFee) as bigint

	// Handle royaltyPayment with fallback to royaltyPayment
	const royaltyPaymentValue = (eventData.royalty_payment ||
		eventData.royaltyPayment) as bigint

	// Handle purchased_at with fallback to purchasedAt
	const purchasedAtValue = (eventData.purchased_at ||
		eventData.purchasedAt) as bigint

	return {
		seller: eventData.seller as string,
		buyer: eventData.buyer as string,
		asset: eventData.asset as string,
		listing: eventData.listing as string,
		price: priceValue,
		protocolFee: protocolFeeValue,
		royaltyPayment: royaltyPaymentValue,
		purchasedAt: purchasedAtValue,
	}
}
