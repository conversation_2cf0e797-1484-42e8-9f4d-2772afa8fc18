import type { PublicKey } from '@solana/web3.js'

/**
 * Event emitted when a listing is cancelled
 * Based on the ListingCancelledEvent in fusion_marketplace.json
 */
export type ListingCancelledEvent = {
	/** Seller of the NFT */
	seller: PublicKey
	/** NFT asset being sold */
	asset: PublicKey
	/** Listing account */
	listing: PublicKey
	/** When the listing was cancelled */
	cancelledAt: bigint
}

/**
 * Processed version of ListingCancelledEvent with normalized types
 */
export type ProcessedListingCancelledEvent = {
	/** Seller of the NFT (base58 string) */
	seller: string
	/** NFT asset being sold (base58 string) */
	asset: string
	/** Listing account (base58 string) */
	listing: string
	/** When the listing was cancelled (BigInt) */
	cancelledAt: bigint
}

/**
 * Process a ListingCancelledEvent from the blockchain
 * @param eventData Raw event data from the blockchain
 * @returns Processed event data with normalized types
 */
export function processListingCancelledEvent(
	eventData: Record<string, unknown>,
): ProcessedListingCancelledEvent {
	// Handle cancelled_at with fallback to cancelledAt
	const cancelledAtValue = (eventData.cancelled_at ||
		eventData.cancelledAt) as bigint

	return {
		seller: eventData.seller as string,
		asset: eventData.asset as string,
		listing: eventData.listing as string,
		cancelledAt: cancelledAtValue,
	}
}
