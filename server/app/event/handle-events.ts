import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EventParser, web3 } from '@coral-xyz/anchor'
import idl from '@/lib/contracts/fusion_marketplace.json' with { type: 'json' }
import { prisma } from '@/server/lib/prismaClient'
import type { TransactionData } from './event-types'
import {
	processBidPlacedEvent,
	processListingCancelledEvent,
	processListingCreatedEvent,
	processListingPurchasedEvent,
	processOfferAcceptedEvent,
	processOfferCancelledEvent,
	processOfferCreatedEvent,
} from './helpers/events'
import { processEventData } from './helpers/process-data'
import { eventHandlers, eventToHandlerName } from './types'

/**
 * Process a specific event based on its type
 * @param eventName Name of the event
 * @param eventData Event data
 * @param txSignature Transaction signature
 */
async function processEvent(
	eventName: string,
	rawEventData: Record<string, unknown>,
	txSignature: string,
): Promise<void> {
	try {
		console.info(`Starting to process event: ${eventName}`, {
			data: { txSignature },
		})

		// Process the event data to normalize all types
		const eventData = processEventData(rawEventData)

		// Now you can use the processed data for your business logic
		switch (eventName) {
			case 'BidPlaced': {
				const processedData = processBidPlacedEvent(eventData)
				console.log('🚀 ~ processedData:', processedData)

				try {
					// Find the listing by its public key
					const listing = await prisma.listing.findFirst({
						where: { listKey: processedData.listing, status: 'ACTIVE' },
						select: { id: true, nftId: true, auctionKey: true, sellerId: true },
					})

					if (!listing) {
						console.error(
							`Listing with public key ${processedData.listing} not found`,
						)
						break
					}

					// Find the user (bidder) by their public key
					const bidder = await prisma.user.findUnique({
						where: { publicKey: processedData.bidder },
						select: { id: true },
					})

					if (!bidder) {
						console.error(
							`User with public key ${processedData.bidder} not found`,
						)
						break
					}

					// Create the bid record
					const bid = await prisma.bid.create({
						data: {
							amount: Number(processedData.bidAmount) / 1_000_000_000,
							createdAt: new Date(Number(processedData.timestamp) * 1000),
							updatedAt: new Date(),
							listingId: listing.id,
							bidderId: bidder.id,
						},
					})

					const nft = await prisma.nFT.findUnique({
						where: { id: listing.nftId },
						select: { lastBiddingPrice: true, collection: true },
					})

					// Update the NFT's lastBiddingPrice
					await prisma.nFT.update({
						where: { id: listing.nftId },
						data: {
							lastBiddingPrice: Number(processedData.bidAmount) / 1_000_000_000,
						},
					})

					// Add activity log for the bid placement
					await prisma.activityLog.create({
						data: {
							type: 'NFT_BID_PLACED',
							transactionHash: txSignature,
							listingId: listing.id,
							fromUserId: bidder.id,
							toUserId: listing.sellerId,
							nftId: listing.nftId,
							userId: bidder.id,
							bidId: bid.id,
							collectionId: nft?.collection?.id,
							amount: Number(processedData.bidAmount) / 1_000_000_000,
							data: {
								amount: Number(processedData.bidAmount) / 1_000_000_000,
							},
						},
					})

					// update the listing's highest bidder and highest bid amount
					await prisma.listing.update({
						where: { id: listing.id },
						data: {
							lastBidId: bid.id,
							price: Number(processedData.bidAmount) / 1_000_000_000,
							updatedAt: new Date(),
						},
					})

					console.info(
						`Created bid for listing: ${processedData.listing} by bidder: ${processedData.bidder}`,
					)
				} catch (error) {
					console.error('Error creating bid:', error, {
						data: { processedData },
					})
				}
				break
			}
			case 'ListingCreated': {
				const processedData = processListingCreatedEvent(eventData)
				console.log('🚀 ~ processedData:', processedData)

				try {
					// First, find the NFT by its public key
					const nft = await prisma.nFT.findUnique({
						where: { publicKey: processedData.asset },
						select: { id: true, collection: true },
					})

					if (!nft) {
						console.error(
							`NFT with public key ${processedData.asset} not found`,
						)
						break
					}

					// Find the user by their public key
					const user = await prisma.user.findUnique({
						where: { publicKey: processedData.seller },
						select: { id: true },
					})

					if (!user) {
						console.error(
							`User with public key ${processedData.seller} not found`,
						)
						break
					}

					// Determine listing type from the processed data
					const listingType =
						Number(processedData.listingType) === 0 ? 'FIXED_PRICE' : 'AUCTION'

					// Create the listing with all required fields
					await prisma.listing.create({
						data: {
							listKey: processedData.listing,
							escrow: processedData.escrow,
							auctionKey: processedData.auction,
							price: Number(processedData.price) / 1_000_000_000,
							currency: 'SOL',
							status: 'ACTIVE',
							listingType,
							startTime: new Date(Number(processedData.createdAt) * 1000),
							endTime: new Date(Number(processedData.endsAt) * 1000),
							createdAt: new Date(Number(processedData.createdAt) * 1000),
							updatedAt: new Date(),
							nftId: nft.id,
							collectionId: nft.collection?.id,
							sellerId: user.id,
							activityLogs: {
								create: {
									type: 'NFT_LISTED',
									transactionHash: txSignature,
									userId: user.id,
									nftId: nft.id,
									collectionId: nft.collection?.id,
									fromUserId: user.id,
									data: {
										amount: Number(processedData.price) / 1_000_000_000,
									},
								},
							},
							// Add auction-specific fields if it's an auction
							...(listingType === 'AUCTION' && {
								minBidIncrement: null, // Set appropriate default or from event
								reservePrice: null, // Set appropriate default or from event
							}),
						},
					})

					console.info(
						`Created listing: ${processedData.listing} for NFT: ${processedData.asset}`,
					)
				} catch (error) {
					console.error('Error creating listing:', error, {
						data: { processedData },
					})
				}
				break
			}
			case 'ListingPurchased': {
				const processedData = processListingPurchasedEvent(eventData)
				console.log('🚀 ~ processedData:', processedData)
				try {
					// Find the listing by its public key
					const listing = await prisma.listing.findFirst({
						where: { listKey: processedData.listing, status: 'ACTIVE' },
						select: {
							id: true,
							nftId: true,
							sellerId: true,
							listingType: true,
							lastBid: true,
							collectionId: true,
							nft: {
								select: {
									creatorId: true,
								},
							},
						},
					})

					if (!listing) {
						console.error(
							`Listing with public key ${processedData.listing} not found for purchase`,
						)
						break
					}

					let bid: {
						id: string
					} | null = null
					if (listing.lastBid) {
						bid = await prisma.bid.findFirst({
							where: { id: listing.lastBid.id },
							select: { id: true },
						})
					}

					// Find the buyer by their public key
					const buyer = await prisma.user.findUnique({
						where: { publicKey: processedData.buyer },
						select: { id: true },
					})

					if (!buyer) {
						console.error(
							`Buyer with public key ${processedData.buyer} not found`,
						)
						break
					}

					// Update the listing status to SOLD
					await prisma.listing.update({
						where: { id: listing.id },
						data: {
							status: 'SOLD',
							updatedAt: new Date(Number(processedData.purchasedAt)),
						},
					})

					// Create an order record for the purchase
					await prisma.order.create({
						data: {
							publicKey: `${processedData.asset}`,
							price: Number(processedData.price) / 1_000_000_000,
							currency: 'SOL',
							status: 'COMPLETED',
							createdAt: new Date(Number(processedData.purchasedAt) * 1000),
							updatedAt: new Date(),
							listingId: listing.id,
							buyerId: buyer.id,
							sellerId: listing.sellerId,
							nftId: listing.nftId,
							collectionId: listing.collectionId,
						},
					})

					await prisma.listing.update({
						where: { id: listing.id },
						data: {
							status: 'SOLD',
							// Update the lastBidId field if it exists in the schema
							...(bid?.id ? { lastBidId: bid.id } : {}),
							price: Number(processedData.price) / 1_000_000_000,
							updatedAt: new Date(Number(processedData.purchasedAt) * 1000),
						},
					})

					// Update the NFT ownership
					await prisma.nFT.update({
						where: { id: listing.nftId },
						data: {
							ownerId: buyer.id,
							updatedAt: new Date(),
						},
					})

					// Add activity log for the purchase
					await prisma.activityLog.create({
						data: {
							type: 'NFT_TRANSFERRED',
							transactionHash: txSignature,
							listingId: listing.id,
							nftId: listing.nftId,
							userId: buyer.id,
							fromUserId: listing.sellerId,
							toUserId: buyer.id,
							amount: Number(processedData.price) / 1_000_000_000,
							collectionId: listing.collectionId,
							data: {
								buyerId: buyer.id,
								sellerId: listing.sellerId,
								amount: Number(processedData.price) / 1_000_000_000,
							},
						},
					})

					if (processedData.royaltyPayment) {
						await prisma.activityLog.create({
							data: {
								type: 'ROYALTY_RECEIVED',
								transactionHash: txSignature,
								listingId: listing.id,
								nftId: listing.nftId,
								userId: listing.nft.creatorId,
								amount: Number(processedData.royaltyPayment) / 1_000_000_000,
								collectionId: listing.collectionId,
								data: {
									amount: Number(processedData.royaltyPayment) / 1_000_000_000,
								},
							},
						})
					}

					console.info(
						`Processed purchase of listing: ${processedData.listing} by buyer: ${processedData.buyer}`,
					)
				} catch (error) {
					console.error('Error processing purchase:', error, {
						data: { processedData },
					})
				}
				break
			}
			case 'ListingCancelled': {
				const processedData = processListingCancelledEvent(eventData)
				console.log('🚀 ~ processedData:', processedData)

				try {
					const listing = await prisma.listing.findFirst({
						where: {
							listKey: processedData.listing,
							status: 'ACTIVE',
						},
						select: {
							id: true,
							nftId: true,
							userId: true,
							price: true,
							collectionId: true,
						},
					})

					if (!listing) {
						console.error(
							`Listing with public key ${processedData.listing} not found for cancellation`,
						)
						break
					}

					// Find and update the listing status to CANCELLED
					const updatedListing = await prisma.listing.updateMany({
						where: { listKey: processedData.listing },
						data: {
							status: 'CANCELLED',
							updatedAt: new Date(Number(processedData.cancelledAt) * 1000),
						},
					})

					if (updatedListing.count === 0) {
						console.error(
							`Listing with public key ${processedData.listing} not found for cancellation`,
						)
						break
					}

					// Add activity log for cancellation
					await prisma.activityLog.create({
						data: {
							type: 'NFT_LISTING_CANCELLED',
							transactionHash: txSignature,
							listingId: listing.id,
							nftId: listing.nftId,
							userId: listing.userId,
							collectionId: listing.collectionId,
							data: {
								amount: Number(listing.price) / 1_000_000_000,
							},
						},
					})

					console.info(`Cancelled listing: ${processedData.listing}`)
				} catch (error) {
					console.error('Error cancelling listing:', error, {
						data: { processedData },
					})
				}
				break
			}
			case 'OfferCreated': {
				const processedData = processOfferCreatedEvent(eventData)
				console.log('🚀 ~ processedData:', processedData)
				try {
					// Find the NFT by its public key
					const nft = await prisma.nFT.findUnique({
						where: { publicKey: processedData.asset },
						select: { id: true, ownerId: true, collectionId: true },
					})

					if (!nft) {
						console.error(
							`NFT with public key ${processedData.asset} not found`,
						)
						break
					}

					// Find the buyer by their public key
					const buyer = await prisma.user.findUnique({
						where: { publicKey: processedData.buyer },
						select: { id: true },
					})

					if (!buyer) {
						console.error(
							`Buyer with public key ${processedData.buyer} not found`,
						)
						break
					}

					// Create the offer record
					await prisma.offer.create({
						data: {
							publicKey: processedData.offer,
							escrow: processedData.escrow,
							price: Number(processedData.price) / 1_000_000_000,
							status: 'PENDING',
							expiresAt: new Date(Number(processedData.expiresAt) * 1000),
							createdAt: new Date(Number(processedData.createdAt) * 1000),
							updatedAt: new Date(),
							collectionId: nft.collectionId,
							nftId: nft.id,
							buyerId: buyer.id,
							sellerId: nft.ownerId as string,
						},
					})

					// Add activity log for the offer creation
					await prisma.activityLog.create({
						data: {
							type: 'NFT_OFFER_CREATED',
							transactionHash: txSignature,
							nftId: nft.id,
							userId: buyer.id,
							fromUserId: buyer.id,
							toUserId: nft.ownerId as string,
							collectionId: nft.collectionId,
							data: {
								amount: Number(processedData.price) / 1_000_000_000,
							},
						},
					})

					await prisma.nFT.update({
						where: { id: nft.id },
						data: {
							lastBiddingPrice: Number(processedData.price) / 1_000_000_000,
							updatedAt: new Date(),
						},
					})

					console.info(
						`Created offer: ${processedData.offer} for NFT: ${processedData.asset}`,
					)
				} catch (error) {
					console.error('Error creating offer:', error, {
						data: { processedData },
					})
				}
				break
			}
			case 'OfferAccepted': {
				const processedData = processOfferAcceptedEvent(eventData)
				console.log('🚀 ~ processedData:', processedData)
				try {
					// Find the offer by its public key
					const offer = await prisma.offer.findFirst({
						where: { publicKey: processedData.offer, status: 'PENDING' },
						select: { id: true, nft: true, buyerId: true, sellerId: true },
					})

					if (!offer) {
						console.error(
							`Offer with public key ${processedData.offer} not found`,
						)
						break
					}

					// Update the offer status to ACCEPTED
					await prisma.offer.update({
						where: { id: offer.id },
						data: {
							status: 'ACCEPTED',
							updatedAt: new Date(Number(processedData.acceptedAt) * 1000),
						},
					})

					// Create an order record for the accepted offer
					await prisma.order.create({
						data: {
							publicKey: `${processedData.asset}`,
							price: Number(processedData.price) / 1_000_000_000,
							currency: 'SOL',
							status: 'COMPLETED',
							createdAt: new Date(Number(processedData.acceptedAt) * 1000),
							updatedAt: new Date(),
							offerId: offer.id,
							buyerId: offer.buyerId,
							sellerId: offer.sellerId,
							nftId: offer.nft.id,
							collectionId: offer.nft.collectionId,
						},
					})

					// Update the NFT ownership
					await prisma.nFT.update({
						where: { id: offer.nft.id },
						data: {
							ownerId: offer.buyerId,
							updatedAt: new Date(),
						},
					})

					// Add activity log for the offer acceptance
					await prisma.activityLog.create({
						data: {
							type: 'NFT_OFFER_ACCEPTED',
							transactionHash: txSignature,
							nftId: offer.nft.id,
							fromUserId: offer.sellerId,
							toUserId: offer.buyerId,
							collectionId: offer.nft.collectionId,
							userId: offer.buyerId,
							data: {
								amount: Number(processedData.price) / 1_000_000_000,
							},
						},
					})

					if (processedData.royaltyPayment) {
						await prisma.activityLog.create({
							data: {
								type: 'ROYALTY_RECEIVED',
								transactionHash: txSignature,
								nftId: offer.nft.id,
								collectionId: offer.nft.collectionId,
								userId: offer.nft.creatorId,
								fromUserId: offer.sellerId,
								toUserId: offer.buyerId,
								amount: Number(processedData.royaltyPayment) / 1_000_000_000,
								data: {
									amount: Number(processedData.royaltyPayment) / 1_000_000_000,
								},
							},
						})
					}

					console.info(
						`Accepted offer: ${processedData.offer} for asset: ${processedData.asset}`,
					)
				} catch (error) {
					console.error('Error accepting offer:', error, {
						data: { processedData },
					})
				}
				break
			}
			case 'OfferCancelled': {
				const processedData = processOfferCancelledEvent(eventData)
				console.log('🚀 ~ processedData:', processedData)
				try {
					// Find the offer by its public key
					const offer = await prisma.offer.findFirst({
						where: { publicKey: processedData.offer, status: 'PENDING' },
						select: { id: true, nft: true, buyerId: true, price: true },
					})

					if (!offer) {
						console.error(
							`Offer with public key ${processedData.offer} not found for cancellation`,
						)
						break
					}

					// Find and update the offer status to CANCELLED
					const updatedOffer = await prisma.offer.updateMany({
						where: { publicKey: processedData.offer },
						data: {
							status: 'CANCELLED',
							updatedAt: new Date(Number(processedData.cancelledAt) * 1000),
						},
					})

					if (updatedOffer.count === 0) {
						console.error(
							`Offer with public key ${processedData.offer} not found for cancellation`,
						)
						break
					}

					// Add activity log for cancellation
					await prisma.activityLog.create({
						data: {
							type: 'NFT_OFFER_CANCELLED',
							transactionHash: txSignature,
							nftId: offer.nft.id,
							fromUserId: offer.buyerId,
							toUserId: offer.nft.ownerId as string,
							userId: offer.buyerId,
							collectionId: offer.nft.collectionId,
							data: {
								amount: Number(offer.price) / 1_000_000_000,
							},
						},
					})

					console.info(
						`Cancelled offer: ${processedData.offer} for asset: ${processedData.asset}`,
					)
				} catch (error) {
					console.error('Error cancelling offer:', error, {
						data: { processedData },
					})
				}
				break
			}
			default:
				console.warn(`No handler implementation for event: ${eventName}`, {
					data: { eventData },
				})
		}

		console.info(`Completed processing event: ${eventName}`, {
			data: { txSignature },
		})
	} catch (error) {
		console.error(`Error processing ${eventName} event`, {
			error,
			data: { txSignature, rawEventData },
		})
		// Don't throw here to prevent one event failure from stopping processing of other events
	}
}

/**
 * Handle blockchain events from webhook payload
 * @param payload Array of transactions containing events
 */
export async function handleRequest(payload: TransactionData[]): Promise<void> {
	if (!payload || !Array.isArray(payload) || payload.length === 0) {
		console.warn('Empty or invalid payload received')
		return
	}

	console.info(`Processing ${payload.length} transactions`)

	const programId = new web3.PublicKey(
		'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8',
	)

	try {
		// biome-ignore lint/suspicious/noExplicitAny: IDL type requires any
		const coder = new BorshCoder(idl as any)
		const eventParser = new EventParser(programId, coder)

		// Process each transaction in the payload
		for (const tx of payload) {
			try {
				if (!tx.meta?.logMessages) {
					console.warn('Transaction missing log messages, skipping', {
						data: { tx },
					})
					continue
				}

				const txSignature = tx.transaction?.signatures?.[0] || 'unknown'
				console.info(`Processing transaction: ${txSignature}`, {
					data: { logMessagesCount: tx.meta.logMessages.length },
				})
				const logs = tx.meta.logMessages
				const eventsArray = eventParser.parseLogs(logs)
				console.info(`Event List: ${eventsArray}`)
				for (const event of eventsArray) {
					let eventName: string | undefined

					// Check if the event name is a handler name (ends with "Event")
					if (event.name.endsWith('Event')) {
						// Get the corresponding event name from the handler name
						eventName = eventHandlers[event.name]
					}
					// If not a handler name or no mapping found, use the event name directly
					if (!eventName) {
						// Check if we have a handler for this event name
						if (!eventToHandlerName[event.name]) {
							console.warn(`No handler registered for event: ${event.name}`, {
								data: event,
							})
							continue
						}
						eventName = event.name
					}

					// Process the event with the determined event name
					console.info(
						`Processing event: ${eventName} from source: ${event.name}`,
						{
							data: { txSignature },
						},
					)
					await processEvent(eventName, event.data, txSignature)
				}
			} catch (txError) {
				// Log the error but continue processing other transactions
				const txSignature = tx.transaction?.signatures?.[0] || 'unknown'
				console.error('Error processing transaction', {
					error: txError,
					data: { txSignature },
				})
			}
		}

		console.info('Finished processing all transactions')
	} catch (err) {
		console.error('Fatal error processing events', { error: err })
		// Handle the error in a appropriate way for your application
		throw err
	}
}
