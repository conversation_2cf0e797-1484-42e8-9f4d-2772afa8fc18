export const fixedPriceListing = [
	{
		blockTime: **********,
		indexWithinBlock: 12,
		meta: {
			err: null,
			fee: 80000,
			innerInstructions: [
				{
					index: 2,
					instructions: [
						{
							accounts: [0, 2],
							data: '11116sZiipZkWjEoX3nSofsnymWgVK6Va4HJZCQp3Ne1YzHYEX8WTP8KgRBCEfpZc6cuV6',
							programIdIndex: 8,
						},
						{
							accounts: [1, 7, 0, 0, 8, 7],
							data: '4tvzokWAsHE16KrYwLViZ33t3iboT9zvVshfLB78NbVGCjyLNm',
							programIdIndex: 7,
						},
						{
							accounts: [0, 1],
							data: '3Bxs4Bg4P1TDCuTM',
							programIdIndex: 8,
						},
						{
							accounts: [1, 7, 0, 0, 8, 7],
							data: 'tPT38Rhwvo6gjt9nBAsxvYS7ugwLQdg29qxg3UzGAtNQdnuF',
							programIdIndex: 7,
						},
						{
							accounts: [0, 1],
							data: '3Bxs43dNkNCxDyUK',
							programIdIndex: 8,
						},
					],
				},
			],
			loadedAddresses: {
				readonly: [],
				writable: [],
			},
			logMessages: [
				'Program ComputeBudget111111111111111111111111111111 invoke [1]',
				'Program ComputeBudget111111111111111111111111111111 success',
				'Program ComputeBudget111111111111111111111111111111 invoke [1]',
				'Program ComputeBudget111111111111111111111111111111 success',
				'Program CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8 invoke [1]',
				'Program log: Instruction: CreateFixedPriceListing',
				'Program 11111111111111111111111111111111 invoke [2]',
				'Program 11111111111111111111111111111111 success',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d invoke [2]',
				'Program log: Instruction: AddPlugin',
				'Program log: programs/mpl-core/src/state/asset.rs:177:Approve',
				'Program 11111111111111111111111111111111 invoke [3]',
				'Program 11111111111111111111111111111111 success',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d consumed 14187 of 175360 compute units',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d success',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d invoke [2]',
				'Program log: Instruction: AddPlugin',
				'Program log: programs/mpl-core/src/state/asset.rs:177:Approve',
				'Program 11111111111111111111111111111111 invoke [3]',
				'Program 11111111111111111111111111111111 success',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d consumed 15197 of 154619 compute units',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d success',
				'Program data: SZOpknnNdgMT0/KCkCuOlEN1369+/p8mEiTmXQo0H9TM0k8hRnOn3xh8JjYLeY5Fyj+JrApi6hVuJh6r98GhtxcE0CnEsKZbeux2QgV801DPTDIGfpOwnAc5OSykmaqvqx6N/bbTml//yZo7AAAAAAARtA9oAAAAAJHuGGgAAAAA',
				'Program CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8 consumed 61976 of 199700 compute units',
				'Program CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8 success',
			],
			postBalances: [
				**********, 4534560, 2101920, 1, 1141440, 1552080, 0, 1141440, 1,
				1009200, 1169280,
			],
			postTokenBalances: [],
			preBalances: [
				**********, 3929040, 0, 1, 1141440, 1552080, 0, 1141440, 1, 1009200,
				1169280,
			],
			preTokenBalances: [],
			rewards: [],
		},
		slot: *********,
		transaction: {
			message: {
				accountKeys: [
					'2LQCE2gFhP8Jw5hte9fEub26f9yTZSfwXHUJzRRnTTuG',
					'2eaap9zizzkN6pz5t3HzyaGrmjGmacrr8xqHS3gh2738',
					'9GqsJVigPYe17ifdt1vEkuLDwmXYdfGyHLouZuq7dBup',
					'ComputeBudget111111111111111111111111111111',
					'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8',
					'GZHHYiR9ooDwd6U3mVdfqU8TMDXUhHHQEfxm4EvKmcgA',
					'52qKSeyzgRpqzd1Yd1VVxdYpRLjG4YNZwHiynwYVwhG5',
					'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d',
					'11111111111111111111111111111111',
					'SysvarRent111111111111111111111111111111111',
					'SysvarC1ock11111111111111111111111111111111',
				],
				addressTableLookups: [],
				header: {
					numReadonlySignedAccounts: 0,
					numReadonlyUnsignedAccounts: 8,
					numRequiredSignatures: 1,
				},
				instructions: [
					{
						accounts: [],
						data: '3qi1MbrtkR83',
						programIdIndex: 3,
					},
					{
						accounts: [],
						data: 'Fj2Eoy',
						programIdIndex: 3,
					},
					{
						accounts: [0, 1, 2, 5, 6, 7, 8, 9, 10],
						data: 'JQuuLsVPnBBgYdfNgtdrbScsZVYSn6Z3M',
						programIdIndex: 4,
					},
				],
				recentBlockhash: '8PFKC13RqptHEW6qpyp3JpiZBYJPTgaJh7B7ibf7nq3i',
			},
			signatures: [
				'4XipZhFUJ1QKRxW8U22hmj2vG3HinpiwstFcrNeWkfcH5JdAMWKwMF2xN3cXQxcHoECJUh3oHc4qgRgnbsBsWTE9',
			],
		},
		version: 0,
	},
]
