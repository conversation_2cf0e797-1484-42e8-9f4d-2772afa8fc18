export const cancelFixedPriceListing = [
	{
		blockTime: **********,
		indexWithinBlock: 11,
		meta: {
			err: null,
			fee: 80000,
			innerInstructions: [
				{
					index: 2,
					instructions: [
						{
							accounts: [1, 7, 0, 6, 8, 7],
							data: '31y1',
							programIdIndex: 7,
						},
						{
							accounts: [1, 7, 0, 0, 8, 7],
							data: 'Jg',
							programIdIndex: 7,
						},
						{
							accounts: [1, 7, 0, 0, 8, 7],
							data: 'Ji',
							programIdIndex: 7,
						},
					],
				},
			],
			loadedAddresses: {
				readonly: [],
				writable: [],
			},
			logMessages: [
				'Program ComputeBudget111111111111111111111111111111 invoke [1]',
				'Program ComputeBudget111111111111111111111111111111 success',
				'Program ComputeBudget111111111111111111111111111111 invoke [1]',
				'Program ComputeBudget111111111111111111111111111111 success',
				'Program CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8 invoke [1]',
				'Program log: Instruction: CancelFixedPriceListing',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d invoke [2]',
				'Program log: Instruction: UpdatePlugin',
				'Program log: programs/mpl-core/src/plugins/lifecycle.rs:343:Base:Approved',
				'Program log: programs/mpl-core/src/plugins/lifecycle.rs:733:Approve',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d consumed 16202 of 186904 compute units',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d success',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d invoke [2]',
				'Program log: Instruction: RemovePlugin',
				'Program log: programs/mpl-core/src/state/asset.rs:199:Approve',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d consumed 17370 of 168196 compute units',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d success',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d invoke [2]',
				'Program log: Instruction: RemovePlugin',
				'Program log: programs/mpl-core/src/state/asset.rs:199:Approve',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d consumed 15434 of 148359 compute units',
				'Program CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d success',
				'Program data: y8wYzSEYl8MT0/KCkCuOlEN1369+/p8mEiTmXQo0H9TM0k8hRnOn3ycJjNLcmF6/BnzAuG6Ho/oIwV7+M2S05frCRHeu1AksFNfBRBH4S60vW6ZDWvpKEJW9LAFvFKmytr7KLcU6xk+jwQtoAAAAAA==',
				'Program CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8 consumed 68288 of 199700 compute units',
				'Program CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8 success',
			],
			postBalances: [
				**********, 3908160, 2101920, 1, 1141440, 1552080, 0, 1141440, 1,
				1169280,
			],
			postTokenBalances: [],
			preBalances: [
				**********, 4513680, 2101920, 1, 1141440, 1552080, 0, 1141440, 1,
				1169280,
			],
			preTokenBalances: [],
			rewards: [],
		},
		slot: *********,
		transaction: {
			message: {
				accountKeys: [
					'2LQCE2gFhP8Jw5hte9fEub26f9yTZSfwXHUJzRRnTTuG',
					'3dPM6AZQxn7ZS1uQt4Z3gDyqFt4AA2bEeWe5ESaAztAX',
					'2QMyBnLXJYMmtLQCLacbpkS5juHxadr6D9MueBW2yW6i',
					'ComputeBudget111111111111111111111111111111',
					'CPbyawszfMJ6adS945iFKG8XVN1zsemsKXn3nRNEEeq8',
					'GZHHYiR9ooDwd6U3mVdfqU8TMDXUhHHQEfxm4EvKmcgA',
					'52qKSeyzgRpqzd1Yd1VVxdYpRLjG4YNZwHiynwYVwhG5',
					'CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d',
					'11111111111111111111111111111111',
					'SysvarC1ock11111111111111111111111111111111',
				],
				addressTableLookups: [],
				header: {
					numReadonlySignedAccounts: 0,
					numReadonlyUnsignedAccounts: 7,
					numRequiredSignatures: 1,
				},
				instructions: [
					{
						accounts: [],
						data: '3qi1MbrtkR83',
						programIdIndex: 3,
					},
					{
						accounts: [],
						data: 'Fj2Eoy',
						programIdIndex: 3,
					},
					{
						accounts: [0, 1, 2, 5, 6, 7, 8, 9],
						data: 'qKVKxT3Fyh',
						programIdIndex: 4,
					},
				],
				recentBlockhash: 'FnMBRb2nK8Tf6nZu1Kogh4iGq9nXm2Ggy6KKEFh66WMP',
			},
			signatures: [
				'367mZfV9XSCLpgfF7dTjEy7ebfAHtYCmn2Dmp6mTsAfxWmWxS3iTRZA86LshDDAGGi6jxSWCHcXwVDETVzEU6b2k',
			],
		},
		version: 0,
	},
]
