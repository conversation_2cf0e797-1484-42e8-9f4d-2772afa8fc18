import type { Context } from 'hono'
import { Hono } from 'hono'
import { collectionRoute } from './collection/collection.route'
import eventRoute from './event'
import { exploreRoute } from './explore'
import homeRoute from './home'
import { nftRoute } from './nft/nft.route'
import { userRoute } from './user/user.route'

// Create a global error handler middleware
const errorHandler = async (err: Error, c: Context) => {
	return c.json(
		{
			success: false,
			error: 'Internal server error',
			message: process.env.NODE_ENV === 'development' ? err.message : undefined,
		},
		500,
	)
}

const apiRoute = new Hono()

apiRoute.route('/home', homeRoute)
apiRoute.route('/users', userRoute)
apiRoute.route('/nft', nftRoute)
apiRoute.route('/collection', collectionRoute)
apiRoute.route('/explore', exploreRoute)

// Add error handling
apiRoute.onError(errorHandler)

// Register routes
apiRoute.route('/event/webhook', eventRoute)

export default apiRoute
