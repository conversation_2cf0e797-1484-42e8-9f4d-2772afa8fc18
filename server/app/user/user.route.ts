import { Buffer } from 'node:buffer'
import type { PrismaClientKnownRequestError } from '@prisma/client/runtime/library'
import { PublicKey } from '@solana/web3.js'
import type { Context } from 'hono'
import { Hono } from 'hono'
import { sign } from 'hono/jwt'
import { z } from 'zod'
import { tryCatch } from '@/lib/try-catch'
import {
	base64ToUint8Array,
	calculateFloorDiff,
	getCurrentTimeStamp,
	verifySolanaSignature,
} from '@/lib/utils'
import {
	ACCESS_TOKEN_EXPIRATION,
	ACCESS_TOKEN_SECRET,
	REFRESH_TOKEN_EXPIRATION,
	REFRESH_TOKEN_SECRET,
} from '@/server/config/auth.config'
import { generateUniqueUsername } from '@/server/helpers/getUniqueUserName'
import { prisma } from '@/server/lib/prismaClient'
import { authMiddleware } from '@/server/middleware/auth-middleware'
import type { FollowPaginationInput } from '@/types/user.types'
import {
	followPaginationSchema,
	followResponseSchema,
	followSchema,
	paginatedCollectionsResponseSchema,
	paginatedNftsResponseSchema,
	paginatedOffersMadeResponseSchema,
	paginatedOffersReceivedResponseSchema,
	paginatedUsersResponseSchema,
	paginationSchema,
} from '@/types/user.types'
import type { Env } from '../../types/hono-env.types'
import { getUserNfts, type NftFilterType } from './helper'

// Constants for marketplace program
const LISTING_SEED = 'listing'
const MARKETPLACE_PROGRAM_ID = process.env.MARKETPLACE_PROGRAM_ID || ''

const userRoute = new Hono<Env>()

userRoute.use('/users', authMiddleware)

userRoute.get('/nfts', async (c) => {
	const userName = c.req.query('username')
	if (!userName) {
		return c.json({ message: 'Username parameter is required' }, 400)
	}

	const filter = (c.req.query('filter') as NftFilterType) || 'all'

	// Parse pagination parameters
	const page = Number(c.req.query('page') ?? '1')
	const limit = Number(c.req.query('limit') ?? '20')
	const search = c.req.query('search')

	// Validate pagination parameters
	const paginationResult = paginationSchema.safeParse({
		page,
		limit,
		search,
	})

	if (!paginationResult.success) {
		return c.json({ message: 'Invalid pagination parameters' }, 400)
	}

	// Get NFTs with pagination and filtering
	const result = await getUserNfts(userName, filter, paginationResult.data)
	if (!result) {
		return c.json({ message: 'Failed to fetch user NFTs' }, 500)
	}

	return c.json(result, 200)
})

// create a me route and return true or false

userRoute.get('/me', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	// Get full user data from the database
	const [dbUser, dbUserError] = await tryCatch(
		prisma.user.findUnique({
			where: { id: user.userId },
			select: {
				id: true,
				publicKey: true,
				userName: true,
				username: true,
				email: true,
				bio: true,
				imageUrl: true,
				bannerUrl: true,
				instagramId: true,
				telegramId: true,
				twitterId: true,
				websiteId: true,
				facebookId: true,
			},
		}),
	).then((res) => [res.data, res.error])

	if (dbUserError) {
		console.error('Error fetching user data:', dbUserError)
		return c.json({ message: 'Error fetching user data' }, 500)
	}

	if (!dbUser) {
		return c.json({ message: 'User not found' }, 404)
	}

	return c.json(dbUser, 200)
})

// Check if the authenticated user is following another user
userRoute.get('/check-follow', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ isFollowing: false }, 401)
	}

	const userId = c.req.query('userId')
	if (!userId) {
		return c.json({ message: 'UserId parameter is required' }, 400)
	}

	// Get the target user's ID from username
	const [targetUser, targetUserError] = await tryCatch(
		prisma.user.findUnique({
			where: { id: userId },
			select: { id: true, username: true },
		}),
	).then((res) => [res.data, res.error])

	if (targetUserError || !targetUser) {
		return c.json(
			{ isFollowing: false, followersCount: 0, followingCount: 0 },
			404,
		)
	}

	if (targetUser instanceof Error) {
		return c.json(
			{ isFollowing: false, followersCount: 0, followingCount: 0 },
			404,
		)
	}

	// Check if follow relationship exists
	const [follow, followError] = await tryCatch(
		prisma.follow.findUnique({
			where: {
				followerId_followingId: {
					followerId: user.userId,
					followingId: targetUser.id,
				},
			},
		}),
	).then((res) => [res.data, res.error])

	if (followError) {
		return c.json(
			{ isFollowing: false, followersCount: 0, followingCount: 0 },
			500,
		)
	}

	// Get followers count
	const [followersCount, followersCountError] = await tryCatch(
		prisma.follow.count({
			where: { followingId: targetUser.id },
		}),
	).then((res) => [res.data, res.error])

	if (followersCountError) {
		return c.json(
			{ isFollowing: !!follow, followersCount: 0, followingCount: 0 },
			500,
		)
	}

	// Get following count
	const [followingCount, followingCountError] = await tryCatch(
		prisma.follow.count({
			where: { followerId: targetUser.id },
		}),
	).then((res) => [res.data, res.error])

	if (followingCountError) {
		return c.json(
			{
				isFollowing: !!follow,
				followersCount: followersCount,
				followingCount: 0,
			},
			500,
		)
	}

	return c.json(
		{
			isFollowing: !!follow,
			followersCount,
			followingCount,
		},
		200,
	)
})

userRoute.post('/follow', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const jsonResult = await c.req.json()
	if (!jsonResult) {
		return c.json({ message: 'Invalid request body' }, 400)
	}
	const parsed = followSchema.safeParse(jsonResult)

	if (!parsed.success) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	const { followingId } = parsed.data

	const { data: existingFollow, error: findError } = await tryCatch(
		prisma.follow.findUnique({
			where: {
				followerId_followingId: {
					followerId: user.userId,
					followingId,
				},
			},
		}),
	)

	if (findError) {
		return c.json({ message: 'Failed to check follow status' }, 500)
	}

	if (existingFollow) {
		return c.json({ message: 'Already following' }, 400)
	}

	const { data: follow, error: createError } = await tryCatch(
		prisma.follow.create({
			data: {
				followerId: user.userId,
				followingId,
			},
		}),
	)

	if (createError || !follow) {
		return c.json({ message: 'Failed to follow' }, 500)
	}

	const validatedResponse = followResponseSchema.safeParse(follow)
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid follow response' }, 500)
	}
	return c.json(validatedResponse.data, 201)
})

userRoute.delete('/unfollow', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const jsonResult = await c.req.json()
	if (!jsonResult) {
		return c.json({ message: 'Invalid request body' }, 400)
	}
	const parsed = followSchema.safeParse(jsonResult)
	if (!parsed.success) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	const { followingId } = parsed.data

	const { error: deleteError } = await tryCatch(
		prisma.follow.delete({
			where: {
				followerId_followingId: {
					followerId: user.userId,
					followingId,
				},
			},
		}),
	)

	if (deleteError) {
		if ((deleteError as PrismaClientKnownRequestError).code === 'P2025') {
			return c.json({ message: 'Not following this user' }, 400)
		}
		return c.json({ message: 'Failed to unfollow' }, 500)
	}

	return c.json({ message: 'Unfollowed' }, 200)
})

function parsePaginationQuery(c: Context): FollowPaginationInput | null {
	const page = Number(c.req.query('page') ?? '1')
	const limit = Number(c.req.query('limit') ?? '10')
	const search = c.req.query('search')
	const targetUserId = c.req.query('targetUserId') || c.get('user')?.userId

	const parsed = followPaginationSchema.safeParse({
		page,
		limit,
		search,
		targetUserId,
	})
	return parsed.success ? parsed.data : null
}

userRoute.get('/following', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const pagination = parsePaginationQuery(c)
	if (!pagination) {
		return c.json({ message: 'Invalid query parameters' }, 400)
	}

	// Use targetUserId from query if provided, otherwise fall back to authenticated user
	const { targetUserId, page, limit, search } = pagination
	if (!targetUserId) {
		return c.json({ message: 'Target user ID is required' }, 400)
	}

	const skip = (page - 1) * limit

	const where = {
		followerId: targetUserId,
		...(search
			? {
					following: {
						username: { contains: search.toLowerCase() },
					},
				}
			: {}),
	}

	const [follows, followsError] = await tryCatch(
		prisma.follow.findMany({
			where,
			skip,
			take: limit,
			select: {
				following: {
					select: {
						id: true,
						username: true,
						imageUrl: true,
						publicKey: true,
					},
				},
			},
			orderBy: { createdAt: 'desc' },
		}),
	).then((res) => [
		res.data as {
			following: {
				id: string
				username: string
				imageUrl: string | null
				publicKey: string
			}
		}[],
		res.error,
	])

	if (followsError) {
		return c.json({ message: 'Failed to fetch following list' }, 500)
	}

	const [total, totalError] = await tryCatch(
		prisma.follow.count({ where }),
	).then((res) => [res.data as number, res.error])

	if (totalError) {
		return c.json({ message: 'Failed to fetch following count' }, 500)
	}

	let users: {
		id: string
		userName: string
		avatar: string | null
		publicKey: string
	}[] = []
	if (Array.isArray(follows)) {
		users = follows.map(
			(f: {
				following: {
					id: string
					username: string
					imageUrl: string | null
					publicKey: string
				}
			}) => ({
				id: f.following.id,
				userName: f.following.username,
				avatar: f.following.imageUrl,
				publicKey: f.following?.publicKey,
			}),
		)
	} else {
		return c.json({ message: 'Error fetching follows' }, 500)
	}

	const validatedResponse = paginatedUsersResponseSchema.safeParse({
		users,
		total,
		page,
		limit,
	})
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid response' }, 500)
	}

	return c.json(validatedResponse.data, 200)
})

userRoute.get('/followers', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const pagination = parsePaginationQuery(c)
	if (!pagination) {
		return c.json({ message: 'Invalid query parameters' }, 400)
	}

	// Use targetUserId from query if provided, otherwise fall back to authenticated user
	const { targetUserId, page, limit, search } = pagination
	if (!targetUserId) {
		return c.json({ message: 'Target user ID is required' }, 400)
	}

	const skip = (page - 1) * limit

	const where = {
		followingId: targetUserId,
		...(search
			? {
					follower: {
						userName: { contains: search.toLowerCase() },
					},
				}
			: {}),
	}

	const [follows, followsError] = await tryCatch(
		prisma.follow.findMany({
			where,
			skip,
			take: limit,
			select: {
				follower: {
					select: {
						id: true,
						userName: true,
						imageUrl: true,
						publicKey: true,
					},
				},
			},
			orderBy: { createdAt: 'desc' },
		}),
	).then((res) => [
		res.data as {
			follower: {
				id: string
				userName: string
				imageUrl: string | null
				publicKey: string
			}
		}[],
		res.error,
	])

	if (followsError) {
		return c.json({ message: 'Failed to fetch followers list' }, 500)
	}

	const [total, totalError] = await tryCatch(
		prisma.follow.count({ where }),
	).then((res) => [res.data as number, res.error])

	if (totalError) {
		return c.json({ message: 'Failed to fetch followers count' }, 500)
	}

	let users: {
		id: string
		userName: string
		avatar: string | null
		publicKey: string
	}[] = []
	if (Array.isArray(follows)) {
		users = follows.map(
			(f: {
				follower: {
					id: string
					userName: string
					imageUrl: string | null
					publicKey: string
				}
			}) => ({
				id: f.follower.id,
				userName: f.follower.userName,
				avatar: f.follower.imageUrl,
				publicKey: f.follower?.publicKey,
			}),
		)
	} else {
		return c.json({ message: 'Error fetching followers' }, 500)
	}
	const validatedResponse = paginatedUsersResponseSchema.safeParse({
		users,
		total,
		page,
		limit,
	})
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid response' }, 500)
	}

	return c.json(validatedResponse.data, 200)
})

userRoute.get('/liked-nfts', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const pagination = parsePaginationQuery(c)
	if (!pagination) {
		return c.json({ message: 'Invalid query parameters' }, 400)
	}
	const { page, limit, search } = pagination
	const skip = (page - 1) * limit

	// Get NFT publicKeys that the user has liked through the NftLike model
	const { data: nftLikes, error: nftLikesError } = await tryCatch(
		prisma.nftLike.findMany({
			where: {
				userId: user.userId,
			},
			select: {
				publicKey: true,
			},
		}),
	)

	if (nftLikesError) {
		return c.json({ message: 'Failed to fetch liked NFTs' }, 500)
	}

	if (!nftLikes || nftLikes.length === 0) {
		return c.json(
			{
				nfts: [],
				total: 0,
				page,
				limit,
			},
			200,
		)
	}

	const likedNftsPublicKeys = nftLikes.map(
		(like: { publicKey: string }) => like.publicKey,
	)

	// Build the query where clause
	const where = {
		publicKey: {
			in: likedNftsPublicKeys,
		},
		...(search
			? {
					name: { contains: search, mode: 'insensitive' as const },
				}
			: {}),
	}

	// Get total count for pagination
	const { data: total, error: totalError } = await tryCatch(
		prisma.nFT.count({ where }),
	)

	if (totalError) {
		return c.json({ message: 'Failed to fetch liked NFTs count' }, 500)
	}

	// Get the actual NFTs with pagination
	const { data: nfts, error: nftsError } = await tryCatch(
		prisma.nFT.findMany({
			where,
			skip,
			take: limit,
			select: {
				name: true,
				publicKey: true,
				description: true,
				imageUrl: true,
				lastBiddingPrice: true,
				collection: {
					select: {
						name: true,
					},
				},
				owner: {
					select: {
						id: true,
						username: true,
						imageUrl: true,
						publicKey: true,
					},
				},
			},
			orderBy: { createdAt: 'desc' },
		}),
	)

	if (nftsError) {
		return c.json({ message: 'Failed to fetch liked NFTs' }, 500)
	}

	if (!nfts || !Array.isArray(nfts)) {
		return c.json({ message: 'Failed to fetch NFTs' }, 500)
	}

	const mappedNfts = nfts.map((nft) => {
		let listingAddress = '0'

		try {
			if (nft.publicKey && nft.owner.publicKey && MARKETPLACE_PROGRAM_ID) {
				const [listing] = PublicKey.findProgramAddressSync(
					[
						Buffer.from(LISTING_SEED),
						new PublicKey(nft.publicKey).toBuffer(),
						new PublicKey(nft.owner.publicKey).toBuffer(),
					],
					new PublicKey(MARKETPLACE_PROGRAM_ID),
				)
				listingAddress = listing.toString()
			}
		} catch (error) {
			console.error('Error calculating listing address:', error)
		}

		return {
			name: nft.name,
			collectionName: nft.collection?.name ?? '',
			publicKey: nft.publicKey,
			description: nft.description || '',
			logoUrl: nft.imageUrl,
			symbol:
				nft.collection && 'symbol' in nft.collection
					? nft.collection.symbol
					: nft.name.substring(0, 3).toUpperCase(),
			biddingPrice: nft.lastBiddingPrice || 0,
			owner: {
				id: nft.owner.id,
				username: nft.owner.username,
				imageUrl: nft.owner.imageUrl || '',
				publicKey: nft.owner.publicKey,
			},
			listing: listingAddress,
		}
	})

	const validatedResponse = paginatedNftsResponseSchema.safeParse({
		nfts: mappedNfts,
		total,
		page,
		limit,
	})
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid response' }, 500)
	}

	return c.json(validatedResponse.data, 200)
})

userRoute.get('/liked-collections', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const pagination = parsePaginationQuery(c)
	if (!pagination) {
		return c.json({ message: 'Invalid query parameters' }, 400)
	}
	const { page, limit, search } = pagination
	const skip = (page - 1) * limit

	// Get collection publicKeys that the user has liked through the CollectionLike model
	const { data: collectionLikes, error: collectionLikesError } = await tryCatch(
		prisma.collectionLike.findMany({
			where: {
				userId: user.userId,
			},
			select: {
				publicKey: true,
			},
		}),
	)

	if (collectionLikesError) {
		return c.json({ message: 'Failed to fetch liked collections' }, 500)
	}

	if (!collectionLikes || collectionLikes.length === 0) {
		return c.json(
			{
				collections: [],
				total: 0,
				page,
				limit,
			},
			200,
		)
	}

	const likedCollectionPublicKeys = collectionLikes.map(
		(like: { publicKey: string }) => like.publicKey,
	)

	// Build the query where clause
	const where = {
		publicKey: {
			in: likedCollectionPublicKeys,
		},
		...(search
			? {
					name: { contains: search, mode: 'insensitive' as const },
				}
			: {}),
	}

	// Get total count for pagination
	const { data: total, error: totalError } = await tryCatch(
		prisma.collection.count({ where }),
	)

	if (totalError) {
		return c.json({ message: 'Failed to fetch liked collections count' }, 500)
	}

	// Get the actual collections with pagination
	const { data: collections, error: collectionsError } = await tryCatch(
		prisma.collection.findMany({
			where,
			skip,
			take: limit,
			select: {
				id: true,
				name: true,
				publicKey: true,
				description: true,
				logoUrl: true,
				bannerUrl: true,
				metadataUri: true,
				totalLikes: true,
				owner: {
					select: {
						id: true,
						userName: true,
						imageUrl: true,
						publicKey: true,
					},
				},
				nfts: {
					select: {
						lastBiddingPrice: true,
					},
					orderBy: {
						lastBiddingPrice: 'asc',
					},
					take: 1,
				},
			},
			orderBy: { createdAt: 'desc' },
		}),
	)

	if (collectionsError) {
		return c.json({ message: 'Failed to fetch liked collections' }, 500)
	}

	if (!collections || !Array.isArray(collections)) {
		return c.json({ message: 'Failed to fetch collections' }, 500)
	}

	const mappedCollections = collections.map((col) => ({
		name: col.name,
		publicKey: col.publicKey,
		description: col.description,
		logoUrl: col.logoUrl,
		bannerUrl: col.bannerUrl,
		metadataUri: col.metadataUri,
		owner: {
			id: col.owner.id,
			userName: col.owner.userName || '',
			avatar: col.owner.imageUrl,
			publicKey: col.owner.publicKey,
		},
		lowestBiddingPrice: col.nfts[0]?.lastBiddingPrice ?? 0,
	}))

	const validatedResponse = paginatedCollectionsResponseSchema.safeParse({
		collections: mappedCollections,
		total,
		page,
		limit,
	})
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid response' }, 500)
	}

	return c.json(validatedResponse.data, 200)
})

userRoute.get('/offers-received', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const pagination = paginationSchema.safeParse(c.req.query())
	if (pagination.error) {
		return c.json({ message: 'Invalid query parameters' }, 400)
	}

	const { page, limit, search, sort } = pagination.data
	const skip = (page - 1) * limit

	const where = {
		sellerId: user.userId,
		...(search
			? {
					buyer: {
						userName: { contains: search, mode: 'insensitive' as const },
					},
				}
			: {}),
	}
	// Determine sort order based on the sort parameter
	const sortOrder = sort === 'asc' ? 'asc' : 'desc'

	const [offers, offersError] = await tryCatch(
		prisma.offer.findMany({
			where,
			skip,
			take: limit,
			orderBy: { createdAt: sortOrder as 'asc' | 'desc' },
			select: {
				id: true,
				publicKey: true,
				escrow: true,
				price: true,
				status: true,
				expiresAt: true,
				createdAt: true,
				updatedAt: true,
				nft: {
					select: {
						id: true,
						name: true,
						imageUrl: true,
						publicKey: true,
						lastBiddingPrice: true,
					},
				},
				buyer: {
					select: {
						id: true,
						userName: true,
						imageUrl: true,
						publicKey: true,
					},
				},
			},
		}),
	).then((res) => [res.data, res.error])

	if (offersError) {
		return c.json({ message: 'Failed to fetch offers received' }, 500)
	}

	const [total, totalError] = await tryCatch(
		prisma.offer.count({ where }),
	).then((res) => [res.data as number, res.error])

	if (totalError) {
		return c.json({ message: 'Failed to fetch offers count' }, 500)
	}

	if (!offers || !Array.isArray(offers)) {
		return c.json({ message: 'Failed to fetch offers' }, 500)
	}

	const mappedOffers = offers.map((offer) => ({
		id: offer.id,
		publicKey: offer.publicKey,
		price: offer.price,
		expiresAt: offer.expiresAt?.toUTCString(),
		floorDiff: calculateFloorDiff(
			offer.price,
			offer.nft?.lastBiddingPrice ?? 0,
		),
		nft: offer.nft
			? {
					id: offer.nft.id,
					name: offer.nft.name,
					imageUrl: offer.nft.imageUrl,
					publicKey: offer.nft.publicKey,
				}
			: null,
		buyer: offer.buyer
			? {
					id: offer.buyer.id,
					userName: offer.buyer.userName,
					avatar: offer.buyer.imageUrl,
					publicKey: offer.buyer.publicKey,
				}
			: null,
	}))

	const validatedResponse = paginatedOffersReceivedResponseSchema.safeParse({
		offers: mappedOffers,
		total,
		page,
		limit,
	})
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid response' }, 500)
	}

	return c.json(validatedResponse.data, 200)
})

userRoute.get('/offers-made', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const pagination = paginationSchema.safeParse(c.req.query())
	if (pagination.error) {
		return c.json({ message: 'Invalid query parameters' }, 400)
	}

	const { page, limit, search, sort } = pagination.data
	const skip = (page - 1) * limit

	const where = {
		buyerId: user.userId,
		...(search
			? {
					seller: {
						userName: { contains: search, mode: 'insensitive' as const },
					},
				}
			: {}),
	}

	const sortOrder = sort === 'asc' ? 'asc' : 'desc'

	const [offers, offersError] = await tryCatch(
		prisma.offer.findMany({
			where,
			skip,
			take: limit,
			orderBy: { createdAt: sortOrder as 'asc' | 'desc' },
			select: {
				id: true,
				publicKey: true,
				escrow: true,
				price: true,
				status: true,
				expiresAt: true,
				createdAt: true,
				updatedAt: true,
				nft: {
					select: {
						id: true,
						name: true,
						imageUrl: true,
						publicKey: true,
						lastBiddingPrice: true,
					},
				},
				seller: {
					select: {
						id: true,
						userName: true,
						imageUrl: true,
						publicKey: true,
					},
				},
			},
		}),
	).then((res) => [res.data, res.error])

	if (offersError) {
		return c.json({ message: 'Failed to fetch offers made' }, 500)
	}

	const [total, totalError] = await tryCatch(
		prisma.offer.count({ where }),
	).then((res) => [res.data as number, res.error])

	if (totalError) {
		return c.json({ message: 'Failed to fetch offers count' }, 500)
	}

	if (!offers || !Array.isArray(offers)) {
		return c.json({ message: 'Failed to fetch offers' }, 500)
	}

	const mappedOffers = offers.map((offer) => ({
		id: offer.id,
		publicKey: offer.publicKey,
		escrow: offer.escrow,
		price: offer.price,
		expiresAt: offer.expiresAt?.toUTCString(),
		floorDiff: calculateFloorDiff(
			offer.price,
			offer.nft?.lastBiddingPrice ?? 0,
		),
		nft: offer.nft
			? {
					id: offer.nft.id,
					name: offer.nft.name,
					imageUrl: offer.nft.imageUrl,
					publicKey: offer.nft.publicKey,
				}
			: null,
		seller: offer.seller
			? {
					id: offer.seller.id,
					userName: offer.seller.userName,
					avatar: offer.seller.imageUrl,
					publicKey: offer.seller.publicKey,
				}
			: null,
	}))

	const validatedResponse = paginatedOffersMadeResponseSchema.safeParse({
		offers: mappedOffers,
		total,
		page,
		limit,
	})
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid response' }, 500)
	}

	return c.json(validatedResponse.data, 200)
})

// Login endpoint has been moved to /api/auth/login
// See: server/app/auth/auth.route.ts

userRoute.post('/logout', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ success: false, message: 'Unauthorized' }, 401)
	}

	try {
		await prisma.session.updateMany({
			where: { id: user.userId },
			data: { active: false },
		})

		return c.json(
			{ success: true, message: 'Logout successful', data: {} },
			200,
		)
	} catch (e) {
		console.error('Logout error:', e)
		return c.json({ success: false, message: 'Logout failed', data: {} }, 500)
	}
})

import { getUser } from '@/server/helpers/getUser'

// Helper to remove undefined keys
function removeUndefined<T extends object>(obj: T): Partial<T> {
	return Object.fromEntries(
		Object.entries(obj).filter(([_, v]) => v !== undefined),
	) as Partial<T>
}

const editProfileSchema = z.object({
	userName: z.string().min(1).optional(),
	imageUrl: z.string().url().optional(),
	instagramId: z.string().optional(),
	twitterId: z.string().optional(),
	websiteId: z.string().optional(),
	telegramId: z.string().optional(),
	bio: z.string().optional(),
	email: z.string().email().optional(),
	bannerUrl: z.string().url().optional(),
	facebookId: z.string().optional(),
})

userRoute.post('/edit-profile', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ success: false, message: 'Unauthorized' }, 401)
	}

	const json = await c.req.json()
	const parsed = editProfileSchema.safeParse(json)

	if (!parsed.success) {
		return c.json(
			{
				success: false,
				message: 'Invalid request',
				errors: parsed.error.errors,
			},
			400,
		)
	}

	const updateParams = removeUndefined({
		...parsed.data,
		username: parsed.data.userName?.toLowerCase(),
	})

	try {
		await prisma.user.update({
			where: { id: user.userId },
			data: updateParams,
		})

		return c.json({ success: true, message: 'User update successful' }, 200)
	} catch (e) {
		console.error('User update failed:', e)
		return c.json({ success: false, message: 'User update failed' }, 500)
	}
})

export { userRoute }
