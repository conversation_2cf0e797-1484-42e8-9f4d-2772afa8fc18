import { fetchAssetsByOwner } from '@metaplex-foundation/mpl-core'
import { publicKey } from '@metaplex-foundation/umi'
import type { Prisma } from 'prisma-client/edge'
import { act } from 'react'
import { enhanceAssetWithMetadata } from '@/lib/nft/asset-utils'
import { tryCatch } from '@/lib/try-catch'
import { prisma } from '@/server/lib/prismaClient'
import { getServerUmi } from '@/server/lib/umiClient'
import type { PaginationInput } from '@/types/user.types'

export type UserProfileNft = {
	readonly metadata: Metadata
	readonly name: string
	readonly publicKey: string
	readonly uri: string
	readonly listing: Listing
	readonly collectionName: string
}

export type Listing = {
	readonly id?: string
	readonly price?: number
	readonly currency?: string
	readonly status?: string
	readonly listingType?: string
	readonly startTime?: Date
	readonly endTime?: Date | null
	readonly isAuction?: boolean
	readonly isFixedPrice?: boolean
	readonly isActive?: boolean
}

export type Metadata = {
	readonly name: string
	readonly description: string
	readonly image: string
	readonly attributes: Attribute[]
	readonly seller_fee_basis_points: number
}

export type Attribute = {
	readonly id?: string
	readonly traitType?: string
	readonly value: string
	readonly trait_type?: string
}

export type UserNftsResponse = {
	nfts: UserProfileNft[]
	pagination: {
		currentPage: number
		totalPages: number
		totalItems: number
		itemsPerPage: number
	}
}

export type NftFilterType = 'all' | 'fixed-price' | 'auction'

/**
 * Get NFTs owned by a user with pagination and filtering
 * @param username Username of the NFT owner
 * @param filter Filter type: 'all', 'fixed-price', or 'auction'
 * @param pagination Pagination parameters
 * @returns Object containing NFTs and pagination metadata
 */
export async function getUserNfts(
	username: string,
	filter: NftFilterType = 'all',
	pagination: PaginationInput = { page: 1, limit: 10 },
): Promise<UserNftsResponse | null> {
	try {
		// Find user by username
		const user = await prisma.user.findFirst({
			where: {
				username: username.toLowerCase(),
			},
		})

		if (!user) {
			console.error('User not found')
			return null
		}

		// Get Umi instance
		const umi = getServerUmi()

		// Fetch assets from blockchain
		const assets = await fetchAssetsByOwner(umi, publicKey(user.publicKey))
		console.log('🚀 ~ assets:', assets.length)
		if (!assets || assets.length === 0) {
			return {
				nfts: [],
				pagination: {
					currentPage: pagination.page,
					totalPages: 0,
					totalItems: 0,
					itemsPerPage: pagination.limit,
				},
			}
		}

		// Calculate pagination
		const { page, limit } = pagination
		const startIndex = (page - 1) * limit
		const endIndex = startIndex + limit

		// Enhance assets with metadata (only for the current page)
		const paginatedAssets = assets.slice(startIndex, endIndex)
		const enhancedAssets = await Promise.all(
			paginatedAssets.map(async (asset) => {
				const enhancedAsset = await enhanceAssetWithMetadata(asset)
				if (!enhancedAsset) {
					console.error('Failed to enhance asset with metadata')
					return null
				}
				return enhancedAsset
			}),
		).then((assets) =>
			assets.filter(
				(asset): asset is NonNullable<typeof asset> => asset !== null,
			),
		)

		// Get NFT public keys for database queries
		const nftKeys = enhancedAssets.map((asset) => asset.publicKey.toString())

		// Build query based on filter
		const listingWhere: Prisma.ListingWhereInput = {
			nft: {
				publicKey: {
					in: nftKeys,
				},
			},
			// Always filter for active listings
			status: 'ACTIVE' as const,
			// Add type-specific filters
			...(filter === 'fixed-price'
				? { listingType: 'FIXED_PRICE' as const }
				: {}),
			...(filter === 'auction' ? { listingType: 'AUCTION' as const } : {}),
		}

		// Fetch active listings for these NFTs
		const { data: activeListing, error: listingError } = await tryCatch(
			prisma.listing.findMany({
				where: listingWhere,
				select: {
					id: true,
					price: true,
					currency: true,
					status: true,
					listingType: true,
					startTime: true,
					endTime: true,
					minBidIncrement: true,
					nft: {
						select: {
							publicKey: true,
							collection: {
								select: {
									name: true,
								},
							},
						},
					},
				},
			}),
		)

		if (listingError) {
			console.error('Error fetching listings:', listingError)
			return null
		}

		// First, filter the assets based on the filter type
		const filteredAssets =
			filter !== 'all'
				? enhancedAssets.filter((asset) => {
						const listing = activeListing?.find(
							(listing) => listing.nft.publicKey === asset.publicKey.toString(),
						)

						if (filter === 'auction') {
							return listing && listing.listingType === 'AUCTION'
						}
						if (filter === 'fixed-price') {
							return listing && listing.listingType === 'FIXED_PRICE'
						}
						return false
					})
				: enhancedAssets

		// Then map the filtered assets to UserProfileNft format
		const nfts = filteredAssets.map((asset) => {
			const listing = activeListing?.find(
				(listing) => listing.nft.publicKey === asset.publicKey.toString(),
			)

			const { metadata, name, publicKey, uri } = asset

			return {
				metadata,
				name,
				publicKey: publicKey.toString(),
				uri,
				collectionName: listing?.nft.collection?.name,
				listing: listing
					? {
							id: listing.id,
							price: listing.price,
							currency: listing.currency,
							status: listing.status,
							listingType: listing.listingType,
							startTime: listing.startTime,
							endTime: listing.endTime,
							isAuction: listing.listingType === 'AUCTION',
							isFixedPrice: listing.listingType === 'FIXED_PRICE',
							isActive: listing.status === 'ACTIVE',
						}
					: {},
			}
		})

		// Calculate pagination based on filtered results
		const totalItems = assets.length
		const totalPages = Math.ceil(totalItems / limit)

		return {
			nfts,
			pagination: {
				currentPage: page,
				totalPages,
				totalItems,
				itemsPerPage: limit,
			},
		}
	} catch (error) {
		console.error('Error fetching user NFTs:', error)
		return null
	}
}
