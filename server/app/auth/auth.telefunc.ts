import { sign, verify } from 'hono/jwt'
import { Abort } from 'telefunc'
import {
	ACCESS_TOKEN_EXPIRATION,
	ACCESS_TOKEN_SECRET,
	REFRESH_TOKEN_EXPIRATION,
	REFRESH_TOKEN_SECRET,
} from '@/server/config/auth.config'
import { getCurrentTimeStamp } from '@/server/helpers/getTime'
import { getUser } from '@/server/helpers/getUser'
import { prisma } from '@/server/lib/prismaClient'
import type { AuthUserPayload } from '@/server/types/auth.types'

// onRefreshToken function has been migrated to REST API endpoint: POST /api/auth/refresh
// See: server/app/auth/auth.route.ts and hooks/useRefreshToken.ts

export async function onMe() {
	const { userId } = getUser()

	const user = await prisma.user.findUnique({
		where: {
			id: userId,
		},
		select: {
			id: true,
			publicKey: true,
			userName: true,
			email: true,
			bio: true,
			imageUrl: true,
			bannerUrl: true,
			instagramId: true,
			telegramId: true,
			twitterId: true,
			websiteId: true,
			facebookId: true,
		},
	})
	if (!user) {
		throw Abort({ message: 'User not found', status: 403 })
	}

	return user
}
