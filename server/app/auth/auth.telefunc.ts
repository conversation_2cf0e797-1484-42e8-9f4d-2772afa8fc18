import { sign, verify } from 'hono/jwt'
import { Abort } from 'telefunc'
import {
	ACCESS_TOKEN_EXPIRATION,
	ACCESS_TOKEN_SECRET,
	REFRESH_TOKEN_EXPIRATION,
	REFRESH_TOKEN_SECRET,
} from '@/server/config/auth.config'
import { getCurrentTimeStamp } from '@/server/helpers/getTime'
import { getUser } from '@/server/helpers/getUser'
import { prisma } from '@/server/lib/prismaClient'
import type { AuthUserPayload } from '@/server/types/auth.types'

export async function onRefreshToken({
	refreshToken,
}: {
	refreshToken: string
}) {
	try {
		if (!refreshToken) {
			throw Abort({ message: 'Refresh token not found', status: 403 })
		}

		const userPayload = (await verify(
			refreshToken,
			REFRESH_TOKEN_SECRET,
		)) as AuthUserPayload

		const session = await prisma.session.findFirst({
			where: {
				refreshToken: refreshToken,
				userId: userPayload.userId,
			},
			select: {
				id: true,
				userId: true,
				active: true,
			},
		})

		if (!session) {
			throw Abort({ message: 'Session not found', status: 403 })
		}

		if (!session.active) {
			await prisma.session.updateMany({
				where: {
					userId: session.userId,
				},
				data: {
					active: false,
				},
			})
			throw Abort({ message: 'Session expired', status: 403 })
		}

		await prisma.session.update({
			where: {
				id: session.id,
			},
			data: {
				active: false,
			},
		})

		const user = await prisma.user.findUnique({
			where: {
				id: session.userId,
			},
			select: {
				id: true,
				publicKey: true,
			},
		})
		if (!user) {
			throw Abort({ message: 'User not found', status: 403 })
		}

		const basePayload = {
			publicKey: user.publicKey,
			userId: user.id,
		}

		const accessTokenPayload = {
			...basePayload,
			exp: getCurrentTimeStamp() + ACCESS_TOKEN_EXPIRATION,
		}
		const refreshTokenPayload = {
			...basePayload,
			exp: getCurrentTimeStamp() + REFRESH_TOKEN_EXPIRATION,
		}
		const newAccessToken = await sign(accessTokenPayload, ACCESS_TOKEN_SECRET)
		const newRefreshToken = await sign(
			refreshTokenPayload,
			REFRESH_TOKEN_SECRET,
		)

		await prisma.session.create({
			data: {
				userId: user.id,
				refreshToken: newRefreshToken,
				active: true,
			},
		})

		return {
			success: true,
			message: 'Refresh token successful',
			accessToken: newAccessToken,
			refreshToken: newRefreshToken,
		}
	} catch (e) {
		throw Abort({ message: 'Error refreshing token', status: 403 })
	}
}

export async function onMe() {
	const { userId } = getUser()

	const user = await prisma.user.findUnique({
		where: {
			id: userId,
		},
		select: {
			id: true,
			publicKey: true,
			userName: true,
			email: true,
			bio: true,
			imageUrl: true,
			bannerUrl: true,
			instagramId: true,
			telegramId: true,
			twitterId: true,
			websiteId: true,
			facebookId: true,
		},
	})
	if (!user) {
		throw Abort({ message: 'User not found', status: 403 })
	}

	return user
}
