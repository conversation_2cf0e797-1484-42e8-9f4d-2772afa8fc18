import { Hono } from 'hono'
import { sign, verify } from 'hono/jwt'
import { z } from 'zod'
import { tryCatch } from '@/lib/try-catch'
import { base64ToUint8Array, verifySolanaSignature } from '@/lib/utils'
import {
	ACCESS_TOKEN_EXPIRATION,
	ACCESS_TOKEN_SECRET,
	REFRESH_TOKEN_EXPIRATION,
	REFRESH_TOKEN_SECRET,
} from '@/server/config/auth.config'
import { getCurrentTimeStamp } from '@/server/helpers/getTime'
import { generateUniqueUsername } from '@/server/helpers/getUniqueUserName'
import { prisma } from '@/server/lib/prismaClient'
import type { AuthUserPayload } from '@/server/types/auth.types'
import type { Env } from '../../types/hono-env.types'

const authRoute = new Hono<Env>()

// Validation schema for refresh token request
const refreshTokenSchema = z.object({
	refreshToken: z.string().min(1, 'Refresh token is required'),
})

// Validation schema for login request
const loginSchema = z.object({
	publicKey: z.string().min(1, 'Public key is required'),
	message: z.string().min(1, 'Message is required'),
	signature: z.string().min(1, 'Signature is required'),
})

/**
 * POST /api/auth/refresh
 * Refresh access token using a valid refresh token
 */
authRoute.post('/refresh', async (c) => {
	try {
		const json = await c.req.json()

		const parsed = refreshTokenSchema.safeParse(json)
		if (!parsed.success) {
			return c.json(
				{
					success: false,
					message: 'Invalid request body',
					errors: parsed.error.errors,
				},
				400,
			)
		}

		const { refreshToken } = parsed.data

		if (!refreshToken) {
			return c.json({ success: false, message: 'Refresh token not found' }, 403)
		}

		// Verify the refresh token
		const { data: userPayload, error: verifyError } = await tryCatch(
			verify(refreshToken, REFRESH_TOKEN_SECRET) as Promise<AuthUserPayload>,
		)

		if (verifyError || !userPayload) {
			return c.json({ success: false, message: 'Invalid refresh token' }, 403)
		}

		// Find the session in the database
		const { data: session, error: sessionError } = await tryCatch(
			prisma.session.findFirst({
				where: {
					refreshToken: refreshToken,
					userId: userPayload.userId,
				},
				select: {
					id: true,
					userId: true,
					active: true,
				},
			}),
		)

		if (sessionError) {
			console.error('Error finding session:', sessionError)
			return c.json({ success: false, message: 'Error refreshing token' }, 500)
		}

		if (!session) {
			return c.json({ success: false, message: 'Session not found' }, 403)
		}

		if (!session.active) {
			// Deactivate all sessions for this user
			await tryCatch(
				prisma.session.updateMany({
					where: {
						userId: session.userId,
					},
					data: {
						active: false,
					},
				}),
			)
			return c.json({ success: false, message: 'Session expired' }, 403)
		}

		// Deactivate the current session
		const { error: deactivateError } = await tryCatch(
			prisma.session.update({
				where: {
					id: session.id,
				},
				data: {
					active: false,
				},
			}),
		)

		if (deactivateError) {
			console.error('Error deactivating session:', deactivateError)
			return c.json({ success: false, message: 'Error refreshing token' }, 500)
		}

		// Get user data
		const { data: user, error: userError } = await tryCatch(
			prisma.user.findUnique({
				where: {
					id: session.userId,
				},
				select: {
					id: true,
					publicKey: true,
				},
			}),
		)

		if (userError) {
			console.error('Error finding user:', userError)
			return c.json({ success: false, message: 'Error refreshing token' }, 500)
		}

		if (!user) {
			return c.json({ success: false, message: 'User not found' }, 403)
		}

		// Create new tokens
		const basePayload = {
			publicKey: user.publicKey,
			userId: user.id,
		}

		const accessTokenPayload = {
			...basePayload,
			exp: getCurrentTimeStamp() + ACCESS_TOKEN_EXPIRATION,
		}
		const refreshTokenPayload = {
			...basePayload,
			exp: getCurrentTimeStamp() + REFRESH_TOKEN_EXPIRATION,
		}

		const { data: newAccessToken, error: accessTokenError } = await tryCatch(
			sign(accessTokenPayload, ACCESS_TOKEN_SECRET),
		)
		const { data: newRefreshToken, error: refreshTokenError } = await tryCatch(
			sign(refreshTokenPayload, REFRESH_TOKEN_SECRET),
		)

		if (
			accessTokenError ||
			refreshTokenError ||
			!newAccessToken ||
			!newRefreshToken
		) {
			console.error('Error creating tokens:', {
				accessTokenError,
				refreshTokenError,
			})
			return c.json({ success: false, message: 'Error creating tokens' }, 500)
		}

		// Create new session
		const { data: newSession, error: newSessionError } = await tryCatch(
			prisma.session.create({
				data: {
					userId: user.id,
					refreshToken: newRefreshToken,
					active: true,
				},
			}),
		)

		if (newSessionError) {
			console.error('Error creating new session:', newSessionError)
			return c.json({ success: false, message: 'Error creating session' }, 500)
		}

		return c.json(
			{
				success: true,
				message: 'Refresh token successful',
				accessToken: newAccessToken,
				refreshToken: newRefreshToken,
			},
			200,
		)
	} catch (error) {
		console.error('Unexpected error in refresh token:', error)
		return c.json({ success: false, message: 'Error refreshing token' }, 500)
	}
})

/**
 * POST /api/auth/login
 * Authenticate user with Solana signature verification
 */
authRoute.post('/login', async (c) => {
	try {
		const json = await c.req.json()

		const parsed = loginSchema.safeParse(json)
		if (!parsed.success) {
			return c.json(
				{
					success: false,
					message: 'Invalid request body',
					errors: parsed.error.errors,
				},
				400,
			)
		}

		const { publicKey, message, signature } = parsed.data

		let isNewUser = false

		const { data: decodedSignature, error: decodeError } = await tryCatch(
			Promise.resolve(base64ToUint8Array(signature)),
		)

		if (decodeError || !decodedSignature) {
			return c.json(
				{
					success: false,
					message: 'Invalid signature format',
					data: { accessToken: '', refreshToken: '' },
				},
				400,
			)
		}

		const { data: verified, error: verifyError } = await tryCatch(
			verifySolanaSignature({
				publicKey,
				signature: decodedSignature,
				message,
			}),
		)

		if (verifyError || !verified) {
			return c.json(
				{
					success: false,
					message: 'Login Failed!',
					data: { accessToken: '', refreshToken: '' },
				},
				401,
			)
		}

		const { data: user, error: userFindError } = await tryCatch(
			prisma.user.findUnique({
				where: { publicKey },
				select: { id: true, publicKey: true, userName: true },
			}),
		)

		if (userFindError) {
			console.error('Error finding user:', userFindError)
			return c.json(
				{
					success: false,
					message: 'Login Failed!',
					data: { accessToken: '', refreshToken: '' },
				},
				500,
			)
		}

		let finalUser = user

		if (!finalUser) {
			const { data: generatedUserName, error: usernameError } = await tryCatch(
				generateUniqueUsername(),
			)

			if (usernameError || !generatedUserName) {
				console.error('Error generating username:', usernameError)
				return c.json(
					{
						success: false,
						message: 'Login Failed!',
						data: { accessToken: '', refreshToken: '' },
					},
					500,
				)
			}

			const { data: newUser, error: createUserError } = await tryCatch(
				prisma.user.create({
					data: {
						publicKey,
						userName: generatedUserName,
						username: generatedUserName.toLowerCase(),
					},
				}),
			)

			if (createUserError || !newUser) {
				console.error('Error creating user:', createUserError)
				return c.json(
					{
						success: false,
						message: 'Login Failed!',
						data: { accessToken: '', refreshToken: '' },
					},
					500,
				)
			}

			finalUser = newUser
			isNewUser = true
		}

		const basePayload = { publicKey: finalUser.publicKey, userId: finalUser.id }

		const refreshTokenPayload = {
			...basePayload,
			exp: getCurrentTimeStamp() + REFRESH_TOKEN_EXPIRATION,
		}

		const { data: refreshToken, error: refreshTokenError } = await tryCatch(
			sign(refreshTokenPayload, REFRESH_TOKEN_SECRET),
		)

		if (refreshTokenError || !refreshToken) {
			console.error('Error creating refresh token:', refreshTokenError)
			return c.json(
				{
					success: false,
					message: 'Login Failed!',
					data: { accessToken: '', refreshToken: '' },
				},
				500,
			)
		}

		const { data: session, error: sessionError } = await tryCatch(
			prisma.session.create({
				data: { userId: finalUser.id, refreshToken, active: true },
			}),
		)

		if (sessionError || !session) {
			console.error('Error creating session:', sessionError)
			return c.json(
				{
					success: false,
					message: 'Login Failed!',
					data: { accessToken: '', refreshToken: '' },
				},
				500,
			)
		}

		const accessTokenPayload = {
			...basePayload,
			exp: getCurrentTimeStamp() + ACCESS_TOKEN_EXPIRATION,
			sessionId: session.id,
		}

		const { data: accessToken, error: accessTokenError } = await tryCatch(
			sign(accessTokenPayload, ACCESS_TOKEN_SECRET),
		)

		if (accessTokenError || !accessToken) {
			console.error('Error creating access token:', accessTokenError)
			return c.json(
				{
					success: false,
					message: 'Login Failed!',
					data: { accessToken: '', refreshToken: '' },
				},
				500,
			)
		}

		return c.json(
			{
				success: true,
				message: 'Login Successful!',
				isNewUser,
				data: {
					accessToken,
					refreshToken,
					userName: finalUser.userName,
				},
			},
			200,
		)
	} catch (error) {
		console.error('Unexpected error in login:', error)
		return c.json(
			{
				success: false,
				message: 'Login Failed!',
				isNewUser: false,
				data: { accessToken: '', refreshToken: '' },
			},
			500,
		)
	}
})

/**
 * POST /api/auth/logout
 * Logout user by invalidating current session
 */
authRoute.post('/logout', async (c) => {
	try {
		// Get the current user from the auth middleware
		const user = c.get('user')

		if (!user || !user.sessionId) {
			return c.json({ success: false, message: 'No active session found' }, 401)
		}

		// Invalidate the current session
		const { error: logoutError } = await tryCatch(
			prisma.session.update({
				where: {
					id: user.sessionId,
				},
				data: {
					active: false,
				},
			}),
		)

		if (logoutError) {
			console.error('Error during logout:', logoutError)
			return c.json({ success: false, message: 'Logout failed' }, 500)
		}

		return c.json(
			{
				success: true,
				message: 'Logout successful',
				data: {},
			},
			200,
		)
	} catch (error) {
		console.error('Unexpected error in logout:', error)
		return c.json({ success: false, message: 'Logout failed' }, 500)
	}
})

/**
 * POST /api/auth/logout-all
 * Logout user from all devices by invalidating all sessions
 */
authRoute.post('/logout-all', async (c) => {
	try {
		// Get the current user from the auth middleware
		const user = c.get('user')

		if (!user || !user.userId) {
			return c.json({ success: false, message: 'No active session found' }, 401)
		}

		// Invalidate all sessions for this user
		const { error: logoutError } = await tryCatch(
			prisma.session.updateMany({
				where: {
					userId: user.userId,
					active: true,
				},
				data: {
					active: false,
				},
			}),
		)

		if (logoutError) {
			console.error('Error during logout from all devices:', logoutError)
			return c.json({ success: false, message: 'Logout failed' }, 500)
		}

		return c.json(
			{
				success: true,
				message: 'Logout successful',
				data: {},
			},
			200,
		)
	} catch (error) {
		console.error('Unexpected error in logout all:', error)
		return c.json({ success: false, message: 'Logout failed' }, 500)
	}
})

/**
 * GET /api/auth/me
 * Get current authenticated user profile data
 */
authRoute.get('/me', async (c) => {
	try {
		// Get the current user from the auth middleware
		const user = c.get('user')

		if (!user || !user.userId) {
			return c.json({ success: false, message: 'Unauthorized' }, 401)
		}

		// Get full user data from the database
		const { data: dbUser, error: dbUserError } = await tryCatch(
			prisma.user.findUnique({
				where: { id: user.userId },
				select: {
					id: true,
					publicKey: true,
					userName: true,
					email: true,
					bio: true,
					imageUrl: true,
					bannerUrl: true,
					instagramId: true,
					telegramId: true,
					twitterId: true,
					websiteId: true,
					facebookId: true,
				},
			}),
		)

		if (dbUserError) {
			console.error('Error fetching user data:', dbUserError)
			return c.json(
				{ success: false, message: 'Error fetching user data' },
				500,
			)
		}

		if (!dbUser) {
			return c.json({ success: false, message: 'User not found' }, 404)
		}

		return c.json(dbUser, 200)
	} catch (error) {
		console.error('Unexpected error in /me endpoint:', error)
		return c.json({ success: false, message: 'Error fetching user data' }, 500)
	}
})

export { authRoute }
