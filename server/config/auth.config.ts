export const ACCESS_TOKEN_SECRET =
	process.env.ACCESS_TOKEN_SECRET || 'access_secret'
export const REFRESH_TOKEN_SECRET =
	process.env.REFRESH_TOKEN_SECRET || 'refresh_secret'

export const MINT_SESSION_SECRET =
	process.env.MINTING_SESSION_SECRET_KEY || 'mint_session_secret'

// Token expiration times
export const ACCESS_TOKEN_EXPIRATION = 7 * 24 * 60 * 60 // 7 days in seconds
export const REFRESH_TOKEN_EXPIRATION = 14 * 24 * 60 * 60 // 14 days in seconds
