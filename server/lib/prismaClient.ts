// import { withAccelerate } from '@prisma/extension-accelerate'
// import { withOptimize } from '@prisma/extension-optimize'
import { PrismaClient } from 'prisma-client/edge'

export const prisma = new PrismaClient({
	datasources: {
		db: {
			url:
				process.env.DATABASE_URL ||
				import.meta.env.DATABASE_URL ||
				'prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiMjk1OGU0OWItNmFhNC00NmM5LTkwZWMtNmJlODk0YzczZjg5IiwidGVuYW50X2lkIjoiNDc4MjJlMTg3NTFhOGZlODA1MTgwMDE2NGNjYTAxNTI4MGJkOTkyNWNhZWU5ODZmM2FlNDhjN2U1ZDkyMGFlMSIsImludGVybmFsX3NlY3JldCI6ImRhZDk2NjczLTkyMmItNGZiMy1hYjQxLWVhZGYwN2M4MjQ0MCJ9.d0-CWso4UR-IsFQO2-ZiNyUJ0O2qot0rvd7HlGat_Jc',
		},
	},
})
// .$extends(withAccelerate())
// .$extends(
// 	withOptimize({
// 		apiKey: import.meta.env.OPTIMIZE_API_KEY,
// 	}),
// )
