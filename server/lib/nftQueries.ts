import { prisma } from './prismaClient'
import type { Prisma } from 'prisma-client/edge'

export type ActivityPeriod = 'all' | '7days' | '30days'
export type SortOrder = 'desc' | 'asc'

export interface NFTQueryOptions {
	page?: number
	limit?: number
	activityPeriod?: ActivityPeriod
	sortOrder?: SortOrder
	startDate?: Date
	endDate?: Date
	collectionId?: string
	creatorId?: string
	ownerId?: string
}

/**
 * Get NFTs sorted by activity count with efficient pagination
 *
 * This function uses the denormalized activity count fields for efficient sorting
 * and applies cursor-based pagination for optimal performance.
 */
export async function getNFTsByActivityCount({
	page = 1,
	limit = 20,
	activityPeriod = 'all',
	sortOrder = 'desc',
	startDate,
	endDate,
	collectionId,
	creatorId,
	ownerId,
}: NFTQueryOptions) {
	// Determine which activity count field to use based on the period
	let activityField: string
	switch (activityPeriod) {
		case '7days':
			activityField = 'totalActivities7Days'
			break
		case '30days':
			activityField = 'totalActivities30Days'
			break
		default:
			activityField = 'totalActivities'
	}

	// Build the where clause for filtering
	const where: Prisma.NFTWhereInput = {}

	// Apply date filtering if provided
	if (startDate || endDate) {
		where.lastActivityAt = {}

		if (startDate) {
			where.lastActivityAt.gte = startDate
		}

		if (endDate) {
			where.lastActivityAt.lte = endDate
		}
	}

	// Apply collection, creator, and owner filters if provided
	if (collectionId) {
		where.collectionId = collectionId
	}

	if (creatorId) {
		where.creatorId = creatorId
	}

	if (ownerId) {
		where.ownerId = ownerId
	}

	// Calculate pagination
	const skip = (page - 1) * limit

	// Execute the query with efficient sorting and pagination
	const nfts = await prisma.nFT.findMany({
		where,
		orderBy: {
			[activityField]: sortOrder,
		},
		skip,
		take: limit,
		include: {
			creator: {
				select: {
					id: true,
					username: true,
					imageUrl: true,
				},
			},
			owner: {
				select: {
					id: true,
					username: true,
					imageUrl: true,
				},
			},
			collection: {
				select: {
					id: true,
					name: true,
					logoUrl: true,
				},
			},
		},
	})

	// Get the total count for pagination metadata
	const totalCount = await prisma.nFT.count({ where })

	return {
		nfts,
		pagination: {
			total: totalCount,
			page,
			limit,
			totalPages: Math.ceil(totalCount / limit),
		},
	}
}

/**
 * Get NFTs sorted by activity count with cursor-based pagination
 *
 * This function uses cursor-based pagination which is more efficient for large datasets
 * than offset-based pagination.
 */
export async function getNFTsByActivityCountCursor({
	limit = 20,
	activityPeriod = 'all',
	sortOrder = 'desc',
	cursor,
	startDate,
	endDate,
	collectionId,
	creatorId,
	ownerId,
}: NFTQueryOptions & { cursor?: string }) {
	// Determine which activity count field to use based on the period
	let activityField: string
	switch (activityPeriod) {
		case '7days':
			activityField = 'totalActivities7Days'
			break
		case '30days':
			activityField = 'totalActivities30Days'
			break
		default:
			activityField = 'totalActivities'
	}

	// Build the where clause for filtering
	const where: Prisma.NFTWhereInput = {}

	// Apply date filtering if provided
	if (startDate || endDate) {
		where.lastActivityAt = {}

		if (startDate) {
			where.lastActivityAt.gte = startDate
		}

		if (endDate) {
			where.lastActivityAt.lte = endDate
		}
	}

	// Apply collection, creator, and owner filters if provided
	if (collectionId) {
		where.collectionId = collectionId
	}

	if (creatorId) {
		where.creatorId = creatorId
	}

	if (ownerId) {
		where.ownerId = ownerId
	}

	// Execute the query with cursor-based pagination
	const nfts = await prisma.nFT.findMany({
		where,
		orderBy: {
			[activityField]: sortOrder,
		},
		take: limit,
		...(cursor ? { cursor: { id: cursor }, skip: 1 } : {}),
		include: {
			creator: {
				select: {
					id: true,
					username: true,
					imageUrl: true,
				},
			},
			owner: {
				select: {
					id: true,
					username: true,
					imageUrl: true,
				},
			},
			collection: {
				select: {
					id: true,
					name: true,
					logoUrl: true,
				},
			},
		},
	})

	return {
		nfts,
		nextCursor: nfts.length > 0 ? nfts[nfts.length - 1].id : null,
		hasMore: nfts.length === limit,
	}
}
