import { randomUsername } from '@/lib/utils'
import { prisma } from '../lib/prismaClient'

export async function generateUniqueUsername(): Promise<string> {
	let username = ''
	let isUnique = false
	while (!isUnique) {
		username = randomUsername()
		const existingUser = await prisma.user.findUnique({
			where: {
				username: username.toLowerCase(),
			},
			select: {
				id: true,
			},
		})
		if (!existingUser) {
			isUnique = true
		}
	}
	return username
}
