export { verifySolanaSignature }

import { verifySignature } from '@solana/kit'
import { convertPublicKeyToCryptoKey, toSignatureBytes } from '@/lib/utils'
import { getCurrentTimeStamp } from './getTime'

async function verifySolanaSignature({
	publicKey,
	signature,
	message,
}: {
	publicKey: string
	signature: Uint8Array
	message: string
}): Promise<boolean> {
	try {
		if (!validateMessage(message)) {
			return false
		}
		const encodedPublicKey = await convertPublicKeyToCryptoKey(publicKey)
		if (!encodedPublicKey) {
			return false
		}
		const verified = await verifySignature(
			encodedPublicKey,
			toSignatureBytes(signature),
			new TextEncoder().encode(message),
		)

		return verified
	} catch (e) {
		return false
	}
}

function validateMessage(message: string) {
	try {
		const timestamp = Number.parseInt(message.replace('signin', ''), 10)
		const differenceInSeconds = getCurrentTimeStamp() - timestamp

		return differenceInSeconds < 300
	} catch (e) {
		return false
	}
}
