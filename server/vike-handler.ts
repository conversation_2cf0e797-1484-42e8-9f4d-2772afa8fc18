import type { Context } from 'hono'
import { renderPage } from 'vike/server'

// Define a type for the runtime object
interface RuntimeContext {
	[key: string]: unknown
}

export const vikeHandler = async (
	request: Request,
	context: Context,
	runtime: RuntimeContext,
) => {
	const pageContextInit = {
		...context,
		...runtime,
		urlOriginal: request.url,
		headersOriginal: request.headers,
	}
	const pageContext = await renderPage(pageContextInit)
	const response = pageContext.httpResponse

	const { readable, writable } = new TransformStream()
	response.pipe(writable)

	return new Response(readable, {
		status: response.statusCode,
		headers: response.headers,
	})
}
