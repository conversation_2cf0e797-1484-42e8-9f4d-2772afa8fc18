# pre-commit:
#   commands:
#     check:
#       glob: "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
#       run: bunx @biomejs/biome check --write --no-errors-on-unmatched --files-ignore-unknown=true --colors=off {staged_files}
#       stage_fixed: true

# pre-push:
#   commands:
#     check:
#       glob: "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
#       run: bunx @biomejs/biome check --no-errors-on-unmatched --files-ignore-unknown=true --max-diagnostics=30 --colors=off {push_files}
