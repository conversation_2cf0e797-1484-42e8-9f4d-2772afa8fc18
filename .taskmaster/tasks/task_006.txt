# Task ID: 6
# Title: Convert onFileUploadPresignedUrl to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: high
# Description: Migrate the onFileUploadPresignedUrl function from server/app/files/files.telefunc.ts to a REST API endpoint
# Details:
Create POST /api/files/presigned-url endpoint for S3 presigned URL generation with proper validation

# Test Strategy:
Test presigned URL generation, verify file upload functionality, ensure proper S3 integration

# Subtasks:
## 6.1. Create Hono route handler for presigned URLs [pending]
### Dependencies: None
### Description: Implement POST /api/files/presigned-url endpoint with S3 integration
### Details:


## 6.2. Create TanStack Query hook for file uploads [pending]
### Dependencies: None
### Description: Create hooks/useFileUpload.ts with proper upload handling
### Details:


## 6.3. Update file upload utilities to use new hook [pending]
### Dependencies: None
### Description: Update lib/fileUpload.ts to use REST API
### Details:


## 6.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify file upload functionality and remove original function
### Details:


