# Task ID: 8
# Title: Convert onGetUserCollections to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Migrate the onGetUserCollections function from pages/(protected)/studio/nft/mint/Page.telefunc.ts to a REST API endpoint
# Details:
Create GET /api/user/collections endpoint to retrieve user's collections for dropdowns and selection

# Test Strategy:
Test collection retrieval, verify user authentication, ensure proper collection data format

# Subtasks:
## 8.1. Create Hono route handler for user collections [pending]
### Dependencies: None
### Description: Implement GET /api/user/collections endpoint
### Details:


## 8.2. Create TanStack Query hook for user collections [pending]
### Dependencies: None
### Description: Create hooks/useUserCollections.ts with caching
### Details:


## 8.3. Update mint page to use new hook [pending]
### Dependencies: None
### Description: Update collection dropdown in mint page to use REST API
### Details:


## 8.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify collection loading and remove original function
### Details:


