# Task ID: 14
# Title: Convert onGetTrendingCollections to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: low
# Description: Migrate the onGetTrendingCollections function from pages/index/Page.telefunc.ts to a REST API endpoint
# Details:
Create GET /api/collections/trending endpoint for homepage trending section

# Test Strategy:
Test trending collections retrieval, verify sorting and data accuracy

# Subtasks:
## 14.1. Create Hono route handler for trending collections [pending]
### Dependencies: None
### Description: Implement GET /api/collections/trending endpoint
### Details:


## 14.2. Create TanStack Query hook for trending collections [pending]
### Dependencies: None
### Description: Create hooks/useTrendingCollections.ts
### Details:


## 14.3. Update home page to use new hook [pending]
### Dependencies: None
### Description: Update index page trending section to use REST API
### Details:


## 14.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify trending functionality and remove original function
### Details:


