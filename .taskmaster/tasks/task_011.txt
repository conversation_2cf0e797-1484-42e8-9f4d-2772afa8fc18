# Task ID: 11
# Title: Convert onGetNFT to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Migrate the onGetNFT function from pages/nft-details/Page.telefunc.ts to a REST API endpoint
# Details:
Create GET /api/nft/:id endpoint for individual NFT details with complex data relationships

# Test Strategy:
Test NFT detail retrieval, verify all related data is included, ensure proper error handling for non-existent NFTs

# Subtasks:
## 11.1. Create Hono route handler for NFT details [pending]
### Dependencies: None
### Description: Implement GET /api/nft/:id endpoint with complex data fetching
### Details:


## 11.2. Create TanStack Query hook for NFT details [pending]
### Dependencies: None
### Description: Create hooks/useNFT.ts with caching and error handling
### Details:


## 11.3. Update NFT details page to use new hook [pending]
### Dependencies: None
### Description: Update nft-details page and list-for-sale page to use REST API
### Details:


## 11.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify NFT details functionality and remove original function
### Details:


