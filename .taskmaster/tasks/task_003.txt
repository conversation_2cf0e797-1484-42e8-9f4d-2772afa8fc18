# Task ID: 3
# Title: Convert onLogin to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: high
# Description: Migrate the onLogin function from components/login/LoginButton.telefunc.ts to a REST API endpoint
# Details:
Create POST /api/auth/login endpoint with Solana signature verification, user creation/lookup, and session management

# Test Strategy:
Test login flow with valid/invalid signatures, verify user creation for new users, ensure proper session creation

# Subtasks:
## 3.1. Create Hono route handler for login [pending]
### Dependencies: None
### Description: Implement POST /api/auth/login endpoint with signature verification
### Details:


## 3.2. Create TanStack Query hook for login [pending]
### Dependencies: None
### Description: Create hooks/useLogin.ts with proper authentication flow
### Details:


## 3.3. Update login components to use new hook [pending]
### Dependencies: None
### Description: Update LoginButton and related components to use REST API
### Details:


## 3.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify login functionality and remove original onLogin
### Details:


