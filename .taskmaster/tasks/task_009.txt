# Task ID: 9
# Title: Convert onEditProfile to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Migrate the onEditProfile function from pages/(protected)/edit-profile/EditProfileForm.telefunc.ts to a REST API endpoint
# Details:
Create PUT /api/user/profile endpoint for user profile updates with validation

# Test Strategy:
Test profile updates, verify data validation, ensure proper user authentication

# Subtasks:
## 9.1. Create Hono route handler for profile updates [pending]
### Dependencies: None
### Description: Implement PUT /api/user/profile endpoint with validation
### Details:


## 9.2. Create TanStack Query hook for profile updates [pending]
### Dependencies: None
### Description: Create hooks/useEditProfile.ts with mutation handling
### Details:


## 9.3. Update edit profile form to use new hook [pending]
### Dependencies: None
### Description: Update EditProfileForm to use REST API
### Details:


## 9.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify profile editing and remove original function
### Details:


