# Task ID: 2
# Title: Convert onRefreshToken to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: high
# Description: Migrate the onRefreshToken function from server/app/auth/auth.telefunc.ts to a REST API endpoint using Hono framework
# Details:
Create POST /api/auth/refresh endpoint with proper JWT validation, session management, and token generation. This is critical for maintaining user authentication sessions.

# Test Strategy:
Test token refresh flow, verify new tokens are valid, ensure old sessions are properly invalidated

# Subtasks:
## 2.1. Create Hono route handler for token refresh [pending]
### Dependencies: None
### Description: Implement POST /api/auth/refresh endpoint in server/app/auth/auth.route.ts
### Details:


## 2.2. Create TanStack Query hook for token refresh [pending]
### Dependencies: None
### Description: Create hooks/useRefreshToken.ts with proper error handling
### Details:


## 2.3. Update client-side code to use new hook [pending]
### Dependencies: None
### Description: Update lib/custom-fetch.ts to use the new REST API endpoint
### Details:


## 2.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify functionality and remove original onRefreshToken from auth.telefunc.ts
### Details:


