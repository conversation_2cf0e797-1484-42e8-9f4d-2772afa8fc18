# Task ID: 10
# Title: Convert onUpdateUser to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Migrate the onUpdateUser function from telefunc/user.telefunc.ts to a REST API endpoint
# Details:
Create PATCH /api/user endpoint for alternative user profile updates

# Test Strategy:
Test user updates, verify field validation, ensure no conflicts with onEditProfile

# Subtasks:
## 10.1. Create Hono route handler for user updates [pending]
### Dependencies: None
### Description: Implement PATCH /api/user endpoint
### Details:


## 10.2. Create TanStack Query hook for user updates [pending]
### Dependencies: None
### Description: Create hooks/useUpdateUser.ts
### Details:


## 10.3. Update user update functionality to use new hook [pending]
### Dependencies: None
### Description: Update any components using onUpdateUser
### Details:


## 10.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify user updates and remove original function
### Details:


