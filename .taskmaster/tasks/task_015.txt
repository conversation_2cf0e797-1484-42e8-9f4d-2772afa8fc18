# Task ID: 15
# Title: Convert profile page functions to REST API endpoints
# Status: pending
# Dependencies: None
# Priority: low
# Description: Migrate onGetNFTsByUser, onGetUserCreatedNfts, onUserAssetCounts, and onCheckIfFollowing from pages/profile/Page.telefunc.ts
# Details:
Create multiple profile-related endpoints for user NFTs, created NFTs, asset counts, and follow status

# Test Strategy:
Test all profile functionality, verify user-specific data filtering, ensure proper authentication

# Subtasks:
## 15.1. Create Hono route handlers for profile endpoints [pending]
### Dependencies: None
### Description: Implement GET /api/user/:id/nfts, /api/user/:id/created-nfts, /api/user/:id/asset-counts, /api/user/:id/follow-status
### Details:


## 15.2. Create TanStack Query hooks for profile features [pending]
### Dependencies: None
### Description: Create hooks for user NFTs, created NFTs, asset counts, and follow status
### Details:


## 15.3. Update profile page to use new hooks [pending]
### Dependencies: None
### Description: Update profile page components to use REST API
### Details:


## 15.4. Test and cleanup original Telefunc functions [pending]
### Dependencies: None
### Description: Verify profile functionality and remove original functions
### Details:


