# Task ID: 13
# Title: Convert explore page functions to REST API endpoints
# Status: pending
# Dependencies: None
# Priority: low
# Description: Migrate onGetNFTs, onGetCollection, and onGetFusionNFTs from pages/explore/Page.telefunc.ts
# Details:
Create GET /api/explore/nfts, GET /api/explore/collections, and GET /api/explore/fusion-nfts endpoints

# Test Strategy:
Test browse functionality, verify filtering and pagination, ensure proper data formatting

# Subtasks:
## 13.1. Create Hono route handlers for explore endpoints [pending]
### Dependencies: None
### Description: Implement all three explore endpoints with filtering
### Details:


## 13.2. Create TanStack Query hooks for explore features [pending]
### Dependencies: None
### Description: Create hooks for NFT browsing, collection browsing, and fusion NFTs
### Details:


## 13.3. Update explore page to use new hooks [pending]
### Dependencies: None
### Description: Update explore page components to use REST API
### Details:


## 13.4. Test and cleanup original Telefunc functions [pending]
### Dependencies: None
### Description: Verify explore functionality and remove original functions
### Details:


