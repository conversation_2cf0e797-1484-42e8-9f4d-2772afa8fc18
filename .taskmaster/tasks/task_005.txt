# Task ID: 5
# Title: Convert onMe to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: high
# Description: Migrate the onMe function from server/app/auth/auth.telefunc.ts to a REST API endpoint
# Details:
Create GET /api/auth/me endpoint to retrieve current user profile data with proper authentication

# Test Strategy:
Test user profile retrieval, verify authentication requirements, ensure proper user data is returned

# Subtasks:
## 5.1. Create Hono route handler for user profile [pending]
### Dependencies: None
### Description: Implement GET /api/auth/me endpoint with authentication middleware
### Details:


## 5.2. Create TanStack Query hook for user profile [pending]
### Dependencies: None
### Description: Create hooks/useMe.ts with proper caching and error handling
### Details:


## 5.3. Update profile data loading to use new hook [pending]
### Dependencies: None
### Description: Update edit-profile data client to use REST API
### Details:


## 5.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify user profile functionality and remove original onMe
### Details:


