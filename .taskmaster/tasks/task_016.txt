# Task ID: 16
# Title: Convert onGetNFTActivities to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: low
# Description: Migrate the onGetNFTActivities function from pages/nft-details/Page.telefunc.ts to a REST API endpoint
# Details:
Create GET /api/nft/:id/activities endpoint for NFT transaction history

# Test Strategy:
Test NFT activity retrieval, verify chronological ordering, ensure proper data relationships

# Subtasks:
## 16.1. Create Hono route handler for NFT activities [pending]
### Dependencies: None
### Description: Implement GET /api/nft/:id/activities endpoint
### Details:


## 16.2. Create TanStack Query hook for NFT activities [pending]
### Dependencies: None
### Description: Create hooks/useNFTActivities.ts
### Details:


## 16.3. Update NFT details page to use new hook [pending]
### Dependencies: None
### Description: Update NFT details activity section to use REST API
### Details:


## 16.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify NFT activities and remove original function
### Details:


