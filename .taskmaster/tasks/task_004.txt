# Task ID: 4
# Title: Convert onLogout to REST API endpoint
# Status: pending
# Dependencies: None
# Priority: high
# Description: Migrate the onLogout function from components/login/LoginButton.telefunc.ts to a REST API endpoint
# Details:
Create POST /api/auth/logout endpoint with session invalidation and proper cleanup

# Test Strategy:
Test logout flow, verify sessions are invalidated, ensure proper cleanup of user state

# Subtasks:
## 4.1. Create Hono route handler for logout [pending]
### Dependencies: None
### Description: Implement POST /api/auth/logout endpoint with session cleanup
### Details:


## 4.2. Create TanStack Query hook for logout [pending]
### Dependencies: None
### Description: Create hooks/useLogout.ts with proper session cleanup
### Details:


## 4.3. Update logout components to use new hook [pending]
### Dependencies: None
### Description: Update logout functionality to use REST API
### Details:


## 4.4. Test and cleanup original Telefunc function [pending]
### Dependencies: None
### Description: Verify logout functionality and remove original onLogout
### Details:


