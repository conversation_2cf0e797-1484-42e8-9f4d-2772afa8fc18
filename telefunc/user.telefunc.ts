import { removeUndefined } from '@/lib/utils'
import { getUser } from '@/server/helpers/getUser'
import { prisma } from '@/server/lib/prismaClient'

export async function onUpdateUser({
	userName,
	imageUrl,
	instagramId,
	twitterId,
	websiteId,
	telegramId,
	email,
	bannerUrl,
	bio,
	facebookId,
}: {
	userName?: string
	imageUrl?: string
	instagramId?: string
	twitterId?: string
	websiteId?: string
	telegramId?: string
	email?: string
	bannerUrl?: string
	bio?: string
	facebookId?: string
}) {
	const user = getUser()
	try {
		const updateParams = removeUndefined({
			userName,
			imageUrl,
			instagramId,
			twitterId,
			websiteId,
			telegramId,
			bio,
			email,
			bannerUrl,
			facebookId,
		}) as Partial<{
			userName: string
			imageUrl: string
			instagramId: string
			twitterId: string
			websiteId: string
			telegramId: string
			bio: string
			email: string
			bannerUrl: string
			facebookId: string
		}> & { username?: string }

		//added the username in lowercase with index
		if (updateParams.userName !== undefined) {
			updateParams.username = updateParams.userName.toLowerCase()
		}

		await prisma.user.update({
			where: {
				id: user.userId,
			},
			data: updateParams,
		})

		return {
			success: true,
			data: {},
			message: 'User update successful',
		}
	} catch (e) {
		return {
			success: false,
			data: {},
			message: 'User update failed',
		}
	}
}
