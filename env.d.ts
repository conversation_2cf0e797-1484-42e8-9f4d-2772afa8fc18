// Global type definitions for environment variables

// For server-side environment variables
declare namespace NodeJS {
	interface ProcessEnv {
		// Database
		DATABASE_URL: string

		// Optimize Database
		OPTIMIZE_API_KEY: string

		// Environment
		PUBLIC_ENV__NODE_ENV: 'development' | 'production' | 'test'

		// R2 Storage
		R2_Access_Key_ID: string
		R2_Secret_Access_Key: string
		R2_BUCKET_NAME: string
		R2_PUBLIC_DOMAIN: string

		// Analytics
		PUBLIC_ENV__GOOGLE_ANALYTICS: string

		// Error Reporting
		SENTRY_DSN: string
		PUBLIC_ENV__SENTRY_DSN: string

		// Irys
		IRYS_PRIVATE_KEY: string
	}
}

// For Vite client-side environment variables
interface ImportMetaEnv {
	// Only variables prefixed with VITE_ are exposed to client-side code
	// Typed as possibly undefined since PUBLIC_ENV__ variables may be
	// handled differently in your setup
	readonly PUBLIC_ENV__NODE_ENV?: 'development' | 'production' | 'test'
	readonly PUBLIC_ENV__GOOGLE_ANALYTICS?: string
	readonly PUBLIC_ENV__SENTRY_DSN?: string
}

interface ImportMeta {
	readonly env: ImportMetaEnv
}
