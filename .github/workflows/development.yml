name: Deploy Worker (Development)
on:
  push:
    branches:
      - development

jobs:
  deploy:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    name: Deploy
    steps:
      - uses: actions/checkout@v4

      - name: Install Bun
        run: |
          curl -fsSL https://bun.sh/install | bash
          echo "$HOME/.bun/bin" >> $GITHUB_PATH  # Ensure Bun is in PATH
      - name: Verify Bun Installation
        run: bun --version  # Check if Bun is installed correctly

      - name: Install Dependencies
        run: bun install

      - name: Build Project
        run: bun run build # Use Bun for the build process

      - name: Deploy to Cloudflare Workers
        run:  bun run deploy
        env: 
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          OPTIMIZE_API_KEY: ${{ secrets.OPTIMIZE_API_KEY }}
          PUBLIC_ENV__NODE_ENV: ${{ secrets.PUBLIC_ENV__NODE_ENV }}
          R2_ACCESS_KEY_ID: ${{ secrets.R2_ACCESS_KEY_ID }}
          R2_SECRET_ACCESS_KEY: ${{ secrets.R2_SECRET_ACCESS_KEY }}
          R2_BUCKET_NAME: ${{ secrets.R2_BUCKET_NAME }}
          R2_PUBLIC_DOMAIN: ${{ secrets.R2_PUBLIC_DOMAIN }}
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          PUBLIC_ENV__SENTRY_DSN: ${{ secrets.PUBLIC_ENV__SENTRY_DSN }}
          IRYS_PRIVATE_KEY: ${{ secrets.IRYS_PRIVATE_KEY }}
          PUBLIC_ENV__NFT_UPLOAD_URL: ${{ secrets.PUBLIC_ENV__NFT_UPLOAD_URL }}
          CLOUDFLARE_ACCOUNT_ID : ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          SOLANA_RPC_URL: ${{ secrets.SOLANA_RPC_URL }}
          PUBLIC_ENV__SERVER_AUTHORITY: ${{ secrets.PUBLIC_ENV__SERVER_AUTHORITY }}
          NODE_ENV: ${{ secrets.NODE_ENV }}

