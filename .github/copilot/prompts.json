{"prompts": [{"name": "Vike Page Component", "description": "Create a new Vike page component following project conventions", "prompt": "Create a new Vike page component for [feature] using React 19 with functional components and TypeScript. Follow project conventions with proper type definitions and Telefunc integration if needed. Structure as per our established folder pattern with +Page.tsx and +config.ts files."}, {"name": "React 19 Component", "description": "Generate a React 19 functional component with proper typing", "prompt": "Generate a React 19 functional component for [purpose] using TypeScript. Use single quotes, 2-space indentation, and implement proper memoization with useCallback for event handlers. Ensure all props are properly typed and follow composition patterns."}, {"name": "EnhancedAsset Implementation", "description": "Transform Metaplex AssetV1 to EnhancedAsset type", "prompt": "Create a function to transform a Metaplex AssetV1 object into our EnhancedAsset type. Include comprehensive error handling, proper TypeScript typing, and follow our established patterns for NFT data transformation."}, {"name": "NFT Metadata Hook", "description": "Create a custom hook for fetching NFT metadata", "prompt": "Generate a custom React hook for fetching NFT metadata from Metaplex. Implement proper error handling, caching, and follow our optimized memoization patterns. Return loading state, error handling, and the enhanced NFT data."}, {"name": "Shadcn UI Component", "description": "Create a component using Shadcn UI and Tailwind", "prompt": "Create a [component type] using Shadcn UI and Tailwind CSS. Implement it as a React 19 functional component with TypeScript typing for all props. Ensure Tailwind classes are sorted according to Biome's useSortedClasses rule."}, {"name": "NFT Card Component", "description": "Generate an NFT card display component", "prompt": "Generate an NFT card component that displays metadata from our EnhancedAsset type. Use Tailwind CSS for styling with properly sorted classes. Implement proper error states and loading indicators, following our established patterns for NFT display components."}, {"name": "Telefunc API Function", "description": "Create a Telefunc serverless function", "prompt": "Create a Telefunc serverless function for [functionality] with TypeScript. Implement proper error handling, database interactions using Prisma if needed, and ensure compatibility with Cloudflare Workers. Follow project conventions for Telefunc files."}, {"name": "Hono Middleware", "description": "Generate a Hono middleware function", "prompt": "Generate a Hono middleware function for [purpose] that integrates with our existing server setup. Include TypeScript typing, proper error handling, and ensure it's optimized for Cloudflare Workers deployment."}, {"name": "Wallet Connection Component", "description": "Create a Solana wallet connection component", "prompt": "Create a wallet connection component for Solana that implements our auto-login functionality. Use React 19 hooks with optimized memoization patterns and provide comprehensive error handling and user feedback. Follow our established authentication flow."}, {"name": "Transaction Handler", "description": "Generate a function to handle Solana transactions", "prompt": "Generate a function to handle [transaction type] on Solana. Implement proper error handling, wallet integration, and user feedback. Ensure the function is reusable and follows our TypeScript conventions."}, {"name": "Component Test", "description": "Create a test suite for a component", "prompt": "Create a test suite for the [component name] component using our testing framework. Include tests for all major functionality, edge cases, and error states. Follow our established testing patterns with proper mocking of APIs and state."}, {"name": "API Function Test", "description": "Generate tests for a Telefunc function", "prompt": "Generate tests for the [function name] Telefunc function. Include tests for successful execution, validation errors, and API failures. Mock necessary dependencies and follow our established testing conventions."}, {"name": "Prisma Model Extension", "description": "Extend our Prisma schema for new functionality", "prompt": "Extend our Prisma schema to include [new entity/relationship] for NFT marketplace functionality. Ensure the schema is compatible with Cloudflare Workers and follows our established data modeling patterns. Include appropriate relations and field types."}, {"name": "Database Query Function", "description": "Create a function to query the database using Prisma", "prompt": "Create a function that queries our database using Prisma for [purpose]. Implement proper error handling, type safety, and optimize for performance in a Cloudflare Workers environment. Follow our established patterns for database interactions."}]}